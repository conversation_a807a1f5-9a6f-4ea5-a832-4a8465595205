#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强相似度匹配演示脚本
专门针对主视频（已剪辑+字幕）与辅助视频（原始素材）的匹配优化

主要改进：
1. 字幕鲁棒特征提取 - 专门处理字幕干扰
2. 智能时长匹配策略 - 多尺度搜索确保精确时长
3. 运动特征分析 - 更好地匹配动态内容
4. 增强的相似度计算 - 针对剪辑差异优化
"""

import os
import sys
import time
from video_processor import VideoProcessor
from quality_validator import QualityValidator, print_quality_report
from enhanced_matching_config import get_auto_config, print_config_info

# 导入日志系统
try:
    from logger_system import log_info, log_error, log_warning, safe_print
except ImportError:
    def log_info(msg): print(f"INFO: {msg}")
    def log_error(msg): print(f"ERROR: {msg}")
    def log_warning(msg): print(f"WARNING: {msg}")
    def safe_print(*args, **kwargs): print(*args, **kwargs)

def enhanced_similarity_matching_demo():
    """
    增强相似度匹配演示
    """
    try:
        print("=" * 80)
        print("🚀 增强相似度匹配演示")
        print("专门优化：主视频(已剪辑+字幕) vs 辅助视频(原始素材)")
        print("=" * 80)
        
        # 初始化处理器和质量验证器
        processor = VideoProcessor(enable_gpu=True)
        validator = QualityValidator()

        # 测试FFmpeg安装
        if not processor.test_ffmpeg_installation():
            log_error("FFmpeg未正确安装，某些功能可能无法使用")
        
        # 设置视频文件路径
        main_video = "main_video.mp4"      # 主视频：已剪辑+字幕
        aux_video = "aux_video.mp4"        # 辅助视频：原始素材
        output_video = "enhanced_output.mp4"
        
        # 检查输入文件
        if not os.path.exists(main_video):
            log_error(f"主视频文件不存在: {main_video}")
            log_info("请将主视频文件命名为 'main_video.mp4' 并放在当前目录")
            return False
            
        if not os.path.exists(aux_video):
            log_error(f"辅助视频文件不存在: {aux_video}")
            log_info("请将辅助视频文件命名为 'aux_video.mp4' 并放在当前目录")
            return False
        
        print(f"\n📁 输入文件:")
        print(f"   主视频: {main_video}")
        print(f"   辅助视频: {aux_video}")
        print(f"   输出视频: {output_video}")

        # 自动配置检测
        print(f"\n🔧 自动配置检测...")
        auto_config = get_auto_config(main_video, aux_video)
        print_config_info(auto_config)
        
        # 步骤1: 场景检测
        print(f"\n🎬 步骤1: 智能场景检测...")
        start_time = time.time()
        
        log_info("检测主视频场景...")
        main_scenes = processor.split_video_into_scenes(main_video, threshold=25.0)
        
        log_info("检测辅助视频场景...")
        aux_scenes = processor.split_video_into_scenes(aux_video, threshold=30.0)
        
        scene_time = time.time() - start_time
        
        print(f"   主视频场景数: {len(main_scenes)}")
        print(f"   辅助视频场景数: {len(aux_scenes)}")
        print(f"   场景检测耗时: {scene_time:.2f}秒")
        
        if not main_scenes or not aux_scenes:
            log_error("场景检测失败，无法继续")
            return False
        
        # 步骤2: 智能阈值分析
        print(f"\n🧠 步骤2: 智能阈值分析...")
        start_time = time.time()
        
        adaptive_threshold = processor.get_adaptive_similarity_threshold(
            main_scenes, aux_scenes, main_video, aux_video,
            base_threshold=0.75, sample_size=3
        )
        
        threshold_time = time.time() - start_time
        print(f"   自适应阈值: {adaptive_threshold:.3f}")
        print(f"   阈值分析耗时: {threshold_time:.2f}秒")
        
        # 步骤3: 增强相似度匹配
        print(f"\n🎯 步骤3: 增强相似度匹配...")
        print("   特征类型: 字幕鲁棒 + 感知哈希 + 颜色直方图 + 边缘 + 运动")
        start_time = time.time()
        
        # 使用自动配置的特征类型和权重
        enhanced_feature_types = auto_config['feature_types']
        enhanced_feature_weights = auto_config['feature_weights']
        
        matches = processor.find_similar_scenes(
            main_scenes, aux_scenes,
            main_video, aux_video,
            similarity_threshold=adaptive_threshold,
            feature_types=enhanced_feature_types,
            feature_weights=enhanced_feature_weights
        )
        
        matching_time = time.time() - start_time
        
        print(f"   找到匹配: {len(matches)}/{len(main_scenes)}")
        print(f"   匹配率: {len(matches)/len(main_scenes)*100:.1f}%")
        print(f"   匹配耗时: {matching_time:.2f}秒")
        
        if not matches:
            log_warning("未找到任何匹配，尝试降低阈值")
            
            # 尝试更低的阈值
            lower_threshold = max(0.5, adaptive_threshold - 0.1)
            print(f"   尝试更低阈值: {lower_threshold:.3f}")
            
            matches = processor.find_similar_scenes(
                main_scenes, aux_scenes,
                main_video, aux_video,
                similarity_threshold=lower_threshold,
                feature_types=enhanced_feature_types,
                feature_weights=enhanced_feature_weights
            )
            
            print(f"   新匹配数: {len(matches)}/{len(main_scenes)}")
        
        if not matches:
            log_error("仍然没有找到匹配，请检查视频内容相关性")
            return False
        
        # 步骤4: 增强质量分析
        print(f"\n📊 步骤4: 增强质量分析...")

        # 使用增强的质量验证器
        quality_report = validator.comprehensive_quality_analysis(
            matches, main_scenes, main_video, aux_video
        )

        # 打印详细质量报告
        print_quality_report(quality_report)
        
        # 步骤5: 增强视频合成
        print(f"\n🎥 步骤5: 增强视频合成...")
        start_time = time.time()
        
        result_path = processor.replace_and_concatenate_videos(
            main_video, aux_video,
            main_scenes, aux_scenes,
            matches, output_video
        )
        
        synthesis_time = time.time() - start_time
        
        if result_path and os.path.exists(result_path):
            file_size = os.path.getsize(result_path) / (1024 * 1024)  # MB
            print(f"   ✅ 视频合成成功!")
            print(f"   输出文件: {result_path}")
            print(f"   文件大小: {file_size:.2f} MB")
            print(f"   合成耗时: {synthesis_time:.2f}秒")
        else:
            log_error("视频合成失败")
            return False
        
        # 总结
        total_time = scene_time + threshold_time + matching_time + synthesis_time
        print(f"\n🏆 处理完成!")
        print(f"   总耗时: {total_time:.2f}秒")
        print(f"   场景检测: {scene_time:.1f}s ({scene_time/total_time*100:.1f}%)")
        print(f"   阈值分析: {threshold_time:.1f}s ({threshold_time/total_time*100:.1f}%)")
        print(f"   相似度匹配: {matching_time:.1f}s ({matching_time/total_time*100:.1f}%)")
        print(f"   视频合成: {synthesis_time:.1f}s ({synthesis_time/total_time*100:.1f}%)")
        
        return True
        
    except Exception as e:
        log_error(f"演示过程发生错误: {e}")
        import traceback
        log_error(traceback.format_exc())
        return False

def main():
    """主函数"""
    try:
        success = enhanced_similarity_matching_demo()
        
        if success:
            print(f"\n🎉 增强相似度匹配演示成功完成!")
            print(f"主要改进:")
            print(f"  ✓ 字幕鲁棒特征 - 自动处理字幕干扰")
            print(f"  ✓ 智能时长匹配 - 多尺度搜索确保精确时长")
            print(f"  ✓ 运动特征分析 - 更好匹配动态内容")
            print(f"  ✓ 增强相似度计算 - 针对剪辑差异优化")
        else:
            print(f"\n❌ 演示失败，请检查错误信息")
            
    except KeyboardInterrupt:
        print(f"\n⏹️ 用户中断操作")
    except Exception as e:
        log_error(f"主函数发生错误: {e}")

if __name__ == "__main__":
    main()
