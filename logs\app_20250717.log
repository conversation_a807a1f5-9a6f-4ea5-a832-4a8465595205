2025-07-17 21:36:44 [INFO] 📜 创建启动脚本...
2025-07-17 21:36:44 [INFO]   创建Shell启动脚本: 启动程序.sh
2025-07-17 21:49:35 [INFO] 📜 创建启动脚本...
2025-07-17 21:49:35 [INFO]   创建Shell启动脚本: 启动程序.sh
2025-07-17 23:46:58 [INFO] ✅ 激活验证模块加载成功
2025-07-17 23:47:02 [INFO] ✅ 单实例应用初始化成功
2025-07-17 23:47:02 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-17 23:47:04 [INFO] 🔑 机器码: 467B-35B4-A595-775D
2025-07-17 23:47:04 [INFO] 📁 配置文件路径: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\activation_config.json
2025-07-17 23:47:04 [INFO] 🔍 检查本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-17 23:47:04 [INFO] 🔍 与服务器验证激活码有效性
2025-07-17 23:47:04 [ERROR] ❌ 服务器验证失败: 激活码已绑定其他设备
2025-07-17 23:47:04 [ERROR] ❌ 激活码服务器验证失败
2025-07-17 23:47:04 [INFO] 📁 配置文件路径: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\activation_config.json
2025-07-17 23:47:04 [INFO] 🗑️ 已清除无效的本地激活配置
2025-07-17 23:47:04 [ERROR] ❌ 本地激活验证失败，需要重新激活
2025-07-17 23:47:04 [INFO] ✅ 激活对话框图标设置成功: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\img\logo.ico
2025-07-17 23:47:05 [INFO] 📁 配置文件路径: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\activation_config.json
2025-07-17 23:47:05 [INFO] 📄 本地激活配置文件不存在
2025-07-17 23:47:30 [INFO] 📊 收集到机器详细信息: 10 项
2025-07-17 23:47:31 [INFO] 📁 配置文件路径: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\activation_config.json
2025-07-17 23:47:31 [INFO] ✅ 激活状态已保存到: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\activation_config.json
2025-07-17 23:47:32 [INFO] ✅ 激活验证成功
2025-07-17 23:47:33 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-17 23:47:38 [ERROR] ❌ FFmpeg检查失败: unexpected indent (video_processor.py, line 1874)
2025-07-17 23:48:18 [INFO] Error initializing OptimizedVideoProcessor: unexpected indent (video_processor.py, line 1874)
2025-07-17 23:50:36 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-07-17 23:51:19 [INFO] 🚪 用户选择完全退出程序
2025-07-17 23:51:37 [INFO] ✅ 激活验证模块加载成功
2025-07-17 23:51:40 [INFO] ✅ 单实例应用初始化成功
2025-07-17 23:51:40 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-17 23:51:43 [INFO] 🔑 机器码: 467B-35B4-A595-775D
2025-07-17 23:51:43 [INFO] 📁 配置文件路径: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\activation_config.json
2025-07-17 23:51:43 [INFO] 🔍 检查本地保存的激活码: 71C9-4CBE-4B7F-48BB
2025-07-17 23:51:43 [INFO] 🔍 与服务器验证激活码有效性
2025-07-17 23:51:43 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-17 23:51:43 [INFO] ✅ 激活码验证通过
2025-07-17 23:51:43 [INFO] ✅ 本地激活验证通过
2025-07-17 23:51:43 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-17 23:51:47 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-07-17 23:51:48 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-17 23:51:48 [INFO] 未检测到CUDA设备
2025-07-17 23:51:48 [INFO] 创建临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-17 23:51:48 [INFO] VideoProcessor初始化:
2025-07-17 23:51:48 [INFO]   CPU线程数: 8
2025-07-17 23:51:48 [INFO]   GPU支持: 否
2025-07-17 23:51:48 [INFO]   GPU加速: 禁用
2025-07-17 23:51:48 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-17 23:51:48 [INFO] ✅ FFmpeg已配置: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-17 23:51:48 [INFO] 🔍 测试FFmpeg安装...
2025-07-17 23:51:48 [INFO] ✅ FFmpeg安装正确: ffmpeg version 7.0.2-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
2025-07-17 23:51:48 [INFO] ✅ 支持PCM音频编码
2025-07-17 23:51:48 [INFO] ✅ FFmpeg测试通过
2025-07-17 23:52:14 [INFO] ✅ 激活验证模块加载成功
2025-07-17 23:52:15 [WARNING] ⚠️ 应用程序已在运行，激活现有窗口
2025-07-17 23:52:15 [INFO] 📢 收到激活信号，显示主窗口
2025-07-17 23:58:37 [INFO] ✅ 激活验证模块加载成功
2025-07-17 23:58:39 [INFO] ✅ 单实例应用初始化成功
2025-07-17 23:58:39 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-17 23:58:40 [INFO] 🔑 机器码: 467B-35B4-A595-775D
2025-07-17 23:58:40 [INFO] 📁 配置文件路径: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\activation_config.json
2025-07-17 23:58:40 [INFO] 🔍 检查本地保存的激活码: 71C9-4CBE-4B7F-48BB
2025-07-17 23:58:40 [INFO] 🔍 与服务器验证激活码有效性
2025-07-17 23:58:40 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-17 23:58:40 [INFO] ✅ 激活码验证通过
2025-07-17 23:58:40 [INFO] ✅ 本地激活验证通过
2025-07-17 23:58:40 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-17 23:58:42 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-07-17 23:58:42 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-17 23:58:42 [INFO] 未检测到CUDA设备
2025-07-17 23:58:42 [INFO] VideoProcessor初始化:
2025-07-17 23:58:42 [INFO]   CPU线程数: 8
2025-07-17 23:58:42 [INFO]   GPU支持: 否
2025-07-17 23:58:42 [INFO]   GPU加速: 禁用
2025-07-17 23:58:42 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-17 23:58:42 [INFO] ✅ FFmpeg已配置: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-17 23:58:42 [INFO] 🔍 测试FFmpeg安装...
2025-07-17 23:58:42 [INFO] ✅ FFmpeg安装正确: ffmpeg version 7.0.2-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
2025-07-17 23:58:43 [INFO] ✅ 支持PCM音频编码
2025-07-17 23:58:43 [INFO] ✅ FFmpeg测试通过
2025-07-17 23:59:26 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-17 23:59:26 [INFO] 未检测到CUDA设备
2025-07-17 23:59:26 [INFO] VideoProcessor初始化:
2025-07-17 23:59:26 [INFO]   CPU线程数: 8
2025-07-17 23:59:26 [INFO]   GPU支持: 否
2025-07-17 23:59:26 [INFO]   GPU加速: 禁用
2025-07-17 23:59:26 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-17 23:59:26 [INFO] 使用PySceneDetect版本: 0.6.6
2025-07-17 23:59:26 [INFO] 成功导入ContentDetector(方法1)
2025-07-17 23:59:26 [INFO] 视频文件大小: 50.26 MB
2025-07-17 23:59:26 [INFO] 视频信息: 30.0 FPS, 7362 帧, 245.40 秒
2025-07-17 23:59:26 [INFO] 尝试使用PySceneDetect原生方法打开视频...
2025-07-17 23:59:26 [INFO] 开始执行场景检测...
2025-07-18 00:00:21 [INFO] 场景检测完成，耗时 55.64 秒
2025-07-18 00:00:21 [INFO] 检测到 7 个场景
2025-07-18 00:00:22 [INFO] 使用PySceneDetect版本: 0.6.6
2025-07-18 00:00:22 [INFO] 成功导入ContentDetector(方法1)
2025-07-18 00:00:22 [INFO] 视频文件大小: 40.83 MB
2025-07-18 00:00:22 [INFO] 视频信息: 30.0 FPS, 9760 帧, 325.33 秒
2025-07-18 00:00:22 [INFO] 尝试使用PySceneDetect原生方法打开视频...
2025-07-18 00:00:22 [INFO] 开始执行场景检测...
2025-07-18 00:02:00 [INFO] 场景检测完成，耗时 97.92 秒
2025-07-18 00:02:00 [INFO] 检测到 9 个场景
2025-07-18 00:02:00 [INFO] 优化相似度匹配: 主视频7场景, 辅助视频9场景
2025-07-18 00:02:00 [INFO] 正在并行提取主视频特征...
2025-07-18 00:02:05 [INFO] 正在并行提取辅助视频特征...
2025-07-18 00:02:11 [INFO] 正在并行计算相似度矩阵...
2025-07-18 00:02:11 [INFO] 正在查找最佳匹配...
2025-07-18 00:02:11 [INFO] 优化匹配完成: 找到 4 个匹配（限制: 7）
2025-07-18 00:02:11 [INFO] 使用临时路径处理视频: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\temp_output_5767a9d0_1752768131282_9669fef6.mp4
2025-07-18 00:02:11 [INFO] 最终输出路径: D:/25125/Videos/wwww112.mp4
2025-07-18 00:02:11 [INFO] 加载主视频: D:/Document/个人/配音模型素材/数据集/douge/小说配音/云浩宇-男/1.20/测试自媒体ab.mp4
2025-07-18 00:02:12 [INFO] 主视频加载成功，时长: 245.41秒
2025-07-18 00:02:12 [INFO] 开始按顺序处理 7 个主视频片段...
2025-07-18 00:02:12 [INFO] 其中 4 个片段将被替换
2025-07-18 00:02:12 [INFO] ✓ 保留主视频片段 1: 0.00-19.90秒
2025-07-18 00:02:12 [INFO] 片段 2 标记为替换 (时长: 9.93秒)
2025-07-18 00:02:13 [INFO] ✓ 保留主视频片段 3: 29.83-52.40秒
2025-07-18 00:02:13 [INFO] 片段 4 标记为替换 (时长: 9.93秒)
2025-07-18 00:02:13 [INFO] 片段 5 标记为替换 (时长: 146.93秒)
2025-07-18 00:02:13 [INFO] 片段 6 标记为替换 (时长: 7.70秒)
2025-07-18 00:02:13 [INFO] ✓ 保留主视频片段 7: 216.97-245.40秒
2025-07-18 00:02:13 [INFO] 处理 4 个替换片段...
2025-07-18 00:02:13 [INFO] 使用传统匹配格式（场景索引）
2025-07-18 00:02:13 [INFO] 加载辅助视频: D:/25125/Videos/pg_tool.mp4
2025-07-18 00:02:14 [INFO] 辅助视频加载成功，时长: 325.33秒
2025-07-18 00:02:14 [INFO] ✓ 替换片段 4: 辅助视频场景 5 (132.30-142.27秒)
2025-07-18 00:02:15 [INFO] ✓ 替换片段 6: 辅助视频场景 7 (289.17-296.87秒)
2025-07-18 00:02:15 [INFO] ✓ 替换片段 2: 辅助视频场景 3 (99.80-109.73秒)
2025-07-18 00:02:16 [INFO] ✓ 替换片段 5: 辅助视频场景 6 (142.27-289.17秒)
2025-07-18 00:02:16 [INFO] 替换处理完成: 成功替换 4/4 个片段
2025-07-18 00:02:16 [INFO] 片段分析: 主视频场景数=7, 匹配数=4, 成功片段数=7
2025-07-18 00:02:16 [INFO] 开始合成 7 个视频片段...
2025-07-18 00:02:16 [INFO] 片段 0 验证通过，时长: 19.90秒
2025-07-18 00:02:16 [INFO] 片段 1 验证通过，时长: 9.93秒
2025-07-18 00:02:17 [INFO] 片段 2 验证通过，时长: 22.57秒
2025-07-18 00:02:17 [INFO] 片段 3 验证通过，时长: 9.97秒
2025-07-18 00:02:17 [INFO] 片段 4 验证通过，时长: 146.90秒
2025-07-18 00:02:18 [INFO] 片段 5 验证通过，时长: 7.70秒
2025-07-18 00:02:18 [INFO] 片段 6 验证通过，时长: 28.43秒
2025-07-18 00:02:18 [INFO] 有效片段数量: 7
2025-07-18 00:02:18 [INFO] 尝试方法1：使用内存优化方法合成视频...
2025-07-18 00:02:18 [INFO] 片段 0 验证通过，时长: 19.90秒
2025-07-18 00:02:19 [INFO] 片段 1 验证通过，时长: 9.93秒
2025-07-18 00:02:19 [INFO] 片段 2 验证通过，时长: 22.57秒
2025-07-18 00:02:20 [INFO] 片段 3 验证通过，时长: 9.97秒
2025-07-18 00:02:20 [INFO] 片段 4 验证通过，时长: 146.90秒
2025-07-18 00:02:21 [INFO] 片段 5 验证通过，时长: 7.70秒
2025-07-18 00:02:21 [INFO] 片段 6 验证通过，时长: 28.43秒
2025-07-18 00:02:21 [INFO] 合并 7 个有效片段，总时长: 245.40秒
2025-07-18 00:02:21 [INFO] 使用分批合并方法 (7 个片段)
2025-07-18 00:02:21 [INFO] 合并批次 1/4
2025-07-18 00:02:21 [INFO] 合并批次 2/4
2025-07-18 00:02:21 [INFO] 合并批次 3/4
2025-07-18 00:02:21 [INFO] 最终合并 4 个批次
2025-07-18 00:02:21 [INFO] 递归合并: 分为 2 和 2 个片段
2025-07-18 00:02:22 [INFO] 片段 0 验证通过，时长: 29.83秒
2025-07-18 00:02:22 [INFO] 片段 1 验证通过，时长: 32.53秒
2025-07-18 00:02:22 [INFO] 合并 2 个有效片段，总时长: 62.37秒
2025-07-18 00:02:22 [INFO] 使用标准合并方法 (2 个片段)
2025-07-18 00:02:23 [INFO] 片段 0 验证通过，时长: 154.60秒
2025-07-18 00:02:23 [INFO] 片段 1 验证通过，时长: 28.43秒
2025-07-18 00:02:23 [INFO] 合并 2 个有效片段，总时长: 183.03秒
2025-07-18 00:02:23 [INFO] 使用标准合并方法 (2 个片段)
2025-07-18 00:02:23 [INFO] 视频合成成功，总时长: 245.40秒
2025-07-18 00:02:24 [INFO] 开始写入视频片段，时长: 245.40秒
2025-07-18 00:02:24 [ERROR] 视频写入过程失败: 'FFMPEG_AudioWriter' object has no attribute 'ext'
2025-07-18 00:02:24 [WARNING] 视频写入失败，重试 1/5: 'FFMPEG_AudioWriter' object has no attribute 'ext'
2025-07-18 00:02:26 [ERROR] 视频写入过程失败: 'FFMPEG_AudioWriter' object has no attribute 'ext'
2025-07-18 00:02:26 [WARNING] 视频写入失败，重试 2/5: 'FFMPEG_AudioWriter' object has no attribute 'ext'
2025-07-18 00:02:29 [ERROR] 视频写入过程失败: 'FFMPEG_AudioWriter' object has no attribute 'ext'
2025-07-18 00:02:29 [WARNING] 视频写入失败，重试 3/5: 'FFMPEG_AudioWriter' object has no attribute 'ext'
2025-07-18 00:02:31 [ERROR] 视频写入过程失败: 'FFMPEG_AudioWriter' object has no attribute 'ext'
2025-07-18 00:02:31 [WARNING] 视频写入失败，重试 4/5: 'FFMPEG_AudioWriter' object has no attribute 'ext'
2025-07-18 00:02:33 [ERROR] 视频写入过程失败: 'FFMPEG_AudioWriter' object has no attribute 'ext'
2025-07-18 00:02:33 [ERROR] 视频写入最终失败: 'FFMPEG_AudioWriter' object has no attribute 'ext'
2025-07-18 00:02:33 [INFO] 尝试使用FFmpeg备用方法写入视频...
2025-07-18 00:02:33 [WARNING] 提取音频失败，将创建无声视频: 'CompositeAudioClip' object has no attribute 'fps'
2025-07-18 00:05:31 [INFO] 📦 窗口已最小化到任务栏
2025-07-18 00:05:31 [INFO] 📦 窗口已最小化到任务栏
2025-07-18 00:17:31 [INFO] 🚪 用户选择完全退出程序
2025-07-18 00:17:31 [ERROR] ❌ 退出程序时发生错误: 'VideoProcessingThread' object has no attribute 'stop'
