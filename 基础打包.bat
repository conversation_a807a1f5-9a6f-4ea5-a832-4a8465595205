@echo off
chcp 65001 >nul
title 基础打包命令

echo.
echo ========================================
echo 🎬 余下视频乱剪工具 - 基础打包命令
echo ========================================
echo.

echo 设置环境变量...
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1

echo.
echo 清理旧文件...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist

echo.
echo 执行基础打包命令...
echo.

pyinstaller --onefile --windowed --icon=img/logo.ico enhanced_video_deduplication_gui.py

echo.
if exist "dist\enhanced_video_deduplication_gui.exe" (
    echo ✅ 基础打包成功！
    echo.
    echo 📁 输出文件: dist\enhanced_video_deduplication_gui.exe
    echo.
    echo 重命名为中文名称...
    ren "dist\enhanced_video_deduplication_gui.exe" "余下视频乱剪工具.exe"
    
    if exist "dist\余下视频乱剪工具.exe" (
        echo ✅ 重命名成功！
        echo 📦 最终文件: dist\余下视频乱剪工具.exe
    )
) else (
    echo ❌ 打包失败！
    echo.
    echo 💡 请尝试以下解决方案:
    echo 1. 安装PyInstaller: pip install pyinstaller
    echo 2. 检查Python环境
    echo 3. 确保主程序文件存在
)

echo.
pause
