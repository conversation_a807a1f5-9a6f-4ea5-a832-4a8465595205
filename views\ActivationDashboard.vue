<template>
  <div class="activation-dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2>激活统计</h2>
        <p class="page-description">查看激活码和设备的统计信息</p>
      </div>
      <div class="header-right">
        <el-button type="info" icon="el-icon-refresh" @click="refreshData">
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card total-codes">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalCodes || 0 }}</div>
              <div class="stat-label">总激活码数</div>
              <div class="stat-trend">
                <i class="el-icon-tickets"></i>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card activated-codes">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.activatedCodes || 0 }}</div>
              <div class="stat-label">已激活</div>
              <div class="stat-trend">
                <i class="el-icon-check"></i>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card active-machines">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.activeMachines || 0 }}</div>
              <div class="stat-label">活跃设备</div>
              <div class="stat-trend">
                <i class="el-icon-monitor"></i>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card expired-codes">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.expiredCodes || 0 }}</div>
              <div class="stat-label">已过期</div>
              <div class="stat-trend">
                <i class="el-icon-time"></i>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细统计 -->
    <div class="details-section">
      <el-row :gutter="20">
        <!-- 激活码状态分布 -->
        <el-col :span="12">
          <el-card>
            <div slot="header" class="card-header">
              <span>激活码状态分布</span>
            </div>
            <div class="status-distribution">
              <div class="status-item">
                <div class="status-bar">
                  <div class="status-label">未激活</div>
                  <div class="status-progress">
                    <el-progress
                      :percentage="getPercentage(statistics.unactivatedCodes, statistics.totalCodes)"
                      :show-text="false"
                      color="#909399"
                    />
                  </div>
                  <div class="status-value">{{ statistics.unactivatedCodes || 0 }}</div>
                </div>
              </div>
              
              <div class="status-item">
                <div class="status-bar">
                  <div class="status-label">已激活</div>
                  <div class="status-progress">
                    <el-progress
                      :percentage="getPercentage(statistics.activatedCodes, statistics.totalCodes)"
                      :show-text="false"
                      color="#67C23A"
                    />
                  </div>
                  <div class="status-value">{{ statistics.activatedCodes || 0 }}</div>
                </div>
              </div>
              
              <div class="status-item">
                <div class="status-bar">
                  <div class="status-label">已禁用</div>
                  <div class="status-progress">
                    <el-progress
                      :percentage="getPercentage(statistics.disabledCodes, statistics.totalCodes)"
                      :show-text="false"
                      color="#F56C6C"
                    />
                  </div>
                  <div class="status-value">{{ statistics.disabledCodes || 0 }}</div>
                </div>
              </div>
              
              <div class="status-item">
                <div class="status-bar">
                  <div class="status-label">已过期</div>
                  <div class="status-progress">
                    <el-progress
                      :percentage="getPercentage(statistics.expiredCodes, statistics.totalCodes)"
                      :show-text="false"
                      color="#E6A23C"
                    />
                  </div>
                  <div class="status-value">{{ statistics.expiredCodes || 0 }}</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 系统健康状态 -->
        <el-col :span="12">
          <el-card>
            <div slot="header" class="card-header">
              <span>系统健康状态</span>
              <el-button
                type="text"
                icon="el-icon-refresh"
                @click="checkHealth"
                :loading="healthLoading"
              >
                检查
              </el-button>
            </div>
            <div class="health-status">
              <div class="health-item">
                <div class="health-icon">
                  <i :class="healthStatus.api ? 'el-icon-success' : 'el-icon-error'" 
                     :style="{ color: healthStatus.api ? '#67C23A' : '#F56C6C' }"></i>
                </div>
                <div class="health-info">
                  <div class="health-title">API服务</div>
                  <div class="health-desc">{{ healthStatus.api ? '运行正常' : '连接失败' }}</div>
                </div>
              </div>
              
              <div class="health-item">
                <div class="health-icon">
                  <i :class="healthStatus.database ? 'el-icon-success' : 'el-icon-error'"
                     :style="{ color: healthStatus.database ? '#67C23A' : '#F56C6C' }"></i>
                </div>
                <div class="health-info">
                  <div class="health-title">数据库</div>
                  <div class="health-desc">{{ healthStatus.database ? '连接正常' : '连接异常' }}</div>
                </div>
              </div>
              
              <div class="health-item">
                <div class="health-icon">
                  <i class="el-icon-time" style="color: #409EFF"></i>
                </div>
                <div class="health-info">
                  <div class="health-title">最后检查</div>
                  <div class="health-desc">{{ formatDateTime(healthStatus.lastCheck) }}</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activity-section">
      <el-card>
        <div slot="header" class="card-header">
          <span>最近活动</span>
        </div>
        <div class="activity-list">
          <div v-if="recentActivities.length === 0" class="no-activity">
            <i class="el-icon-info"></i>
            <span>暂无最近活动记录</span>
          </div>
          <div v-else>
            <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
              <div class="activity-icon">
                <i :class="getActivityIcon(activity.type)"></i>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-desc">{{ activity.description }}</div>
                <div class="activity-time">{{ formatDateTime(activity.time) }}</div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import activationApi from '@/api/activation'

export default {
  name: 'ActivationDashboard',
  data() {
    return {
      statistics: {},
      healthStatus: {
        api: false,
        database: false,
        lastCheck: null
      },
      healthLoading: false,
      recentActivities: []
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    // 加载数据
    async loadData() {
      await Promise.all([
        this.loadStatistics(),
        this.checkHealth()
      ])
    },
    
    // 加载统计数据
    async loadStatistics() {
      try {
        const response = await activationApi.getActivationStatistics()
        if (response.success) {
          this.statistics = response.data || {}
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },
    
    // 检查健康状态
    async checkHealth() {
      this.healthLoading = true
      try {
        const response = await activationApi.healthCheck()
        this.healthStatus = {
          api: response.success,
          database: response.success, // 假设API正常则数据库也正常
          lastCheck: new Date()
        }
      } catch (error) {
        console.error('健康检查失败:', error)
        this.healthStatus = {
          api: false,
          database: false,
          lastCheck: new Date()
        }
      } finally {
        this.healthLoading = false
      }
    },
    
    // 刷新数据
    refreshData() {
      this.loadData()
    },
    
    // 计算百分比
    getPercentage(value, total) {
      if (!total || total === 0) return 0
      return Math.round((value / total) * 100)
    },
    
    // 获取活动图标
    getActivityIcon(type) {
      const iconMap = {
        'activation': 'el-icon-check',
        'generation': 'el-icon-plus',
        'disable': 'el-icon-close',
        'enable': 'el-icon-refresh'
      }
      return iconMap[type] || 'el-icon-info'
    },
    
    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      return new Date(dateTime).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.activation-dashboard {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      h2 {
        margin: 0 0 5px 0;
        color: #303133;
        font-size: 24px;
        font-weight: 600;
      }
      
      .page-description {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }
  
  .statistics-section {
    margin-bottom: 20px;
    
    .stat-card {
      position: relative;
      overflow: hidden;
      min-height: 120px;
      
      &.total-codes {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }
      
      &.activated-codes {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
      }
      
      &.active-machines {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
      }
      
      &.expired-codes {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
      }
      
      .stat-content {
        position: relative;
        z-index: 2;
        
        .stat-number {
          font-size: 32px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        
        .stat-label {
          font-size: 14px;
          opacity: 0.9;
        }
        
        .stat-trend {
          position: absolute;
          right: 20px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 40px;
          opacity: 0.3;
        }
      }
    }
  }
  
  .details-section {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
    }
    
    .status-distribution {
      .status-item {
        margin-bottom: 15px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .status-bar {
          display: flex;
          align-items: center;
          gap: 15px;
          
          .status-label {
            width: 60px;
            font-size: 14px;
            color: #606266;
          }
          
          .status-progress {
            flex: 1;
          }
          
          .status-value {
            width: 40px;
            text-align: right;
            font-weight: bold;
            color: #303133;
          }
        }
      }
    }
    
    .health-status {
      .health-item {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .health-icon {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          background-color: #F5F7FA;
          margin-right: 15px;
          
          i {
            font-size: 20px;
          }
        }
        
        .health-info {
          .health-title {
            font-weight: bold;
            color: #303133;
            margin-bottom: 5px;
          }
          
          .health-desc {
            font-size: 14px;
            color: #606266;
          }
        }
      }
    }
  }
  
  .recent-activity-section {
    .activity-list {
      .no-activity {
        text-align: center;
        padding: 40px 0;
        color: #909399;
        
        i {
          font-size: 48px;
          margin-bottom: 10px;
          display: block;
        }
      }
      
      .activity-item {
        display: flex;
        align-items: flex-start;
        padding: 15px 0;
        border-bottom: 1px solid #EBEEF5;
        
        &:last-child {
          border-bottom: none;
        }
        
        .activity-icon {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          background-color: #F5F7FA;
          margin-right: 15px;
          
          i {
            font-size: 18px;
            color: #409EFF;
          }
        }
        
        .activity-content {
          flex: 1;
          
          .activity-title {
            font-weight: bold;
            color: #303133;
            margin-bottom: 5px;
          }
          
          .activity-desc {
            font-size: 14px;
            color: #606266;
            margin-bottom: 5px;
          }
          
          .activity-time {
            font-size: 12px;
            color: #C0C4CC;
          }
        }
      }
    }
  }
}
</style>
