# 🔧 编辑激活码API问题排查指南

## 🚨 问题描述
前端调用编辑激活码API时出现400错误：`AxiosError: Request failed with status code 400`

## 🔍 可能的原因和解决方案

### 1. 数据类型问题

#### 问题：maxActivations类型转换失败
**原因**：前端发送的数字可能被当作字符串处理

**解决方案**：
- ✅ 已修复后端类型转换逻辑
- ✅ 前端确保发送整数类型：`parseInt(this.form.maxActivations)`

#### 问题：时间格式不正确
**原因**：前端发送的时间格式后端无法解析

**解决方案**：
- ✅ 已修复后端时间解析逻辑，支持多种格式
- ✅ 前端使用标准ISO格式：`YYYY-MM-DDTHH:mm:ss`

### 2. CORS跨域问题

#### 问题：浏览器阻止跨域请求
**解决方案**：
- ✅ 已添加专门的CORS配置类：`backend/config/CorsConfig.java`
- ✅ 控制器已添加`@CrossOrigin`注解

### 3. 请求路径问题

#### 检查API路径是否正确
- 前端：`PUT /api/activation/update/{activationCode}`
- 后端：`@PutMapping("/update/{activationCode}")`
- 完整URL：`http://localhost:8080/api/activation/update/XXXX-XXXX-XXXX-XXXX`

### 4. 请求体格式问题

#### 正确的请求格式
```json
{
  "userId": "string",
  "maxActivations": 5,
  "expireTime": "2024-12-31T23:59:59",
  "remark": "string"
}
```

## 🧪 测试步骤

### 1. 使用测试页面
打开 `test_edit_api.html` 进行测试：
1. 确保后端服务运行在 `http://localhost:8080`
2. 点击"获取激活码列表"获取现有激活码
3. 选择一个激活码进行编辑测试
4. 先尝试"简单测试（仅备注）"

### 2. 检查后端日志
查看控制台输出的调试信息：
```
收到编辑激活码请求:
激活码: XXXX-XXXX-XXXX-XXXX
请求数据: {userId=..., maxActivations=..., ...}
```

### 3. 检查前端控制台
查看浏览器开发者工具的控制台和网络选项卡：
- 请求URL是否正确
- 请求头是否包含`Content-Type: application/json`
- 请求体数据格式是否正确

## 🔧 调试工具

### 1. 后端调试
已添加详细日志输出：
- 控制器层：请求参数和数据
- 服务层：业务逻辑执行过程

### 2. 前端调试
已添加控制台输出：
- 发送的数据格式
- 响应状态和数据
- 错误详细信息

### 3. 独立测试工具
- `test_edit_api.html`：浏览器测试页面
- `test_edit_activation_code.py`：Python测试脚本

## 🚀 快速修复检查清单

### 后端检查
- [ ] 后端服务是否正常启动
- [ ] 数据库连接是否正常
- [ ] CORS配置是否生效
- [ ] 控制器路径映射是否正确

### 前端检查
- [ ] API基础URL是否正确
- [ ] 请求方法是否为PUT
- [ ] Content-Type是否为application/json
- [ ] 数据类型是否正确（特别是maxActivations）

### 数据检查
- [ ] 激活码是否存在
- [ ] 时间格式是否正确
- [ ] 数字类型是否正确转换

## 📝 常见错误和解决方案

### 错误1：400 Bad Request - 时间格式错误
```json
{
  "success": false,
  "message": "过期时间格式错误，期望格式：yyyy-MM-ddTHH:mm:ss"
}
```
**解决**：检查前端时间格式化逻辑

### 错误2：400 Bad Request - 最大激活次数格式错误
```json
{
  "success": false,
  "message": "最大激活次数格式错误"
}
```
**解决**：确保前端发送整数类型

### 错误3：404 Not Found
**解决**：检查API路径和后端服务状态

### 错误4：CORS错误
**解决**：确保CORS配置正确，或使用代理

## 🔄 重新测试流程

1. **重启后端服务**
2. **清除浏览器缓存**
3. **使用测试页面进行简单测试**
4. **检查控制台日志**
5. **逐步增加复杂度**

## 📞 获取帮助

如果问题仍然存在，请提供：
1. 完整的错误信息
2. 前端发送的请求数据
3. 后端控制台日志
4. 浏览器网络选项卡截图

---

**最后更新**：2025-07-05  
**状态**：问题排查中
