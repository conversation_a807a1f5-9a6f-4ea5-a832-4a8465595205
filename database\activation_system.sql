-- 激活码验证系统数据库脚本
-- 数据库: MySQL 5.7+

-- 创建数据库
CREATE DATABASE IF NOT EXISTS activation_system 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE activation_system;

-- 激活码表
CREATE TABLE IF NOT EXISTS activation_code (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    activation_code VARCHAR(50) NOT NULL UNIQUE COMMENT '激活码',
    machine_code VARCHAR(100) DEFAULT NULL COMMENT '绑定的机器码',
    user_id VARCHAR(100) DEFAULT NULL COMMENT '用户标识',
    status TINYINT NOT NULL DEFAULT 0 COMMENT '激活状态：0-未激活，1-已激活，2-已禁用',
    activation_time DATETIME DEFAULT NULL COMMENT '激活时间',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    expire_time DATETIME DEFAULT NULL COMMENT '过期时间',
    max_activations INT NOT NULL DEFAULT 1 COMMENT '最大激活次数',
    current_activations INT NOT NULL DEFAULT 0 COMMENT '当前激活次数',
    remark TEXT DEFAULT NULL COMMENT '备注信息',
    
    INDEX idx_activation_code (activation_code),
    INDEX idx_machine_code (machine_code),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time),
    INDEX idx_expire_time (expire_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='激活码表';

-- 机器信息表
CREATE TABLE IF NOT EXISTS machine_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    machine_code VARCHAR(100) NOT NULL UNIQUE COMMENT '机器码',
    computer_name VARCHAR(255) DEFAULT NULL COMMENT '计算机名称',
    operating_system VARCHAR(255) DEFAULT NULL COMMENT '操作系统',
    processor VARCHAR(255) DEFAULT NULL COMMENT '处理器信息',
    cpu_id VARCHAR(255) DEFAULT NULL COMMENT 'CPU序列号',
    motherboard_serial VARCHAR(255) DEFAULT NULL COMMENT '主板序列号',
    bios_serial VARCHAR(255) DEFAULT NULL COMMENT 'BIOS序列号',
    disk_serial TEXT DEFAULT NULL COMMENT '硬盘序列号',
    mac_address VARCHAR(255) DEFAULT NULL COMMENT 'MAC地址',
    windows_product_id VARCHAR(255) DEFAULT NULL COMMENT 'Windows产品ID',
    machine_guid VARCHAR(255) DEFAULT NULL COMMENT '机器GUID',
    activation_status TINYINT NOT NULL DEFAULT 0 COMMENT '激活状态：0-未激活，1-已激活，2-已禁用',
    first_activation_time DATETIME DEFAULT NULL COMMENT '首次激活时间',
    last_activation_time DATETIME DEFAULT NULL COMMENT '最后激活时间',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    activation_count INT NOT NULL DEFAULT 0 COMMENT '激活次数',
    remark TEXT DEFAULT NULL COMMENT '备注信息',
    
    INDEX idx_machine_code (machine_code),
    INDEX idx_computer_name (computer_name),
    INDEX idx_activation_status (activation_status),
    INDEX idx_first_activation_time (first_activation_time),
    INDEX idx_last_activation_time (last_activation_time),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='机器信息表';

-- 激活日志表（可选，用于记录激活历史）
CREATE TABLE IF NOT EXISTS activation_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    activation_code VARCHAR(50) NOT NULL COMMENT '激活码',
    machine_code VARCHAR(100) NOT NULL COMMENT '机器码',
    operation_type TINYINT NOT NULL COMMENT '操作类型：1-激活，2-验证，3-禁用，4-启用',
    operation_result TINYINT NOT NULL COMMENT '操作结果：0-失败，1-成功',
    error_message TEXT DEFAULT NULL COMMENT '错误信息',
    client_ip VARCHAR(45) DEFAULT NULL COMMENT '客户端IP',
    user_agent TEXT DEFAULT NULL COMMENT '用户代理',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_activation_code (activation_code),
    INDEX idx_machine_code (machine_code),
    INDEX idx_operation_type (operation_type),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='激活日志表';

-- 插入示例数据
INSERT INTO activation_code (activation_code, user_id, status, expire_time, max_activations, remark) VALUES
('DEMO-1234-5678-9ABC', 'demo_user', 0, DATE_ADD(NOW(), INTERVAL 1 YEAR), 1, '演示激活码'),
('TEST-ABCD-EFGH-IJKL', 'test_user', 0, DATE_ADD(NOW(), INTERVAL 6 MONTH), 3, '测试激活码'),
('PROD-9876-5432-1DEF', 'prod_user', 0, DATE_ADD(NOW(), INTERVAL 2 YEAR), 1, '生产环境激活码');

-- 创建视图：激活码统计
CREATE VIEW v_activation_statistics AS
SELECT 
    COUNT(*) as total_codes,
    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as unactivated_codes,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as activated_codes,
    SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as disabled_codes,
    SUM(CASE WHEN expire_time IS NOT NULL AND expire_time < NOW() THEN 1 ELSE 0 END) as expired_codes
FROM activation_code;

-- 创建视图：机器统计
CREATE VIEW v_machine_statistics AS
SELECT 
    COUNT(*) as total_machines,
    SUM(CASE WHEN activation_status = 0 THEN 1 ELSE 0 END) as unactivated_machines,
    SUM(CASE WHEN activation_status = 1 THEN 1 ELSE 0 END) as activated_machines,
    SUM(CASE WHEN activation_status = 2 THEN 1 ELSE 0 END) as disabled_machines,
    AVG(activation_count) as avg_activation_count
FROM machine_info;

-- 创建存储过程：清理过期激活码
DELIMITER //
CREATE PROCEDURE CleanExpiredActivationCodes()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE expired_count INT DEFAULT 0;
    
    -- 统计过期激活码数量
    SELECT COUNT(*) INTO expired_count 
    FROM activation_code 
    WHERE expire_time IS NOT NULL AND expire_time < NOW();
    
    -- 删除过期激活码
    DELETE FROM activation_code 
    WHERE expire_time IS NOT NULL AND expire_time < NOW();
    
    -- 返回删除数量
    SELECT expired_count as deleted_count;
END //
DELIMITER ;

-- 创建存储过程：生成激活码
DELIMITER //
CREATE PROCEDURE GenerateActivationCode(
    IN p_user_id VARCHAR(100),
    IN p_expire_days INT,
    IN p_max_activations INT,
    IN p_remark TEXT,
    OUT p_activation_code VARCHAR(50)
)
BEGIN
    DECLARE v_code VARCHAR(50);
    DECLARE v_exists INT DEFAULT 1;
    DECLARE v_expire_time DATETIME;
    
    -- 计算过期时间
    IF p_expire_days > 0 THEN
        SET v_expire_time = DATE_ADD(NOW(), INTERVAL p_expire_days DAY);
    ELSE
        SET v_expire_time = NULL;
    END IF;
    
    -- 生成唯一激活码
    WHILE v_exists > 0 DO
        SET v_code = CONCAT(
            SUBSTRING(MD5(CONCAT(NOW(), RAND())), 1, 4), '-',
            SUBSTRING(MD5(CONCAT(NOW(), RAND())), 5, 4), '-',
            SUBSTRING(MD5(CONCAT(NOW(), RAND())), 9, 4), '-',
            SUBSTRING(MD5(CONCAT(NOW(), RAND())), 13, 4)
        );
        SET v_code = UPPER(v_code);
        
        SELECT COUNT(*) INTO v_exists FROM activation_code WHERE activation_code = v_code;
    END WHILE;
    
    -- 插入激活码
    INSERT INTO activation_code (
        activation_code, user_id, status, expire_time, 
        max_activations, current_activations, remark
    ) VALUES (
        v_code, p_user_id, 0, v_expire_time, 
        IFNULL(p_max_activations, 1), 0, p_remark
    );
    
    SET p_activation_code = v_code;
END //
DELIMITER ;

-- 创建触发器：记录激活日志
DELIMITER //
CREATE TRIGGER tr_activation_code_update 
AFTER UPDATE ON activation_code
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status AND NEW.status = 1 THEN
        INSERT INTO activation_log (
            activation_code, machine_code, operation_type, 
            operation_result, create_time
        ) VALUES (
            NEW.activation_code, NEW.machine_code, 1, 1, NOW()
        );
    END IF;
END //
DELIMITER ;

-- 创建用户和权限（可选）
-- CREATE USER 'activation_user'@'%' IDENTIFIED BY 'your_password_here';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON activation_system.* TO 'activation_user'@'%';
-- FLUSH PRIVILEGES;

-- 显示表结构
SHOW TABLES;
DESCRIBE activation_code;
DESCRIBE machine_info;
DESCRIBE activation_log;
