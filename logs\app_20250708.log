2025-07-08 16:08:38 [INFO] ✅ 激活验证模块加载成功
2025-07-08 16:08:41 [INFO] ✅ 单实例应用初始化成功
2025-07-08 16:08:41 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 16:08:42 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-08 16:08:42 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-08 16:08:42 [INFO] ✅ 已更新最后验证时间
2025-07-08 16:08:42 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-08 16:08:42 [INFO] ✅ 本地激活配置验证通过
2025-07-08 16:08:42 [INFO] ✅ 本地激活验证通过
2025-07-08 16:08:42 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 16:09:20 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-07-08 16:09:21 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-08 16:09:21 [INFO] 未检测到CUDA设备
2025-07-08 16:09:21 [INFO] VideoProcessor初始化:
2025-07-08 16:09:21 [INFO]   CPU线程数: 8
2025-07-08 16:09:21 [INFO]   GPU支持: 否
2025-07-08 16:09:21 [INFO]   GPU加速: 禁用
2025-07-08 16:09:21 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-08 16:09:21 [INFO] 开始批量处理大型视频...
2025-07-08 16:09:21 [INFO] 主视频: D:/25125/Videos/video_test/02_t.mp4
2025-07-08 16:09:21 [INFO] 辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:09:21 [INFO] 输出视频: D:/25125/Videos/video_test/www.mp4
2025-07-08 16:09:21 [INFO] 切割主视频...
2025-07-08 16:10:05 [INFO] 主视频切割完成，共 487 段
2025-07-08 16:10:05 [INFO] 切割辅助视频...
2025-07-08 16:22:13 [INFO] 辅助视频切割完成，共 2565 段
2025-07-08 16:22:14 [INFO] 查找相似视频段...
2025-07-08 16:22:14 [INFO] 正在并行提取主视频特征...
2025-07-08 16:24:04 [INFO] 正在并行提取辅助视频特征...
2025-07-08 16:56:38 [INFO] 正在并行计算相似度矩阵...
2025-07-08 16:57:21 [INFO] 正在查找最佳匹配...
2025-07-08 16:57:21 [INFO] 找到 2515 对相似视频段
2025-07-08 16:57:21 [INFO] 替换并合成视频...
2025-07-08 16:57:21 [INFO] 使用优化版本的视频替换和合并功能...
2025-07-08 16:57:21 [INFO] 优化匹配顺序，共 2515 个匹配
2025-07-08 16:57:21 [INFO] 加载主视频: D:/25125/Videos/video_test/02_t.mp4
2025-07-08 16:57:22 [INFO] 主视频加载成功，时长: 1025.86秒
2025-07-08 16:57:22 [INFO] 处理 487 个主视频片段...
2025-07-08 16:57:22 [INFO] 保留主视频片段 6: 20.29-21.33秒
2025-07-08 16:57:22 [INFO] 保留主视频片段 28: 60.46-69.58秒
2025-07-08 16:57:23 [INFO] 保留主视频片段 29: 69.58-71.08秒
2025-07-08 16:57:23 [INFO] 保留主视频片段 31: 71.96-73.38秒
2025-07-08 16:57:23 [INFO] 保留主视频片段 43: 98.38-99.96秒
2025-07-08 16:57:23 [INFO] 保留主视频片段 47: 103.25-105.04秒
2025-07-08 16:57:23 [INFO] 保留主视频片段 89: 172.29-173.54秒
2025-07-08 16:57:23 [INFO] 保留主视频片段 93: 177.08-179.38秒
2025-07-08 16:57:24 [INFO] 保留主视频片段 95: 181.21-182.62秒
2025-07-08 16:57:24 [INFO] 保留主视频片段 101: 190.50-192.92秒
2025-07-08 16:57:24 [INFO] 保留主视频片段 114: 209.79-212.17秒
2025-07-08 16:57:24 [INFO] 保留主视频片段 144: 276.79-278.46秒
2025-07-08 16:57:24 [INFO] 保留主视频片段 148: 283.33-287.00秒
2025-07-08 16:57:25 [INFO] 保留主视频片段 159: 304.96-306.88秒
2025-07-08 16:57:25 [INFO] 保留主视频片段 163: 310.33-311.58秒
2025-07-08 16:57:25 [INFO] 保留主视频片段 167: 316.54-318.04秒
2025-07-08 16:57:25 [INFO] 保留主视频片段 168: 318.04-319.50秒
2025-07-08 16:57:25 [INFO] 保留主视频片段 171: 327.00-341.92秒
2025-07-08 16:57:26 [INFO] 保留主视频片段 178: 362.50-374.08秒
2025-07-08 16:57:26 [INFO] 保留主视频片段 181: 379.38-381.42秒
2025-07-08 16:57:26 [INFO] 保留主视频片段 200: 420.46-423.08秒
2025-07-08 16:57:26 [INFO] 保留主视频片段 210: 435.96-437.96秒
2025-07-08 16:57:26 [INFO] 保留主视频片段 214: 442.50-446.04秒
2025-07-08 16:57:26 [INFO] 保留主视频片段 220: 452.62-459.17秒
2025-07-08 16:57:27 [INFO] 保留主视频片段 228: 473.25-474.29秒
2025-07-08 16:57:27 [INFO] 保留主视频片段 249: 516.79-517.67秒
2025-07-08 16:57:27 [INFO] 保留主视频片段 259: 531.58-533.58秒
2025-07-08 16:57:27 [INFO] 保留主视频片段 262: 537.08-538.46秒
2025-07-08 16:57:27 [INFO] 保留主视频片段 265: 539.92-540.92秒
2025-07-08 16:57:28 [INFO] 保留主视频片段 272: 548.12-550.79秒
2025-07-08 16:57:28 [INFO] 保留主视频片段 273: 550.79-551.50秒
2025-07-08 16:57:28 [INFO] 保留主视频片段 274: 551.50-553.04秒
2025-07-08 16:57:28 [INFO] 保留主视频片段 275: 553.04-553.88秒
2025-07-08 16:57:28 [INFO] 保留主视频片段 277: 555.46-557.83秒
2025-07-08 16:57:28 [INFO] 保留主视频片段 299: 585.79-589.00秒
2025-07-08 16:57:28 [INFO] 保留主视频片段 309: 610.75-616.29秒
2025-07-08 16:57:29 [INFO] 保留主视频片段 311: 617.75-620.12秒
2025-07-08 16:57:29 [INFO] 保留主视频片段 322: 646.04-650.92秒
2025-07-08 16:57:29 [INFO] 保留主视频片段 324: 651.96-653.46秒
2025-07-08 16:57:29 [INFO] 保留主视频片段 328: 657.12-657.83秒
2025-07-08 16:57:29 [INFO] 保留主视频片段 330: 659.04-661.33秒
2025-07-08 16:57:29 [INFO] 保留主视频片段 340: 684.42-685.08秒
2025-07-08 16:57:30 [INFO] 保留主视频片段 341: 685.08-688.92秒
2025-07-08 16:57:30 [INFO] 保留主视频片段 343: 690.38-692.04秒
2025-07-08 16:57:30 [INFO] 保留主视频片段 346: 697.25-698.08秒
2025-07-08 16:57:30 [INFO] 保留主视频片段 367: 747.25-749.17秒
2025-07-08 16:57:30 [INFO] 保留主视频片段 393: 788.21-789.17秒
2025-07-08 16:57:30 [INFO] 保留主视频片段 413: 832.25-833.96秒
2025-07-08 16:57:31 [INFO] 保留主视频片段 415: 835.38-837.04秒
2025-07-08 16:57:31 [INFO] 保留主视频片段 425: 851.58-853.38秒
2025-07-08 16:57:31 [INFO] 保留主视频片段 426: 853.38-862.67秒
2025-07-08 16:57:31 [INFO] 保留主视频片段 427: 862.67-868.04秒
2025-07-08 16:57:31 [INFO] 保留主视频片段 435: 883.08-883.88秒
2025-07-08 16:57:31 [INFO] 保留主视频片段 446: 901.62-903.58秒
2025-07-08 16:57:32 [INFO] 保留主视频片段 448: 905.25-906.38秒
2025-07-08 16:57:32 [INFO] 保留主视频片段 455: 922.00-941.04秒
2025-07-08 16:57:32 [INFO] 保留主视频片段 457: 945.50-950.17秒
2025-07-08 16:57:32 [INFO] 保留主视频片段 481: 1004.38-1008.04秒
2025-07-08 16:57:33 [INFO] 保留主视频片段 482: 1008.04-1010.54秒
2025-07-08 16:57:33 [INFO] 保留主视频片段 486: 1016.67-1025.83秒
2025-07-08 16:57:33 [INFO] 处理 2515 个替换片段...
2025-07-08 16:57:33 [INFO] 处理替换批次 1/252，包含 10 个片段
2025-07-08 16:57:33 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:57:33 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:57:33 [INFO] 替换片段 380: 使用辅助视频 0.00-6.25秒
2025-07-08 16:57:34 [INFO] 替换片段 317: 使用辅助视频 6.25-18.83秒
2025-07-08 16:57:34 [INFO] 替换片段 379: 使用辅助视频 18.83-20.96秒
2025-07-08 16:57:34 [INFO] 替换片段 17: 使用辅助视频 20.96-21.88秒
2025-07-08 16:57:34 [INFO] 替换片段 451: 使用辅助视频 21.88-23.83秒
2025-07-08 16:57:35 [INFO] 替换片段 479: 使用辅助视频 23.83-28.96秒
2025-07-08 16:57:35 [INFO] 替换片段 57: 使用辅助视频 28.96-56.96秒
2025-07-08 16:57:35 [INFO] 替换片段 40: 使用辅助视频 56.96-64.04秒
2025-07-08 16:57:36 [INFO] 替换片段 166: 使用辅助视频 64.04-81.04秒
2025-07-08 16:57:36 [INFO] 替换片段 290: 使用辅助视频 89.00-91.83秒
2025-07-08 16:57:36 [INFO] 处理替换批次 2/252，包含 10 个片段
2025-07-08 16:57:36 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:57:37 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:57:37 [INFO] 替换片段 370: 使用辅助视频 91.83-92.50秒
2025-07-08 16:57:37 [INFO] 替换片段 34: 使用辅助视频 92.50-93.96秒
2025-07-08 16:57:37 [INFO] 替换片段 134: 使用辅助视频 93.96-98.29秒
2025-07-08 16:57:37 [INFO] 替换片段 51: 使用辅助视频 98.29-99.04秒
2025-07-08 16:57:38 [INFO] 替换片段 40: 使用辅助视频 99.04-100.58秒
2025-07-08 16:57:38 [INFO] 替换片段 450: 使用辅助视频 103.33-111.12秒
2025-07-08 16:57:38 [INFO] 替换片段 208: 使用辅助视频 111.12-121.08秒
2025-07-08 16:57:38 [INFO] 替换片段 384: 使用辅助视频 121.08-128.12秒
2025-07-08 16:57:39 [INFO] 替换片段 384: 使用辅助视频 128.12-135.79秒
2025-07-08 16:57:39 [INFO] 替换片段 223: 使用辅助视频 135.79-142.08秒
2025-07-08 16:57:39 [INFO] 处理替换批次 3/252，包含 10 个片段
2025-07-08 16:57:39 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:57:40 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:57:40 [INFO] 替换片段 417: 使用辅助视频 142.08-149.25秒
2025-07-08 16:57:40 [INFO] 替换片段 473: 使用辅助视频 149.25-157.25秒
2025-07-08 16:57:41 [INFO] 替换片段 209: 使用辅助视频 157.25-170.71秒
2025-07-08 16:57:41 [INFO] 替换片段 24: 使用辅助视频 170.71-195.58秒
2025-07-08 16:57:41 [INFO] 替换片段 284: 使用辅助视频 195.58-198.38秒
2025-07-08 16:57:42 [INFO] 替换片段 437: 使用辅助视频 198.38-245.58秒
2025-07-08 16:57:42 [INFO] 替换片段 361: 使用辅助视频 245.58-251.96秒
2025-07-08 16:57:43 [INFO] 替换片段 54: 使用辅助视频 251.96-255.21秒
2025-07-08 16:57:43 [INFO] 替换片段 35: 使用辅助视频 255.21-269.17秒
2025-07-08 16:57:43 [INFO] 替换片段 270: 使用辅助视频 269.17-274.54秒
2025-07-08 16:57:43 [INFO] 处理替换批次 4/252，包含 10 个片段
2025-07-08 16:57:43 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:57:44 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:57:44 [INFO] 替换片段 248: 使用辅助视频 274.54-276.71秒
2025-07-08 16:57:44 [INFO] 替换片段 279: 使用辅助视频 276.71-281.21秒
2025-07-08 16:57:45 [INFO] 替换片段 306: 使用辅助视频 296.00-300.29秒
2025-07-08 16:57:45 [INFO] 替换片段 4: 使用辅助视频 300.29-321.58秒
2025-07-08 16:57:45 [INFO] 替换片段 397: 使用辅助视频 321.58-326.29秒
2025-07-08 16:57:46 [INFO] 替换片段 130: 使用辅助视频 326.29-327.21秒
2025-07-08 16:57:46 [INFO] 替换片段 256: 使用辅助视频 327.21-330.25秒
2025-07-08 16:57:46 [INFO] 替换片段 166: 使用辅助视频 330.25-332.58秒
2025-07-08 16:57:47 [INFO] 替换片段 102: 使用辅助视频 332.58-335.67秒
2025-07-08 16:57:47 [INFO] 替换片段 358: 使用辅助视频 335.67-337.00秒
2025-07-08 16:57:47 [INFO] 处理替换批次 5/252，包含 10 个片段
2025-07-08 16:57:47 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:57:47 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:57:48 [INFO] 替换片段 190: 使用辅助视频 337.00-341.25秒
2025-07-08 16:57:48 [INFO] 替换片段 18: 使用辅助视频 341.25-342.62秒
2025-07-08 16:57:48 [INFO] 替换片段 286: 使用辅助视频 342.62-345.04秒
2025-07-08 16:57:49 [INFO] 替换片段 147: 使用辅助视频 345.04-346.25秒
2025-07-08 16:57:49 [INFO] 替换片段 112: 使用辅助视频 346.25-347.29秒
2025-07-08 16:57:49 [INFO] 替换片段 391: 使用辅助视频 347.29-349.42秒
2025-07-08 16:57:49 [INFO] 替换片段 454: 使用辅助视频 349.42-350.96秒
2025-07-08 16:57:49 [INFO] 替换片段 141: 使用辅助视频 350.96-356.58秒
2025-07-08 16:57:50 [INFO] 替换片段 125: 使用辅助视频 356.58-359.54秒
2025-07-08 16:57:50 [INFO] 替换片段 289: 使用辅助视频 359.54-361.71秒
2025-07-08 16:57:50 [INFO] 处理替换批次 6/252，包含 10 个片段
2025-07-08 16:57:50 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:57:51 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:57:51 [INFO] 替换片段 286: 使用辅助视频 361.71-364.17秒
2025-07-08 16:57:52 [INFO] 替换片段 1: 使用辅助视频 364.17-365.50秒
2025-07-08 16:57:52 [INFO] 替换片段 217: 使用辅助视频 365.50-375.62秒
2025-07-08 16:57:52 [INFO] 替换片段 284: 使用辅助视频 375.62-376.79秒
2025-07-08 16:57:52 [INFO] 替换片段 402: 使用辅助视频 376.79-391.46秒
2025-07-08 16:57:53 [INFO] 替换片段 199: 使用辅助视频 391.46-395.25秒
2025-07-08 16:57:53 [INFO] 替换片段 19: 使用辅助视频 395.25-400.08秒
2025-07-08 16:57:54 [INFO] 替换片段 321: 使用辅助视频 400.08-403.46秒
2025-07-08 16:57:54 [INFO] 替换片段 188: 使用辅助视频 403.46-419.96秒
2025-07-08 16:57:54 [INFO] 替换片段 49: 使用辅助视频 419.96-423.29秒
2025-07-08 16:57:54 [INFO] 处理替换批次 7/252，包含 10 个片段
2025-07-08 16:57:54 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:57:55 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:57:56 [INFO] 替换片段 98: 使用辅助视频 423.29-426.38秒
2025-07-08 16:57:56 [INFO] 替换片段 397: 使用辅助视频 426.38-428.33秒
2025-07-08 16:57:56 [INFO] 替换片段 410: 使用辅助视频 428.33-430.29秒
2025-07-08 16:57:56 [INFO] 替换片段 382: 使用辅助视频 430.29-431.79秒
2025-07-08 16:57:57 [INFO] 替换片段 8: 使用辅助视频 431.79-434.29秒
2025-07-08 16:57:57 [INFO] 替换片段 295: 使用辅助视频 434.29-437.17秒
2025-07-08 16:57:57 [INFO] 替换片段 10: 使用辅助视频 437.17-438.79秒
2025-07-08 16:57:58 [INFO] 替换片段 222: 使用辅助视频 438.79-442.75秒
2025-07-08 16:57:58 [INFO] 替换片段 187: 使用辅助视频 442.75-445.21秒
2025-07-08 16:57:58 [INFO] 替换片段 395: 使用辅助视频 445.21-449.71秒
2025-07-08 16:57:59 [INFO] 处理替换批次 8/252，包含 10 个片段
2025-07-08 16:57:59 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:57:59 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:57:59 [INFO] 替换片段 136: 使用辅助视频 449.71-451.88秒
2025-07-08 16:58:00 [INFO] 替换片段 388: 使用辅助视频 451.88-455.54秒
2025-07-08 16:58:00 [INFO] 替换片段 289: 使用辅助视频 455.54-457.21秒
2025-07-08 16:58:01 [INFO] 替换片段 142: 使用辅助视频 457.21-459.00秒
2025-07-08 16:58:01 [INFO] 替换片段 170: 使用辅助视频 459.00-463.00秒
2025-07-08 16:58:01 [INFO] 替换片段 44: 使用辅助视频 463.00-466.08秒
2025-07-08 16:58:02 [INFO] 替换片段 197: 使用辅助视频 466.08-475.79秒
2025-07-08 16:58:02 [INFO] 替换片段 172: 使用辅助视频 475.79-479.33秒
2025-07-08 16:58:03 [INFO] 替换片段 197: 使用辅助视频 479.33-491.92秒
2025-07-08 16:58:03 [INFO] 替换片段 7: 使用辅助视频 491.92-495.83秒
2025-07-08 16:58:03 [INFO] 处理替换批次 9/252，包含 10 个片段
2025-07-08 16:58:03 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:58:04 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:58:04 [INFO] 替换片段 379: 使用辅助视频 495.83-497.58秒
2025-07-08 16:58:04 [INFO] 替换片段 255: 使用辅助视频 497.58-500.21秒
2025-07-08 16:58:05 [INFO] 替换片段 320: 使用辅助视频 500.21-502.12秒
2025-07-08 16:58:05 [INFO] 替换片段 321: 使用辅助视频 502.12-503.25秒
2025-07-08 16:58:05 [INFO] 替换片段 366: 使用辅助视频 503.25-505.88秒
2025-07-08 16:58:06 [INFO] 替换片段 364: 使用辅助视频 505.88-506.88秒
2025-07-08 16:58:06 [INFO] 替换片段 347: 使用辅助视频 506.88-508.21秒
2025-07-08 16:58:06 [INFO] 替换片段 400: 使用辅助视频 508.21-510.96秒
2025-07-08 16:58:06 [INFO] 替换片段 156: 使用辅助视频 510.96-513.54秒
2025-07-08 16:58:07 [INFO] 替换片段 344: 使用辅助视频 513.54-520.17秒
2025-07-08 16:58:07 [INFO] 处理替换批次 10/252，包含 10 个片段
2025-07-08 16:58:07 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:58:07 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:58:08 [INFO] 替换片段 351: 使用辅助视频 520.17-523.54秒
2025-07-08 16:58:08 [INFO] 替换片段 53: 使用辅助视频 523.54-525.46秒
2025-07-08 16:58:09 [INFO] 替换片段 423: 使用辅助视频 525.46-527.92秒
2025-07-08 16:58:09 [INFO] 替换片段 26: 使用辅助视频 527.92-530.79秒
2025-07-08 16:58:09 [INFO] 替换片段 67: 使用辅助视频 530.79-532.12秒
2025-07-08 16:58:09 [INFO] 替换片段 338: 使用辅助视频 532.12-536.83秒
2025-07-08 16:58:10 [INFO] 替换片段 27: 使用辅助视频 536.83-539.38秒
2025-07-08 16:58:10 [INFO] 替换片段 156: 使用辅助视频 539.38-545.29秒
2025-07-08 16:58:11 [INFO] 替换片段 254: 使用辅助视频 545.29-547.38秒
2025-07-08 16:58:11 [INFO] 替换片段 371: 使用辅助视频 547.38-549.50秒
2025-07-08 16:58:11 [INFO] 处理替换批次 11/252，包含 10 个片段
2025-07-08 16:58:11 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:58:12 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:58:13 [INFO] 替换片段 468: 使用辅助视频 549.50-550.58秒
2025-07-08 16:58:13 [INFO] 替换片段 145: 使用辅助视频 550.58-551.42秒
2025-07-08 16:58:13 [INFO] 替换片段 14: 使用辅助视频 551.42-552.25秒
2025-07-08 16:58:13 [INFO] 替换片段 141: 使用辅助视频 552.25-553.12秒
2025-07-08 16:58:13 [INFO] 替换片段 416: 使用辅助视频 553.12-554.50秒
2025-07-08 16:58:13 [INFO] 替换片段 371: 使用辅助视频 554.50-556.04秒
2025-07-08 16:58:14 [INFO] 替换片段 213: 使用辅助视频 556.04-557.33秒
2025-07-08 16:58:14 [INFO] 替换片段 348: 使用辅助视频 557.33-558.21秒
2025-07-08 16:58:14 [INFO] 替换片段 371: 使用辅助视频 558.21-559.04秒
2025-07-08 16:58:14 [INFO] 替换片段 334: 使用辅助视频 559.04-560.04秒
2025-07-08 16:58:14 [INFO] 处理替换批次 12/252，包含 10 个片段
2025-07-08 16:58:14 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:58:15 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:58:15 [INFO] 替换片段 208: 使用辅助视频 560.04-563.67秒
2025-07-08 16:58:16 [INFO] 替换片段 21: 使用辅助视频 563.67-566.25秒
2025-07-08 16:58:16 [INFO] 替换片段 291: 使用辅助视频 566.25-568.00秒
2025-07-08 16:58:16 [INFO] 替换片段 371: 使用辅助视频 568.00-572.54秒
2025-07-08 16:58:16 [INFO] 替换片段 134: 使用辅助视频 572.54-574.79秒
2025-07-08 16:58:17 [INFO] 替换片段 48: 使用辅助视频 574.79-576.46秒
2025-07-08 16:58:17 [INFO] 替换片段 319: 使用辅助视频 576.46-578.88秒
2025-07-08 16:58:17 [INFO] 替换片段 126: 使用辅助视频 578.88-580.96秒
2025-07-08 16:58:18 [INFO] 替换片段 238: 使用辅助视频 580.96-581.96秒
2025-07-08 16:58:18 [INFO] 替换片段 197: 使用辅助视频 581.96-583.50秒
2025-07-08 16:58:18 [INFO] 处理替换批次 13/252，包含 10 个片段
2025-07-08 16:58:18 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:58:18 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:58:19 [INFO] 替换片段 98: 使用辅助视频 583.50-584.96秒
2025-07-08 16:58:19 [INFO] 替换片段 157: 使用辅助视频 584.96-586.29秒
2025-07-08 16:58:19 [INFO] 替换片段 226: 使用辅助视频 586.29-588.17秒
2025-07-08 16:58:19 [INFO] 替换片段 472: 使用辅助视频 588.17-590.92秒
2025-07-08 16:58:20 [INFO] 替换片段 459: 使用辅助视频 590.92-593.38秒
2025-07-08 16:58:20 [INFO] 替换片段 52: 使用辅助视频 593.38-600.46秒
2025-07-08 16:58:20 [INFO] 替换片段 177: 使用辅助视频 600.46-602.12秒
2025-07-08 16:58:20 [INFO] 替换片段 350: 使用辅助视频 602.12-605.17秒
2025-07-08 16:58:21 [INFO] 替换片段 293: 使用辅助视频 605.17-607.21秒
2025-07-08 16:58:21 [INFO] 替换片段 254: 使用辅助视频 607.21-608.46秒
2025-07-08 16:58:21 [INFO] 处理替换批次 14/252，包含 10 个片段
2025-07-08 16:58:21 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:58:22 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:58:22 [INFO] 替换片段 13: 使用辅助视频 608.46-614.79秒
2025-07-08 16:58:22 [INFO] 替换片段 169: 使用辅助视频 614.79-618.46秒
2025-07-08 16:58:23 [INFO] 替换片段 468: 使用辅助视频 618.46-619.79秒
2025-07-08 16:58:23 [INFO] 替换片段 454: 使用辅助视频 619.79-624.21秒
2025-07-08 16:58:23 [INFO] 替换片段 128: 使用辅助视频 624.21-628.00秒
2025-07-08 16:58:24 [INFO] 替换片段 351: 使用辅助视频 628.00-630.79秒
2025-07-08 16:58:24 [INFO] 替换片段 423: 使用辅助视频 630.79-632.21秒
2025-07-08 16:58:25 [INFO] 替换片段 467: 使用辅助视频 633.38-634.75秒
2025-07-08 16:58:25 [INFO] 替换片段 215: 使用辅助视频 639.96-642.29秒
2025-07-08 16:58:25 [INFO] 替换片段 113: 使用辅助视频 642.29-646.88秒
2025-07-08 16:58:25 [INFO] 处理替换批次 15/252，包含 10 个片段
2025-07-08 16:58:25 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:58:26 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:58:27 [INFO] 替换片段 410: 使用辅助视频 646.88-654.17秒
2025-07-08 16:58:27 [INFO] 替换片段 85: 使用辅助视频 654.17-655.96秒
2025-07-08 16:58:28 [INFO] 替换片段 321: 使用辅助视频 655.96-662.96秒
2025-07-08 16:58:28 [INFO] 替换片段 150: 使用辅助视频 662.96-665.96秒
2025-07-08 16:58:29 [INFO] 替换片段 334: 使用辅助视频 665.96-667.25秒
2025-07-08 16:58:29 [INFO] 替换片段 104: 使用辅助视频 667.25-668.29秒
2025-07-08 16:58:29 [INFO] 替换片段 312: 使用辅助视频 668.29-669.42秒
2025-07-08 16:58:29 [INFO] 替换片段 350: 使用辅助视频 669.42-670.83秒
2025-07-08 16:58:29 [INFO] 替换片段 65: 使用辅助视频 670.83-673.12秒
2025-07-08 16:58:30 [INFO] 替换片段 7: 使用辅助视频 673.12-678.08秒
2025-07-08 16:58:30 [INFO] 处理替换批次 16/252，包含 10 个片段
2025-07-08 16:58:30 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:58:30 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:58:31 [INFO] 替换片段 132: 使用辅助视频 678.08-691.29秒
2025-07-08 16:58:31 [INFO] 替换片段 349: 使用辅助视频 691.29-692.88秒
2025-07-08 16:58:32 [INFO] 替换片段 104: 使用辅助视频 692.88-698.38秒
2025-07-08 16:58:32 [INFO] 替换片段 293: 使用辅助视频 698.38-704.42秒
2025-07-08 16:58:33 [INFO] 替换片段 217: 使用辅助视频 704.42-706.71秒
2025-07-08 16:58:33 [INFO] 替换片段 472: 使用辅助视频 706.71-710.17秒
2025-07-08 16:58:34 [INFO] 替换片段 253: 使用辅助视频 710.17-716.96秒
2025-07-08 16:58:34 [INFO] 替换片段 117: 使用辅助视频 716.96-719.46秒
2025-07-08 16:58:34 [INFO] 替换片段 199: 使用辅助视频 719.46-722.67秒
2025-07-08 16:58:35 [INFO] 替换片段 223: 使用辅助视频 722.67-725.62秒
2025-07-08 16:58:35 [INFO] 处理替换批次 17/252，包含 10 个片段
2025-07-08 16:58:35 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:58:35 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:58:36 [INFO] 替换片段 115: 使用辅助视频 725.62-728.67秒
2025-07-08 16:58:36 [INFO] 替换片段 223: 使用辅助视频 728.67-730.58秒
2025-07-08 16:58:36 [INFO] 替换片段 116: 使用辅助视频 730.58-737.00秒
2025-07-08 16:58:36 [INFO] 替换片段 116: 使用辅助视频 737.00-740.33秒
2025-07-08 16:58:37 [INFO] 替换片段 117: 使用辅助视频 740.33-743.58秒
2025-07-08 16:58:37 [INFO] 替换片段 118: 使用辅助视频 743.58-750.83秒
2025-07-08 16:58:38 [INFO] 替换片段 384: 使用辅助视频 750.83-755.75秒
2025-07-08 16:58:38 [INFO] 替换片段 118: 使用辅助视频 755.75-762.25秒
2025-07-08 16:58:38 [INFO] 替换片段 197: 使用辅助视频 762.25-768.04秒
2025-07-08 16:58:39 [INFO] 替换片段 131: 使用辅助视频 768.04-772.50秒
2025-07-08 16:58:39 [INFO] 处理替换批次 18/252，包含 10 个片段
2025-07-08 16:58:39 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:58:39 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:58:39 [INFO] 替换片段 0: 使用辅助视频 772.50-775.29秒
2025-07-08 16:58:40 [INFO] 替换片段 5: 使用辅助视频 775.29-781.17秒
2025-07-08 16:58:40 [INFO] 替换片段 204: 使用辅助视频 781.17-787.79秒
2025-07-08 16:58:41 [INFO] 替换片段 108: 使用辅助视频 787.79-799.50秒
2025-07-08 16:58:41 [INFO] 替换片段 344: 使用辅助视频 799.50-801.25秒
2025-07-08 16:58:41 [INFO] 替换片段 308: 使用辅助视频 801.25-804.58秒
2025-07-08 16:58:41 [INFO] 替换片段 475: 使用辅助视频 824.08-826.46秒
2025-07-08 16:58:42 [INFO] 替换片段 130: 使用辅助视频 826.46-831.17秒
2025-07-08 16:58:42 [INFO] 替换片段 458: 使用辅助视频 831.17-840.29秒
2025-07-08 16:58:43 [INFO] 替换片段 255: 使用辅助视频 840.29-850.79秒
2025-07-08 16:58:43 [INFO] 处理替换批次 19/252，包含 10 个片段
2025-07-08 16:58:43 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:58:43 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:58:44 [INFO] 替换片段 428: 使用辅助视频 850.79-854.29秒
2025-07-08 16:58:44 [INFO] 替换片段 453: 使用辅助视频 854.29-864.58秒
2025-07-08 16:58:44 [INFO] 替换片段 131: 使用辅助视频 886.29-887.96秒
2025-07-08 16:58:45 [INFO] 替换片段 40: 使用辅助视频 887.96-890.62秒
2025-07-08 16:58:45 [INFO] 替换片段 470: 使用辅助视频 890.62-893.12秒
2025-07-08 16:58:45 [INFO] 替换片段 177: 使用辅助视频 893.12-897.58秒
2025-07-08 16:58:46 [INFO] 替换片段 337: 使用辅助视频 897.58-902.04秒
2025-07-08 16:58:46 [INFO] 替换片段 439: 使用辅助视频 902.04-903.33秒
2025-07-08 16:58:46 [INFO] 替换片段 217: 使用辅助视频 903.33-904.38秒
2025-07-08 16:58:46 [INFO] 替换片段 473: 使用辅助视频 904.38-909.04秒
2025-07-08 16:58:46 [INFO] 处理替换批次 20/252，包含 10 个片段
2025-07-08 16:58:46 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:58:47 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:58:47 [INFO] 替换片段 13: 使用辅助视频 909.04-911.88秒
2025-07-08 16:58:47 [INFO] 替换片段 407: 使用辅助视频 911.88-914.58秒
2025-07-08 16:58:48 [INFO] 替换片段 177: 使用辅助视频 914.58-918.83秒
2025-07-08 16:58:48 [INFO] 替换片段 32: 使用辅助视频 918.83-925.21秒
2025-07-08 16:58:48 [INFO] 替换片段 199: 使用辅助视频 925.21-929.71秒
2025-07-08 16:58:49 [INFO] 替换片段 149: 使用辅助视频 929.71-932.46秒
2025-07-08 16:58:49 [INFO] 替换片段 371: 使用辅助视频 932.46-933.96秒
2025-07-08 16:58:49 [INFO] 替换片段 384: 使用辅助视频 933.96-936.83秒
2025-07-08 16:58:50 [INFO] 替换片段 142: 使用辅助视频 936.83-938.75秒
2025-07-08 16:58:50 [INFO] 替换片段 450: 使用辅助视频 938.75-940.62秒
2025-07-08 16:58:50 [INFO] 处理替换批次 21/252，包含 10 个片段
2025-07-08 16:58:50 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:58:51 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:58:51 [INFO] 替换片段 344: 使用辅助视频 940.62-942.83秒
2025-07-08 16:58:51 [INFO] 替换片段 294: 使用辅助视频 942.83-945.17秒
2025-07-08 16:58:51 [INFO] 替换片段 453: 使用辅助视频 945.17-947.54秒
2025-07-08 16:58:52 [INFO] 替换片段 438: 使用辅助视频 947.54-948.67秒
2025-07-08 16:58:52 [INFO] 替换片段 22: 使用辅助视频 948.67-950.42秒
2025-07-08 16:58:52 [INFO] 替换片段 14: 使用辅助视频 950.42-951.25秒
2025-07-08 16:58:52 [INFO] 替换片段 256: 使用辅助视频 951.25-953.83秒
2025-07-08 16:58:53 [INFO] 替换片段 400: 使用辅助视频 953.83-958.08秒
2025-07-08 16:58:53 [INFO] 替换片段 474: 使用辅助视频 958.08-959.42秒
2025-07-08 16:58:53 [INFO] 替换片段 146: 使用辅助视频 959.42-960.71秒
2025-07-08 16:58:53 [INFO] 处理替换批次 22/252，包含 10 个片段
2025-07-08 16:58:53 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:58:54 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:58:54 [INFO] 替换片段 442: 使用辅助视频 960.71-961.58秒
2025-07-08 16:58:54 [INFO] 替换片段 410: 使用辅助视频 961.58-963.17秒
2025-07-08 16:58:54 [INFO] 替换片段 456: 使用辅助视频 963.17-965.33秒
2025-07-08 16:58:55 [INFO] 替换片段 449: 使用辅助视频 965.33-970.58秒
2025-07-08 16:58:55 [INFO] 替换片段 201: 使用辅助视频 970.58-972.08秒
2025-07-08 16:58:55 [INFO] 替换片段 217: 使用辅助视频 972.08-974.96秒
2025-07-08 16:58:56 [INFO] 替换片段 235: 使用辅助视频 974.96-977.75秒
2025-07-08 16:58:56 [INFO] 替换片段 115: 使用辅助视频 977.75-979.50秒
2025-07-08 16:58:56 [INFO] 替换片段 286: 使用辅助视频 979.50-982.54秒
2025-07-08 16:58:57 [INFO] 替换片段 371: 使用辅助视频 982.54-984.54秒
2025-07-08 16:58:57 [INFO] 处理替换批次 23/252，包含 10 个片段
2025-07-08 16:58:57 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:58:57 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:58:57 [INFO] 替换片段 372: 使用辅助视频 984.54-987.96秒
2025-07-08 16:58:58 [INFO] 替换片段 36: 使用辅助视频 987.96-994.71秒
2025-07-08 16:58:58 [INFO] 替换片段 255: 使用辅助视频 994.71-1001.71秒
2025-07-08 16:58:58 [INFO] 替换片段 223: 使用辅助视频 1001.71-1003.83秒
2025-07-08 16:58:59 [INFO] 替换片段 376: 使用辅助视频 1003.83-1010.12秒
2025-07-08 16:58:59 [INFO] 替换片段 42: 使用辅助视频 1010.12-1014.25秒
2025-07-08 16:59:00 [INFO] 替换片段 11: 使用辅助视频 1014.25-1019.71秒
2025-07-08 16:59:00 [INFO] 替换片段 424: 使用辅助视频 1019.71-1022.42秒
2025-07-08 16:59:00 [INFO] 替换片段 147: 使用辅助视频 1022.42-1026.21秒
2025-07-08 16:59:01 [INFO] 替换片段 254: 使用辅助视频 1026.21-1027.29秒
2025-07-08 16:59:01 [INFO] 处理替换批次 24/252，包含 10 个片段
2025-07-08 16:59:01 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:59:01 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:59:02 [INFO] 替换片段 18: 使用辅助视频 1027.29-1029.00秒
2025-07-08 16:59:02 [INFO] 替换片段 254: 使用辅助视频 1029.00-1029.96秒
2025-07-08 16:59:02 [INFO] 替换片段 18: 使用辅助视频 1029.96-1036.88秒
2025-07-08 16:59:02 [INFO] 替换片段 117: 使用辅助视频 1036.88-1037.79秒
2025-07-08 16:59:02 [INFO] 替换片段 18: 使用辅助视频 1037.79-1038.71秒
2025-07-08 16:59:03 [INFO] 替换片段 242: 使用辅助视频 1038.71-1041.33秒
2025-07-08 16:59:03 [INFO] 替换片段 18: 使用辅助视频 1041.33-1045.96秒
2025-07-08 16:59:03 [INFO] 替换片段 242: 使用辅助视频 1045.96-1048.00秒
2025-07-08 16:59:03 [INFO] 替换片段 88: 使用辅助视频 1048.00-1057.79秒
2025-07-08 16:59:04 [INFO] 替换片段 199: 使用辅助视频 1057.79-1061.12秒
2025-07-08 16:59:04 [INFO] 处理替换批次 25/252，包含 10 个片段
2025-07-08 16:59:04 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:59:04 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:59:05 [INFO] 替换片段 149: 使用辅助视频 1061.12-1064.00秒
2025-07-08 16:59:05 [INFO] 替换片段 414: 使用辅助视频 1064.00-1067.58秒
2025-07-08 16:59:06 [INFO] 替换片段 86: 使用辅助视频 1067.58-1068.50秒
2025-07-08 16:59:06 [INFO] 替换片段 468: 使用辅助视频 1068.50-1075.88秒
2025-07-08 16:59:06 [INFO] 替换片段 55: 使用辅助视频 1075.88-1079.04秒
2025-07-08 16:59:07 [INFO] 替换片段 254: 使用辅助视频 1079.04-1082.58秒
2025-07-08 16:59:07 [INFO] 替换片段 177: 使用辅助视频 1082.58-1101.67秒
2025-07-08 16:59:08 [INFO] 替换片段 408: 使用辅助视频 1101.67-1108.88秒
2025-07-08 16:59:08 [INFO] 替换片段 186: 使用辅助视频 1108.88-1113.12秒
2025-07-08 16:59:08 [INFO] 替换片段 282: 使用辅助视频 1113.12-1115.88秒
2025-07-08 16:59:08 [INFO] 处理替换批次 26/252，包含 10 个片段
2025-07-08 16:59:08 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:59:09 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:59:09 [INFO] 替换片段 439: 使用辅助视频 1115.88-1117.75秒
2025-07-08 16:59:09 [INFO] 替换片段 18: 使用辅助视频 1117.75-1124.12秒
2025-07-08 16:59:10 [INFO] 替换片段 473: 使用辅助视频 1124.12-1125.38秒
2025-07-08 16:59:10 [INFO] 替换片段 18: 使用辅助视频 1125.38-1126.79秒
2025-07-08 16:59:10 [INFO] 替换片段 153: 使用辅助视频 1126.79-1129.08秒
2025-07-08 16:59:10 [INFO] 替换片段 18: 使用辅助视频 1129.08-1132.71秒
2025-07-08 16:59:11 [INFO] 替换片段 162: 使用辅助视频 1132.71-1134.38秒
2025-07-08 16:59:11 [INFO] 替换片段 289: 使用辅助视频 1134.38-1140.00秒
2025-07-08 16:59:11 [INFO] 替换片段 117: 使用辅助视频 1161.71-1166.96秒
2025-07-08 16:59:12 [INFO] 替换片段 257: 使用辅助视频 1166.96-1168.46秒
2025-07-08 16:59:12 [INFO] 处理替换批次 27/252，包含 10 个片段
2025-07-08 16:59:12 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:59:12 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:59:13 [INFO] 替换片段 44: 使用辅助视频 1168.46-1169.17秒
2025-07-08 16:59:13 [INFO] 替换片段 295: 使用辅助视频 1169.17-1170.21秒
2025-07-08 16:59:13 [INFO] 替换片段 46: 使用辅助视频 1170.21-1171.67秒
2025-07-08 16:59:13 [INFO] 替换片段 4: 使用辅助视频 1171.67-1175.12秒
2025-07-08 16:59:14 [INFO] 替换片段 179: 使用辅助视频 1175.12-1176.92秒
2025-07-08 16:59:14 [INFO] 替换片段 108: 使用辅助视频 1176.92-1179.50秒
2025-07-08 16:59:14 [INFO] 替换片段 450: 使用辅助视频 1179.50-1182.88秒
2025-07-08 16:59:15 [INFO] 替换片段 32: 使用辅助视频 1182.88-1186.04秒
2025-07-08 16:59:15 [INFO] 替换片段 331: 使用辅助视频 1186.04-1192.42秒
2025-07-08 16:59:16 [INFO] 替换片段 386: 使用辅助视频 1192.42-1193.46秒
2025-07-08 16:59:16 [INFO] 处理替换批次 28/252，包含 10 个片段
2025-07-08 16:59:16 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:59:17 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:59:17 [INFO] 替换片段 394: 使用辅助视频 1193.46-1195.83秒
2025-07-08 16:59:18 [INFO] 替换片段 357: 使用辅助视频 1195.83-1197.04秒
2025-07-08 16:59:18 [INFO] 替换片段 44: 使用辅助视频 1197.04-1200.17秒
2025-07-08 16:59:18 [INFO] 替换片段 44: 使用辅助视频 1200.17-1202.67秒
2025-07-08 16:59:18 [INFO] 替换片段 454: 使用辅助视频 1202.67-1203.50秒
2025-07-08 16:59:19 [INFO] 替换片段 186: 使用辅助视频 1203.50-1205.04秒
2025-07-08 16:59:19 [INFO] 替换片段 153: 使用辅助视频 1205.04-1205.71秒
2025-07-08 16:59:19 [INFO] 替换片段 8: 使用辅助视频 1205.71-1207.75秒
2025-07-08 16:59:19 [INFO] 替换片段 186: 使用辅助视频 1207.75-1208.62秒
2025-07-08 16:59:19 [INFO] 替换片段 416: 使用辅助视频 1208.62-1209.58秒
2025-07-08 16:59:19 [INFO] 处理替换批次 29/252，包含 10 个片段
2025-07-08 16:59:19 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:59:20 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:59:20 [INFO] 替换片段 70: 使用辅助视频 1209.58-1212.50秒
2025-07-08 16:59:21 [INFO] 替换片段 391: 使用辅助视频 1212.50-1213.54秒
2025-07-08 16:59:21 [INFO] 替换片段 150: 使用辅助视频 1213.54-1215.88秒
2025-07-08 16:59:21 [INFO] 替换片段 333: 使用辅助视频 1215.88-1220.04秒
2025-07-08 16:59:22 [INFO] 替换片段 443: 使用辅助视频 1220.04-1225.12秒
2025-07-08 16:59:23 [INFO] 替换片段 147: 使用辅助视频 1225.12-1226.00秒
2025-07-08 16:59:23 [INFO] 替换片段 98: 使用辅助视频 1226.00-1227.75秒
2025-07-08 16:59:23 [INFO] 替换片段 459: 使用辅助视频 1227.75-1229.83秒
2025-07-08 16:59:23 [INFO] 替换片段 204: 使用辅助视频 1229.83-1232.08秒
2025-07-08 16:59:23 [INFO] 替换片段 371: 使用辅助视频 1232.08-1237.12秒
2025-07-08 16:59:24 [INFO] 处理替换批次 30/252，包含 10 个片段
2025-07-08 16:59:24 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:59:24 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:59:24 [INFO] 替换片段 87: 使用辅助视频 1237.12-1244.12秒
2025-07-08 16:59:25 [INFO] 替换片段 100: 使用辅助视频 1244.12-1246.33秒
2025-07-08 16:59:25 [INFO] 替换片段 108: 使用辅助视频 1246.33-1247.88秒
2025-07-08 16:59:25 [INFO] 替换片段 368: 使用辅助视频 1247.88-1249.75秒
2025-07-08 16:59:25 [INFO] 替换片段 150: 使用辅助视频 1249.75-1251.58秒
2025-07-08 16:59:26 [INFO] 替换片段 42: 使用辅助视频 1251.58-1253.54秒
2025-07-08 16:59:26 [INFO] 替换片段 290: 使用辅助视频 1253.54-1254.58秒
2025-07-08 16:59:26 [INFO] 替换片段 38: 使用辅助视频 1254.58-1256.00秒
2025-07-08 16:59:26 [INFO] 替换片段 423: 使用辅助视频 1256.00-1256.71秒
2025-07-08 16:59:26 [INFO] 替换片段 91: 使用辅助视频 1256.71-1257.67秒
2025-07-08 16:59:26 [INFO] 处理替换批次 31/252，包含 10 个片段
2025-07-08 16:59:26 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:59:27 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:59:27 [INFO] 替换片段 119: 使用辅助视频 1257.67-1263.21秒
2025-07-08 16:59:28 [INFO] 替换片段 321: 使用辅助视频 1263.21-1269.75秒
2025-07-08 16:59:28 [INFO] 替换片段 119: 使用辅助视频 1269.75-1279.92秒
2025-07-08 16:59:29 [INFO] 替换片段 49: 使用辅助视频 1279.92-1282.75秒
2025-07-08 16:59:29 [INFO] 替换片段 186: 使用辅助视频 1282.75-1284.96秒
2025-07-08 16:59:29 [INFO] 替换片段 285: 使用辅助视频 1284.96-1288.04秒
2025-07-08 16:59:30 [INFO] 替换片段 99: 使用辅助视频 1288.04-1291.71秒
2025-07-08 16:59:30 [INFO] 替换片段 211: 使用辅助视频 1291.71-1294.04秒
2025-07-08 16:59:31 [INFO] 替换片段 357: 使用辅助视频 1294.04-1300.04秒
2025-07-08 16:59:31 [INFO] 替换片段 467: 使用辅助视频 1300.04-1301.67秒
2025-07-08 16:59:31 [INFO] 处理替换批次 32/252，包含 10 个片段
2025-07-08 16:59:31 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:59:31 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:59:32 [INFO] 替换片段 403: 使用辅助视频 1301.67-1304.50秒
2025-07-08 16:59:32 [INFO] 替换片段 30: 使用辅助视频 1304.50-1305.92秒
2025-07-08 16:59:32 [INFO] 替换片段 49: 使用辅助视频 1305.92-1308.17秒
2025-07-08 16:59:33 [INFO] 替换片段 291: 使用辅助视频 1308.17-1309.71秒
2025-07-08 16:59:33 [INFO] 替换片段 51: 使用辅助视频 1309.71-1313.00秒
2025-07-08 16:59:33 [INFO] 替换片段 445: 使用辅助视频 1313.00-1315.17秒
2025-07-08 16:59:34 [INFO] 替换片段 51: 使用辅助视频 1315.17-1317.21秒
2025-07-08 16:59:34 [INFO] 替换片段 323: 使用辅助视频 1317.21-1320.25秒
2025-07-08 16:59:34 [INFO] 替换片段 120: 使用辅助视频 1320.25-1322.54秒
2025-07-08 16:59:35 [INFO] 替换片段 339: 使用辅助视频 1322.54-1324.12秒
2025-07-08 16:59:35 [INFO] 处理替换批次 33/252，包含 10 个片段
2025-07-08 16:59:35 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:59:35 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:59:36 [INFO] 替换片段 166: 使用辅助视频 1324.12-1324.88秒
2025-07-08 16:59:36 [INFO] 替换片段 52: 使用辅助视频 1324.88-1329.00秒
2025-07-08 16:59:37 [INFO] 替换片段 476: 使用辅助视频 1329.00-1331.33秒
2025-07-08 16:59:37 [INFO] 替换片段 150: 使用辅助视频 1331.33-1335.38秒
2025-07-08 16:59:38 [INFO] 替换片段 14: 使用辅助视频 1335.38-1341.25秒
2025-07-08 16:59:38 [INFO] 替换片段 164: 使用辅助视频 1341.25-1343.50秒
2025-07-08 16:59:38 [INFO] 替换片段 254: 使用辅助视频 1343.50-1347.17秒
2025-07-08 16:59:39 [INFO] 替换片段 53: 使用辅助视频 1347.17-1348.75秒
2025-07-08 16:59:39 [INFO] 替换片段 24: 使用辅助视频 1348.75-1352.08秒
2025-07-08 16:59:40 [INFO] 替换片段 257: 使用辅助视频 1352.08-1360.00秒
2025-07-08 16:59:40 [INFO] 处理替换批次 34/252，包含 10 个片段
2025-07-08 16:59:40 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:59:40 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:59:40 [INFO] 替换片段 185: 使用辅助视频 1360.00-1366.21秒
2025-07-08 16:59:41 [INFO] 替换片段 118: 使用辅助视频 1366.21-1368.08秒
2025-07-08 16:59:41 [INFO] 替换片段 410: 使用辅助视频 1368.08-1370.58秒
2025-07-08 16:59:41 [INFO] 替换片段 254: 使用辅助视频 1370.58-1377.54秒
2025-07-08 16:59:42 [INFO] 替换片段 147: 使用辅助视频 1377.54-1382.29秒
2025-07-08 16:59:42 [INFO] 替换片段 216: 使用辅助视频 1382.29-1393.46秒
2025-07-08 16:59:42 [INFO] 替换片段 313: 使用辅助视频 1393.46-1395.38秒
2025-07-08 16:59:43 [INFO] 替换片段 395: 使用辅助视频 1395.38-1397.25秒
2025-07-08 16:59:43 [INFO] 替换片段 104: 使用辅助视频 1397.25-1398.58秒
2025-07-08 16:59:43 [INFO] 替换片段 219: 使用辅助视频 1398.58-1404.21秒
2025-07-08 16:59:43 [INFO] 处理替换批次 35/252，包含 10 个片段
2025-07-08 16:59:43 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:59:44 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:59:44 [INFO] 替换片段 234: 使用辅助视频 1404.21-1407.00秒
2025-07-08 16:59:44 [INFO] 替换片段 344: 使用辅助视频 1407.00-1411.04秒
2025-07-08 16:59:45 [INFO] 替换片段 216: 使用辅助视频 1411.04-1415.25秒
2025-07-08 16:59:45 [INFO] 替换片段 50: 使用辅助视频 1415.25-1416.46秒
2025-07-08 16:59:45 [INFO] 替换片段 404: 使用辅助视频 1416.46-1417.71秒
2025-07-08 16:59:46 [INFO] 替换片段 246: 使用辅助视频 1417.71-1421.42秒
2025-07-08 16:59:46 [INFO] 替换片段 134: 使用辅助视频 1421.42-1432.46秒
2025-07-08 16:59:47 [INFO] 替换片段 246: 使用辅助视频 1432.46-1438.92秒
2025-07-08 16:59:47 [INFO] 替换片段 141: 使用辅助视频 1438.92-1443.00秒
2025-07-08 16:59:48 [INFO] 替换片段 118: 使用辅助视频 1443.00-1445.67秒
2025-07-08 16:59:48 [INFO] 处理替换批次 36/252，包含 10 个片段
2025-07-08 16:59:48 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:59:48 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:59:49 [INFO] 替换片段 314: 使用辅助视频 1445.67-1448.04秒
2025-07-08 16:59:49 [INFO] 替换片段 298: 使用辅助视频 1448.04-1448.83秒
2025-07-08 16:59:49 [INFO] 替换片段 108: 使用辅助视频 1448.83-1449.50秒
2025-07-08 16:59:49 [INFO] 替换片段 227: 使用辅助视频 1449.50-1450.29秒
2025-07-08 16:59:49 [INFO] 替换片段 44: 使用辅助视频 1450.29-1451.46秒
2025-07-08 16:59:49 [INFO] 替换片段 410: 使用辅助视频 1451.46-1452.38秒
2025-07-08 16:59:50 [INFO] 替换片段 60: 使用辅助视频 1452.38-1453.21秒
2025-07-08 16:59:50 [INFO] 替换片段 174: 使用辅助视频 1453.21-1453.83秒
2025-07-08 16:59:50 [INFO] 替换片段 141: 使用辅助视频 1453.83-1454.62秒
2025-07-08 16:59:50 [INFO] 替换片段 257: 使用辅助视频 1454.62-1455.83秒
2025-07-08 16:59:50 [INFO] 处理替换批次 37/252，包含 10 个片段
2025-07-08 16:59:50 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:59:50 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:59:51 [INFO] 替换片段 2: 使用辅助视频 1455.83-1456.79秒
2025-07-08 16:59:51 [INFO] 替换片段 320: 使用辅助视频 1456.79-1458.12秒
2025-07-08 16:59:51 [INFO] 替换片段 141: 使用辅助视频 1458.12-1460.46秒
2025-07-08 16:59:51 [INFO] 替换片段 349: 使用辅助视频 1460.46-1462.12秒
2025-07-08 16:59:52 [INFO] 替换片段 216: 使用辅助视频 1462.12-1463.88秒
2025-07-08 16:59:52 [INFO] 替换片段 314: 使用辅助视频 1463.88-1465.46秒
2025-07-08 16:59:52 [INFO] 替换片段 410: 使用辅助视频 1465.46-1466.88秒
2025-07-08 16:59:53 [INFO] 替换片段 254: 使用辅助视频 1466.88-1470.25秒
2025-07-08 16:59:53 [INFO] 替换片段 49: 使用辅助视频 1470.25-1471.21秒
2025-07-08 16:59:53 [INFO] 替换片段 379: 使用辅助视频 1471.21-1473.12秒
2025-07-08 16:59:54 [INFO] 处理替换批次 38/252，包含 10 个片段
2025-07-08 16:59:54 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:59:54 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:59:54 [INFO] 替换片段 49: 使用辅助视频 1473.12-1474.38秒
2025-07-08 16:59:54 [INFO] 替换片段 130: 使用辅助视频 1474.38-1475.67秒
2025-07-08 16:59:55 [INFO] 替换片段 449: 使用辅助视频 1475.67-1476.67秒
2025-07-08 16:59:55 [INFO] 替换片段 484: 使用辅助视频 1476.67-1477.46秒
2025-07-08 16:59:55 [INFO] 替换片段 285: 使用辅助视频 1477.46-1478.75秒
2025-07-08 16:59:55 [INFO] 替换片段 14: 使用辅助视频 1478.75-1480.25秒
2025-07-08 16:59:55 [INFO] 替换片段 49: 使用辅助视频 1480.25-1481.21秒
2025-07-08 16:59:55 [INFO] 替换片段 141: 使用辅助视频 1481.21-1482.46秒
2025-07-08 16:59:56 [INFO] 替换片段 342: 使用辅助视频 1482.46-1484.25秒
2025-07-08 16:59:56 [INFO] 替换片段 358: 使用辅助视频 1484.25-1484.96秒
2025-07-08 16:59:56 [INFO] 处理替换批次 39/252，包含 10 个片段
2025-07-08 16:59:56 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 16:59:56 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 16:59:57 [INFO] 替换片段 141: 使用辅助视频 1484.96-1485.58秒
2025-07-08 16:59:57 [INFO] 替换片段 241: 使用辅助视频 1485.58-1489.21秒
2025-07-08 16:59:58 [INFO] 替换片段 179: 使用辅助视频 1503.62-1510.50秒
2025-07-08 16:59:58 [INFO] 替换片段 416: 使用辅助视频 1510.50-1512.33秒
2025-07-08 16:59:58 [INFO] 替换片段 294: 使用辅助视频 1512.33-1517.79秒
2025-07-08 16:59:59 [INFO] 替换片段 199: 使用辅助视频 1517.79-1528.54秒
2025-07-08 16:59:59 [INFO] 替换片段 205: 使用辅助视频 1528.54-1538.71秒
2025-07-08 17:00:00 [INFO] 替换片段 197: 使用辅助视频 1538.71-1546.46秒
2025-07-08 17:00:00 [INFO] 替换片段 203: 使用辅助视频 1546.46-1549.25秒
2025-07-08 17:00:01 [INFO] 替换片段 147: 使用辅助视频 1549.25-1552.29秒
2025-07-08 17:00:01 [INFO] 处理替换批次 40/252，包含 10 个片段
2025-07-08 17:00:01 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:00:01 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:00:01 [INFO] 替换片段 81: 使用辅助视频 1552.29-1557.79秒
2025-07-08 17:00:02 [INFO] 替换片段 318: 使用辅助视频 1557.79-1560.42秒
2025-07-08 17:00:02 [INFO] 替换片段 63: 使用辅助视频 1560.42-1561.17秒
2025-07-08 17:00:02 [INFO] 替换片段 58: 使用辅助视频 1561.17-1562.29秒
2025-07-08 17:00:02 [INFO] 替换片段 399: 使用辅助视频 1562.29-1563.79秒
2025-07-08 17:00:02 [INFO] 替换片段 60: 使用辅助视频 1563.79-1565.04秒
2025-07-08 17:00:03 [INFO] 替换片段 63: 使用辅助视频 1565.04-1566.79秒
2025-07-08 17:00:03 [INFO] 替换片段 58: 使用辅助视频 1566.79-1567.58秒
2025-07-08 17:00:03 [INFO] 替换片段 63: 使用辅助视频 1567.58-1568.29秒
2025-07-08 17:00:03 [INFO] 替换片段 60: 使用辅助视频 1568.29-1569.17秒
2025-07-08 17:00:03 [INFO] 处理替换批次 41/252，包含 10 个片段
2025-07-08 17:00:03 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:00:04 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:00:04 [INFO] 替换片段 63: 使用辅助视频 1569.17-1570.67秒
2025-07-08 17:00:04 [INFO] 替换片段 63: 使用辅助视频 1570.67-1571.38秒
2025-07-08 17:00:04 [INFO] 替换片段 60: 使用辅助视频 1571.38-1572.21秒
2025-07-08 17:00:04 [INFO] 替换片段 63: 使用辅助视频 1572.21-1572.92秒
2025-07-08 17:00:04 [INFO] 替换片段 58: 使用辅助视频 1572.92-1573.83秒
2025-07-08 17:00:05 [INFO] 替换片段 64: 使用辅助视频 1573.83-1576.50秒
2025-07-08 17:00:05 [INFO] 替换片段 103: 使用辅助视频 1576.50-1577.46秒
2025-07-08 17:00:05 [INFO] 替换片段 63: 使用辅助视频 1577.46-1578.17秒
2025-07-08 17:00:05 [INFO] 替换片段 102: 使用辅助视频 1578.17-1579.79秒
2025-07-08 17:00:05 [INFO] 替换片段 387: 使用辅助视频 1579.79-1580.71秒
2025-07-08 17:00:05 [INFO] 处理替换批次 42/252，包含 10 个片段
2025-07-08 17:00:05 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:00:06 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:00:06 [INFO] 替换片段 67: 使用辅助视频 1580.71-1581.79秒
2025-07-08 17:00:06 [INFO] 替换片段 374: 使用辅助视频 1581.79-1582.42秒
2025-07-08 17:00:06 [INFO] 替换片段 69: 使用辅助视频 1582.42-1583.62秒
2025-07-08 17:00:07 [INFO] 替换片段 70: 使用辅助视频 1583.62-1584.50秒
2025-07-08 17:00:07 [INFO] 替换片段 71: 使用辅助视频 1584.50-1585.67秒
2025-07-08 17:00:07 [INFO] 替换片段 72: 使用辅助视频 1585.67-1586.46秒
2025-07-08 17:00:07 [INFO] 替换片段 73: 使用辅助视频 1586.46-1587.33秒
2025-07-08 17:00:07 [INFO] 替换片段 74: 使用辅助视频 1587.33-1589.25秒
2025-07-08 17:00:07 [INFO] 替换片段 170: 使用辅助视频 1589.25-1590.88秒
2025-07-08 17:00:08 [INFO] 替换片段 76: 使用辅助视频 1590.88-1593.04秒
2025-07-08 17:00:08 [INFO] 处理替换批次 43/252，包含 10 个片段
2025-07-08 17:00:08 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:00:08 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:00:08 [INFO] 替换片段 77: 使用辅助视频 1593.04-1596.79秒
2025-07-08 17:00:09 [INFO] 替换片段 78: 使用辅助视频 1596.79-1599.08秒
2025-07-08 17:00:09 [INFO] 替换片段 79: 使用辅助视频 1599.08-1600.46秒
2025-07-08 17:00:09 [INFO] 替换片段 80: 使用辅助视频 1600.46-1602.08秒
2025-07-08 17:00:10 [INFO] 替换片段 81: 使用辅助视频 1602.08-1605.67秒
2025-07-08 17:00:10 [INFO] 替换片段 33: 使用辅助视频 1605.67-1609.00秒
2025-07-08 17:00:11 [INFO] 替换片段 83: 使用辅助视频 1609.00-1613.33秒
2025-07-08 17:00:11 [INFO] 替换片段 84: 使用辅助视频 1613.33-1614.58秒
2025-07-08 17:00:11 [INFO] 替换片段 85: 使用辅助视频 1614.58-1617.54秒
2025-07-08 17:00:12 [INFO] 替换片段 59: 使用辅助视频 1617.54-1619.33秒
2025-07-08 17:00:12 [INFO] 处理替换批次 44/252，包含 10 个片段
2025-07-08 17:00:12 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:00:12 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:00:12 [INFO] 替换片段 452: 使用辅助视频 1619.33-1620.08秒
2025-07-08 17:00:12 [INFO] 替换片段 254: 使用辅助视频 1620.08-1620.71秒
2025-07-08 17:00:13 [INFO] 替换片段 449: 使用辅助视频 1620.71-1621.46秒
2025-07-08 17:00:13 [INFO] 替换片段 254: 使用辅助视频 1621.46-1622.25秒
2025-07-08 17:00:13 [INFO] 替换片段 449: 使用辅助视频 1622.25-1623.12秒
2025-07-08 17:00:13 [INFO] 替换片段 41: 使用辅助视频 1623.12-1624.21秒
2025-07-08 17:00:13 [INFO] 替换片段 141: 使用辅助视频 1624.21-1625.50秒
2025-07-08 17:00:13 [INFO] 替换片段 323: 使用辅助视频 1625.50-1627.46秒
2025-07-08 17:00:13 [INFO] 替换片段 316: 使用辅助视频 1627.46-1628.62秒
2025-07-08 17:00:14 [INFO] 替换片段 45: 使用辅助视频 1628.62-1629.25秒
2025-07-08 17:00:14 [INFO] 处理替换批次 45/252，包含 10 个片段
2025-07-08 17:00:14 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:00:14 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:00:15 [INFO] 替换片段 199: 使用辅助视频 1629.25-1631.25秒
2025-07-08 17:00:15 [INFO] 替换片段 258: 使用辅助视频 1631.25-1632.54秒
2025-07-08 17:00:15 [INFO] 替换片段 20: 使用辅助视频 1632.54-1633.25秒
2025-07-08 17:00:15 [INFO] 替换片段 382: 使用辅助视频 1633.25-1634.29秒
2025-07-08 17:00:15 [INFO] 替换片段 149: 使用辅助视频 1634.29-1634.96秒
2025-07-08 17:00:15 [INFO] 替换片段 391: 使用辅助视频 1634.96-1636.21秒
2025-07-08 17:00:16 [INFO] 替换片段 422: 使用辅助视频 1636.21-1637.12秒
2025-07-08 17:00:16 [INFO] 替换片段 305: 使用辅助视频 1637.12-1637.96秒
2025-07-08 17:00:16 [INFO] 替换片段 198: 使用辅助视频 1637.96-1640.88秒
2025-07-08 17:00:16 [INFO] 替换片段 258: 使用辅助视频 1640.88-1642.00秒
2025-07-08 17:00:16 [INFO] 处理替换批次 46/252，包含 10 个片段
2025-07-08 17:00:16 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:00:17 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:00:17 [INFO] 替换片段 306: 使用辅助视频 1642.00-1644.38秒
2025-07-08 17:00:17 [INFO] 替换片段 380: 使用辅助视频 1644.38-1645.54秒
2025-07-08 17:00:18 [INFO] 替换片段 312: 使用辅助视频 1645.54-1646.42秒
2025-07-08 17:00:18 [INFO] 替换片段 439: 使用辅助视频 1646.42-1647.29秒
2025-07-08 17:00:18 [INFO] 替换片段 166: 使用辅助视频 1647.29-1649.58秒
2025-07-08 17:00:18 [INFO] 替换片段 151: 使用辅助视频 1649.58-1650.75秒
2025-07-08 17:00:18 [INFO] 替换片段 402: 使用辅助视频 1650.75-1651.46秒
2025-07-08 17:00:18 [INFO] 替换片段 365: 使用辅助视频 1651.46-1653.04秒
2025-07-08 17:00:19 [INFO] 替换片段 306: 使用辅助视频 1653.04-1653.83秒
2025-07-08 17:00:19 [INFO] 替换片段 254: 使用辅助视频 1653.83-1654.58秒
2025-07-08 17:00:19 [INFO] 处理替换批次 47/252，包含 10 个片段
2025-07-08 17:00:19 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:00:19 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:00:20 [INFO] 替换片段 3: 使用辅助视频 1654.58-1656.46秒
2025-07-08 17:00:20 [INFO] 替换片段 467: 使用辅助视频 1656.46-1657.42秒
2025-07-08 17:00:20 [INFO] 替换片段 339: 使用辅助视频 1657.42-1658.92秒
2025-07-08 17:00:20 [INFO] 替换片段 229: 使用辅助视频 1658.92-1660.12秒
2025-07-08 17:00:20 [INFO] 替换片段 306: 使用辅助视频 1660.12-1660.79秒
2025-07-08 17:00:20 [INFO] 替换片段 442: 使用辅助视频 1660.79-1663.21秒
2025-07-08 17:00:21 [INFO] 替换片段 401: 使用辅助视频 1663.21-1665.67秒
2025-07-08 17:00:21 [INFO] 替换片段 126: 使用辅助视频 1665.67-1666.79秒
2025-07-08 17:00:21 [INFO] 替换片段 447: 使用辅助视频 1666.79-1668.58秒
2025-07-08 17:00:22 [INFO] 替换片段 370: 使用辅助视频 1668.58-1670.79秒
2025-07-08 17:00:22 [INFO] 处理替换批次 48/252，包含 10 个片段
2025-07-08 17:00:22 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:00:22 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:00:22 [INFO] 替换片段 56: 使用辅助视频 1670.79-1672.00秒
2025-07-08 17:00:22 [INFO] 替换片段 467: 使用辅助视频 1672.00-1672.83秒
2025-07-08 17:00:23 [INFO] 替换片段 449: 使用辅助视频 1672.83-1675.88秒
2025-07-08 17:00:23 [INFO] 替换片段 352: 使用辅助视频 1675.88-1681.21秒
2025-07-08 17:00:23 [INFO] 替换片段 195: 使用辅助视频 1681.21-1685.75秒
2025-07-08 17:00:24 [INFO] 替换片段 240: 使用辅助视频 1685.75-1686.50秒
2025-07-08 17:00:24 [INFO] 替换片段 473: 使用辅助视频 1686.50-1687.71秒
2025-07-08 17:00:24 [INFO] 替换片段 130: 使用辅助视频 1687.71-1688.50秒
2025-07-08 17:00:24 [INFO] 替换片段 208: 使用辅助视频 1688.50-1689.46秒
2025-07-08 17:00:24 [INFO] 替换片段 102: 使用辅助视频 1689.46-1690.42秒
2025-07-08 17:00:24 [INFO] 处理替换批次 49/252，包含 10 个片段
2025-07-08 17:00:24 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:00:25 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:00:25 [INFO] 替换片段 100: 使用辅助视频 1690.42-1691.25秒
2025-07-08 17:00:25 [INFO] 替换片段 289: 使用辅助视频 1691.25-1692.79秒
2025-07-08 17:00:25 [INFO] 替换片段 310: 使用辅助视频 1692.79-1694.71秒
2025-07-08 17:00:26 [INFO] 替换片段 255: 使用辅助视频 1694.71-1698.62秒
2025-07-08 17:00:26 [INFO] 替换片段 88: 使用辅助视频 1698.62-1700.50秒
2025-07-08 17:00:26 [INFO] 替换片段 116: 使用辅助视频 1700.50-1702.92秒
2025-07-08 17:00:27 [INFO] 替换片段 49: 使用辅助视频 1702.92-1703.58秒
2025-07-08 17:00:27 [INFO] 替换片段 204: 使用辅助视频 1703.58-1705.04秒
2025-07-08 17:00:27 [INFO] 替换片段 287: 使用辅助视频 1705.04-1705.75秒
2025-07-08 17:00:27 [INFO] 替换片段 308: 使用辅助视频 1707.75-1708.50秒
2025-07-08 17:00:27 [INFO] 处理替换批次 50/252，包含 10 个片段
2025-07-08 17:00:27 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:00:28 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:00:28 [INFO] 替换片段 90: 使用辅助视频 1708.50-1709.46秒
2025-07-08 17:00:28 [INFO] 替换片段 13: 使用辅助视频 1709.46-1710.08秒
2025-07-08 17:00:29 [INFO] 替换片段 337: 使用辅助视频 1714.21-1715.42秒
2025-07-08 17:00:29 [INFO] 替换片段 389: 使用辅助视频 1715.42-1718.08秒
2025-07-08 17:00:29 [INFO] 替换片段 162: 使用辅助视频 1718.08-1719.54秒
2025-07-08 17:00:29 [INFO] 替换片段 254: 使用辅助视频 1719.54-1721.29秒
2025-07-08 17:00:30 [INFO] 替换片段 92: 使用辅助视频 1721.29-1722.88秒
2025-07-08 17:00:30 [INFO] 替换片段 267: 使用辅助视频 1722.88-1723.92秒
2025-07-08 17:00:30 [INFO] 替换片段 217: 使用辅助视频 1723.92-1724.92秒
2025-07-08 17:00:30 [INFO] 替换片段 143: 使用辅助视频 1724.92-1725.88秒
2025-07-08 17:00:30 [INFO] 处理替换批次 51/252，包含 10 个片段
2025-07-08 17:00:30 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:00:31 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:00:31 [INFO] 替换片段 196: 使用辅助视频 1725.88-1726.67秒
2025-07-08 17:00:31 [INFO] 替换片段 335: 使用辅助视频 1726.67-1728.46秒
2025-07-08 17:00:31 [INFO] 替换片段 472: 使用辅助视频 1728.46-1730.29秒
2025-07-08 17:00:32 [INFO] 替换片段 456: 使用辅助视频 1732.25-1735.79秒
2025-07-08 17:00:32 [INFO] 替换片段 133: 使用辅助视频 1735.79-1738.29秒
2025-07-08 17:00:33 [INFO] 替换片段 348: 使用辅助视频 1738.29-1743.21秒
2025-07-08 17:00:33 [INFO] 替换片段 229: 使用辅助视频 1743.21-1744.83秒
2025-07-08 17:00:34 [INFO] 替换片段 363: 使用辅助视频 1746.21-1748.00秒
2025-07-08 17:00:34 [INFO] 替换片段 96: 使用辅助视频 1748.00-1752.96秒
2025-07-08 17:00:34 [INFO] 替换片段 18: 使用辅助视频 1752.96-1757.50秒
2025-07-08 17:00:34 [INFO] 处理替换批次 52/252，包含 10 个片段
2025-07-08 17:00:34 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:00:35 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:00:35 [INFO] 替换片段 255: 使用辅助视频 1757.50-1758.38秒
2025-07-08 17:00:36 [INFO] 替换片段 99: 使用辅助视频 1758.38-1759.62秒
2025-07-08 17:00:36 [INFO] 替换片段 255: 使用辅助视频 1759.62-1762.42秒
2025-07-08 17:00:36 [INFO] 替换片段 389: 使用辅助视频 1762.42-1764.75秒
2025-07-08 17:00:36 [INFO] 替换片段 14: 使用辅助视频 1764.75-1768.04秒
2025-07-08 17:00:37 [INFO] 替换片段 445: 使用辅助视频 1768.04-1770.58秒
2025-07-08 17:00:37 [INFO] 替换片段 248: 使用辅助视频 1770.58-1778.96秒
2025-07-08 17:00:38 [INFO] 替换片段 282: 使用辅助视频 1778.96-1784.04秒
2025-07-08 17:00:38 [INFO] 替换片段 197: 使用辅助视频 1784.04-1786.17秒
2025-07-08 17:00:38 [INFO] 替换片段 451: 使用辅助视频 1786.17-1789.42秒
2025-07-08 17:00:39 [INFO] 处理替换批次 53/252，包含 10 个片段
2025-07-08 17:00:39 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:00:39 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:00:39 [INFO] 替换片段 316: 使用辅助视频 1789.42-1793.83秒
2025-07-08 17:00:40 [INFO] 替换片段 217: 使用辅助视频 1793.83-1796.08秒
2025-07-08 17:00:40 [INFO] 替换片段 310: 使用辅助视频 1796.08-1800.42秒
2025-07-08 17:00:40 [INFO] 替换片段 293: 使用辅助视频 1800.42-1802.17秒
2025-07-08 17:00:41 [INFO] 替换片段 252: 使用辅助视频 1802.17-1804.92秒
2025-07-08 17:00:41 [INFO] 替换片段 127: 使用辅助视频 1804.92-1808.58秒
2025-07-08 17:00:42 [INFO] 替换片段 472: 使用辅助视频 1808.58-1813.04秒
2025-07-08 17:00:42 [INFO] 替换片段 127: 使用辅助视频 1813.04-1816.33秒
2025-07-08 17:00:42 [INFO] 替换片段 310: 使用辅助视频 1816.33-1819.08秒
2025-07-08 17:00:43 [INFO] 替换片段 127: 使用辅助视频 1819.08-1825.42秒
2025-07-08 17:00:43 [INFO] 处理替换批次 54/252，包含 10 个片段
2025-07-08 17:00:43 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:00:43 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:00:44 [INFO] 替换片段 117: 使用辅助视频 1825.42-1828.33秒
2025-07-08 17:00:44 [INFO] 替换片段 216: 使用辅助视频 1828.33-1835.00秒
2025-07-08 17:00:44 [INFO] 替换片段 472: 使用辅助视频 1835.00-1843.58秒
2025-07-08 17:00:45 [INFO] 替换片段 479: 使用辅助视频 1843.58-1847.58秒
2025-07-08 17:00:45 [INFO] 替换片段 219: 使用辅助视频 1847.58-1857.00秒
2025-07-08 17:00:46 [INFO] 替换片段 424: 使用辅助视频 1857.00-1866.92秒
2025-07-08 17:00:47 [INFO] 替换片段 389: 使用辅助视频 1866.92-1867.58秒
2025-07-08 17:00:47 [INFO] 替换片段 128: 使用辅助视频 1867.58-1876.33秒
2025-07-08 17:00:47 [INFO] 替换片段 186: 使用辅助视频 1876.33-1878.21秒
2025-07-08 17:00:47 [INFO] 替换片段 382: 使用辅助视频 1878.21-1881.92秒
2025-07-08 17:00:47 [INFO] 处理替换批次 55/252，包含 10 个片段
2025-07-08 17:00:47 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:00:48 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:00:48 [INFO] 替换片段 423: 使用辅助视频 1881.92-1885.42秒
2025-07-08 17:00:49 [INFO] 替换片段 307: 使用辅助视频 1885.42-1886.42秒
2025-07-08 17:00:49 [INFO] 替换片段 103: 使用辅助视频 1886.42-1889.25秒
2025-07-08 17:00:49 [INFO] 替换片段 314: 使用辅助视频 1889.25-1890.88秒
2025-07-08 17:00:50 [INFO] 替换片段 145: 使用辅助视频 1890.88-1895.92秒
2025-07-08 17:00:50 [INFO] 替换片段 261: 使用辅助视频 1895.92-1900.29秒
2025-07-08 17:00:50 [INFO] 替换片段 473: 使用辅助视频 1900.29-1901.38秒
2025-07-08 17:00:50 [INFO] 替换片段 7: 使用辅助视频 1901.38-1902.58秒
2025-07-08 17:00:51 [INFO] 替换片段 439: 使用辅助视频 1902.58-1904.12秒
2025-07-08 17:00:51 [INFO] 替换片段 106: 使用辅助视频 1904.12-1906.08秒
2025-07-08 17:00:51 [INFO] 处理替换批次 56/252，包含 10 个片段
2025-07-08 17:00:51 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:00:52 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:00:52 [INFO] 替换片段 117: 使用辅助视频 1906.08-1913.75秒
2025-07-08 17:00:53 [INFO] 替换片段 109: 使用辅助视频 1913.75-1918.62秒
2025-07-08 17:00:53 [INFO] 替换片段 476: 使用辅助视频 1918.62-1919.96秒
2025-07-08 17:00:53 [INFO] 替换片段 122: 使用辅助视频 1919.96-1921.54秒
2025-07-08 17:00:53 [INFO] 替换片段 117: 使用辅助视频 1921.54-1923.38秒
2025-07-08 17:00:54 [INFO] 替换片段 477: 使用辅助视频 1923.38-1925.33秒
2025-07-08 17:00:54 [INFO] 替换片段 320: 使用辅助视频 1925.33-1926.04秒
2025-07-08 17:00:54 [INFO] 替换片段 447: 使用辅助视频 1926.04-1926.88秒
2025-07-08 17:00:54 [INFO] 替换片段 227: 使用辅助视频 1926.88-1929.58秒
2025-07-08 17:00:54 [INFO] 替换片段 106: 使用辅助视频 1929.58-1933.50秒
2025-07-08 17:00:54 [INFO] 处理替换批次 57/252，包含 10 个片段
2025-07-08 17:00:54 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:00:55 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:00:55 [INFO] 替换片段 318: 使用辅助视频 1933.50-1945.62秒
2025-07-08 17:00:56 [INFO] 替换片段 1: 使用辅助视频 1945.62-1949.71秒
2025-07-08 17:00:56 [INFO] 替换片段 75: 使用辅助视频 1949.71-1955.25秒
2025-07-08 17:00:57 [INFO] 替换片段 295: 使用辅助视频 1955.25-1957.88秒
2025-07-08 17:00:57 [INFO] 替换片段 156: 使用辅助视频 1957.88-1959.88秒
2025-07-08 17:00:57 [INFO] 替换片段 412: 使用辅助视频 1959.88-1961.54秒
2025-07-08 17:00:57 [INFO] 替换片段 215: 使用辅助视频 1961.54-1963.58秒
2025-07-08 17:00:58 [INFO] 替换片段 295: 使用辅助视频 1963.58-1968.79秒
2025-07-08 17:00:58 [INFO] 替换片段 460: 使用辅助视频 1968.79-1969.92秒
2025-07-08 17:00:58 [INFO] 替换片段 307: 使用辅助视频 1969.92-1974.00秒
2025-07-08 17:00:58 [INFO] 处理替换批次 58/252，包含 10 个片段
2025-07-08 17:00:58 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:00:59 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:00:59 [INFO] 替换片段 484: 使用辅助视频 1974.00-1974.88秒
2025-07-08 17:00:59 [INFO] 替换片段 236: 使用辅助视频 1974.88-1977.62秒
2025-07-08 17:01:00 [INFO] 替换片段 376: 使用辅助视频 1977.62-1979.96秒
2025-07-08 17:01:00 [INFO] 替换片段 116: 使用辅助视频 1979.96-1981.96秒
2025-07-08 17:01:00 [INFO] 替换片段 19: 使用辅助视频 1981.96-1988.08秒
2025-07-08 17:01:00 [INFO] 替换片段 391: 使用辅助视频 1988.08-1990.04秒
2025-07-08 17:01:01 [INFO] 替换片段 412: 使用辅助视频 1997.96-2000.92秒
2025-07-08 17:01:01 [INFO] 替换片段 110: 使用辅助视频 2000.92-2003.17秒
2025-07-08 17:01:02 [INFO] 替换片段 310: 使用辅助视频 2003.17-2011.08秒
2025-07-08 17:01:02 [INFO] 替换片段 131: 使用辅助视频 2011.08-2014.08秒
2025-07-08 17:01:02 [INFO] 处理替换批次 59/252，包含 10 个片段
2025-07-08 17:01:02 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:01:03 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:01:03 [INFO] 替换片段 8: 使用辅助视频 2036.08-2042.75秒
2025-07-08 17:01:03 [INFO] 替换片段 191: 使用辅助视频 2042.75-2045.04秒
2025-07-08 17:01:04 [INFO] 替换片段 286: 使用辅助视频 2045.04-2046.12秒
2025-07-08 17:01:04 [INFO] 替换片段 375: 使用辅助视频 2046.12-2058.08秒
2025-07-08 17:01:04 [INFO] 替换片段 390: 使用辅助视频 2058.08-2069.21秒
2025-07-08 17:01:04 [INFO] 替换片段 370: 使用辅助视频 2069.21-2080.71秒
2025-07-08 17:01:05 [INFO] 替换片段 208: 使用辅助视频 2080.71-2085.71秒
2025-07-08 17:01:05 [INFO] 替换片段 379: 使用辅助视频 2085.71-2088.38秒
2025-07-08 17:01:05 [INFO] 替换片段 102: 使用辅助视频 2088.38-2090.83秒
2025-07-08 17:01:06 [INFO] 替换片段 283: 使用辅助视频 2090.83-2097.71秒
2025-07-08 17:01:06 [INFO] 处理替换批次 60/252，包含 10 个片段
2025-07-08 17:01:06 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:01:06 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:01:07 [INFO] 替换片段 103: 使用辅助视频 2097.71-2101.29秒
2025-07-08 17:01:07 [INFO] 替换片段 62: 使用辅助视频 2101.29-2106.58秒
2025-07-08 17:01:07 [INFO] 替换片段 58: 使用辅助视频 2106.58-2107.46秒
2025-07-08 17:01:07 [INFO] 替换片段 321: 使用辅助视频 2107.46-2110.04秒
2025-07-08 17:01:08 [INFO] 替换片段 397: 使用辅助视频 2110.04-2111.62秒
2025-07-08 17:01:08 [INFO] 替换片段 416: 使用辅助视频 2111.62-2112.58秒
2025-07-08 17:01:08 [INFO] 替换片段 325: 使用辅助视频 2112.58-2113.92秒
2025-07-08 17:01:08 [INFO] 替换片段 35: 使用辅助视频 2113.92-2118.96秒
2025-07-08 17:01:09 [INFO] 替换片段 106: 使用辅助视频 2118.96-2120.08秒
2025-07-08 17:01:09 [INFO] 替换片段 104: 使用辅助视频 2120.08-2121.58秒
2025-07-08 17:01:09 [INFO] 处理替换批次 61/252，包含 10 个片段
2025-07-08 17:01:09 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:01:10 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:01:10 [INFO] 替换片段 7: 使用辅助视频 2121.58-2123.29秒
2025-07-08 17:01:10 [INFO] 替换片段 459: 使用辅助视频 2123.29-2124.46秒
2025-07-08 17:01:10 [INFO] 替换片段 319: 使用辅助视频 2124.46-2127.42秒
2025-07-08 17:01:11 [INFO] 替换片段 60: 使用辅助视频 2127.42-2129.92秒
2025-07-08 17:01:11 [INFO] 替换片段 172: 使用辅助视频 2129.92-2133.62秒
2025-07-08 17:01:11 [INFO] 替换片段 23: 使用辅助视频 2133.62-2135.25秒
2025-07-08 17:01:12 [INFO] 替换片段 399: 使用辅助视频 2135.25-2137.25秒
2025-07-08 17:01:12 [INFO] 替换片段 118: 使用辅助视频 2137.25-2138.58秒
2025-07-08 17:01:12 [INFO] 替换片段 235: 使用辅助视频 2138.58-2140.50秒
2025-07-08 17:01:12 [INFO] 替换片段 174: 使用辅助视频 2140.50-2146.12秒
2025-07-08 17:01:12 [INFO] 处理替换批次 62/252，包含 10 个片段
2025-07-08 17:01:12 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:01:13 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:01:13 [INFO] 替换片段 395: 使用辅助视频 2146.12-2155.58秒
2025-07-08 17:01:14 [INFO] 替换片段 218: 使用辅助视频 2155.58-2158.58秒
2025-07-08 17:01:14 [INFO] 替换片段 109: 使用辅助视频 2158.58-2162.46秒
2025-07-08 17:01:15 [INFO] 替换片段 242: 使用辅助视频 2162.46-2167.54秒
2025-07-08 17:01:15 [INFO] 替换片段 465: 使用辅助视频 2167.54-2171.33秒
2025-07-08 17:01:16 [INFO] 替换片段 243: 使用辅助视频 2171.33-2179.08秒
2025-07-08 17:01:16 [INFO] 替换片段 227: 使用辅助视频 2179.08-2181.17秒
2025-07-08 17:01:16 [INFO] 替换片段 384: 使用辅助视频 2181.17-2183.00秒
2025-07-08 17:01:17 [INFO] 替换片段 407: 使用辅助视频 2183.00-2184.83秒
2025-07-08 17:01:17 [INFO] 替换片段 111: 使用辅助视频 2184.83-2186.62秒
2025-07-08 17:01:17 [INFO] 处理替换批次 63/252，包含 10 个片段
2025-07-08 17:01:17 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:01:18 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:01:18 [INFO] 替换片段 150: 使用辅助视频 2186.62-2188.75秒
2025-07-08 17:01:18 [INFO] 替换片段 447: 使用辅助视频 2188.75-2216.67秒
2025-07-08 17:01:19 [INFO] 替换片段 282: 使用辅助视频 2216.67-2221.29秒
2025-07-08 17:01:19 [INFO] 替换片段 120: 使用辅助视频 2221.29-2224.38秒
2025-07-08 17:01:20 [INFO] 替换片段 123: 使用辅助视频 2224.38-2226.67秒
2025-07-08 17:01:20 [INFO] 替换片段 477: 使用辅助视频 2226.67-2228.88秒
2025-07-08 17:01:21 [INFO] 替换片段 123: 使用辅助视频 2228.88-2231.50秒
2025-07-08 17:01:21 [INFO] 替换片段 41: 使用辅助视频 2231.50-2237.17秒
2025-07-08 17:01:21 [INFO] 替换片段 123: 使用辅助视频 2237.17-2238.67秒
2025-07-08 17:01:22 [INFO] 替换片段 459: 使用辅助视频 2238.67-2242.62秒
2025-07-08 17:01:22 [INFO] 处理替换批次 64/252，包含 10 个片段
2025-07-08 17:01:22 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:01:22 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:01:23 [INFO] 替换片段 14: 使用辅助视频 2242.62-2243.79秒
2025-07-08 17:01:23 [INFO] 替换片段 204: 使用辅助视频 2243.79-2245.00秒
2025-07-08 17:01:23 [INFO] 替换片段 289: 使用辅助视频 2245.00-2247.75秒
2025-07-08 17:01:23 [INFO] 替换片段 18: 使用辅助视频 2247.75-2250.29秒
2025-07-08 17:01:24 [INFO] 替换片段 315: 使用辅助视频 2250.29-2252.92秒
2025-07-08 17:01:24 [INFO] 替换片段 397: 使用辅助视频 2252.92-2255.00秒
2025-07-08 17:01:25 [INFO] 替换片段 485: 使用辅助视频 2255.00-2257.12秒
2025-07-08 17:01:25 [INFO] 替换片段 38: 使用辅助视频 2257.12-2259.71秒
2025-07-08 17:01:25 [INFO] 替换片段 485: 使用辅助视频 2259.71-2262.17秒
2025-07-08 17:01:26 [INFO] 替换片段 64: 使用辅助视频 2262.17-2266.79秒
2025-07-08 17:01:26 [INFO] 处理替换批次 65/252，包含 10 个片段
2025-07-08 17:01:26 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:01:26 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:01:27 [INFO] 替换片段 128: 使用辅助视频 2266.79-2270.62秒
2025-07-08 17:01:28 [INFO] 替换片段 129: 使用辅助视频 2270.62-2273.12秒
2025-07-08 17:01:28 [INFO] 替换片段 103: 使用辅助视频 2273.12-2274.04秒
2025-07-08 17:01:28 [INFO] 替换片段 130: 使用辅助视频 2274.04-2285.12秒
2025-07-08 17:01:29 [INFO] 替换片段 388: 使用辅助视频 2285.12-2286.46秒
2025-07-08 17:01:29 [INFO] 替换片段 177: 使用辅助视频 2286.46-2295.08秒
2025-07-08 17:01:29 [INFO] 替换片段 476: 使用辅助视频 2295.08-2297.96秒
2025-07-08 17:01:30 [INFO] 替换片段 50: 使用辅助视频 2297.96-2301.21秒
2025-07-08 17:01:30 [INFO] 替换片段 345: 使用辅助视频 2301.21-2304.21秒
2025-07-08 17:01:31 [INFO] 替换片段 182: 使用辅助视频 2304.21-2305.75秒
2025-07-08 17:01:31 [INFO] 处理替换批次 66/252，包含 10 个片段
2025-07-08 17:01:31 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:01:31 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:01:32 [INFO] 替换片段 474: 使用辅助视频 2305.75-2307.79秒
2025-07-08 17:01:32 [INFO] 替换片段 9: 使用辅助视频 2307.79-2311.67秒
2025-07-08 17:01:32 [INFO] 替换片段 177: 使用辅助视频 2311.67-2313.88秒
2025-07-08 17:01:33 [INFO] 替换片段 185: 使用辅助视频 2313.88-2319.17秒
2025-07-08 17:01:33 [INFO] 替换片段 205: 使用辅助视频 2319.17-2324.83秒
2025-07-08 17:01:33 [INFO] 替换片段 404: 使用辅助视频 2324.83-2325.75秒
2025-07-08 17:01:34 [INFO] 替换片段 134: 使用辅助视频 2325.75-2326.71秒
2025-07-08 17:01:34 [INFO] 替换片段 185: 使用辅助视频 2326.71-2337.00秒
2025-07-08 17:01:34 [INFO] 替换片段 137: 使用辅助视频 2337.00-2338.21秒
2025-07-08 17:01:34 [INFO] 替换片段 135: 使用辅助视频 2338.21-2340.50秒
2025-07-08 17:01:34 [INFO] 处理替换批次 67/252，包含 10 个片段
2025-07-08 17:01:34 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:01:35 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:01:35 [INFO] 替换片段 118: 使用辅助视频 2340.50-2341.12秒
2025-07-08 17:01:35 [INFO] 替换片段 140: 使用辅助视频 2341.12-2354.08秒
2025-07-08 17:01:36 [INFO] 替换片段 348: 使用辅助视频 2354.08-2355.96秒
2025-07-08 17:01:36 [INFO] 替换片段 428: 使用辅助视频 2355.96-2361.83秒
2025-07-08 17:01:36 [INFO] 替换片段 143: 使用辅助视频 2361.83-2366.96秒
2025-07-08 17:01:36 [INFO] 替换片段 254: 使用辅助视频 2366.96-2370.67秒
2025-07-08 17:01:37 [INFO] 替换片段 227: 使用辅助视频 2370.67-2373.08秒
2025-07-08 17:01:37 [INFO] 替换片段 248: 使用辅助视频 2373.08-2377.00秒
2025-07-08 17:01:38 [INFO] 替换片段 296: 使用辅助视频 2377.00-2384.42秒
2025-07-08 17:01:38 [INFO] 替换片段 254: 使用辅助视频 2384.42-2389.17秒
2025-07-08 17:01:38 [INFO] 处理替换批次 68/252，包含 10 个片段
2025-07-08 17:01:38 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:01:39 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:01:39 [INFO] 替换片段 412: 使用辅助视频 2389.17-2391.54秒
2025-07-08 17:01:40 [INFO] 替换片段 254: 使用辅助视频 2391.54-2393.67秒
2025-07-08 17:01:40 [INFO] 替换片段 145: 使用辅助视频 2393.67-2399.96秒
2025-07-08 17:01:40 [INFO] 替换片段 254: 使用辅助视频 2399.96-2402.00秒
2025-07-08 17:01:40 [INFO] 替换片段 145: 使用辅助视频 2402.00-2404.62秒
2025-07-08 17:01:41 [INFO] 替换片段 61: 使用辅助视频 2404.62-2406.29秒
2025-07-08 17:01:41 [INFO] 替换片段 357: 使用辅助视频 2406.29-2409.42秒
2025-07-08 17:01:42 [INFO] 替换片段 472: 使用辅助视频 2409.42-2413.54秒
2025-07-08 17:01:42 [INFO] 替换片段 128: 使用辅助视频 2413.54-2418.33秒
2025-07-08 17:01:43 [INFO] 替换片段 459: 使用辅助视频 2418.33-2422.62秒
2025-07-08 17:01:43 [INFO] 处理替换批次 69/252，包含 10 个片段
2025-07-08 17:01:43 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:01:43 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:01:44 [INFO] 替换片段 142: 使用辅助视频 2422.62-2424.00秒
2025-07-08 17:01:44 [INFO] 替换片段 294: 使用辅助视频 2424.00-2429.50秒
2025-07-08 17:01:44 [INFO] 替换片段 401: 使用辅助视频 2429.50-2431.46秒
2025-07-08 17:01:44 [INFO] 替换片段 85: 使用辅助视频 2431.46-2434.04秒
2025-07-08 17:01:45 [INFO] 替换片段 321: 使用辅助视频 2438.21-2443.00秒
2025-07-08 17:01:45 [INFO] 替换片段 483: 使用辅助视频 2443.00-2448.21秒
2025-07-08 17:01:45 [INFO] 替换片段 192: 使用辅助视频 2448.21-2454.79秒
2025-07-08 17:01:46 [INFO] 替换片段 345: 使用辅助视频 2454.79-2460.46秒
2025-07-08 17:01:46 [INFO] 替换片段 126: 使用辅助视频 2460.46-2463.46秒
2025-07-08 17:01:47 [INFO] 替换片段 428: 使用辅助视频 2463.46-2468.58秒
2025-07-08 17:01:47 [INFO] 处理替换批次 70/252，包含 10 个片段
2025-07-08 17:01:47 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:01:47 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:01:48 [INFO] 替换片段 316: 使用辅助视频 2468.58-2470.46秒
2025-07-08 17:01:48 [INFO] 替换片段 428: 使用辅助视频 2470.46-2476.75秒
2025-07-08 17:01:48 [INFO] 替换片段 308: 使用辅助视频 2476.75-2480.58秒
2025-07-08 17:01:49 [INFO] 替换片段 404: 使用辅助视频 2480.58-2482.50秒
2025-07-08 17:01:49 [INFO] 替换片段 37: 使用辅助视频 2482.50-2483.83秒
2025-07-08 17:01:49 [INFO] 替换片段 308: 使用辅助视频 2483.83-2486.12秒
2025-07-08 17:01:50 [INFO] 替换片段 245: 使用辅助视频 2486.12-2488.08秒
2025-07-08 17:01:50 [INFO] 替换片段 142: 使用辅助视频 2488.08-2491.96秒
2025-07-08 17:01:51 [INFO] 替换片段 399: 使用辅助视频 2491.96-2498.21秒
2025-07-08 17:01:51 [INFO] 替换片段 227: 使用辅助视频 2498.21-2498.92秒
2025-07-08 17:01:51 [INFO] 处理替换批次 71/252，包含 10 个片段
2025-07-08 17:01:51 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:01:51 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:01:52 [INFO] 替换片段 254: 使用辅助视频 2498.92-2499.75秒
2025-07-08 17:01:52 [INFO] 替换片段 290: 使用辅助视频 2499.75-2500.46秒
2025-07-08 17:01:52 [INFO] 替换片段 449: 使用辅助视频 2500.46-2501.96秒
2025-07-08 17:01:52 [INFO] 替换片段 18: 使用辅助视频 2501.96-2502.92秒
2025-07-08 17:01:53 [INFO] 替换片段 142: 使用辅助视频 2502.92-2510.25秒
2025-07-08 17:01:53 [INFO] 替换片段 235: 使用辅助视频 2510.25-2512.50秒
2025-07-08 17:01:53 [INFO] 替换片段 142: 使用辅助视频 2512.50-2519.83秒
2025-07-08 17:01:54 [INFO] 替换片段 98: 使用辅助视频 2519.83-2522.46秒
2025-07-08 17:01:54 [INFO] 替换片段 128: 使用辅助视频 2522.46-2526.62秒
2025-07-08 17:01:55 [INFO] 替换片段 98: 使用辅助视频 2526.62-2530.21秒
2025-07-08 17:01:55 [INFO] 处理替换批次 72/252，包含 10 个片段
2025-07-08 17:01:55 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:01:55 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:01:56 [INFO] 替换片段 384: 使用辅助视频 2530.21-2535.12秒
2025-07-08 17:01:56 [INFO] 替换片段 53: 使用辅助视频 2535.12-2543.96秒
2025-07-08 17:01:56 [INFO] 替换片段 359: 使用辅助视频 2543.96-2545.58秒
2025-07-08 17:01:57 [INFO] 替换片段 246: 使用辅助视频 2545.58-2546.67秒
2025-07-08 17:01:57 [INFO] 替换片段 382: 使用辅助视频 2546.67-2547.79秒
2025-07-08 17:01:57 [INFO] 替换片段 7: 使用辅助视频 2547.79-2548.96秒
2025-07-08 17:01:57 [INFO] 替换片段 180: 使用辅助视频 2548.96-2550.29秒
2025-07-08 17:01:57 [INFO] 替换片段 152: 使用辅助视频 2550.29-2551.83秒
2025-07-08 17:01:58 [INFO] 替换片段 357: 使用辅助视频 2551.83-2553.08秒
2025-07-08 17:01:58 [INFO] 替换片段 411: 使用辅助视频 2553.08-2553.88秒
2025-07-08 17:01:58 [INFO] 处理替换批次 73/252，包含 10 个片段
2025-07-08 17:01:58 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:01:58 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:01:59 [INFO] 替换片段 219: 使用辅助视频 2553.88-2556.25秒
2025-07-08 17:01:59 [INFO] 替换片段 295: 使用辅助视频 2556.25-2557.92秒
2025-07-08 17:01:59 [INFO] 替换片段 170: 使用辅助视频 2557.92-2560.04秒
2025-07-08 17:02:00 [INFO] 替换片段 244: 使用辅助视频 2560.04-2560.96秒
2025-07-08 17:02:00 [INFO] 替换片段 363: 使用辅助视频 2560.96-2561.79秒
2025-07-08 17:02:00 [INFO] 替换片段 138: 使用辅助视频 2561.79-2563.62秒
2025-07-08 17:02:00 [INFO] 替换片段 399: 使用辅助视频 2563.62-2565.38秒
2025-07-08 17:02:00 [INFO] 替换片段 221: 使用辅助视频 2565.38-2567.29秒
2025-07-08 17:02:01 [INFO] 替换片段 292: 使用辅助视频 2567.29-2569.25秒
2025-07-08 17:02:01 [INFO] 替换片段 117: 使用辅助视频 2569.25-2570.96秒
2025-07-08 17:02:01 [INFO] 处理替换批次 74/252，包含 10 个片段
2025-07-08 17:02:01 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:02:02 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:02:02 [INFO] 替换片段 102: 使用辅助视频 2570.96-2572.75秒
2025-07-08 17:02:02 [INFO] 替换片段 357: 使用辅助视频 2572.75-2574.92秒
2025-07-08 17:02:02 [INFO] 替换片段 357: 使用辅助视频 2574.92-2576.12秒
2025-07-08 17:02:03 [INFO] 替换片段 254: 使用辅助视频 2576.12-2577.12秒
2025-07-08 17:02:03 [INFO] 替换片段 235: 使用辅助视频 2577.12-2581.88秒
2025-07-08 17:02:03 [INFO] 替换片段 206: 使用辅助视频 2581.88-2583.38秒
2025-07-08 17:02:03 [INFO] 替换片段 263: 使用辅助视频 2583.38-2584.92秒
2025-07-08 17:02:03 [INFO] 替换片段 339: 使用辅助视频 2584.92-2588.00秒
2025-07-08 17:02:04 [INFO] 替换片段 153: 使用辅助视频 2588.00-2590.25秒
2025-07-08 17:02:04 [INFO] 替换片段 282: 使用辅助视频 2590.25-2593.12秒
2025-07-08 17:02:04 [INFO] 处理替换批次 75/252，包含 10 个片段
2025-07-08 17:02:04 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:02:05 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:02:05 [INFO] 替换片段 199: 使用辅助视频 2593.12-2593.96秒
2025-07-08 17:02:05 [INFO] 替换片段 365: 使用辅助视频 2593.96-2595.58秒
2025-07-08 17:02:05 [INFO] 替换片段 199: 使用辅助视频 2595.58-2596.25秒
2025-07-08 17:02:05 [INFO] 替换片段 85: 使用辅助视频 2596.25-2602.21秒
2025-07-08 17:02:06 [INFO] 替换片段 22: 使用辅助视频 2602.21-2603.50秒
2025-07-08 17:02:06 [INFO] 替换片段 199: 使用辅助视频 2603.50-2604.25秒
2025-07-08 17:02:06 [INFO] 替换片段 472: 使用辅助视频 2604.25-2615.29秒
2025-07-08 17:02:06 [INFO] 替换片段 156: 使用辅助视频 2615.29-2621.92秒
2025-07-08 17:02:07 [INFO] 替换片段 211: 使用辅助视频 2621.92-2623.29秒
2025-07-08 17:02:07 [INFO] 替换片段 118: 使用辅助视频 2623.29-2626.17秒
2025-07-08 17:02:07 [INFO] 处理替换批次 76/252，包含 10 个片段
2025-07-08 17:02:07 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:02:07 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:02:08 [INFO] 替换片段 395: 使用辅助视频 2626.17-2629.54秒
2025-07-08 17:02:08 [INFO] 替换片段 270: 使用辅助视频 2629.54-2630.50秒
2025-07-08 17:02:08 [INFO] 替换片段 170: 使用辅助视频 2630.50-2634.67秒
2025-07-08 17:02:09 [INFO] 替换片段 226: 使用辅助视频 2634.67-2638.67秒
2025-07-08 17:02:10 [INFO] 替换片段 222: 使用辅助视频 2638.67-2641.96秒
2025-07-08 17:02:10 [INFO] 替换片段 354: 使用辅助视频 2641.96-2644.17秒
2025-07-08 17:02:10 [INFO] 替换片段 130: 使用辅助视频 2644.17-2646.50秒
2025-07-08 17:02:11 [INFO] 替换片段 161: 使用辅助视频 2646.50-2649.83秒
2025-07-08 17:02:11 [INFO] 替换片段 130: 使用辅助视频 2649.83-2653.50秒
2025-07-08 17:02:12 [INFO] 替换片段 287: 使用辅助视频 2653.50-2655.25秒
2025-07-08 17:02:12 [INFO] 处理替换批次 77/252，包含 10 个片段
2025-07-08 17:02:12 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:02:12 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:02:12 [INFO] 替换片段 296: 使用辅助视频 2655.25-2658.75秒
2025-07-08 17:02:13 [INFO] 替换片段 375: 使用辅助视频 2658.75-2660.96秒
2025-07-08 17:02:13 [INFO] 替换片段 184: 使用辅助视频 2660.96-2664.17秒
2025-07-08 17:02:14 [INFO] 替换片段 369: 使用辅助视频 2664.17-2665.50秒
2025-07-08 17:02:14 [INFO] 替换片段 166: 使用辅助视频 2665.50-2668.67秒
2025-07-08 17:02:14 [INFO] 替换片段 138: 使用辅助视频 2668.67-2670.33秒
2025-07-08 17:02:15 [INFO] 替换片段 428: 使用辅助视频 2670.33-2675.29秒
2025-07-08 17:02:15 [INFO] 替换片段 141: 使用辅助视频 2675.29-2677.25秒
2025-07-08 17:02:15 [INFO] 替换片段 60: 使用辅助视频 2677.25-2678.67秒
2025-07-08 17:02:16 [INFO] 替换片段 124: 使用辅助视频 2678.67-2681.71秒
2025-07-08 17:02:16 [INFO] 处理替换批次 78/252，包含 10 个片段
2025-07-08 17:02:16 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:02:16 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:02:16 [INFO] 替换片段 176: 使用辅助视频 2681.71-2683.88秒
2025-07-08 17:02:17 [INFO] 替换片段 407: 使用辅助视频 2683.88-2684.75秒
2025-07-08 17:02:17 [INFO] 替换片段 141: 使用辅助视频 2684.75-2688.92秒
2025-07-08 17:02:18 [INFO] 替换片段 102: 使用辅助视频 2688.92-2692.12秒
2025-07-08 17:02:18 [INFO] 替换片段 477: 使用辅助视频 2692.12-2693.62秒
2025-07-08 17:02:18 [INFO] 替换片段 293: 使用辅助视频 2693.62-2695.67秒
2025-07-08 17:02:19 [INFO] 替换片段 428: 使用辅助视频 2695.67-2700.88秒
2025-07-08 17:02:19 [INFO] 替换片段 292: 使用辅助视频 2700.88-2701.83秒
2025-07-08 17:02:19 [INFO] 替换片段 194: 使用辅助视频 2701.83-2703.79秒
2025-07-08 17:02:19 [INFO] 替换片段 160: 使用辅助视频 2703.79-2705.46秒
2025-07-08 17:02:19 [INFO] 处理替换批次 79/252，包含 10 个片段
2025-07-08 17:02:19 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:02:20 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:02:20 [INFO] 替换片段 229: 使用辅助视频 2705.46-2706.58秒
2025-07-08 17:02:20 [INFO] 替换片段 286: 使用辅助视频 2706.58-2707.25秒
2025-07-08 17:02:20 [INFO] 替换片段 143: 使用辅助视频 2707.25-2708.50秒
2025-07-08 17:02:21 [INFO] 替换片段 164: 使用辅助视频 2708.50-2709.88秒
2025-07-08 17:02:21 [INFO] 替换片段 165: 使用辅助视频 2709.88-2711.96秒
2025-07-08 17:02:21 [INFO] 替换片段 477: 使用辅助视频 2711.96-2713.62秒
2025-07-08 17:02:21 [INFO] 替换片段 195: 使用辅助视频 2713.62-2714.92秒
2025-07-08 17:02:22 [INFO] 替换片段 18: 使用辅助视频 2714.92-2716.29秒
2025-07-08 17:02:22 [INFO] 替换片段 293: 使用辅助视频 2716.29-2717.83秒
2025-07-08 17:02:22 [INFO] 替换片段 289: 使用辅助视频 2717.83-2721.96秒
2025-07-08 17:02:22 [INFO] 处理替换批次 80/252，包含 10 个片段
2025-07-08 17:02:22 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:02:23 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:02:23 [INFO] 替换片段 170: 使用辅助视频 2721.96-2726.17秒
2025-07-08 17:02:23 [INFO] 替换片段 34: 使用辅助视频 2726.17-2729.54秒
2025-07-08 17:02:24 [INFO] 替换片段 177: 使用辅助视频 2729.54-2734.62秒
2025-07-08 17:02:24 [INFO] 替换片段 52: 使用辅助视频 2734.62-2751.17秒
2025-07-08 17:02:24 [INFO] 替换片段 295: 使用辅助视频 2751.17-2758.75秒
2025-07-08 17:02:25 [INFO] 替换片段 147: 使用辅助视频 2758.75-2763.54秒
2025-07-08 17:02:25 [INFO] 替换片段 16: 使用辅助视频 2763.54-2765.50秒
2025-07-08 17:02:25 [INFO] 替换片段 359: 使用辅助视频 2765.50-2768.08秒
2025-07-08 17:02:26 [INFO] 替换片段 257: 使用辅助视频 2768.08-2773.33秒
2025-07-08 17:02:26 [INFO] 替换片段 147: 使用辅助视频 2773.33-2788.29秒
2025-07-08 17:02:26 [INFO] 处理替换批次 81/252，包含 10 个片段
2025-07-08 17:02:26 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:02:27 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:02:27 [INFO] 替换片段 98: 使用辅助视频 2788.29-2790.67秒
2025-07-08 17:02:27 [INFO] 替换片段 237: 使用辅助视频 2790.67-2803.21秒
2025-07-08 17:02:28 [INFO] 替换片段 198: 使用辅助视频 2803.21-2806.04秒
2025-07-08 17:02:28 [INFO] 替换片段 102: 使用辅助视频 2806.04-2809.83秒
2025-07-08 17:02:29 [INFO] 替换片段 175: 使用辅助视频 2809.83-2812.00秒
2025-07-08 17:02:29 [INFO] 替换片段 198: 使用辅助视频 2812.00-2815.04秒
2025-07-08 17:02:29 [INFO] 替换片段 177: 使用辅助视频 2815.04-2817.42秒
2025-07-08 17:02:30 [INFO] 替换片段 206: 使用辅助视频 2817.42-2819.62秒
2025-07-08 17:02:30 [INFO] 替换片段 476: 使用辅助视频 2819.62-2820.79秒
2025-07-08 17:02:30 [INFO] 替换片段 203: 使用辅助视频 2820.79-2835.12秒
2025-07-08 17:02:30 [INFO] 处理替换批次 82/252，包含 10 个片段
2025-07-08 17:02:30 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:02:31 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:02:31 [INFO] 替换片段 179: 使用辅助视频 2835.12-2853.42秒
2025-07-08 17:02:32 [INFO] 替换片段 175: 使用辅助视频 2853.42-2857.54秒
2025-07-08 17:02:32 [INFO] 替换片段 44: 使用辅助视频 2857.54-2879.04秒
2025-07-08 17:02:32 [INFO] 替换片段 475: 使用辅助视频 2879.04-2879.71秒
2025-07-08 17:02:33 [INFO] 替换片段 131: 使用辅助视频 2879.71-2882.71秒
2025-07-08 17:02:33 [INFO] 替换片段 8: 使用辅助视频 2882.71-2885.42秒
2025-07-08 17:02:33 [INFO] 替换片段 285: 使用辅助视频 2885.42-2888.79秒
2025-07-08 17:02:34 [INFO] 替换片段 257: 使用辅助视频 2888.79-2889.88秒
2025-07-08 17:02:34 [INFO] 替换片段 289: 使用辅助视频 2889.88-2890.92秒
2025-07-08 17:02:34 [INFO] 替换片段 215: 使用辅助视频 2890.92-2892.54秒
2025-07-08 17:02:34 [INFO] 处理替换批次 83/252，包含 10 个片段
2025-07-08 17:02:34 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:02:35 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:02:35 [INFO] 替换片段 226: 使用辅助视频 2892.54-2894.00秒
2025-07-08 17:02:36 [INFO] 替换片段 208: 使用辅助视频 2906.25-2907.46秒
2025-07-08 17:02:36 [INFO] 替换片段 59: 使用辅助视频 2907.46-2908.21秒
2025-07-08 17:02:36 [INFO] 替换片段 350: 使用辅助视频 2908.21-2910.54秒
2025-07-08 17:02:36 [INFO] 替换片段 258: 使用辅助视频 2910.54-2913.29秒
2025-07-08 17:02:37 [INFO] 替换片段 439: 使用辅助视频 2913.29-2914.21秒
2025-07-08 17:02:37 [INFO] 替换片段 236: 使用辅助视频 2914.21-2918.46秒
2025-07-08 17:02:37 [INFO] 替换片段 138: 使用辅助视频 2918.46-2920.12秒
2025-07-08 17:02:37 [INFO] 替换片段 473: 使用辅助视频 2920.12-2921.62秒
2025-07-08 17:02:38 [INFO] 替换片段 15: 使用辅助视频 2921.62-2922.88秒
2025-07-08 17:02:38 [INFO] 处理替换批次 84/252，包含 10 个片段
2025-07-08 17:02:38 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:02:38 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:02:39 [INFO] 替换片段 316: 使用辅助视频 2922.88-2924.08秒
2025-07-08 17:02:39 [INFO] 替换片段 223: 使用辅助视频 2924.08-2930.75秒
2025-07-08 17:02:39 [INFO] 替换片段 358: 使用辅助视频 2930.75-2932.54秒
2025-07-08 17:02:39 [INFO] 替换片段 98: 使用辅助视频 2932.54-2933.83秒
2025-07-08 17:02:40 [INFO] 替换片段 196: 使用辅助视频 2933.83-2934.83秒
2025-07-08 17:02:40 [INFO] 替换片段 40: 使用辅助视频 2934.83-2936.33秒
2025-07-08 17:02:40 [INFO] 替换片段 239: 使用辅助视频 2936.33-2941.33秒
2025-07-08 17:02:40 [INFO] 替换片段 149: 使用辅助视频 2941.33-2943.42秒
2025-07-08 17:02:41 [INFO] 替换片段 199: 使用辅助视频 2943.42-2945.58秒
2025-07-08 17:02:41 [INFO] 替换片段 110: 使用辅助视频 2945.58-2947.75秒
2025-07-08 17:02:41 [INFO] 处理替换批次 85/252，包含 10 个片段
2025-07-08 17:02:41 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:02:42 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:02:42 [INFO] 替换片段 120: 使用辅助视频 2947.75-2956.54秒
2025-07-08 17:02:42 [INFO] 替换片段 320: 使用辅助视频 2956.54-2959.17秒
2025-07-08 17:02:43 [INFO] 替换片段 151: 使用辅助视频 2959.17-2960.46秒
2025-07-08 17:02:43 [INFO] 替换片段 9: 使用辅助视频 2960.46-2962.42秒
2025-07-08 17:02:43 [INFO] 替换片段 151: 使用辅助视频 2962.42-2964.33秒
2025-07-08 17:02:44 [INFO] 替换片段 132: 使用辅助视频 2964.33-2965.12秒
2025-07-08 17:02:44 [INFO] 替换片段 186: 使用辅助视频 2965.12-2965.83秒
2025-07-08 17:02:44 [INFO] 替换片段 172: 使用辅助视频 2965.83-2966.79秒
2025-07-08 17:02:44 [INFO] 替换片段 17: 使用辅助视频 2966.79-2976.33秒
2025-07-08 17:02:44 [INFO] 替换片段 191: 使用辅助视频 2976.33-2977.50秒
2025-07-08 17:02:45 [INFO] 处理替换批次 86/252，包含 10 个片段
2025-07-08 17:02:45 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:02:45 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:02:45 [INFO] 替换片段 226: 使用辅助视频 2977.50-2984.46秒
2025-07-08 17:02:46 [INFO] 替换片段 410: 使用辅助视频 2984.46-2987.33秒
2025-07-08 17:02:46 [INFO] 替换片段 314: 使用辅助视频 2987.33-2988.88秒
2025-07-08 17:02:46 [INFO] 替换片段 348: 使用辅助视频 2988.88-2993.50秒
2025-07-08 17:02:47 [INFO] 替换片段 223: 使用辅助视频 2993.50-2998.38秒
2025-07-08 17:02:47 [INFO] 替换片段 211: 使用辅助视频 2998.38-3000.08秒
2025-07-08 17:02:47 [INFO] 替换片段 130: 使用辅助视频 3000.08-3001.29秒
2025-07-08 17:02:47 [INFO] 替换片段 211: 使用辅助视频 3001.29-3002.25秒
2025-07-08 17:02:48 [INFO] 替换片段 14: 使用辅助视频 3002.25-3003.83秒
2025-07-08 17:02:48 [INFO] 替换片段 223: 使用辅助视频 3003.83-3005.62秒
2025-07-08 17:02:48 [INFO] 处理替换批次 87/252，包含 10 个片段
2025-07-08 17:02:48 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:02:48 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:02:49 [INFO] 替换片段 99: 使用辅助视频 3005.62-3008.33秒
2025-07-08 17:02:49 [INFO] 替换片段 282: 使用辅助视频 3008.33-3010.29秒
2025-07-08 17:02:49 [INFO] 替换片段 155: 使用辅助视频 3010.29-3011.83秒
2025-07-08 17:02:49 [INFO] 替换片段 49: 使用辅助视频 3011.83-3014.38秒
2025-07-08 17:02:50 [INFO] 替换片段 338: 使用辅助视频 3014.38-3017.12秒
2025-07-08 17:02:50 [INFO] 替换片段 400: 使用辅助视频 3017.12-3019.75秒
2025-07-08 17:02:51 [INFO] 替换片段 164: 使用辅助视频 3019.75-3023.71秒
2025-07-08 17:02:51 [INFO] 替换片段 439: 使用辅助视频 3023.71-3026.12秒
2025-07-08 17:02:51 [INFO] 替换片段 362: 使用辅助视频 3026.12-3027.29秒
2025-07-08 17:02:52 [INFO] 替换片段 243: 使用辅助视频 3027.29-3033.21秒
2025-07-08 17:02:52 [INFO] 处理替换批次 88/252，包含 10 个片段
2025-07-08 17:02:52 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:02:52 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:02:53 [INFO] 替换片段 155: 使用辅助视频 3033.21-3035.88秒
2025-07-08 17:02:53 [INFO] 替换片段 173: 使用辅助视频 3035.88-3037.71秒
2025-07-08 17:02:53 [INFO] 替换片段 395: 使用辅助视频 3037.71-3038.50秒
2025-07-08 17:02:53 [INFO] 替换片段 382: 使用辅助视频 3038.50-3041.21秒
2025-07-08 17:02:54 [INFO] 替换片段 221: 使用辅助视频 3041.21-3043.04秒
2025-07-08 17:02:54 [INFO] 替换片段 456: 使用辅助视频 3043.04-3046.38秒
2025-07-08 17:02:55 [INFO] 替换片段 286: 使用辅助视频 3046.38-3047.33秒
2025-07-08 17:02:55 [INFO] 替换片段 41: 使用辅助视频 3047.33-3051.12秒
2025-07-08 17:02:55 [INFO] 替换片段 16: 使用辅助视频 3051.12-3052.29秒
2025-07-08 17:02:55 [INFO] 替换片段 131: 使用辅助视频 3052.29-3055.50秒
2025-07-08 17:02:56 [INFO] 处理替换批次 89/252，包含 10 个片段
2025-07-08 17:02:56 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:02:56 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:02:56 [INFO] 替换片段 117: 使用辅助视频 3055.50-3067.79秒
2025-07-08 17:02:56 [INFO] 替换片段 420: 使用辅助视频 3067.79-3068.71秒
2025-07-08 17:02:57 [INFO] 替换片段 108: 使用辅助视频 3068.71-3070.21秒
2025-07-08 17:02:57 [INFO] 替换片段 412: 使用辅助视频 3070.21-3075.42秒
2025-07-08 17:02:57 [INFO] 替换片段 161: 使用辅助视频 3075.42-3077.08秒
2025-07-08 17:02:57 [INFO] 替换片段 443: 使用辅助视频 3077.08-3078.29秒
2025-07-08 17:02:58 [INFO] 替换片段 386: 使用辅助视频 3078.29-3079.33秒
2025-07-08 17:02:58 [INFO] 替换片段 357: 使用辅助视频 3079.33-3080.79秒
2025-07-08 17:02:58 [INFO] 替换片段 257: 使用辅助视频 3080.79-3082.71秒
2025-07-08 17:02:58 [INFO] 替换片段 377: 使用辅助视频 3082.71-3084.08秒
2025-07-08 17:02:58 [INFO] 处理替换批次 90/252，包含 10 个片段
2025-07-08 17:02:58 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:02:59 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:02:59 [INFO] 替换片段 147: 使用辅助视频 3084.08-3085.54秒
2025-07-08 17:02:59 [INFO] 替换片段 417: 使用辅助视频 3085.54-3087.42秒
2025-07-08 17:03:00 [INFO] 替换片段 291: 使用辅助视频 3087.42-3088.42秒
2025-07-08 17:03:00 [INFO] 替换片段 19: 使用辅助视频 3088.42-3094.42秒
2025-07-08 17:03:00 [INFO] 替换片段 193: 使用辅助视频 3094.42-3098.88秒
2025-07-08 17:03:00 [INFO] 替换片段 11: 使用辅助视频 3098.88-3102.00秒
2025-07-08 17:03:01 [INFO] 替换片段 472: 使用辅助视频 3127.04-3129.54秒
2025-07-08 17:03:01 [INFO] 替换片段 230: 使用辅助视频 3129.54-3131.62秒
2025-07-08 17:03:01 [INFO] 替换片段 143: 使用辅助视频 3131.62-3137.29秒
2025-07-08 17:03:02 [INFO] 替换片段 431: 使用辅助视频 3137.29-3138.67秒
2025-07-08 17:03:02 [INFO] 处理替换批次 91/252，包含 10 个片段
2025-07-08 17:03:02 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:03:02 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:03:03 [INFO] 替换片段 117: 使用辅助视频 3138.67-3141.21秒
2025-07-08 17:03:03 [INFO] 替换片段 196: 使用辅助视频 3141.21-3143.50秒
2025-07-08 17:03:03 [INFO] 替换片段 440: 使用辅助视频 3143.50-3146.29秒
2025-07-08 17:03:04 [INFO] 替换片段 197: 使用辅助视频 3146.29-3149.50秒
2025-07-08 17:03:04 [INFO] 替换片段 382: 使用辅助视频 3149.50-3150.71秒
2025-07-08 17:03:04 [INFO] 替换片段 199: 使用辅助视频 3150.71-3151.62秒
2025-07-08 17:03:04 [INFO] 替换片段 382: 使用辅助视频 3151.62-3152.50秒
2025-07-08 17:03:04 [INFO] 替换片段 199: 使用辅助视频 3152.50-3158.08秒
2025-07-08 17:03:05 [INFO] 替换片段 293: 使用辅助视频 3158.08-3159.79秒
2025-07-08 17:03:05 [INFO] 替换片段 286: 使用辅助视频 3159.79-3161.04秒
2025-07-08 17:03:05 [INFO] 处理替换批次 92/252，包含 10 个片段
2025-07-08 17:03:05 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:03:06 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:03:06 [INFO] 替换片段 254: 使用辅助视频 3161.04-3163.08秒
2025-07-08 17:03:06 [INFO] 替换片段 177: 使用辅助视频 3163.08-3168.25秒
2025-07-08 17:03:06 [INFO] 替换片段 102: 使用辅助视频 3168.25-3172.38秒
2025-07-08 17:03:07 [INFO] 替换片段 244: 使用辅助视频 3172.38-3173.12秒
2025-07-08 17:03:07 [INFO] 替换片段 201: 使用辅助视频 3173.12-3174.67秒
2025-07-08 17:03:07 [INFO] 替换片段 470: 使用辅助视频 3174.67-3176.62秒
2025-07-08 17:03:08 [INFO] 替换片段 19: 使用辅助视频 3176.62-3180.21秒
2025-07-08 17:03:08 [INFO] 替换片段 471: 使用辅助视频 3180.21-3187.00秒
2025-07-08 17:03:08 [INFO] 替换片段 106: 使用辅助视频 3187.00-3189.67秒
2025-07-08 17:03:09 [INFO] 替换片段 344: 使用辅助视频 3189.67-3192.79秒
2025-07-08 17:03:09 [INFO] 处理替换批次 93/252，包含 10 个片段
2025-07-08 17:03:09 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:03:09 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:03:10 [INFO] 替换片段 191: 使用辅助视频 3192.79-3196.33秒
2025-07-08 17:03:10 [INFO] 替换片段 20: 使用辅助视频 3196.33-3198.42秒
2025-07-08 17:03:11 [INFO] 替换片段 337: 使用辅助视频 3198.42-3202.71秒
2025-07-08 17:03:11 [INFO] 替换片段 473: 使用辅助视频 3202.71-3205.67秒
2025-07-08 17:03:11 [INFO] 替换片段 382: 使用辅助视频 3205.67-3207.54秒
2025-07-08 17:03:12 [INFO] 替换片段 247: 使用辅助视频 3207.54-3208.71秒
2025-07-08 17:03:12 [INFO] 替换片段 17: 使用辅助视频 3208.71-3212.96秒
2025-07-08 17:03:12 [INFO] 替换片段 153: 使用辅助视频 3212.96-3216.50秒
2025-07-08 17:03:13 [INFO] 替换片段 33: 使用辅助视频 3216.50-3218.33秒
2025-07-08 17:03:13 [INFO] 替换片段 130: 使用辅助视频 3218.33-3221.00秒
2025-07-08 17:03:13 [INFO] 处理替换批次 94/252，包含 10 个片段
2025-07-08 17:03:13 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:03:14 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:03:14 [INFO] 替换片段 391: 使用辅助视频 3221.00-3222.50秒
2025-07-08 17:03:14 [INFO] 替换片段 387: 使用辅助视频 3222.50-3237.58秒
2025-07-08 17:03:14 [INFO] 替换片段 421: 使用辅助视频 3245.38-3246.83秒
2025-07-08 17:03:15 [INFO] 替换片段 23: 使用辅助视频 3246.83-3252.00秒
2025-07-08 17:03:15 [INFO] 替换片段 358: 使用辅助视频 3252.00-3254.50秒
2025-07-08 17:03:15 [INFO] 替换片段 463: 使用辅助视频 3254.50-3265.50秒
2025-07-08 17:03:16 [INFO] 替换片段 293: 使用辅助视频 3265.50-3271.33秒
2025-07-08 17:03:16 [INFO] 替换片段 98: 使用辅助视频 3271.33-3274.71秒
2025-07-08 17:03:17 [INFO] 替换片段 5: 使用辅助视频 3274.71-3279.12秒
2025-07-08 17:03:17 [INFO] 替换片段 452: 使用辅助视频 3279.12-3282.71秒
2025-07-08 17:03:17 [INFO] 处理替换批次 95/252，包含 10 个片段
2025-07-08 17:03:17 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:03:17 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:03:18 [INFO] 替换片段 130: 使用辅助视频 3282.71-3287.46秒
2025-07-08 17:03:18 [INFO] 替换片段 362: 使用辅助视频 3287.46-3288.75秒
2025-07-08 17:03:18 [INFO] 替换片段 121: 使用辅助视频 3288.75-3289.58秒
2025-07-08 17:03:18 [INFO] 替换片段 35: 使用辅助视频 3289.58-3294.33秒
2025-07-08 17:03:19 [INFO] 替换片段 293: 使用辅助视频 3294.33-3296.92秒
2025-07-08 17:03:19 [INFO] 替换片段 121: 使用辅助视频 3296.92-3306.00秒
2025-07-08 17:03:19 [INFO] 替换片段 145: 使用辅助视频 3306.00-3307.54秒
2025-07-08 17:03:20 [INFO] 替换片段 128: 使用辅助视频 3307.54-3309.46秒
2025-07-08 17:03:20 [INFO] 替换片段 156: 使用辅助视频 3309.46-3311.17秒
2025-07-08 17:03:20 [INFO] 替换片段 117: 使用辅助视频 3311.17-3314.08秒
2025-07-08 17:03:20 [INFO] 处理替换批次 96/252，包含 10 个片段
2025-07-08 17:03:20 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:03:21 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:03:21 [INFO] 替换片段 222: 使用辅助视频 3314.08-3315.71秒
2025-07-08 17:03:21 [INFO] 替换片段 484: 使用辅助视频 3315.71-3316.75秒
2025-07-08 17:03:21 [INFO] 替换片段 213: 使用辅助视频 3316.75-3321.58秒
2025-07-08 17:03:22 [INFO] 替换片段 474: 使用辅助视频 3321.58-3323.42秒
2025-07-08 17:03:22 [INFO] 替换片段 247: 使用辅助视频 3323.42-3324.79秒
2025-07-08 17:03:22 [INFO] 替换片段 104: 使用辅助视频 3324.79-3326.75秒
2025-07-08 17:03:22 [INFO] 替换片段 389: 使用辅助视频 3326.75-3327.71秒
2025-07-08 17:03:22 [INFO] 替换片段 98: 使用辅助视频 3327.71-3330.04秒
2025-07-08 17:03:23 [INFO] 替换片段 1: 使用辅助视频 3330.04-3332.67秒
2025-07-08 17:03:23 [INFO] 替换片段 407: 使用辅助视频 3332.67-3345.25秒
2025-07-08 17:03:23 [INFO] 处理替换批次 97/252，包含 10 个片段
2025-07-08 17:03:23 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:03:24 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:03:24 [INFO] 替换片段 348: 使用辅助视频 3345.25-3348.17秒
2025-07-08 17:03:24 [INFO] 替换片段 484: 使用辅助视频 3348.17-3349.71秒
2025-07-08 17:03:25 [INFO] 替换片段 384: 使用辅助视频 3349.71-3351.71秒
2025-07-08 17:03:25 [INFO] 替换片段 438: 使用辅助视频 3351.71-3353.33秒
2025-07-08 17:03:25 [INFO] 替换片段 215: 使用辅助视频 3353.33-3356.88秒
2025-07-08 17:03:26 [INFO] 替换片段 250: 使用辅助视频 3356.88-3360.92秒
2025-07-08 17:03:26 [INFO] 替换片段 14: 使用辅助视频 3360.92-3367.79秒
2025-07-08 17:03:27 [INFO] 替换片段 50: 使用辅助视频 3367.79-3373.42秒
2025-07-08 17:03:27 [INFO] 替换片段 255: 使用辅助视频 3373.42-3375.67秒
2025-07-08 17:03:27 [INFO] 替换片段 139: 使用辅助视频 3375.67-3380.25秒
2025-07-08 17:03:27 [INFO] 处理替换批次 98/252，包含 10 个片段
2025-07-08 17:03:27 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:03:28 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:03:28 [INFO] 替换片段 150: 使用辅助视频 3380.25-3381.62秒
2025-07-08 17:03:28 [INFO] 替换片段 125: 使用辅助视频 3381.62-3385.17秒
2025-07-08 17:03:29 [INFO] 替换片段 30: 使用辅助视频 3385.17-3387.71秒
2025-07-08 17:03:29 [INFO] 替换片段 475: 使用辅助视频 3387.71-3390.83秒
2025-07-08 17:03:30 [INFO] 替换片段 141: 使用辅助视频 3390.83-3395.42秒
2025-07-08 17:03:30 [INFO] 替换片段 320: 使用辅助视频 3395.42-3397.08秒
2025-07-08 17:03:30 [INFO] 替换片段 141: 使用辅助视频 3397.08-3398.42秒
2025-07-08 17:03:31 [INFO] 替换片段 204: 使用辅助视频 3398.42-3402.75秒
2025-07-08 17:03:31 [INFO] 替换片段 477: 使用辅助视频 3402.75-3404.96秒
2025-07-08 17:03:31 [INFO] 替换片段 300: 使用辅助视频 3404.96-3407.46秒
2025-07-08 17:03:31 [INFO] 处理替换批次 99/252，包含 10 个片段
2025-07-08 17:03:31 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:03:32 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:03:32 [INFO] 替换片段 145: 使用辅助视频 3407.46-3411.54秒
2025-07-08 17:03:33 [INFO] 替换片段 384: 使用辅助视频 3411.54-3413.79秒
2025-07-08 17:03:33 [INFO] 替换片段 174: 使用辅助视频 3413.79-3417.58秒
2025-07-08 17:03:33 [INFO] 替换片段 423: 使用辅助视频 3417.58-3422.79秒
2025-07-08 17:03:34 [INFO] 替换片段 290: 使用辅助视频 3422.79-3425.92秒
2025-07-08 17:03:34 [INFO] 替换片段 394: 使用辅助视频 3425.92-3427.42秒
2025-07-08 17:03:34 [INFO] 替换片段 203: 使用辅助视频 3427.42-3428.50秒
2025-07-08 17:03:34 [INFO] 替换片段 130: 使用辅助视频 3428.50-3429.75秒
2025-07-08 17:03:35 [INFO] 替换片段 226: 使用辅助视频 3429.75-3430.92秒
2025-07-08 17:03:35 [INFO] 替换片段 388: 使用辅助视频 3430.92-3431.75秒
2025-07-08 17:03:35 [INFO] 处理替换批次 100/252，包含 10 个片段
2025-07-08 17:03:35 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:03:35 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:03:36 [INFO] 替换片段 444: 使用辅助视频 3431.75-3432.50秒
2025-07-08 17:03:36 [INFO] 替换片段 188: 使用辅助视频 3432.50-3433.46秒
2025-07-08 17:03:36 [INFO] 替换片段 153: 使用辅助视频 3433.46-3436.29秒
2025-07-08 17:03:36 [INFO] 替换片段 98: 使用辅助视频 3436.29-3439.42秒
2025-07-08 17:03:37 [INFO] 替换片段 120: 使用辅助视频 3439.42-3440.50秒
2025-07-08 17:03:37 [INFO] 替换片段 219: 使用辅助视频 3440.50-3441.62秒
2025-07-08 17:03:37 [INFO] 替换片段 337: 使用辅助视频 3441.62-3442.33秒
2025-07-08 17:03:37 [INFO] 替换片段 290: 使用辅助视频 3442.33-3444.75秒
2025-07-08 17:03:38 [INFO] 替换片段 66: 使用辅助视频 3444.75-3449.88秒
2025-07-08 17:03:38 [INFO] 替换片段 121: 使用辅助视频 3449.88-3452.00秒
2025-07-08 17:03:38 [INFO] 处理替换批次 101/252，包含 10 个片段
2025-07-08 17:03:38 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:03:39 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:03:39 [INFO] 替换片段 206: 使用辅助视频 3452.00-3453.46秒
2025-07-08 17:03:39 [INFO] 替换片段 398: 使用辅助视频 3453.46-3463.58秒
2025-07-08 17:03:40 [INFO] 替换片段 204: 使用辅助视频 3463.58-3465.83秒
2025-07-08 17:03:40 [INFO] 替换片段 102: 使用辅助视频 3465.83-3466.62秒
2025-07-08 17:03:40 [INFO] 替换片段 310: 使用辅助视频 3466.62-3467.33秒
2025-07-08 17:03:40 [INFO] 替换片段 191: 使用辅助视频 3467.33-3468.00秒
2025-07-08 17:03:40 [INFO] 替换片段 282: 使用辅助视频 3468.00-3469.33秒
2025-07-08 17:03:40 [INFO] 替换片段 226: 使用辅助视频 3469.33-3471.08秒
2025-07-08 17:03:41 [INFO] 替换片段 470: 使用辅助视频 3471.08-3471.75秒
2025-07-08 17:03:41 [INFO] 替换片段 290: 使用辅助视频 3471.75-3472.75秒
2025-07-08 17:03:41 [INFO] 处理替换批次 102/252，包含 10 个片段
2025-07-08 17:03:41 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:03:41 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:03:42 [INFO] 替换片段 102: 使用辅助视频 3472.75-3473.96秒
2025-07-08 17:03:42 [INFO] 替换片段 102: 使用辅助视频 3473.96-3474.58秒
2025-07-08 17:03:42 [INFO] 替换片段 1: 使用辅助视频 3474.58-3475.38秒
2025-07-08 17:03:42 [INFO] 替换片段 102: 使用辅助视频 3475.38-3476.54秒
2025-07-08 17:03:42 [INFO] 替换片段 102: 使用辅助视频 3476.54-3479.42秒
2025-07-08 17:03:43 [INFO] 替换片段 226: 使用辅助视频 3479.42-3489.38秒
2025-07-08 17:03:43 [INFO] 替换片段 408: 使用辅助视频 3489.38-3490.58秒
2025-07-08 17:03:43 [INFO] 替换片段 11: 使用辅助视频 3490.58-3492.38秒
2025-07-08 17:03:44 [INFO] 替换片段 102: 使用辅助视频 3492.38-3495.42秒
2025-07-08 17:03:44 [INFO] 替换片段 217: 使用辅助视频 3495.42-3497.25秒
2025-07-08 17:03:44 [INFO] 处理替换批次 103/252，包含 10 个片段
2025-07-08 17:03:44 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:03:45 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:03:45 [INFO] 替换片段 484: 使用辅助视频 3497.25-3501.33秒
2025-07-08 17:03:45 [INFO] 替换片段 102: 使用辅助视频 3501.33-3502.62秒
2025-07-08 17:03:46 [INFO] 替换片段 102: 使用辅助视频 3502.62-3503.29秒
2025-07-08 17:03:46 [INFO] 替换片段 240: 使用辅助视频 3503.29-3504.12秒
2025-07-08 17:03:46 [INFO] 替换片段 131: 使用辅助视频 3504.12-3507.33秒
2025-07-08 17:03:46 [INFO] 替换片段 254: 使用辅助视频 3507.33-3508.25秒
2025-07-08 17:03:46 [INFO] 替换片段 2: 使用辅助视频 3508.25-3510.46秒
2025-07-08 17:03:47 [INFO] 替换片段 459: 使用辅助视频 3510.46-3521.88秒
2025-07-08 17:03:47 [INFO] 替换片段 2: 使用辅助视频 3521.88-3524.33秒
2025-07-08 17:03:47 [INFO] 替换片段 391: 使用辅助视频 3524.33-3530.29秒
2025-07-08 17:03:47 [INFO] 处理替换批次 104/252，包含 10 个片段
2025-07-08 17:03:47 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:03:48 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:03:48 [INFO] 替换片段 459: 使用辅助视频 3530.29-3536.21秒
2025-07-08 17:03:48 [INFO] 替换片段 50: 使用辅助视频 3536.21-3538.46秒
2025-07-08 17:03:49 [INFO] 替换片段 132: 使用辅助视频 3538.46-3540.58秒
2025-07-08 17:03:49 [INFO] 替换片段 472: 使用辅助视频 3540.58-3541.50秒
2025-07-08 17:03:49 [INFO] 替换片段 475: 使用辅助视频 3541.50-3543.58秒
2025-07-08 17:03:50 [INFO] 替换片段 348: 使用辅助视频 3543.58-3544.75秒
2025-07-08 17:03:50 [INFO] 替换片段 325: 使用辅助视频 3544.75-3548.62秒
2025-07-08 17:03:50 [INFO] 替换片段 485: 使用辅助视频 3548.62-3550.46秒
2025-07-08 17:03:51 [INFO] 替换片段 397: 使用辅助视频 3550.46-3551.96秒
2025-07-08 17:03:51 [INFO] 替换片段 223: 使用辅助视频 3551.96-3554.17秒
2025-07-08 17:03:51 [INFO] 处理替换批次 105/252，包含 10 个片段
2025-07-08 17:03:51 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:03:51 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:03:52 [INFO] 替换片段 224: 使用辅助视频 3554.17-3556.50秒
2025-07-08 17:03:52 [INFO] 替换片段 290: 使用辅助视频 3556.50-3557.71秒
2025-07-08 17:03:52 [INFO] 替换片段 225: 使用辅助视频 3557.71-3562.12秒
2025-07-08 17:03:52 [INFO] 替换片段 451: 使用辅助视频 3562.12-3571.00秒
2025-07-08 17:03:53 [INFO] 替换片段 286: 使用辅助视频 3571.00-3574.96秒
2025-07-08 17:03:53 [INFO] 替换片段 242: 使用辅助视频 3574.96-3576.62秒
2025-07-08 17:03:54 [INFO] 替换片段 2: 使用辅助视频 3576.62-3581.12秒
2025-07-08 17:03:54 [INFO] 替换片段 162: 使用辅助视频 3581.12-3582.50秒
2025-07-08 17:03:54 [INFO] 替换片段 247: 使用辅助视频 3582.50-3584.08秒
2025-07-08 17:03:54 [INFO] 替换片段 18: 使用辅助视频 3584.08-3585.54秒
2025-07-08 17:03:54 [INFO] 处理替换批次 106/252，包含 10 个片段
2025-07-08 17:03:54 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:03:55 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:03:55 [INFO] 替换片段 173: 使用辅助视频 3585.54-3588.46秒
2025-07-08 17:03:55 [INFO] 替换片段 219: 使用辅助视频 3588.46-3590.42秒
2025-07-08 17:03:56 [INFO] 替换片段 472: 使用辅助视频 3590.42-3591.79秒
2025-07-08 17:03:56 [INFO] 替换片段 123: 使用辅助视频 3591.79-3592.50秒
2025-07-08 17:03:56 [INFO] 替换片段 473: 使用辅助视频 3592.50-3593.29秒
2025-07-08 17:03:56 [INFO] 替换片段 227: 使用辅助视频 3593.29-3596.46秒
2025-07-08 17:03:57 [INFO] 替换片段 108: 使用辅助视频 3596.46-3598.83秒
2025-07-08 17:03:57 [INFO] 替换片段 18: 使用辅助视频 3598.83-3600.12秒
2025-07-08 17:03:57 [INFO] 替换片段 176: 使用辅助视频 3600.12-3603.25秒
2025-07-08 17:03:57 [INFO] 替换片段 2: 使用辅助视频 3603.25-3604.62秒
2025-07-08 17:03:57 [INFO] 处理替换批次 107/252，包含 10 个片段
2025-07-08 17:03:57 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:03:58 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:03:58 [INFO] 替换片段 191: 使用辅助视频 3604.62-3608.21秒
2025-07-08 17:03:59 [INFO] 替换片段 18: 使用辅助视频 3608.21-3609.12秒
2025-07-08 17:03:59 [INFO] 替换片段 108: 使用辅助视频 3609.12-3614.17秒
2025-07-08 17:03:59 [INFO] 替换片段 18: 使用辅助视频 3614.17-3615.33秒
2025-07-08 17:03:59 [INFO] 替换片段 108: 使用辅助视频 3615.33-3618.25秒
2025-07-08 17:04:00 [INFO] 替换片段 301: 使用辅助视频 3618.25-3618.96秒
2025-07-08 17:04:00 [INFO] 替换片段 223: 使用辅助视频 3618.96-3621.12秒
2025-07-08 17:04:00 [INFO] 替换片段 290: 使用辅助视频 3621.12-3622.88秒
2025-07-08 17:04:01 [INFO] 替换片段 50: 使用辅助视频 3622.88-3624.83秒
2025-07-08 17:04:01 [INFO] 替换片段 176: 使用辅助视频 3624.83-3626.62秒
2025-07-08 17:04:01 [INFO] 处理替换批次 108/252，包含 10 个片段
2025-07-08 17:04:01 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:04:01 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:04:02 [INFO] 替换片段 108: 使用辅助视频 3626.62-3628.96秒
2025-07-08 17:04:02 [INFO] 替换片段 402: 使用辅助视频 3628.96-3629.88秒
2025-07-08 17:04:02 [INFO] 替换片段 407: 使用辅助视频 3629.88-3630.92秒
2025-07-08 17:04:02 [INFO] 替换片段 345: 使用辅助视频 3630.92-3633.42秒
2025-07-08 17:04:03 [INFO] 替换片段 349: 使用辅助视频 3633.42-3634.46秒
2025-07-08 17:04:03 [INFO] 替换片段 290: 使用辅助视频 3634.46-3638.92秒
2025-07-08 17:04:03 [INFO] 替换片段 117: 使用辅助视频 3638.92-3640.00秒
2025-07-08 17:04:03 [INFO] 替换片段 176: 使用辅助视频 3640.00-3640.62秒
2025-07-08 17:04:03 [INFO] 替换片段 222: 使用辅助视频 3640.62-3642.00秒
2025-07-08 17:04:04 [INFO] 替换片段 18: 使用辅助视频 3642.00-3643.67秒
2025-07-08 17:04:04 [INFO] 处理替换批次 109/252，包含 10 个片段
2025-07-08 17:04:04 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:04:04 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:04:04 [INFO] 替换片段 373: 使用辅助视频 3643.67-3645.12秒
2025-07-08 17:04:05 [INFO] 替换片段 50: 使用辅助视频 3645.12-3646.62秒
2025-07-08 17:04:05 [INFO] 替换片段 18: 使用辅助视频 3646.62-3650.12秒
2025-07-08 17:04:05 [INFO] 替换片段 190: 使用辅助视频 3650.12-3652.46秒
2025-07-08 17:04:06 [INFO] 替换片段 363: 使用辅助视频 3652.46-3653.33秒
2025-07-08 17:04:06 [INFO] 替换片段 111: 使用辅助视频 3653.33-3656.33秒
2025-07-08 17:04:06 [INFO] 替换片段 459: 使用辅助视频 3656.33-3662.00秒
2025-07-08 17:04:06 [INFO] 替换片段 102: 使用辅助视频 3662.00-3662.67秒
2025-07-08 17:04:07 [INFO] 替换片段 459: 使用辅助视频 3662.67-3663.83秒
2025-07-08 17:04:07 [INFO] 替换片段 115: 使用辅助视频 3663.83-3665.04秒
2025-07-08 17:04:07 [INFO] 处理替换批次 110/252，包含 10 个片段
2025-07-08 17:04:07 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:04:07 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:04:07 [INFO] 替换片段 142: 使用辅助视频 3665.04-3669.58秒
2025-07-08 17:04:08 [INFO] 替换片段 357: 使用辅助视频 3669.58-3672.38秒
2025-07-08 17:04:08 [INFO] 替换片段 223: 使用辅助视频 3672.38-3677.58秒
2025-07-08 17:04:09 [INFO] 替换片段 338: 使用辅助视频 3677.58-3680.67秒
2025-07-08 17:04:09 [INFO] 替换片段 193: 使用辅助视频 3680.67-3682.46秒
2025-07-08 17:04:09 [INFO] 替换片段 410: 使用辅助视频 3682.46-3686.75秒
2025-07-08 17:04:10 [INFO] 替换片段 298: 使用辅助视频 3686.75-3691.21秒
2025-07-08 17:04:10 [INFO] 替换片段 254: 使用辅助视频 3691.21-3692.33秒
2025-07-08 17:04:10 [INFO] 替换片段 371: 使用辅助视频 3692.33-3693.75秒
2025-07-08 17:04:10 [INFO] 替换片段 52: 使用辅助视频 3693.75-3695.17秒
2025-07-08 17:04:11 [INFO] 处理替换批次 111/252，包含 10 个片段
2025-07-08 17:04:11 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:04:11 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:04:11 [INFO] 替换片段 50: 使用辅助视频 3695.17-3696.50秒
2025-07-08 17:04:12 [INFO] 替换片段 294: 使用辅助视频 3696.50-3697.79秒
2025-07-08 17:04:12 [INFO] 替换片段 374: 使用辅助视频 3697.79-3698.83秒
2025-07-08 17:04:12 [INFO] 替换片段 179: 使用辅助视频 3698.83-3700.54秒
2025-07-08 17:04:12 [INFO] 替换片段 251: 使用辅助视频 3700.54-3702.71秒
2025-07-08 17:04:12 [INFO] 替换片段 118: 使用辅助视频 3702.71-3703.92秒
2025-07-08 17:04:13 [INFO] 替换片段 294: 使用辅助视频 3703.92-3705.25秒
2025-07-08 17:04:13 [INFO] 替换片段 199: 使用辅助视频 3705.25-3706.67秒
2025-07-08 17:04:13 [INFO] 替换片段 123: 使用辅助视频 3706.67-3708.17秒
2025-07-08 17:04:13 [INFO] 替换片段 246: 使用辅助视频 3708.17-3708.83秒
2025-07-08 17:04:13 [INFO] 处理替换批次 112/252，包含 10 个片段
2025-07-08 17:04:13 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:04:14 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:04:14 [INFO] 替换片段 123: 使用辅助视频 3708.83-3709.92秒
2025-07-08 17:04:14 [INFO] 替换片段 57: 使用辅助视频 3709.92-3710.58秒
2025-07-08 17:04:14 [INFO] 替换片段 190: 使用辅助视频 3710.58-3712.67秒
2025-07-08 17:04:15 [INFO] 替换片段 270: 使用辅助视频 3712.67-3714.00秒
2025-07-08 17:04:15 [INFO] 替换片段 170: 使用辅助视频 3714.00-3714.79秒
2025-07-08 17:04:15 [INFO] 替换片段 439: 使用辅助视频 3714.79-3718.08秒
2025-07-08 17:04:15 [INFO] 替换片段 170: 使用辅助视频 3718.08-3719.25秒
2025-07-08 17:04:16 [INFO] 替换片段 418: 使用辅助视频 3719.25-3720.54秒
2025-07-08 17:04:16 [INFO] 替换片段 227: 使用辅助视频 3720.54-3721.67秒
2025-07-08 17:04:16 [INFO] 替换片段 198: 使用辅助视频 3721.67-3725.54秒
2025-07-08 17:04:16 [INFO] 处理替换批次 113/252，包含 10 个片段
2025-07-08 17:04:16 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:04:17 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:04:17 [INFO] 替换片段 227: 使用辅助视频 3725.54-3726.75秒
2025-07-08 17:04:17 [INFO] 替换片段 254: 使用辅助视频 3726.75-3727.88秒
2025-07-08 17:04:17 [INFO] 替换片段 216: 使用辅助视频 3727.88-3728.79秒
2025-07-08 17:04:17 [INFO] 替换片段 229: 使用辅助视频 3728.79-3730.88秒
2025-07-08 17:04:18 [INFO] 替换片段 216: 使用辅助视频 3730.88-3731.83秒
2025-07-08 17:04:18 [INFO] 替换片段 221: 使用辅助视频 3731.83-3733.79秒
2025-07-08 17:04:18 [INFO] 替换片段 254: 使用辅助视频 3733.79-3739.21秒
2025-07-08 17:04:18 [INFO] 替换片段 255: 使用辅助视频 3739.21-3741.54秒
2025-07-08 17:04:19 [INFO] 替换片段 397: 使用辅助视频 3741.54-3744.38秒
2025-07-08 17:04:19 [INFO] 替换片段 108: 使用辅助视频 3750.79-3752.58秒
2025-07-08 17:04:19 [INFO] 处理替换批次 114/252，包含 10 个片段
2025-07-08 17:04:19 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:04:19 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:04:20 [INFO] 替换片段 476: 使用辅助视频 3752.58-3754.38秒
2025-07-08 17:04:20 [INFO] 替换片段 391: 使用辅助视频 3754.38-3756.04秒
2025-07-08 17:04:20 [INFO] 替换片段 193: 使用辅助视频 3756.04-3757.29秒
2025-07-08 17:04:20 [INFO] 替换片段 362: 使用辅助视频 3763.71-3765.67秒
2025-07-08 17:04:21 [INFO] 替换片段 459: 使用辅助视频 3765.67-3773.33秒
2025-07-08 17:04:21 [INFO] 替换片段 279: 使用辅助视频 3773.33-3776.33秒
2025-07-08 17:04:22 [INFO] 替换片段 179: 使用辅助视频 3776.33-3777.92秒
2025-07-08 17:04:22 [INFO] 替换片段 118: 使用辅助视频 3777.92-3779.25秒
2025-07-08 17:04:22 [INFO] 替换片段 53: 使用辅助视频 3779.25-3781.92秒
2025-07-08 17:04:22 [INFO] 替换片段 298: 使用辅助视频 3781.92-3783.04秒
2025-07-08 17:04:22 [INFO] 处理替换批次 115/252，包含 10 个片段
2025-07-08 17:04:22 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:04:23 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:04:23 [INFO] 替换片段 141: 使用辅助视频 3783.04-3785.71秒
2025-07-08 17:04:24 [INFO] 替换片段 141: 使用辅助视频 3785.71-3787.04秒
2025-07-08 17:04:24 [INFO] 替换片段 366: 使用辅助视频 3787.04-3788.54秒
2025-07-08 17:04:24 [INFO] 替换片段 18: 使用辅助视频 3788.54-3790.67秒
2025-07-08 17:04:24 [INFO] 替换片段 270: 使用辅助视频 3790.67-3793.12秒
2025-07-08 17:04:25 [INFO] 替换片段 451: 使用辅助视频 3793.12-3795.83秒
2025-07-08 17:04:25 [INFO] 替换片段 155: 使用辅助视频 3795.83-3801.21秒
2025-07-08 17:04:26 [INFO] 替换片段 374: 使用辅助视频 3801.21-3804.92秒
2025-07-08 17:04:26 [INFO] 替换片段 149: 使用辅助视频 3804.92-3814.67秒
2025-07-08 17:04:26 [INFO] 替换片段 11: 使用辅助视频 3814.67-3817.38秒
2025-07-08 17:04:26 [INFO] 处理替换批次 116/252，包含 10 个片段
2025-07-08 17:04:26 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:04:27 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:04:27 [INFO] 替换片段 350: 使用辅助视频 3817.38-3819.21秒
2025-07-08 17:04:28 [INFO] 替换片段 1: 使用辅助视频 3819.21-3820.50秒
2025-07-08 17:04:28 [INFO] 替换片段 102: 使用辅助视频 3820.50-3821.33秒
2025-07-08 17:04:28 [INFO] 替换片段 11: 使用辅助视频 3821.33-3822.08秒
2025-07-08 17:04:28 [INFO] 替换片段 123: 使用辅助视频 3822.08-3823.71秒
2025-07-08 17:04:28 [INFO] 替换片段 176: 使用辅助视频 3823.71-3824.71秒
2025-07-08 17:04:28 [INFO] 替换片段 243: 使用辅助视频 3824.71-3827.46秒
2025-07-08 17:04:29 [INFO] 替换片段 357: 使用辅助视频 3827.46-3829.00秒
2025-07-08 17:04:29 [INFO] 替换片段 357: 使用辅助视频 3829.00-3830.29秒
2025-07-08 17:04:29 [INFO] 替换片段 406: 使用辅助视频 3830.29-3832.58秒
2025-07-08 17:04:29 [INFO] 处理替换批次 117/252，包含 10 个片段
2025-07-08 17:04:29 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:04:30 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:04:30 [INFO] 替换片段 452: 使用辅助视频 3832.58-3834.25秒
2025-07-08 17:04:30 [INFO] 替换片段 345: 使用辅助视频 3834.25-3835.92秒
2025-07-08 17:04:30 [INFO] 替换片段 441: 使用辅助视频 3835.92-3837.38秒
2025-07-08 17:04:31 [INFO] 替换片段 408: 使用辅助视频 3837.38-3842.12秒
2025-07-08 17:04:31 [INFO] 替换片段 356: 使用辅助视频 3842.12-3848.46秒
2025-07-08 17:04:31 [INFO] 替换片段 336: 使用辅助视频 3848.46-3858.38秒
2025-07-08 17:04:32 [INFO] 替换片段 462: 使用辅助视频 3858.38-3861.96秒
2025-07-08 17:04:32 [INFO] 替换片段 233: 使用辅助视频 3861.96-3866.38秒
2025-07-08 17:04:32 [INFO] 替换片段 147: 使用辅助视频 3866.38-3868.75秒
2025-07-08 17:04:33 [INFO] 替换片段 223: 使用辅助视频 3868.75-3870.62秒
2025-07-08 17:04:33 [INFO] 处理替换批次 118/252，包含 10 个片段
2025-07-08 17:04:33 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:04:33 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:04:34 [INFO] 替换片段 179: 使用辅助视频 3870.62-3872.04秒
2025-07-08 17:04:34 [INFO] 替换片段 474: 使用辅助视频 3872.04-3878.54秒
2025-07-08 17:04:34 [INFO] 替换片段 118: 使用辅助视频 3878.54-3880.38秒
2025-07-08 17:04:34 [INFO] 替换片段 452: 使用辅助视频 3880.38-3882.04秒
2025-07-08 17:04:35 [INFO] 替换片段 241: 使用辅助视频 3882.04-3883.25秒
2025-07-08 17:04:35 [INFO] 替换片段 389: 使用辅助视频 3883.25-3885.08秒
2025-07-08 17:04:35 [INFO] 替换片段 452: 使用辅助视频 3885.08-3888.67秒
2025-07-08 17:04:35 [INFO] 替换片段 255: 使用辅助视频 3888.67-3891.12秒
2025-07-08 17:04:36 [INFO] 替换片段 223: 使用辅助视频 3891.12-3892.50秒
2025-07-08 17:04:36 [INFO] 替换片段 105: 使用辅助视频 3892.50-3896.46秒
2025-07-08 17:04:36 [INFO] 处理替换批次 119/252，包含 10 个片段
2025-07-08 17:04:36 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:04:37 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:04:37 [INFO] 替换片段 235: 使用辅助视频 3896.46-3901.58秒
2025-07-08 17:04:37 [INFO] 替换片段 16: 使用辅助视频 3901.58-3905.25秒
2025-07-08 17:04:38 [INFO] 替换片段 208: 使用辅助视频 3905.25-3906.92秒
2025-07-08 17:04:38 [INFO] 替换片段 223: 使用辅助视频 3906.92-3907.83秒
2025-07-08 17:04:38 [INFO] 替换片段 16: 使用辅助视频 3907.83-3910.08秒
2025-07-08 17:04:38 [INFO] 替换片段 318: 使用辅助视频 3910.08-3915.46秒
2025-07-08 17:04:39 [INFO] 替换片段 441: 使用辅助视频 3915.46-3922.67秒
2025-07-08 17:04:39 [INFO] 替换片段 363: 使用辅助视频 3922.67-3925.25秒
2025-07-08 17:04:39 [INFO] 替换片段 120: 使用辅助视频 3925.25-3928.00秒
2025-07-08 17:04:40 [INFO] 替换片段 395: 使用辅助视频 3928.00-3933.33秒
2025-07-08 17:04:40 [INFO] 处理替换批次 120/252，包含 10 个片段
2025-07-08 17:04:40 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:04:40 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:04:41 [INFO] 替换片段 103: 使用辅助视频 3933.33-3935.92秒
2025-07-08 17:04:41 [INFO] 替换片段 445: 使用辅助视频 3935.92-3939.00秒
2025-07-08 17:04:41 [INFO] 替换片段 104: 使用辅助视频 3939.00-3943.00秒
2025-07-08 17:04:42 [INFO] 替换片段 135: 使用辅助视频 3943.00-3946.75秒
2025-07-08 17:04:43 [INFO] 替换片段 453: 使用辅助视频 3946.75-3949.79秒
2025-07-08 17:04:43 [INFO] 替换片段 256: 使用辅助视频 3949.79-3953.08秒
2025-07-08 17:04:44 [INFO] 替换片段 319: 使用辅助视频 3953.08-3956.33秒
2025-07-08 17:04:44 [INFO] 替换片段 359: 使用辅助视频 3956.33-3959.08秒
2025-07-08 17:04:45 [INFO] 替换片段 203: 使用辅助视频 3959.08-3961.83秒
2025-07-08 17:04:45 [INFO] 替换片段 338: 使用辅助视频 3961.83-3964.00秒
2025-07-08 17:04:45 [INFO] 处理替换批次 121/252，包含 10 个片段
2025-07-08 17:04:45 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:04:46 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:04:46 [INFO] 替换片段 349: 使用辅助视频 3964.00-3967.88秒
2025-07-08 17:04:46 [INFO] 替换片段 191: 使用辅助视频 3967.88-3969.88秒
2025-07-08 17:04:47 [INFO] 替换片段 118: 使用辅助视频 3969.88-3973.42秒
2025-07-08 17:04:47 [INFO] 替换片段 469: 使用辅助视频 3973.42-3977.33秒
2025-07-08 17:04:48 [INFO] 替换片段 317: 使用辅助视频 3977.33-3981.08秒
2025-07-08 17:04:48 [INFO] 替换片段 221: 使用辅助视频 3981.08-3986.79秒
2025-07-08 17:04:49 [INFO] 替换片段 66: 使用辅助视频 3986.79-4000.88秒
2025-07-08 17:04:49 [INFO] 替换片段 235: 使用辅助视频 4000.88-4015.17秒
2025-07-08 17:04:49 [INFO] 替换片段 219: 使用辅助视频 4015.17-4017.46秒
2025-07-08 17:04:50 [INFO] 替换片段 207: 使用辅助视频 4017.46-4025.54秒
2025-07-08 17:04:50 [INFO] 处理替换批次 122/252，包含 10 个片段
2025-07-08 17:04:50 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:04:50 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:04:51 [INFO] 替换片段 176: 使用辅助视频 4025.54-4027.71秒
2025-07-08 17:04:51 [INFO] 替换片段 156: 使用辅助视频 4027.71-4032.75秒
2025-07-08 17:04:51 [INFO] 替换片段 295: 使用辅助视频 4032.75-4039.50秒
2025-07-08 17:04:52 [INFO] 替换片段 209: 使用辅助视频 4039.50-4043.21秒
2025-07-08 17:04:52 [INFO] 替换片段 240: 使用辅助视频 4043.21-4045.54秒
2025-07-08 17:04:52 [INFO] 替换片段 117: 使用辅助视频 4045.54-4048.04秒
2025-07-08 17:04:53 [INFO] 替换片段 384: 使用辅助视频 4048.04-4052.00秒
2025-07-08 17:04:53 [INFO] 替换片段 363: 使用辅助视频 4052.00-4056.54秒
2025-07-08 17:04:54 [INFO] 替换片段 147: 使用辅助视频 4056.54-4066.33秒
2025-07-08 17:04:54 [INFO] 替换片段 185: 使用辅助视频 4066.33-4073.00秒
2025-07-08 17:04:54 [INFO] 处理替换批次 123/252，包含 10 个片段
2025-07-08 17:04:54 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:04:55 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:04:55 [INFO] 替换片段 14: 使用辅助视频 4073.00-4080.75秒
2025-07-08 17:04:55 [INFO] 替换片段 379: 使用辅助视频 4080.75-4089.21秒
2025-07-08 17:04:55 [INFO] 替换片段 293: 使用辅助视频 4089.21-4094.12秒
2025-07-08 17:04:56 [INFO] 替换片段 175: 使用辅助视频 4094.12-4096.38秒
2025-07-08 17:04:56 [INFO] 替换片段 382: 使用辅助视频 4096.38-4105.54秒
2025-07-08 17:04:57 [INFO] 替换片段 244: 使用辅助视频 4105.54-4109.75秒
2025-07-08 17:04:57 [INFO] 替换片段 362: 使用辅助视频 4109.75-4118.04秒
2025-07-08 17:04:57 [INFO] 替换片段 195: 使用辅助视频 4118.04-4124.25秒
2025-07-08 17:04:58 [INFO] 替换片段 235: 使用辅助视频 4124.25-4127.62秒
2025-07-08 17:04:58 [INFO] 替换片段 483: 使用辅助视频 4127.62-4131.00秒
2025-07-08 17:04:58 [INFO] 处理替换批次 124/252，包含 10 个片段
2025-07-08 17:04:58 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:04:59 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:04:59 [INFO] 替换片段 289: 使用辅助视频 4131.00-4137.33秒
2025-07-08 17:04:59 [INFO] 替换片段 410: 使用辅助视频 4137.33-4141.12秒
2025-07-08 17:05:00 [INFO] 替换片段 280: 使用辅助视频 4141.12-4151.04秒
2025-07-08 17:05:00 [INFO] 替换片段 44: 使用辅助视频 4151.04-4152.67秒
2025-07-08 17:05:00 [INFO] 替换片段 116: 使用辅助视频 4152.67-4161.92秒
2025-07-08 17:05:01 [INFO] 替换片段 468: 使用辅助视频 4161.92-4164.83秒
2025-07-08 17:05:01 [INFO] 替换片段 290: 使用辅助视频 4164.83-4170.21秒
2025-07-08 17:05:02 [INFO] 替换片段 91: 使用辅助视频 4170.21-4176.21秒
2025-07-08 17:05:02 [INFO] 替换片段 447: 使用辅助视频 4176.21-4180.29秒
2025-07-08 17:05:03 [INFO] 替换片段 420: 使用辅助视频 4180.29-4186.21秒
2025-07-08 17:05:03 [INFO] 处理替换批次 125/252，包含 10 个片段
2025-07-08 17:05:03 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:05:03 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:05:03 [INFO] 替换片段 18: 使用辅助视频 4186.21-4189.17秒
2025-07-08 17:05:04 [INFO] 替换片段 348: 使用辅助视频 4189.17-4195.88秒
2025-07-08 17:05:04 [INFO] 替换片段 219: 使用辅助视频 4195.88-4199.71秒
2025-07-08 17:05:05 [INFO] 替换片段 2: 使用辅助视频 4199.71-4201.42秒
2025-07-08 17:05:05 [INFO] 替换片段 157: 使用辅助视频 4201.42-4215.04秒
2025-07-08 17:05:05 [INFO] 替换片段 108: 使用辅助视频 4215.04-4215.83秒
2025-07-08 17:05:05 [INFO] 替换片段 227: 使用辅助视频 4215.83-4217.62秒
2025-07-08 17:05:06 [INFO] 替换片段 112: 使用辅助视频 4217.62-4221.54秒
2025-07-08 17:05:06 [INFO] 替换片段 50: 使用辅助视频 4221.54-4222.67秒
2025-07-08 17:05:06 [INFO] 替换片段 63: 使用辅助视频 4222.67-4223.67秒
2025-07-08 17:05:06 [INFO] 处理替换批次 126/252，包含 10 个片段
2025-07-08 17:05:06 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:05:07 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:05:07 [INFO] 替换片段 335: 使用辅助视频 4223.67-4224.46秒
2025-07-08 17:05:07 [INFO] 替换片段 16: 使用辅助视频 4224.46-4226.00秒
2025-07-08 17:05:08 [INFO] 替换片段 96: 使用辅助视频 4226.00-4227.08秒
2025-07-08 17:05:08 [INFO] 替换片段 339: 使用辅助视频 4227.08-4228.42秒
2025-07-08 17:05:08 [INFO] 替换片段 204: 使用辅助视频 4228.42-4233.46秒
2025-07-08 17:05:08 [INFO] 替换片段 254: 使用辅助视频 4233.46-4236.42秒
2025-07-08 17:05:09 [INFO] 替换片段 411: 使用辅助视频 4236.42-4239.12秒
2025-07-08 17:05:09 [INFO] 替换片段 371: 使用辅助视频 4239.12-4245.54秒
2025-07-08 17:05:09 [INFO] 替换片段 270: 使用辅助视频 4245.54-4247.33秒
2025-07-08 17:05:10 [INFO] 替换片段 304: 使用辅助视频 4247.33-4249.46秒
2025-07-08 17:05:10 [INFO] 处理替换批次 127/252，包含 10 个片段
2025-07-08 17:05:10 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:05:10 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:05:10 [INFO] 替换片段 18: 使用辅助视频 4249.46-4251.92秒
2025-07-08 17:05:11 [INFO] 替换片段 96: 使用辅助视频 4251.92-4254.00秒
2025-07-08 17:05:11 [INFO] 替换片段 14: 使用辅助视频 4254.00-4255.54秒
2025-07-08 17:05:11 [INFO] 替换片段 371: 使用辅助视频 4255.54-4258.46秒
2025-07-08 17:05:12 [INFO] 替换片段 475: 使用辅助视频 4258.46-4260.92秒
2025-07-08 17:05:12 [INFO] 替换片段 116: 使用辅助视频 4260.92-4261.83秒
2025-07-08 17:05:12 [INFO] 替换片段 205: 使用辅助视频 4261.83-4262.88秒
2025-07-08 17:05:12 [INFO] 替换片段 371: 使用辅助视频 4262.88-4264.58秒
2025-07-08 17:05:13 [INFO] 替换片段 208: 使用辅助视频 4264.58-4266.21秒
2025-07-08 17:05:13 [INFO] 替换片段 254: 使用辅助视频 4266.21-4268.12秒
2025-07-08 17:05:13 [INFO] 处理替换批次 128/252，包含 10 个片段
2025-07-08 17:05:13 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:05:14 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:05:14 [INFO] 替换片段 230: 使用辅助视频 4268.12-4276.54秒
2025-07-08 17:05:14 [INFO] 替换片段 338: 使用辅助视频 4276.54-4278.33秒
2025-07-08 17:05:14 [INFO] 替换片段 405: 使用辅助视频 4278.33-4283.00秒
2025-07-08 17:05:15 [INFO] 替换片段 451: 使用辅助视频 4283.00-4288.88秒
2025-07-08 17:05:15 [INFO] 替换片段 364: 使用辅助视频 4288.88-4290.71秒
2025-07-08 17:05:15 [INFO] 替换片段 451: 使用辅助视频 4290.71-4292.62秒
2025-07-08 17:05:15 [INFO] 替换片段 325: 使用辅助视频 4292.62-4294.50秒
2025-07-08 17:05:16 [INFO] 替换片段 350: 使用辅助视频 4294.50-4296.04秒
2025-07-08 17:05:16 [INFO] 替换片段 358: 使用辅助视频 4296.04-4297.46秒
2025-07-08 17:05:16 [INFO] 替换片段 199: 使用辅助视频 4297.46-4303.38秒
2025-07-08 17:05:16 [INFO] 处理替换批次 129/252，包含 10 个片段
2025-07-08 17:05:16 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:05:17 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:05:17 [INFO] 替换片段 118: 使用辅助视频 4303.38-4305.46秒
2025-07-08 17:05:17 [INFO] 替换片段 285: 使用辅助视频 4305.46-4307.21秒
2025-07-08 17:05:17 [INFO] 替换片段 348: 使用辅助视频 4307.21-4314.38秒
2025-07-08 17:05:18 [INFO] 替换片段 209: 使用辅助视频 4314.38-4322.96秒
2025-07-08 17:05:18 [INFO] 替换片段 198: 使用辅助视频 4322.96-4326.88秒
2025-07-08 17:05:19 [INFO] 替换片段 357: 使用辅助视频 4326.88-4329.88秒
2025-07-08 17:05:19 [INFO] 替换片段 219: 使用辅助视频 4329.88-4332.83秒
2025-07-08 17:05:19 [INFO] 替换片段 198: 使用辅助视频 4332.83-4335.46秒
2025-07-08 17:05:20 [INFO] 替换片段 337: 使用辅助视频 4335.46-4337.00秒
2025-07-08 17:05:20 [INFO] 替换片段 198: 使用辅助视频 4337.00-4338.71秒
2025-07-08 17:05:20 [INFO] 处理替换批次 130/252，包含 10 个片段
2025-07-08 17:05:20 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:05:21 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:05:21 [INFO] 替换片段 104: 使用辅助视频 4338.71-4342.50秒
2025-07-08 17:05:22 [INFO] 替换片段 198: 使用辅助视频 4342.50-4346.79秒
2025-07-08 17:05:22 [INFO] 替换片段 257: 使用辅助视频 4346.79-4349.29秒
2025-07-08 17:05:22 [INFO] 替换片段 230: 使用辅助视频 4349.29-4354.46秒
2025-07-08 17:05:23 [INFO] 替换片段 141: 使用辅助视频 4354.46-4355.62秒
2025-07-08 17:05:23 [INFO] 替换片段 223: 使用辅助视频 4355.62-4357.17秒
2025-07-08 17:05:23 [INFO] 替换片段 357: 使用辅助视频 4357.17-4357.96秒
2025-07-08 17:05:23 [INFO] 替换片段 156: 使用辅助视频 4357.96-4359.67秒
2025-07-08 17:05:24 [INFO] 替换片段 153: 使用辅助视频 4359.67-4362.42秒
2025-07-08 17:05:24 [INFO] 替换片段 247: 使用辅助视频 4362.42-4367.67秒
2025-07-08 17:05:24 [INFO] 处理替换批次 131/252，包含 10 个片段
2025-07-08 17:05:24 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:05:24 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:05:25 [INFO] 替换片段 189: 使用辅助视频 4367.67-4371.25秒
2025-07-08 17:05:25 [INFO] 替换片段 11: 使用辅助视频 4371.25-4372.67秒
2025-07-08 17:05:25 [INFO] 替换片段 247: 使用辅助视频 4372.67-4373.92秒
2025-07-08 17:05:26 [INFO] 替换片段 123: 使用辅助视频 4373.92-4377.75秒
2025-07-08 17:05:26 [INFO] 替换片段 258: 使用辅助视频 4377.75-4380.46秒
2025-07-08 17:05:27 [INFO] 替换片段 165: 使用辅助视频 4380.46-4383.17秒
2025-07-08 17:05:27 [INFO] 替换片段 465: 使用辅助视频 4383.17-4384.42秒
2025-07-08 17:05:27 [INFO] 替换片段 218: 使用辅助视频 4384.42-4387.12秒
2025-07-08 17:05:28 [INFO] 替换片段 336: 使用辅助视频 4387.12-4391.08秒
2025-07-08 17:05:28 [INFO] 替换片段 362: 使用辅助视频 4391.08-4395.88秒
2025-07-08 17:05:28 [INFO] 处理替换批次 132/252，包含 10 个片段
2025-07-08 17:05:28 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:05:29 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:05:29 [INFO] 替换片段 294: 使用辅助视频 4395.88-4399.71秒
2025-07-08 17:05:30 [INFO] 替换片段 424: 使用辅助视频 4399.71-4405.17秒
2025-07-08 17:05:30 [INFO] 替换片段 18: 使用辅助视频 4405.17-4409.54秒
2025-07-08 17:05:30 [INFO] 替换片段 391: 使用辅助视频 4409.54-4411.33秒
2025-07-08 17:05:30 [INFO] 替换片段 257: 使用辅助视频 4411.33-4418.88秒
2025-07-08 17:05:31 [INFO] 替换片段 198: 使用辅助视频 4418.88-4433.79秒
2025-07-08 17:05:31 [INFO] 替换片段 478: 使用辅助视频 4433.79-4436.96秒
2025-07-08 17:05:31 [INFO] 替换片段 84: 使用辅助视频 4436.96-4441.54秒
2025-07-08 17:05:32 [INFO] 替换片段 132: 使用辅助视频 4441.54-4452.96秒
2025-07-08 17:05:32 [INFO] 替换片段 102: 使用辅助视频 4452.96-4458.79秒
2025-07-08 17:05:32 [INFO] 处理替换批次 133/252，包含 10 个片段
2025-07-08 17:05:32 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:05:33 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:05:33 [INFO] 替换片段 254: 使用辅助视频 4458.79-4460.08秒
2025-07-08 17:05:33 [INFO] 替换片段 440: 使用辅助视频 4460.08-4461.79秒
2025-07-08 17:05:33 [INFO] 替换片段 166: 使用辅助视频 4461.79-4462.67秒
2025-07-08 17:05:33 [INFO] 替换片段 293: 使用辅助视频 4462.67-4467.08秒
2025-07-08 17:05:34 [INFO] 替换片段 344: 使用辅助视频 4467.08-4469.58秒
2025-07-08 17:05:34 [INFO] 替换片段 258: 使用辅助视频 4469.58-4470.75秒
2025-07-08 17:05:34 [INFO] 替换片段 414: 使用辅助视频 4470.75-4472.50秒
2025-07-08 17:05:35 [INFO] 替换片段 73: 使用辅助视频 4472.50-4485.92秒
2025-07-08 17:05:35 [INFO] 替换片段 359: 使用辅助视频 4485.92-4488.96秒
2025-07-08 17:05:35 [INFO] 替换片段 308: 使用辅助视频 4488.96-4490.50秒
2025-07-08 17:05:35 [INFO] 处理替换批次 134/252，包含 10 个片段
2025-07-08 17:05:35 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:05:36 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:05:36 [INFO] 替换片段 231: 使用辅助视频 4490.50-4491.58秒
2025-07-08 17:05:36 [INFO] 替换片段 294: 使用辅助视频 4491.58-4492.88秒
2025-07-08 17:05:37 [INFO] 替换片段 219: 使用辅助视频 4492.88-4493.92秒
2025-07-08 17:05:37 [INFO] 替换片段 294: 使用辅助视频 4493.92-4495.17秒
2025-07-08 17:05:37 [INFO] 替换片段 219: 使用辅助视频 4495.17-4496.12秒
2025-07-08 17:05:37 [INFO] 替换片段 217: 使用辅助视频 4496.12-4501.92秒
2025-07-08 17:05:38 [INFO] 替换片段 359: 使用辅助视频 4509.46-4510.96秒
2025-07-08 17:05:38 [INFO] 替换片段 9: 使用辅助视频 4510.96-4511.79秒
2025-07-08 17:05:38 [INFO] 替换片段 354: 使用辅助视频 4511.79-4513.71秒
2025-07-08 17:05:38 [INFO] 替换片段 414: 使用辅助视频 4513.71-4521.21秒
2025-07-08 17:05:38 [INFO] 处理替换批次 135/252，包含 10 个片段
2025-07-08 17:05:38 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:05:39 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:05:40 [INFO] 替换片段 386: 使用辅助视频 4521.21-4523.12秒
2025-07-08 17:05:41 [INFO] 替换片段 218: 使用辅助视频 4523.12-4525.54秒
2025-07-08 17:05:41 [INFO] 替换片段 423: 使用辅助视频 4525.54-4528.38秒
2025-07-08 17:05:42 [INFO] 替换片段 188: 使用辅助视频 4528.38-4532.46秒
2025-07-08 17:05:42 [INFO] 替换片段 139: 使用辅助视频 4532.46-4536.75秒
2025-07-08 17:05:43 [INFO] 替换片段 412: 使用辅助视频 4536.75-4539.50秒
2025-07-08 17:05:43 [INFO] 替换片段 104: 使用辅助视频 4539.50-4545.96秒
2025-07-08 17:05:44 [INFO] 替换片段 191: 使用辅助视频 4545.96-4550.17秒
2025-07-08 17:05:44 [INFO] 替换片段 412: 使用辅助视频 4550.17-4551.12秒
2025-07-08 17:05:45 [INFO] 替换片段 386: 使用辅助视频 4551.12-4552.46秒
2025-07-08 17:05:45 [INFO] 处理替换批次 136/252，包含 10 个片段
2025-07-08 17:05:45 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:05:45 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:05:45 [INFO] 替换片段 102: 使用辅助视频 4552.46-4554.67秒
2025-07-08 17:05:46 [INFO] 替换片段 294: 使用辅助视频 4554.67-4557.21秒
2025-07-08 17:05:46 [INFO] 替换片段 130: 使用辅助视频 4557.21-4559.46秒
2025-07-08 17:05:46 [INFO] 替换片段 104: 使用辅助视频 4559.46-4560.54秒
2025-07-08 17:05:47 [INFO] 替换片段 364: 使用辅助视频 4560.54-4562.29秒
2025-07-08 17:05:47 [INFO] 替换片段 8: 使用辅助视频 4562.29-4563.46秒
2025-07-08 17:05:47 [INFO] 替换片段 142: 使用辅助视频 4563.46-4564.12秒
2025-07-08 17:05:47 [INFO] 替换片段 403: 使用辅助视频 4564.12-4564.88秒
2025-07-08 17:05:47 [INFO] 替换片段 180: 使用辅助视频 4564.88-4566.46秒
2025-07-08 17:05:47 [INFO] 替换片段 11: 使用辅助视频 4566.46-4567.88秒
2025-07-08 17:05:48 [INFO] 处理替换批次 137/252，包含 10 个片段
2025-07-08 17:05:48 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:05:48 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:05:48 [INFO] 替换片段 139: 使用辅助视频 4567.88-4569.08秒
2025-07-08 17:05:49 [INFO] 替换片段 344: 使用辅助视频 4569.08-4569.96秒
2025-07-08 17:05:49 [INFO] 替换片段 9: 使用辅助视频 4569.96-4571.83秒
2025-07-08 17:05:49 [INFO] 替换片段 109: 使用辅助视频 4571.83-4576.67秒
2025-07-08 17:05:50 [INFO] 替换片段 260: 使用辅助视频 4576.67-4578.79秒
2025-07-08 17:05:50 [INFO] 替换片段 261: 使用辅助视频 4578.79-4580.17秒
2025-07-08 17:05:50 [INFO] 替换片段 335: 使用辅助视频 4580.17-4581.75秒
2025-07-08 17:05:50 [INFO] 替换片段 476: 使用辅助视频 4581.75-4583.08秒
2025-07-08 17:05:51 [INFO] 替换片段 459: 使用辅助视频 4583.08-4584.17秒
2025-07-08 17:05:51 [INFO] 替换片段 142: 使用辅助视频 4584.17-4585.42秒
2025-07-08 17:05:51 [INFO] 处理替换批次 138/252，包含 10 个片段
2025-07-08 17:05:51 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:05:51 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:05:52 [INFO] 替换片段 320: 使用辅助视频 4585.42-4586.88秒
2025-07-08 17:05:52 [INFO] 替换片段 142: 使用辅助视频 4586.88-4587.83秒
2025-07-08 17:05:52 [INFO] 替换片段 451: 使用辅助视频 4587.83-4594.21秒
2025-07-08 17:05:52 [INFO] 替换片段 141: 使用辅助视频 4595.88-4596.67秒
2025-07-08 17:05:52 [INFO] 替换片段 110: 使用辅助视频 4596.67-4597.33秒
2025-07-08 17:05:53 [INFO] 替换片段 263: 使用辅助视频 4597.33-4598.08秒
2025-07-08 17:05:53 [INFO] 替换片段 98: 使用辅助视频 4598.08-4598.79秒
2025-07-08 17:05:53 [INFO] 替换片段 204: 使用辅助视频 4598.79-4599.79秒
2025-07-08 17:05:53 [INFO] 替换片段 266: 使用辅助视频 4599.79-4601.75秒
2025-07-08 17:05:53 [INFO] 替换片段 267: 使用辅助视频 4601.75-4604.00秒
2025-07-08 17:05:53 [INFO] 处理替换批次 139/252，包含 10 个片段
2025-07-08 17:05:53 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:05:54 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:05:54 [INFO] 替换片段 141: 使用辅助视频 4604.00-4606.42秒
2025-07-08 17:05:54 [INFO] 替换片段 319: 使用辅助视频 4606.42-4609.08秒
2025-07-08 17:05:55 [INFO] 替换片段 379: 使用辅助视频 4609.08-4614.17秒
2025-07-08 17:05:55 [INFO] 替换片段 479: 使用辅助视频 4614.17-4615.25秒
2025-07-08 17:05:55 [INFO] 替换片段 221: 使用辅助视频 4615.25-4616.00秒
2025-07-08 17:05:55 [INFO] 替换片段 295: 使用辅助视频 4616.00-4617.46秒
2025-07-08 17:05:56 [INFO] 替换片段 308: 使用辅助视频 4617.46-4618.12秒
2025-07-08 17:05:56 [INFO] 替换片段 438: 使用辅助视频 4618.12-4618.92秒
2025-07-08 17:05:56 [INFO] 替换片段 141: 使用辅助视频 4618.92-4619.96秒
2025-07-08 17:05:56 [INFO] 替换片段 131: 使用辅助视频 4619.96-4621.25秒
2025-07-08 17:05:56 [INFO] 处理替换批次 140/252，包含 10 个片段
2025-07-08 17:05:56 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:05:57 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:05:57 [INFO] 替换片段 293: 使用辅助视频 4621.25-4622.50秒
2025-07-08 17:05:57 [INFO] 替换片段 137: 使用辅助视频 4622.50-4624.21秒
2025-07-08 17:05:57 [INFO] 替换片段 179: 使用辅助视频 4624.21-4625.38秒
2025-07-08 17:05:58 [INFO] 替换片段 304: 使用辅助视频 4625.38-4626.12秒
2025-07-08 17:05:58 [INFO] 替换片段 193: 使用辅助视频 4626.12-4626.92秒
2025-07-08 17:05:58 [INFO] 替换片段 479: 使用辅助视频 4626.92-4627.96秒
2025-07-08 17:05:58 [INFO] 替换片段 10: 使用辅助视频 4627.96-4630.42秒
2025-07-08 17:05:58 [INFO] 替换片段 356: 使用辅助视频 4630.42-4631.83秒
2025-07-08 17:05:59 [INFO] 替换片段 124: 使用辅助视频 4631.83-4633.25秒
2025-07-08 17:05:59 [INFO] 替换片段 352: 使用辅助视频 4633.25-4634.83秒
2025-07-08 17:05:59 [INFO] 处理替换批次 141/252，包含 10 个片段
2025-07-08 17:05:59 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:06:00 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:06:00 [INFO] 替换片段 131: 使用辅助视频 4634.83-4638.08秒
2025-07-08 17:06:00 [INFO] 替换片段 177: 使用辅助视频 4638.08-4638.96秒
2025-07-08 17:06:00 [INFO] 替换片段 120: 使用辅助视频 4638.96-4640.17秒
2025-07-08 17:06:01 [INFO] 替换片段 208: 使用辅助视频 4640.17-4641.62秒
2025-07-08 17:06:01 [INFO] 替换片段 282: 使用辅助视频 4641.62-4642.42秒
2025-07-08 17:06:01 [INFO] 替换片段 301: 使用辅助视频 4642.42-4643.33秒
2025-07-08 17:06:01 [INFO] 替换片段 139: 使用辅助视频 4643.33-4644.38秒
2025-07-08 17:06:01 [INFO] 替换片段 362: 使用辅助视频 4644.38-4645.67秒
2025-07-08 17:06:02 [INFO] 替换片段 334: 使用辅助视频 4645.67-4646.58秒
2025-07-08 17:06:02 [INFO] 替换片段 479: 使用辅助视频 4646.58-4647.46秒
2025-07-08 17:06:02 [INFO] 处理替换批次 142/252，包含 10 个片段
2025-07-08 17:06:02 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:06:02 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:06:03 [INFO] 替换片段 453: 使用辅助视频 4647.46-4648.29秒
2025-07-08 17:06:03 [INFO] 替换片段 392: 使用辅助视频 4648.29-4649.96秒
2025-07-08 17:06:03 [INFO] 替换片段 473: 使用辅助视频 4649.96-4650.96秒
2025-07-08 17:06:03 [INFO] 替换片段 477: 使用辅助视频 4650.96-4651.67秒
2025-07-08 17:06:03 [INFO] 替换片段 287: 使用辅助视频 4651.67-4653.96秒
2025-07-08 17:06:04 [INFO] 替换片段 215: 使用辅助视频 4653.96-4654.67秒
2025-07-08 17:06:04 [INFO] 替换片段 145: 使用辅助视频 4654.67-4655.33秒
2025-07-08 17:06:04 [INFO] 替换片段 156: 使用辅助视频 4655.33-4656.46秒
2025-07-08 17:06:04 [INFO] 替换片段 399: 使用辅助视频 4656.46-4658.04秒
2025-07-08 17:06:04 [INFO] 替换片段 20: 使用辅助视频 4658.04-4659.79秒
2025-07-08 17:06:04 [INFO] 处理替换批次 143/252，包含 10 个片段
2025-07-08 17:06:04 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:06:05 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:06:05 [INFO] 替换片段 110: 使用辅助视频 4659.79-4660.88秒
2025-07-08 17:06:06 [INFO] 替换片段 318: 使用辅助视频 4660.88-4663.88秒
2025-07-08 17:06:06 [INFO] 替换片段 363: 使用辅助视频 4663.88-4665.92秒
2025-07-08 17:06:07 [INFO] 替换片段 64: 使用辅助视频 4665.92-4667.75秒
2025-07-08 17:06:07 [INFO] 替换片段 172: 使用辅助视频 4667.75-4669.12秒
2025-07-08 17:06:07 [INFO] 替换片段 338: 使用辅助视频 4669.12-4670.08秒
2025-07-08 17:06:08 [INFO] 替换片段 452: 使用辅助视频 4670.08-4673.38秒
2025-07-08 17:06:08 [INFO] 替换片段 268: 使用辅助视频 4673.38-4674.79秒
2025-07-08 17:06:08 [INFO] 替换片段 279: 使用辅助视频 4674.79-4675.92秒
2025-07-08 17:06:09 [INFO] 替换片段 2: 使用辅助视频 4675.92-4676.96秒
2025-07-08 17:06:09 [INFO] 处理替换批次 144/252，包含 10 个片段
2025-07-08 17:06:09 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:06:10 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:06:10 [INFO] 替换片段 349: 使用辅助视频 4676.96-4678.04秒
2025-07-08 17:06:10 [INFO] 替换片段 271: 使用辅助视频 4678.04-4678.88秒
2025-07-08 17:06:11 [INFO] 替换片段 282: 使用辅助视频 4678.88-4680.67秒
2025-07-08 17:06:11 [INFO] 替换片段 74: 使用辅助视频 4680.67-4681.38秒
2025-07-08 17:06:11 [INFO] 替换片段 407: 使用辅助视频 4681.38-4683.29秒
2025-07-08 17:06:12 [INFO] 替换片段 401: 使用辅助视频 4683.29-4684.17秒
2025-07-08 17:06:12 [INFO] 替换片段 319: 使用辅助视频 4684.17-4684.96秒
2025-07-08 17:06:12 [INFO] 替换片段 348: 使用辅助视频 4684.96-4686.00秒
2025-07-08 17:06:12 [INFO] 替换片段 215: 使用辅助视频 4686.00-4687.38秒
2025-07-08 17:06:12 [INFO] 替换片段 217: 使用辅助视频 4687.38-4688.04秒
2025-07-08 17:06:12 [INFO] 处理替换批次 145/252，包含 10 个片段
2025-07-08 17:06:12 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:06:13 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:06:13 [INFO] 替换片段 26: 使用辅助视频 4688.04-4690.67秒
2025-07-08 17:06:14 [INFO] 替换片段 227: 使用辅助视频 4690.67-4691.46秒
2025-07-08 17:06:14 [INFO] 替换片段 188: 使用辅助视频 4691.46-4693.50秒
2025-07-08 17:06:14 [INFO] 替换片段 332: 使用辅助视频 4693.50-4694.21秒
2025-07-08 17:06:14 [INFO] 替换片段 447: 使用辅助视频 4694.21-4695.04秒
2025-07-08 17:06:14 [INFO] 替换片段 478: 使用辅助视频 4695.04-4696.04秒
2025-07-08 17:06:14 [INFO] 替换片段 255: 使用辅助视频 4696.04-4697.12秒
2025-07-08 17:06:15 [INFO] 替换片段 146: 使用辅助视频 4697.12-4697.92秒
2025-07-08 17:06:15 [INFO] 替换片段 399: 使用辅助视频 4697.92-4698.79秒
2025-07-08 17:06:15 [INFO] 替换片段 219: 使用辅助视频 4698.79-4699.67秒
2025-07-08 17:06:15 [INFO] 处理替换批次 146/252，包含 10 个片段
2025-07-08 17:06:15 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:06:15 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:06:16 [INFO] 替换片段 106: 使用辅助视频 4699.67-4701.29秒
2025-07-08 17:06:16 [INFO] 替换片段 71: 使用辅助视频 4701.29-4702.58秒
2025-07-08 17:06:16 [INFO] 替换片段 410: 使用辅助视频 4702.58-4703.79秒
2025-07-08 17:06:16 [INFO] 替换片段 294: 使用辅助视频 4703.79-4704.83秒
2025-07-08 17:06:16 [INFO] 替换片段 122: 使用辅助视频 4704.83-4707.00秒
2025-07-08 17:06:17 [INFO] 替换片段 248: 使用辅助视频 4707.00-4708.04秒
2025-07-08 17:06:17 [INFO] 替换片段 115: 使用辅助视频 4708.04-4709.17秒
2025-07-08 17:06:17 [INFO] 替换片段 182: 使用辅助视频 4709.17-4710.38秒
2025-07-08 17:06:17 [INFO] 替换片段 61: 使用辅助视频 4710.38-4711.17秒
2025-07-08 17:06:17 [INFO] 替换片段 357: 使用辅助视频 4711.17-4711.79秒
2025-07-08 17:06:17 [INFO] 处理替换批次 147/252，包含 10 个片段
2025-07-08 17:06:17 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:06:18 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:06:18 [INFO] 替换片段 467: 使用辅助视频 4711.79-4712.54秒
2025-07-08 17:06:19 [INFO] 替换片段 276: 使用辅助视频 4714.42-4716.00秒
2025-07-08 17:06:19 [INFO] 替换片段 44: 使用辅助视频 4716.00-4717.38秒
2025-07-08 17:06:19 [INFO] 替换片段 304: 使用辅助视频 4717.38-4718.79秒
2025-07-08 17:06:19 [INFO] 替换片段 349: 使用辅助视频 4718.79-4720.54秒
2025-07-08 17:06:20 [INFO] 替换片段 454: 使用辅助视频 4720.54-4721.62秒
2025-07-08 17:06:20 [INFO] 替换片段 113: 使用辅助视频 4721.62-4723.04秒
2025-07-08 17:06:20 [INFO] 替换片段 314: 使用辅助视频 4723.04-4723.96秒
2025-07-08 17:06:20 [INFO] 替换片段 64: 使用辅助视频 4723.96-4725.12秒
2025-07-08 17:06:20 [INFO] 替换片段 218: 使用辅助视频 4725.12-4727.33秒
2025-07-08 17:06:20 [INFO] 处理替换批次 148/252，包含 10 个片段
2025-07-08 17:06:20 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:06:21 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:06:21 [INFO] 替换片段 344: 使用辅助视频 4727.33-4728.46秒
2025-07-08 17:06:21 [INFO] 替换片段 116: 使用辅助视频 4728.46-4729.79秒
2025-07-08 17:06:22 [INFO] 替换片段 319: 使用辅助视频 4729.79-4730.75秒
2025-07-08 17:06:22 [INFO] 替换片段 201: 使用辅助视频 4730.75-4731.92秒
2025-07-08 17:06:22 [INFO] 替换片段 447: 使用辅助视频 4731.92-4733.17秒
2025-07-08 17:06:22 [INFO] 替换片段 124: 使用辅助视频 4733.17-4734.08秒
2025-07-08 17:06:22 [INFO] 替换片段 477: 使用辅助视频 4734.08-4735.21秒
2025-07-08 17:06:22 [INFO] 替换片段 113: 使用辅助视频 4735.21-4736.38秒
2025-07-08 17:06:23 [INFO] 替换片段 344: 使用辅助视频 4736.38-4737.17秒
2025-07-08 17:06:23 [INFO] 替换片段 122: 使用辅助视频 4737.17-4739.75秒
2025-07-08 17:06:23 [INFO] 处理替换批次 149/252，包含 10 个片段
2025-07-08 17:06:23 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:06:23 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:06:24 [INFO] 替换片段 395: 使用辅助视频 4739.75-4740.46秒
2025-07-08 17:06:24 [INFO] 替换片段 452: 使用辅助视频 4745.21-4746.88秒
2025-07-08 17:06:25 [INFO] 替换片段 166: 使用辅助视频 4746.88-4748.21秒
2025-07-08 17:06:25 [INFO] 替换片段 179: 使用辅助视频 4748.21-4749.29秒
2025-07-08 17:06:25 [INFO] 替换片段 115: 使用辅助视频 4749.29-4751.54秒
2025-07-08 17:06:26 [INFO] 替换片段 19: 使用辅助视频 4751.54-4752.38秒
2025-07-08 17:06:26 [INFO] 替换片段 45: 使用辅助视频 4752.38-4753.12秒
2025-07-08 17:06:26 [INFO] 替换片段 316: 使用辅助视频 4753.12-4754.00秒
2025-07-08 17:06:27 [INFO] 替换片段 470: 使用辅助视频 4756.25-4757.21秒
2025-07-08 17:06:27 [INFO] 替换片段 12: 使用辅助视频 4757.21-4758.75秒
2025-07-08 17:06:27 [INFO] 处理替换批次 150/252，包含 10 个片段
2025-07-08 17:06:27 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:06:27 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:06:28 [INFO] 替换片段 320: 使用辅助视频 4758.75-4760.92秒
2025-07-08 17:06:28 [INFO] 替换片段 66: 使用辅助视频 4760.92-4762.92秒
2025-07-08 17:06:28 [INFO] 替换片段 449: 使用辅助视频 4762.92-4764.29秒
2025-07-08 17:06:28 [INFO] 替换片段 197: 使用辅助视频 4764.29-4765.00秒
2025-07-08 17:06:28 [INFO] 替换片段 117: 使用辅助视频 4765.00-4767.42秒
2025-07-08 17:06:29 [INFO] 替换片段 485: 使用辅助视频 4767.42-4768.33秒
2025-07-08 17:06:29 [INFO] 替换片段 189: 使用辅助视频 4768.33-4769.67秒
2025-07-08 17:06:29 [INFO] 替换片段 53: 使用辅助视频 4774.25-4776.00秒
2025-07-08 17:06:30 [INFO] 替换片段 99: 使用辅助视频 4776.00-4777.50秒
2025-07-08 17:06:30 [INFO] 替换片段 279: 使用辅助视频 4777.50-4779.54秒
2025-07-08 17:06:30 [INFO] 处理替换批次 151/252，包含 10 个片段
2025-07-08 17:06:30 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:06:30 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:06:31 [INFO] 替换片段 303: 使用辅助视频 4779.54-4787.12秒
2025-07-08 17:06:31 [INFO] 替换片段 472: 使用辅助视频 4787.12-4788.67秒
2025-07-08 17:06:31 [INFO] 替换片段 63: 使用辅助视频 4788.67-4791.12秒
2025-07-08 17:06:32 [INFO] 替换片段 279: 使用辅助视频 4791.12-4793.29秒
2025-07-08 17:06:32 [INFO] 替换片段 321: 使用辅助视频 4793.29-4794.50秒
2025-07-08 17:06:32 [INFO] 替换片段 281: 使用辅助视频 4794.50-4796.17秒
2025-07-08 17:06:33 [INFO] 替换片段 279: 使用辅助视频 4796.17-4797.17秒
2025-07-08 17:06:33 [INFO] 替换片段 7: 使用辅助视频 4797.17-4800.75秒
2025-07-08 17:06:34 [INFO] 替换片段 335: 使用辅助视频 4800.75-4804.00秒
2025-07-08 17:06:34 [INFO] 替换片段 222: 使用辅助视频 4804.00-4805.25秒
2025-07-08 17:06:34 [INFO] 处理替换批次 152/252，包含 10 个片段
2025-07-08 17:06:34 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:06:35 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:06:36 [INFO] 替换片段 131: 使用辅助视频 4805.25-4807.04秒
2025-07-08 17:06:36 [INFO] 替换片段 204: 使用辅助视频 4807.04-4808.79秒
2025-07-08 17:06:36 [INFO] 替换片段 102: 使用辅助视频 4808.79-4810.00秒
2025-07-08 17:06:36 [INFO] 替换片段 348: 使用辅助视频 4810.00-4810.62秒
2025-07-08 17:06:36 [INFO] 替换片段 282: 使用辅助视频 4810.62-4811.25秒
2025-07-08 17:06:37 [INFO] 替换片段 339: 使用辅助视频 4811.25-4812.33秒
2025-07-08 17:06:37 [INFO] 替换片段 176: 使用辅助视频 4812.33-4814.92秒
2025-07-08 17:06:37 [INFO] 替换片段 121: 使用辅助视频 4814.92-4815.79秒
2025-07-08 17:06:37 [INFO] 替换片段 313: 使用辅助视频 4815.79-4816.71秒
2025-07-08 17:06:37 [INFO] 替换片段 98: 使用辅助视频 4816.71-4817.33秒
2025-07-08 17:06:37 [INFO] 处理替换批次 153/252，包含 10 个片段
2025-07-08 17:06:37 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:06:38 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:06:38 [INFO] 替换片段 23: 使用辅助视频 4821.04-4821.88秒
2025-07-08 17:06:38 [INFO] 替换片段 460: 使用辅助视频 4821.88-4824.88秒
2025-07-08 17:06:39 [INFO] 替换片段 362: 使用辅助视频 4824.88-4825.88秒
2025-07-08 17:06:39 [INFO] 替换片段 199: 使用辅助视频 4825.88-4827.04秒
2025-07-08 17:06:39 [INFO] 替换片段 412: 使用辅助视频 4827.04-4828.33秒
2025-07-08 17:06:39 [INFO] 替换片段 177: 使用辅助视频 4828.33-4828.96秒
2025-07-08 17:06:40 [INFO] 替换片段 120: 使用辅助视频 4828.96-4831.21秒
2025-07-08 17:06:40 [INFO] 替换片段 459: 使用辅助视频 4831.21-4832.88秒
2025-07-08 17:06:40 [INFO] 替换片段 106: 使用辅助视频 4832.88-4834.12秒
2025-07-08 17:06:40 [INFO] 替换片段 187: 使用辅助视频 4834.12-4835.54秒
2025-07-08 17:06:40 [INFO] 处理替换批次 154/252，包含 10 个片段
2025-07-08 17:06:40 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:06:41 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:06:41 [INFO] 替换片段 382: 使用辅助视频 4835.54-4836.33秒
2025-07-08 17:06:41 [INFO] 替换片段 454: 使用辅助视频 4836.33-4837.67秒
2025-07-08 17:06:42 [INFO] 替换片段 35: 使用辅助视频 4837.67-4839.58秒
2025-07-08 17:06:42 [INFO] 替换片段 156: 使用辅助视频 4839.58-4842.62秒
2025-07-08 17:06:42 [INFO] 替换片段 107: 使用辅助视频 4842.62-4848.92秒
2025-07-08 17:06:43 [INFO] 替换片段 372: 使用辅助视频 4848.92-4851.92秒
2025-07-08 17:06:43 [INFO] 替换片段 485: 使用辅助视频 4851.92-4853.96秒
2025-07-08 17:06:43 [INFO] 替换片段 302: 使用辅助视频 4853.96-4855.67秒
2025-07-08 17:06:44 [INFO] 替换片段 438: 使用辅助视频 4855.67-4857.79秒
2025-07-08 17:06:44 [INFO] 替换片段 239: 使用辅助视频 4857.79-4861.17秒
2025-07-08 17:06:44 [INFO] 处理替换批次 155/252，包含 10 个片段
2025-07-08 17:06:44 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:06:45 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:06:45 [INFO] 替换片段 102: 使用辅助视频 4861.17-4866.42秒
2025-07-08 17:06:45 [INFO] 替换片段 96: 使用辅助视频 4866.42-4869.25秒
2025-07-08 17:06:46 [INFO] 替换片段 104: 使用辅助视频 4869.25-4880.00秒
2025-07-08 17:06:46 [INFO] 替换片段 288: 使用辅助视频 4880.00-4880.92秒
2025-07-08 17:06:47 [INFO] 替换片段 342: 使用辅助视频 4880.92-4885.29秒
2025-07-08 17:06:47 [INFO] 替换片段 412: 使用辅助视频 4885.29-4887.00秒
2025-07-08 17:06:47 [INFO] 替换片段 344: 使用辅助视频 4887.00-4888.62秒
2025-07-08 17:06:47 [INFO] 替换片段 217: 使用辅助视频 4888.62-4891.00秒
2025-07-08 17:06:48 [INFO] 替换片段 7: 使用辅助视频 4891.00-4893.75秒
2025-07-08 17:06:48 [INFO] 替换片段 305: 使用辅助视频 4893.75-4895.54秒
2025-07-08 17:06:48 [INFO] 处理替换批次 156/252，包含 10 个片段
2025-07-08 17:06:48 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:06:49 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:06:50 [INFO] 替换片段 401: 使用辅助视频 4895.54-4899.21秒
2025-07-08 17:06:50 [INFO] 替换片段 196: 使用辅助视频 4899.21-4902.79秒
2025-07-08 17:06:51 [INFO] 替换片段 50: 使用辅助视频 4902.79-4905.33秒
2025-07-08 17:06:51 [INFO] 替换片段 169: 使用辅助视频 4905.33-4911.62秒
2025-07-08 17:06:52 [INFO] 替换片段 170: 使用辅助视频 4911.62-4937.21秒
2025-07-08 17:06:52 [INFO] 替换片段 348: 使用辅助视频 4937.21-4940.58秒
2025-07-08 17:06:52 [INFO] 替换片段 391: 使用辅助视频 4940.58-4947.04秒
2025-07-08 17:06:53 [INFO] 替换片段 348: 使用辅助视频 4947.04-4950.21秒
2025-07-08 17:06:53 [INFO] 替换片段 286: 使用辅助视频 4950.21-4953.83秒
2025-07-08 17:06:54 [INFO] 替换片段 438: 使用辅助视频 4953.83-4955.54秒
2025-07-08 17:06:54 [INFO] 处理替换批次 157/252，包含 10 个片段
2025-07-08 17:06:54 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:06:54 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:06:55 [INFO] 替换片段 218: 使用辅助视频 4955.54-4958.04秒
2025-07-08 17:06:55 [INFO] 替换片段 137: 使用辅助视频 4958.04-4963.04秒
2025-07-08 17:06:55 [INFO] 替换片段 254: 使用辅助视频 4963.04-4971.04秒
2025-07-08 17:06:56 [INFO] 替换片段 289: 使用辅助视频 4971.04-4976.04秒
2025-07-08 17:06:56 [INFO] 替换片段 410: 使用辅助视频 4976.04-4978.46秒
2025-07-08 17:06:56 [INFO] 替换片段 289: 使用辅助视频 4978.46-4979.71秒
2025-07-08 17:06:56 [INFO] 替换片段 13: 使用辅助视频 4979.71-4981.17秒
2025-07-08 17:06:57 [INFO] 替换片段 289: 使用辅助视频 4981.17-4984.79秒
2025-07-08 17:06:57 [INFO] 替换片段 290: 使用辅助视频 4984.79-4986.88秒
2025-07-08 17:06:57 [INFO] 替换片段 289: 使用辅助视频 4986.88-4988.71秒
2025-07-08 17:06:57 [INFO] 处理替换批次 158/252，包含 10 个片段
2025-07-08 17:06:57 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:06:58 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:06:58 [INFO] 替换片段 286: 使用辅助视频 4988.71-4990.42秒
2025-07-08 17:06:59 [INFO] 替换片段 368: 使用辅助视频 4990.42-4991.54秒
2025-07-08 17:06:59 [INFO] 替换片段 2: 使用辅助视频 4991.54-4993.17秒
2025-07-08 17:06:59 [INFO] 替换片段 472: 使用辅助视频 4993.17-4994.79秒
2025-07-08 17:06:59 [INFO] 替换片段 374: 使用辅助视频 4994.79-4997.58秒
2025-07-08 17:07:00 [INFO] 替换片段 154: 使用辅助视频 4997.58-4999.04秒
2025-07-08 17:07:00 [INFO] 替换片段 2: 使用辅助视频 4999.04-5001.12秒
2025-07-08 17:07:00 [INFO] 替换片段 150: 使用辅助视频 5001.12-5004.79秒
2025-07-08 17:07:01 [INFO] 替换片段 349: 使用辅助视频 5004.79-5009.58秒
2025-07-08 17:07:01 [INFO] 替换片段 256: 使用辅助视频 5009.58-5013.71秒
2025-07-08 17:07:02 [INFO] 处理替换批次 159/252，包含 10 个片段
2025-07-08 17:07:02 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:07:02 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:07:03 [INFO] 替换片段 291: 使用辅助视频 5013.71-5015.62秒
2025-07-08 17:07:03 [INFO] 替换片段 471: 使用辅助视频 5015.62-5018.50秒
2025-07-08 17:07:04 [INFO] 替换片段 295: 使用辅助视频 5018.50-5026.25秒
2025-07-08 17:07:04 [INFO] 替换片段 392: 使用辅助视频 5026.25-5028.46秒
2025-07-08 17:07:04 [INFO] 替换片段 255: 使用辅助视频 5028.46-5030.04秒
2025-07-08 17:07:05 [INFO] 替换片段 190: 使用辅助视频 5030.04-5031.54秒
2025-07-08 17:07:05 [INFO] 替换片段 48: 使用辅助视频 5031.54-5034.25秒
2025-07-08 17:07:05 [INFO] 替换片段 91: 使用辅助视频 5034.25-5038.83秒
2025-07-08 17:07:06 [INFO] 替换片段 212: 使用辅助视频 5038.83-5040.42秒
2025-07-08 17:07:06 [INFO] 替换片段 156: 使用辅助视频 5040.42-5044.17秒
2025-07-08 17:07:06 [INFO] 处理替换批次 160/252，包含 10 个片段
2025-07-08 17:07:06 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:07:07 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:07:07 [INFO] 替换片段 193: 使用辅助视频 5044.17-5045.79秒
2025-07-08 17:07:08 [INFO] 替换片段 14: 使用辅助视频 5045.79-5047.62秒
2025-07-08 17:07:09 [INFO] 替换片段 407: 使用辅助视频 5047.62-5048.92秒
2025-07-08 17:07:09 [INFO] 替换片段 147: 使用辅助视频 5052.17-5054.79秒
2025-07-08 17:07:10 [INFO] 替换片段 397: 使用辅助视频 5054.79-5056.33秒
2025-07-08 17:07:10 [INFO] 替换片段 296: 使用辅助视频 5056.33-5059.54秒
2025-07-08 17:07:11 [INFO] 替换片段 459: 使用辅助视频 5059.54-5063.04秒
2025-07-08 17:07:12 [INFO] 替换片段 298: 使用辅助视频 5063.04-5066.00秒
2025-07-08 17:07:13 [INFO] 替换片段 287: 使用辅助视频 5066.00-5067.71秒
2025-07-08 17:07:13 [INFO] 替换片段 463: 使用辅助视频 5067.71-5072.62秒
2025-07-08 17:07:13 [INFO] 处理替换批次 161/252，包含 10 个片段
2025-07-08 17:07:13 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:07:14 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:07:14 [INFO] 替换片段 310: 使用辅助视频 5072.62-5077.29秒
2025-07-08 17:07:14 [INFO] 替换片段 348: 使用辅助视频 5077.29-5084.92秒
2025-07-08 17:07:15 [INFO] 替换片段 100: 使用辅助视频 5084.92-5086.38秒
2025-07-08 17:07:15 [INFO] 替换片段 118: 使用辅助视频 5086.38-5087.88秒
2025-07-08 17:07:16 [INFO] 替换片段 479: 使用辅助视频 5087.88-5103.67秒
2025-07-08 17:07:16 [INFO] 替换片段 103: 使用辅助视频 5103.67-5105.00秒
2025-07-08 17:07:16 [INFO] 替换片段 206: 使用辅助视频 5105.00-5107.25秒
2025-07-08 17:07:17 [INFO] 替换片段 146: 使用辅助视频 5107.25-5109.12秒
2025-07-08 17:07:17 [INFO] 替换片段 120: 使用辅助视频 5109.12-5114.92秒
2025-07-08 17:07:18 [INFO] 替换片段 301: 使用辅助视频 5114.92-5116.08秒
2025-07-08 17:07:18 [INFO] 处理替换批次 162/252，包含 10 个片段
2025-07-08 17:07:18 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:07:19 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:07:19 [INFO] 替换片段 300: 使用辅助视频 5116.08-5120.79秒
2025-07-08 17:07:19 [INFO] 替换片段 104: 使用辅助视频 5120.79-5122.38秒
2025-07-08 17:07:20 [INFO] 替换片段 304: 使用辅助视频 5122.38-5123.79秒
2025-07-08 17:07:20 [INFO] 替换片段 132: 使用辅助视频 5123.79-5124.79秒
2025-07-08 17:07:20 [INFO] 替换片段 420: 使用辅助视频 5124.79-5126.67秒
2025-07-08 17:07:21 [INFO] 替换片段 113: 使用辅助视频 5126.67-5130.71秒
2025-07-08 17:07:22 [INFO] 替换片段 232: 使用辅助视频 5130.71-5133.25秒
2025-07-08 17:07:22 [INFO] 替换片段 103: 使用辅助视频 5133.25-5136.96秒
2025-07-08 17:07:23 [INFO] 替换片段 350: 使用辅助视频 5136.96-5139.42秒
2025-07-08 17:07:24 [INFO] 替换片段 376: 使用辅助视频 5139.42-5140.88秒
2025-07-08 17:07:24 [INFO] 处理替换批次 163/252，包含 10 个片段
2025-07-08 17:07:24 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:07:24 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:07:25 [INFO] 替换片段 331: 使用辅助视频 5140.88-5143.25秒
2025-07-08 17:07:25 [INFO] 替换片段 312: 使用辅助视频 5143.25-5150.83秒
2025-07-08 17:07:26 [INFO] 替换片段 376: 使用辅助视频 5150.83-5153.71秒
2025-07-08 17:07:27 [INFO] 替换片段 319: 使用辅助视频 5153.71-5155.21秒
2025-07-08 17:07:27 [INFO] 替换片段 310: 使用辅助视频 5155.21-5158.25秒
2025-07-08 17:07:27 [INFO] 替换片段 109: 使用辅助视频 5158.25-5160.29秒
2025-07-08 17:07:28 [INFO] 替换片段 314: 使用辅助视频 5160.29-5162.75秒
2025-07-08 17:07:28 [INFO] 替换片段 315: 使用辅助视频 5162.75-5165.46秒
2025-07-08 17:07:29 [INFO] 替换片段 104: 使用辅助视频 5165.46-5166.88秒
2025-07-08 17:07:29 [INFO] 替换片段 337: 使用辅助视频 5166.88-5168.79秒
2025-07-08 17:07:29 [INFO] 处理替换批次 164/252，包含 10 个片段
2025-07-08 17:07:29 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:07:30 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:07:30 [INFO] 替换片段 318: 使用辅助视频 5168.79-5171.00秒
2025-07-08 17:07:31 [INFO] 替换片段 132: 使用辅助视频 5171.00-5172.50秒
2025-07-08 17:07:31 [INFO] 替换片段 316: 使用辅助视频 5172.50-5173.92秒
2025-07-08 17:07:31 [INFO] 替换片段 321: 使用辅助视频 5173.92-5176.58秒
2025-07-08 17:07:32 [INFO] 替换片段 187: 使用辅助视频 5176.58-5181.46秒
2025-07-08 17:07:32 [INFO] 替换片段 323: 使用辅助视频 5181.46-5182.50秒
2025-07-08 17:07:33 [INFO] 替换片段 451: 使用辅助视频 5182.50-5184.83秒
2025-07-08 17:07:33 [INFO] 替换片段 66: 使用辅助视频 5184.83-5186.33秒
2025-07-08 17:07:33 [INFO] 替换片段 456: 使用辅助视频 5186.33-5187.00秒
2025-07-08 17:07:34 [INFO] 替换片段 337: 使用辅助视频 5187.00-5188.79秒
2025-07-08 17:07:34 [INFO] 处理替换批次 165/252，包含 10 个片段
2025-07-08 17:07:34 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:07:34 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:07:35 [INFO] 替换片段 282: 使用辅助视频 5188.79-5189.50秒
2025-07-08 17:07:35 [INFO] 替换片段 203: 使用辅助视频 5189.50-5190.42秒
2025-07-08 17:07:35 [INFO] 替换片段 194: 使用辅助视频 5190.42-5191.33秒
2025-07-08 17:07:35 [INFO] 替换片段 106: 使用辅助视频 5191.33-5191.96秒
2025-07-08 17:07:35 [INFO] 替换片段 240: 使用辅助视频 5191.96-5192.79秒
2025-07-08 17:07:36 [INFO] 替换片段 112: 使用辅助视频 5195.29-5196.00秒
2025-07-08 17:07:37 [INFO] 替换片段 130: 使用辅助视频 5198.96-5201.38秒
2025-07-08 17:07:37 [INFO] 替换片段 116: 使用辅助视频 5201.38-5202.29秒
2025-07-08 17:07:37 [INFO] 替换片段 312: 使用辅助视频 5202.29-5203.25秒
2025-07-08 17:07:38 [INFO] 替换片段 420: 使用辅助视频 5203.25-5204.04秒
2025-07-08 17:07:38 [INFO] 处理替换批次 166/252，包含 10 个片段
2025-07-08 17:07:38 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:07:38 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:07:39 [INFO] 替换片段 49: 使用辅助视频 5204.04-5204.88秒
2025-07-08 17:07:39 [INFO] 替换片段 104: 使用辅助视频 5204.88-5205.67秒
2025-07-08 17:07:39 [INFO] 替换片段 289: 使用辅助视频 5205.67-5206.29秒
2025-07-08 17:07:39 [INFO] 替换片段 120: 使用辅助视频 5206.29-5207.08秒
2025-07-08 17:07:39 [INFO] 替换片段 94: 使用辅助视频 5207.08-5208.29秒
2025-07-08 17:07:40 [INFO] 替换片段 154: 使用辅助视频 5208.29-5209.12秒
2025-07-08 17:07:40 [INFO] 替换片段 247: 使用辅助视频 5209.12-5209.79秒
2025-07-08 17:07:40 [INFO] 替换片段 326: 使用辅助视频 5209.79-5211.42秒
2025-07-08 17:07:40 [INFO] 替换片段 327: 使用辅助视频 5211.42-5212.71秒
2025-07-08 17:07:40 [INFO] 替换片段 474: 使用辅助视频 5212.71-5213.42秒
2025-07-08 17:07:41 [INFO] 处理替换批次 167/252，包含 10 个片段
2025-07-08 17:07:41 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:07:41 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:07:41 [INFO] 替换片段 329: 使用辅助视频 5213.42-5214.62秒
2025-07-08 17:07:42 [INFO] 替换片段 406: 使用辅助视频 5214.62-5217.62秒
2025-07-08 17:07:42 [INFO] 替换片段 104: 使用辅助视频 5217.62-5218.33秒
2025-07-08 17:07:42 [INFO] 替换片段 371: 使用辅助视频 5218.33-5219.12秒
2025-07-08 17:07:43 [INFO] 替换片段 451: 使用辅助视频 5219.12-5219.75秒
2025-07-08 17:07:43 [INFO] 替换片段 371: 使用辅助视频 5219.75-5221.21秒
2025-07-08 17:07:43 [INFO] 替换片段 424: 使用辅助视频 5221.21-5223.71秒
2025-07-08 17:07:43 [INFO] 替换片段 150: 使用辅助视频 5223.71-5224.79秒
2025-07-08 17:07:44 [INFO] 替换片段 297: 使用辅助视频 5224.79-5227.12秒
2025-07-08 17:07:44 [INFO] 替换片段 357: 使用辅助视频 5227.12-5228.67秒
2025-07-08 17:07:44 [INFO] 处理替换批次 168/252，包含 10 个片段
2025-07-08 17:07:44 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:07:45 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:07:45 [INFO] 替换片段 38: 使用辅助视频 5228.67-5243.25秒
2025-07-08 17:07:45 [INFO] 替换片段 285: 使用辅助视频 5243.25-5246.54秒
2025-07-08 17:07:46 [INFO] 替换片段 397: 使用辅助视频 5246.54-5248.54秒
2025-07-08 17:07:46 [INFO] 替换片段 411: 使用辅助视频 5248.54-5249.58秒
2025-07-08 17:07:47 [INFO] 替换片段 357: 使用辅助视频 5249.58-5251.58秒
2025-07-08 17:07:47 [INFO] 替换片段 263: 使用辅助视频 5251.58-5252.25秒
2025-07-08 17:07:47 [INFO] 替换片段 179: 使用辅助视频 5252.25-5252.92秒
2025-07-08 17:07:47 [INFO] 替换片段 99: 使用辅助视频 5252.92-5254.88秒
2025-07-08 17:07:48 [INFO] 替换片段 407: 使用辅助视频 5254.88-5255.67秒
2025-07-08 17:07:48 [INFO] 替换片段 291: 使用辅助视频 5255.67-5256.54秒
2025-07-08 17:07:48 [INFO] 处理替换批次 169/252，包含 10 个片段
2025-07-08 17:07:48 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:07:48 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:07:49 [INFO] 替换片段 217: 使用辅助视频 5256.54-5257.38秒
2025-07-08 17:07:49 [INFO] 替换片段 131: 使用辅助视频 5257.38-5258.92秒
2025-07-08 17:07:49 [INFO] 替换片段 226: 使用辅助视频 5258.92-5260.08秒
2025-07-08 17:07:49 [INFO] 替换片段 185: 使用辅助视频 5263.79-5264.67秒
2025-07-08 17:07:50 [INFO] 替换片段 50: 使用辅助视频 5264.67-5265.88秒
2025-07-08 17:07:50 [INFO] 替换片段 117: 使用辅助视频 5265.88-5270.21秒
2025-07-08 17:07:50 [INFO] 替换片段 463: 使用辅助视频 5270.21-5272.17秒
2025-07-08 17:07:51 [INFO] 替换片段 2: 使用辅助视频 5272.17-5274.67秒
2025-07-08 17:07:51 [INFO] 替换片段 424: 使用辅助视频 5274.67-5275.71秒
2025-07-08 17:07:52 [INFO] 替换片段 349: 使用辅助视频 5275.71-5276.67秒
2025-07-08 17:07:52 [INFO] 处理替换批次 170/252，包含 10 个片段
2025-07-08 17:07:52 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:07:52 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:07:53 [INFO] 替换片段 147: 使用辅助视频 5276.67-5277.92秒
2025-07-08 17:07:53 [INFO] 替换片段 458: 使用辅助视频 5277.92-5284.79秒
2025-07-08 17:07:53 [INFO] 替换片段 226: 使用辅助视频 5284.79-5290.12秒
2025-07-08 17:07:54 [INFO] 替换片段 269: 使用辅助视频 5290.12-5291.79秒
2025-07-08 17:07:54 [INFO] 替换片段 320: 使用辅助视频 5298.67-5302.79秒
2025-07-08 17:07:56 [INFO] 替换片段 254: 使用辅助视频 5302.79-5303.71秒
2025-07-08 17:07:56 [INFO] 替换片段 108: 使用辅助视频 5303.71-5304.58秒
2025-07-08 17:07:56 [INFO] 替换片段 66: 使用辅助视频 5304.58-5306.38秒
2025-07-08 17:07:56 [INFO] 替换片段 120: 使用辅助视频 5306.38-5308.38秒
2025-07-08 17:07:57 [INFO] 替换片段 208: 使用辅助视频 5308.38-5309.08秒
2025-07-08 17:07:57 [INFO] 处理替换批次 171/252，包含 10 个片段
2025-07-08 17:07:57 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:07:58 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:07:58 [INFO] 替换片段 130: 使用辅助视频 5309.08-5309.88秒
2025-07-08 17:07:58 [INFO] 替换片段 19: 使用辅助视频 5309.88-5311.46秒
2025-07-08 17:07:58 [INFO] 替换片段 392: 使用辅助视频 5311.46-5312.71秒
2025-07-08 17:07:59 [INFO] 替换片段 137: 使用辅助视频 5312.71-5313.79秒
2025-07-08 17:07:59 [INFO] 替换片段 290: 使用辅助视频 5313.79-5314.62秒
2025-07-08 17:07:59 [INFO] 替换片段 411: 使用辅助视频 5314.62-5315.79秒
2025-07-08 17:07:59 [INFO] 替换片段 349: 使用辅助视频 5315.79-5317.12秒
2025-07-08 17:08:00 [INFO] 替换片段 153: 使用辅助视频 5317.12-5318.08秒
2025-07-08 17:08:00 [INFO] 替换片段 397: 使用辅助视频 5318.08-5318.75秒
2025-07-08 17:08:00 [INFO] 替换片段 240: 使用辅助视频 5318.75-5319.54秒
2025-07-08 17:08:00 [INFO] 处理替换批次 172/252，包含 10 个片段
2025-07-08 17:08:00 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:08:01 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:08:01 [INFO] 替换片段 410: 使用辅助视频 5319.54-5320.58秒
2025-07-08 17:08:01 [INFO] 替换片段 174: 使用辅助视频 5320.58-5321.83秒
2025-07-08 17:08:01 [INFO] 替换片段 451: 使用辅助视频 5321.83-5323.83秒
2025-07-08 17:08:02 [INFO] 替换片段 174: 使用辅助视频 5323.83-5324.88秒
2025-07-08 17:08:02 [INFO] 替换片段 475: 使用辅助视频 5324.88-5325.96秒
2025-07-08 17:08:02 [INFO] 替换片段 430: 使用辅助视频 5325.96-5327.54秒
2025-07-08 17:08:02 [INFO] 替换片段 107: 使用辅助视频 5327.54-5332.50秒
2025-07-08 17:08:03 [INFO] 替换片段 102: 使用辅助视频 5332.50-5333.58秒
2025-07-08 17:08:03 [INFO] 替换片段 177: 使用辅助视频 5333.58-5335.67秒
2025-07-08 17:08:04 [INFO] 替换片段 334: 使用辅助视频 5335.67-5352.83秒
2025-07-08 17:08:04 [INFO] 处理替换批次 173/252，包含 10 个片段
2025-07-08 17:08:04 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:08:04 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:08:05 [INFO] 替换片段 198: 使用辅助视频 5352.83-5355.17秒
2025-07-08 17:08:05 [INFO] 替换片段 335: 使用辅助视频 5355.17-5359.96秒
2025-07-08 17:08:05 [INFO] 替换片段 339: 使用辅助视频 5359.96-5363.25秒
2025-07-08 17:08:06 [INFO] 替换片段 342: 使用辅助视频 5363.25-5365.54秒
2025-07-08 17:08:07 [INFO] 替换片段 394: 使用辅助视频 5365.54-5369.29秒
2025-07-08 17:08:07 [INFO] 替换片段 141: 使用辅助视频 5369.29-5370.58秒
2025-07-08 17:08:08 [INFO] 替换片段 108: 使用辅助视频 5370.58-5373.50秒
2025-07-08 17:08:08 [INFO] 替换片段 356: 使用辅助视频 5377.75-5380.79秒
2025-07-08 17:08:09 [INFO] 替换片段 331: 使用辅助视频 5380.79-5385.00秒
2025-07-08 17:08:09 [INFO] 替换片段 109: 使用辅助视频 5385.00-5390.96秒
2025-07-08 17:08:09 [INFO] 处理替换批次 174/252，包含 10 个片段
2025-07-08 17:08:09 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:08:10 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:08:10 [INFO] 替换片段 320: 使用辅助视频 5390.96-5395.08秒
2025-07-08 17:08:11 [INFO] 替换片段 116: 使用辅助视频 5395.08-5398.54秒
2025-07-08 17:08:12 [INFO] 替换片段 197: 使用辅助视频 5404.71-5406.50秒
2025-07-08 17:08:12 [INFO] 替换片段 451: 使用辅助视频 5406.50-5408.46秒
2025-07-08 17:08:12 [INFO] 替换片段 315: 使用辅助视频 5408.46-5409.96秒
2025-07-08 17:08:13 [INFO] 替换片段 64: 使用辅助视频 5409.96-5411.25秒
2025-07-08 17:08:13 [INFO] 替换片段 412: 使用辅助视频 5411.25-5414.62秒
2025-07-08 17:08:14 [INFO] 替换片段 177: 使用辅助视频 5414.62-5425.54秒
2025-07-08 17:08:14 [INFO] 替换片段 438: 使用辅助视频 5425.54-5428.58秒
2025-07-08 17:08:15 [INFO] 替换片段 410: 使用辅助视频 5428.58-5431.58秒
2025-07-08 17:08:15 [INFO] 处理替换批次 175/252，包含 10 个片段
2025-07-08 17:08:15 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:08:16 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:08:16 [INFO] 替换片段 118: 使用辅助视频 5431.58-5445.00秒
2025-07-08 17:08:16 [INFO] 替换片段 98: 使用辅助视频 5445.00-5446.92秒
2025-07-08 17:08:17 [INFO] 替换片段 7: 使用辅助视频 5446.92-5448.58秒
2025-07-08 17:08:17 [INFO] 替换片段 219: 使用辅助视频 5448.58-5450.29秒
2025-07-08 17:08:17 [INFO] 替换片段 120: 使用辅助视频 5450.29-5453.67秒
2025-07-08 17:08:18 [INFO] 替换片段 381: 使用辅助视频 5453.67-5455.67秒
2025-07-08 17:08:19 [INFO] 替换片段 318: 使用辅助视频 5455.67-5456.79秒
2025-07-08 17:08:19 [INFO] 替换片段 219: 使用辅助视频 5456.79-5460.62秒
2025-07-08 17:08:19 [INFO] 替换片段 38: 使用辅助视频 5460.62-5468.50秒
2025-07-08 17:08:20 [INFO] 替换片段 109: 使用辅助视频 5475.46-5480.04秒
2025-07-08 17:08:20 [INFO] 处理替换批次 176/252，包含 10 个片段
2025-07-08 17:08:20 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:08:21 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:08:21 [INFO] 替换片段 379: 使用辅助视频 5480.04-5487.71秒
2025-07-08 17:08:21 [INFO] 替换片段 348: 使用辅助视频 5487.71-5495.62秒
2025-07-08 17:08:22 [INFO] 替换片段 255: 使用辅助视频 5495.62-5502.96秒
2025-07-08 17:08:22 [INFO] 替换片段 177: 使用辅助视频 5502.96-5519.08秒
2025-07-08 17:08:22 [INFO] 替换片段 404: 使用辅助视频 5519.08-5535.25秒
2025-07-08 17:08:23 [INFO] 替换片段 49: 使用辅助视频 5535.25-5538.00秒
2025-07-08 17:08:23 [INFO] 替换片段 407: 使用辅助视频 5538.00-5540.33秒
2025-07-08 17:08:24 [INFO] 替换片段 218: 使用辅助视频 5540.33-5547.12秒
2025-07-08 17:08:24 [INFO] 替换片段 345: 使用辅助视频 5547.12-5554.25秒
2025-07-08 17:08:24 [INFO] 替换片段 195: 使用辅助视频 5554.25-5560.67秒
2025-07-08 17:08:24 [INFO] 处理替换批次 177/252，包含 10 个片段
2025-07-08 17:08:24 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:08:25 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:08:25 [INFO] 替换片段 27: 使用辅助视频 5560.67-5566.88秒
2025-07-08 17:08:26 [INFO] 替换片段 278: 使用辅助视频 5566.88-5576.38秒
2025-07-08 17:08:26 [INFO] 替换片段 132: 使用辅助视频 5576.38-5581.88秒
2025-07-08 17:08:26 [INFO] 替换片段 128: 使用辅助视频 5581.88-5583.58秒
2025-07-08 17:08:27 [INFO] 替换片段 130: 使用辅助视频 5583.58-5585.79秒
2025-07-08 17:08:27 [INFO] 替换片段 106: 使用辅助视频 5585.79-5588.71秒
2025-07-08 17:08:28 [INFO] 替换片段 353: 使用辅助视频 5588.71-5591.96秒
2025-07-08 17:08:28 [INFO] 替换片段 445: 使用辅助视频 5591.96-5593.54秒
2025-07-08 17:08:29 [INFO] 替换片段 14: 使用辅助视频 5593.54-5596.08秒
2025-07-08 17:08:29 [INFO] 替换片段 141: 使用辅助视频 5596.08-5597.04秒
2025-07-08 17:08:29 [INFO] 处理替换批次 178/252，包含 10 个片段
2025-07-08 17:08:29 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:08:30 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:08:30 [INFO] 替换片段 150: 使用辅助视频 5597.04-5597.96秒
2025-07-08 17:08:31 [INFO] 替换片段 385: 使用辅助视频 5597.96-5600.54秒
2025-07-08 17:08:31 [INFO] 替换片段 54: 使用辅助视频 5600.54-5602.83秒
2025-07-08 17:08:32 [INFO] 替换片段 25: 使用辅助视频 5602.83-5604.08秒
2025-07-08 17:08:32 [INFO] 替换片段 325: 使用辅助视频 5604.08-5605.17秒
2025-07-08 17:08:32 [INFO] 替换片段 405: 使用辅助视频 5605.17-5606.46秒
2025-07-08 17:08:32 [INFO] 替换片段 106: 使用辅助视频 5606.46-5610.62秒
2025-07-08 17:08:33 [INFO] 替换片段 166: 使用辅助视频 5610.62-5611.50秒
2025-07-08 17:08:33 [INFO] 替换片段 38: 使用辅助视频 5611.50-5612.54秒
2025-07-08 17:08:33 [INFO] 替换片段 379: 使用辅助视频 5612.54-5614.50秒
2025-07-08 17:08:34 [INFO] 处理替换批次 179/252，包含 10 个片段
2025-07-08 17:08:34 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:08:34 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:08:35 [INFO] 替换片段 48: 使用辅助视频 5614.50-5616.46秒
2025-07-08 17:08:35 [INFO] 替换片段 127: 使用辅助视频 5616.46-5619.17秒
2025-07-08 17:08:36 [INFO] 替换片段 391: 使用辅助视频 5619.17-5619.88秒
2025-07-08 17:08:36 [INFO] 替换片段 466: 使用辅助视频 5619.88-5622.62秒
2025-07-08 17:08:36 [INFO] 替换片段 164: 使用辅助视频 5622.62-5624.21秒
2025-07-08 17:08:37 [INFO] 替换片段 397: 使用辅助视频 5627.54-5629.62秒
2025-07-08 17:08:37 [INFO] 替换片段 404: 使用辅助视频 5629.62-5630.96秒
2025-07-08 17:08:37 [INFO] 替换片段 202: 使用辅助视频 5630.96-5634.50秒
2025-07-08 17:08:38 [INFO] 替换片段 25: 使用辅助视频 5634.50-5643.04秒
2025-07-08 17:08:38 [INFO] 替换片段 120: 使用辅助视频 5643.04-5644.67秒
2025-07-08 17:08:38 [INFO] 处理替换批次 180/252，包含 10 个片段
2025-07-08 17:08:38 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:08:39 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:08:39 [INFO] 替换片段 106: 使用辅助视频 5644.67-5645.71秒
2025-07-08 17:08:40 [INFO] 替换片段 473: 使用辅助视频 5655.00-5656.79秒
2025-07-08 17:08:40 [INFO] 替换片段 222: 使用辅助视频 5656.79-5659.83秒
2025-07-08 17:08:41 [INFO] 替换片段 126: 使用辅助视频 5659.83-5662.12秒
2025-07-08 17:08:41 [INFO] 替换片段 128: 使用辅助视频 5662.12-5667.62秒
2025-07-08 17:08:42 [INFO] 替换片段 18: 使用辅助视频 5667.62-5674.04秒
2025-07-08 17:08:42 [INFO] 替换片段 223: 使用辅助视频 5674.04-5676.38秒
2025-07-08 17:08:43 [INFO] 替换片段 182: 使用辅助视频 5676.38-5680.71秒
2025-07-08 17:08:43 [INFO] 替换片段 134: 使用辅助视频 5680.71-5685.71秒
2025-07-08 17:08:43 [INFO] 替换片段 1: 使用辅助视频 5685.71-5689.71秒
2025-07-08 17:08:43 [INFO] 处理替换批次 181/252，包含 10 个片段
2025-07-08 17:08:43 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:08:44 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:08:45 [INFO] 替换片段 416: 使用辅助视频 5689.71-5694.92秒
2025-07-08 17:08:45 [INFO] 替换片段 287: 使用辅助视频 5694.92-5698.88秒
2025-07-08 17:08:46 [INFO] 替换片段 162: 使用辅助视频 5698.88-5701.88秒
2025-07-08 17:08:46 [INFO] 替换片段 197: 使用辅助视频 5701.88-5704.42秒
2025-07-08 17:08:47 [INFO] 替换片段 223: 使用辅助视频 5704.42-5706.83秒
2025-07-08 17:08:47 [INFO] 替换片段 348: 使用辅助视频 5706.83-5708.67秒
2025-07-08 17:08:48 [INFO] 替换片段 354: 使用辅助视频 5708.67-5713.21秒
2025-07-08 17:08:48 [INFO] 替换片段 153: 使用辅助视频 5713.21-5719.62秒
2025-07-08 17:08:48 [INFO] 替换片段 257: 使用辅助视频 5719.62-5721.83秒
2025-07-08 17:08:49 [INFO] 替换片段 414: 使用辅助视频 5721.83-5727.12秒
2025-07-08 17:08:49 [INFO] 处理替换批次 182/252，包含 10 个片段
2025-07-08 17:08:49 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:08:49 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:08:50 [INFO] 替换片段 355: 使用辅助视频 5727.12-5741.04秒
2025-07-08 17:08:50 [INFO] 替换片段 338: 使用辅助视频 5741.04-5744.00秒
2025-07-08 17:08:51 [INFO] 替换片段 16: 使用辅助视频 5744.00-5745.54秒
2025-07-08 17:08:51 [INFO] 替换片段 363: 使用辅助视频 5745.54-5747.46秒
2025-07-08 17:08:51 [INFO] 替换片段 295: 使用辅助视频 5747.46-5750.12秒
2025-07-08 17:08:52 [INFO] 替换片段 360: 使用辅助视频 5750.12-5752.92秒
2025-07-08 17:08:52 [INFO] 替换片段 361: 使用辅助视频 5752.92-5755.17秒
2025-07-08 17:08:53 [INFO] 替换片段 472: 使用辅助视频 5755.17-5756.88秒
2025-07-08 17:08:53 [INFO] 替换片段 166: 使用辅助视频 5756.88-5759.17秒
2025-07-08 17:08:54 [INFO] 替换片段 153: 使用辅助视频 5759.17-5761.21秒
2025-07-08 17:08:54 [INFO] 处理替换批次 183/252，包含 10 个片段
2025-07-08 17:08:54 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:08:54 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:08:55 [INFO] 替换片段 388: 使用辅助视频 5761.21-5768.92秒
2025-07-08 17:08:55 [INFO] 替换片段 337: 使用辅助视频 5768.92-5774.17秒
2025-07-08 17:08:55 [INFO] 替换片段 135: 使用辅助视频 5774.17-5778.21秒
2025-07-08 17:08:56 [INFO] 替换片段 422: 使用辅助视频 5778.21-5780.50秒
2025-07-08 17:08:57 [INFO] 替换片段 470: 使用辅助视频 5780.50-5783.04秒
2025-07-08 17:08:57 [INFO] 替换片段 216: 使用辅助视频 5783.04-5784.62秒
2025-07-08 17:08:57 [INFO] 替换片段 313: 使用辅助视频 5784.62-5787.21秒
2025-07-08 17:08:58 [INFO] 替换片段 70: 使用辅助视频 5787.21-5788.67秒
2025-07-08 17:08:58 [INFO] 替换片段 338: 使用辅助视频 5788.67-5790.67秒
2025-07-08 17:08:59 [INFO] 替换片段 111: 使用辅助视频 5790.67-5793.54秒
2025-07-08 17:08:59 [INFO] 处理替换批次 184/252，包含 10 个片段
2025-07-08 17:08:59 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:08:59 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:09:00 [INFO] 替换片段 318: 使用辅助视频 5793.54-5795.96秒
2025-07-08 17:09:00 [INFO] 替换片段 468: 使用辅助视频 5795.96-5797.83秒
2025-07-08 17:09:01 [INFO] 替换片段 451: 使用辅助视频 5797.83-5803.67秒
2025-07-08 17:09:01 [INFO] 替换片段 205: 使用辅助视频 5803.67-5805.92秒
2025-07-08 17:09:02 [INFO] 替换片段 318: 使用辅助视频 5805.92-5809.00秒
2025-07-08 17:09:02 [INFO] 替换片段 57: 使用辅助视频 5809.00-5813.62秒
2025-07-08 17:09:03 [INFO] 替换片段 318: 使用辅助视频 5813.62-5815.79秒
2025-07-08 17:09:03 [INFO] 替换片段 399: 使用辅助视频 5815.79-5818.00秒
2025-07-08 17:09:04 [INFO] 替换片段 288: 使用辅助视频 5818.00-5819.75秒
2025-07-08 17:09:04 [INFO] 替换片段 205: 使用辅助视频 5819.75-5834.88秒
2025-07-08 17:09:04 [INFO] 处理替换批次 185/252，包含 10 个片段
2025-07-08 17:09:04 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:09:05 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:09:05 [INFO] 替换片段 139: 使用辅助视频 5834.88-5837.88秒
2025-07-08 17:09:06 [INFO] 替换片段 342: 使用辅助视频 5837.88-5844.04秒
2025-07-08 17:09:06 [INFO] 替换片段 284: 使用辅助视频 5844.04-5846.38秒
2025-07-08 17:09:07 [INFO] 替换片段 447: 使用辅助视频 5846.38-5849.04秒
2025-07-08 17:09:07 [INFO] 替换片段 173: 使用辅助视频 5849.04-5850.42秒
2025-07-08 17:09:08 [INFO] 替换片段 50: 使用辅助视频 5850.42-5851.62秒
2025-07-08 17:09:08 [INFO] 替换片段 173: 使用辅助视频 5851.62-5859.83秒
2025-07-08 17:09:08 [INFO] 替换片段 349: 使用辅助视频 5859.83-5860.83秒
2025-07-08 17:09:09 [INFO] 替换片段 153: 使用辅助视频 5860.83-5866.83秒
2025-07-08 17:09:09 [INFO] 替换片段 465: 使用辅助视频 5866.83-5868.67秒
2025-07-08 17:09:09 [INFO] 处理替换批次 186/252，包含 10 个片段
2025-07-08 17:09:09 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:09:10 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:09:10 [INFO] 替换片段 164: 使用辅助视频 5868.67-5869.83秒
2025-07-08 17:09:10 [INFO] 替换片段 398: 使用辅助视频 5869.83-5872.12秒
2025-07-08 17:09:11 [INFO] 替换片段 384: 使用辅助视频 5872.12-5872.88秒
2025-07-08 17:09:11 [INFO] 替换片段 411: 使用辅助视频 5872.88-5875.04秒
2025-07-08 17:09:11 [INFO] 替换片段 107: 使用辅助视频 5875.04-5879.50秒
2025-07-08 17:09:12 [INFO] 替换片段 221: 使用辅助视频 5879.50-5883.92秒
2025-07-08 17:09:12 [INFO] 替换片段 364: 使用辅助视频 5883.92-5887.21秒
2025-07-08 17:09:13 [INFO] 替换片段 223: 使用辅助视频 5887.21-5893.12秒
2025-07-08 17:09:13 [INFO] 替换片段 193: 使用辅助视频 5893.12-5896.21秒
2025-07-08 17:09:14 [INFO] 替换片段 454: 使用辅助视频 5896.21-5897.71秒
2025-07-08 17:09:14 [INFO] 处理替换批次 187/252，包含 10 个片段
2025-07-08 17:09:14 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:09:15 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:09:15 [INFO] 替换片段 180: 使用辅助视频 5897.71-5900.71秒
2025-07-08 17:09:16 [INFO] 替换片段 142: 使用辅助视频 5900.71-5903.58秒
2025-07-08 17:09:16 [INFO] 替换片段 110: 使用辅助视频 5903.58-5906.17秒
2025-07-08 17:09:17 [INFO] 替换片段 365: 使用辅助视频 5906.17-5908.62秒
2025-07-08 17:09:17 [INFO] 替换片段 22: 使用辅助视频 5908.62-5910.96秒
2025-07-08 17:09:18 [INFO] 替换片段 206: 使用辅助视频 5910.96-5914.25秒
2025-07-08 17:09:18 [INFO] 替换片段 357: 使用辅助视频 5918.75-5925.50秒
2025-07-08 17:09:18 [INFO] 替换片段 217: 使用辅助视频 5925.50-5928.62秒
2025-07-08 17:09:19 [INFO] 替换片段 366: 使用辅助视频 5928.62-5934.21秒
2025-07-08 17:09:19 [INFO] 替换片段 345: 使用辅助视频 5934.21-5935.54秒
2025-07-08 17:09:19 [INFO] 处理替换批次 188/252，包含 10 个片段
2025-07-08 17:09:19 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:09:20 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:09:20 [INFO] 替换片段 189: 使用辅助视频 5935.54-5938.17秒
2025-07-08 17:09:21 [INFO] 替换片段 117: 使用辅助视频 5938.17-5938.96秒
2025-07-08 17:09:21 [INFO] 替换片段 108: 使用辅助视频 5938.96-5940.00秒
2025-07-08 17:09:21 [INFO] 替换片段 11: 使用辅助视频 5940.00-5941.75秒
2025-07-08 17:09:21 [INFO] 替换片段 108: 使用辅助视频 5941.75-5943.17秒
2025-07-08 17:09:22 [INFO] 替换片段 404: 使用辅助视频 5943.17-5946.58秒
2025-07-08 17:09:22 [INFO] 替换片段 381: 使用辅助视频 5946.58-5948.25秒
2025-07-08 17:09:23 [INFO] 替换片段 451: 使用辅助视频 5948.25-5949.54秒
2025-07-08 17:09:23 [INFO] 替换片段 419: 使用辅助视频 5949.54-5950.46秒
2025-07-08 17:09:23 [INFO] 替换片段 370: 使用辅助视频 5950.46-5951.67秒
2025-07-08 17:09:23 [INFO] 处理替换批次 189/252，包含 10 个片段
2025-07-08 17:09:23 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:09:24 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:09:24 [INFO] 替换片段 196: 使用辅助视频 5951.67-5954.08秒
2025-07-08 17:09:25 [INFO] 替换片段 371: 使用辅助视频 5954.08-5955.50秒
2025-07-08 17:09:25 [INFO] 替换片段 173: 使用辅助视频 5955.50-5956.54秒
2025-07-08 17:09:25 [INFO] 替换片段 118: 使用辅助视频 5956.54-5957.33秒
2025-07-08 17:09:25 [INFO] 替换片段 123: 使用辅助视频 5957.33-5958.25秒
2025-07-08 17:09:25 [INFO] 替换片段 407: 使用辅助视频 5958.25-5959.25秒
2025-07-08 17:09:26 [INFO] 替换片段 131: 使用辅助视频 5959.25-5960.08秒
2025-07-08 17:09:26 [INFO] 替换片段 451: 使用辅助视频 5960.08-5961.79秒
2025-07-08 17:09:26 [INFO] 替换片段 371: 使用辅助视频 5961.79-5965.46秒
2025-07-08 17:09:27 [INFO] 替换片段 0: 使用辅助视频 5965.46-5967.88秒
2025-07-08 17:09:27 [INFO] 处理替换批次 190/252，包含 10 个片段
2025-07-08 17:09:27 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:09:28 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:09:28 [INFO] 替换片段 223: 使用辅助视频 5967.88-5970.08秒
2025-07-08 17:09:28 [INFO] 替换片段 177: 使用辅助视频 5970.08-5971.62秒
2025-07-08 17:09:28 [INFO] 替换片段 213: 使用辅助视频 5971.62-5973.79秒
2025-07-08 17:09:29 [INFO] 替换片段 376: 使用辅助视频 5973.79-5977.62秒
2025-07-08 17:09:30 [INFO] 替换片段 38: 使用辅助视频 5977.62-5982.04秒
2025-07-08 17:09:30 [INFO] 替换片段 129: 使用辅助视频 5982.04-5984.33秒
2025-07-08 17:09:30 [INFO] 替换片段 131: 使用辅助视频 5984.33-5985.62秒
2025-07-08 17:09:30 [INFO] 替换片段 199: 使用辅助视频 5985.62-5990.58秒
2025-07-08 17:09:31 [INFO] 替换片段 439: 使用辅助视频 5990.58-5991.79秒
2025-07-08 17:09:31 [INFO] 替换片段 243: 使用辅助视频 5991.79-5999.08秒
2025-07-08 17:09:31 [INFO] 处理替换批次 191/252，包含 10 个片段
2025-07-08 17:09:31 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:09:32 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:09:32 [INFO] 替换片段 199: 使用辅助视频 5999.08-6000.83秒
2025-07-08 17:09:32 [INFO] 替换片段 204: 使用辅助视频 6000.83-6001.71秒
2025-07-08 17:09:33 [INFO] 替换片段 423: 使用辅助视频 6001.71-6004.08秒
2025-07-08 17:09:33 [INFO] 替换片段 213: 使用辅助视频 6004.08-6006.83秒
2025-07-08 17:09:34 [INFO] 替换片段 199: 使用辅助视频 6006.83-6009.38秒
2025-07-08 17:09:34 [INFO] 替换片段 164: 使用辅助视频 6009.38-6010.42秒
2025-07-08 17:09:34 [INFO] 替换片段 139: 使用辅助视频 6010.42-6013.33秒
2025-07-08 17:09:35 [INFO] 替换片段 294: 使用辅助视频 6013.33-6014.75秒
2025-07-08 17:09:35 [INFO] 替换片段 294: 使用辅助视频 6014.75-6019.96秒
2025-07-08 17:09:35 [INFO] 替换片段 344: 使用辅助视频 6019.96-6023.00秒
2025-07-08 17:09:35 [INFO] 处理替换批次 192/252，包含 10 个片段
2025-07-08 17:09:35 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:09:36 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:09:36 [INFO] 替换片段 157: 使用辅助视频 6023.00-6025.75秒
2025-07-08 17:09:37 [INFO] 替换片段 282: 使用辅助视频 6025.75-6027.83秒
2025-07-08 17:09:37 [INFO] 替换片段 371: 使用辅助视频 6027.83-6029.58秒
2025-07-08 17:09:38 [INFO] 替换片段 348: 使用辅助视频 6029.58-6030.92秒
2025-07-08 17:09:38 [INFO] 替换片段 141: 使用辅助视频 6030.92-6031.96秒
2025-07-08 17:09:38 [INFO] 替换片段 203: 使用辅助视频 6031.96-6033.38秒
2025-07-08 17:09:39 [INFO] 替换片段 244: 使用辅助视频 6033.38-6034.58秒
2025-07-08 17:09:39 [INFO] 替换片段 177: 使用辅助视频 6034.58-6035.58秒
2025-07-08 17:09:39 [INFO] 替换片段 204: 使用辅助视频 6035.58-6038.33秒
2025-07-08 17:09:40 [INFO] 替换片段 379: 使用辅助视频 6038.33-6041.42秒
2025-07-08 17:09:40 [INFO] 处理替换批次 193/252，包含 10 个片段
2025-07-08 17:09:40 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:09:40 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:09:41 [INFO] 替换片段 222: 使用辅助视频 6041.42-6042.38秒
2025-07-08 17:09:41 [INFO] 替换片段 56: 使用辅助视频 6042.38-6043.00秒
2025-07-08 17:09:41 [INFO] 替换片段 41: 使用辅助视频 6043.00-6043.92秒
2025-07-08 17:09:41 [INFO] 替换片段 319: 使用辅助视频 6043.92-6045.71秒
2025-07-08 17:09:41 [INFO] 替换片段 379: 使用辅助视频 6045.71-6050.88秒
2025-07-08 17:09:42 [INFO] 替换片段 478: 使用辅助视频 6050.88-6053.38秒
2025-07-08 17:09:42 [INFO] 替换片段 11: 使用辅助视频 6053.38-6055.88秒
2025-07-08 17:09:43 [INFO] 替换片段 383: 使用辅助视频 6055.88-6057.67秒
2025-07-08 17:09:43 [INFO] 替换片段 384: 使用辅助视频 6057.67-6058.96秒
2025-07-08 17:09:43 [INFO] 替换片段 161: 使用辅助视频 6058.96-6062.67秒
2025-07-08 17:09:43 [INFO] 处理替换批次 194/252，包含 10 个片段
2025-07-08 17:09:43 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:09:44 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:09:44 [INFO] 替换片段 235: 使用辅助视频 6062.67-6064.71秒
2025-07-08 17:09:45 [INFO] 替换片段 387: 使用辅助视频 6064.71-6065.83秒
2025-07-08 17:09:45 [INFO] 替换片段 247: 使用辅助视频 6065.83-6067.00秒
2025-07-08 17:09:45 [INFO] 替换片段 450: 使用辅助视频 6067.00-6068.54秒
2025-07-08 17:09:45 [INFO] 替换片段 472: 使用辅助视频 6068.54-6072.83秒
2025-07-08 17:09:46 [INFO] 替换片段 196: 使用辅助视频 6072.83-6076.54秒
2025-07-08 17:09:46 [INFO] 替换片段 66: 使用辅助视频 6076.54-6081.00秒
2025-07-08 17:09:47 [INFO] 替换片段 434: 使用辅助视频 6081.00-6085.00秒
2025-07-08 17:09:48 [INFO] 替换片段 202: 使用辅助视频 6085.00-6087.92秒
2025-07-08 17:09:48 [INFO] 替换片段 118: 使用辅助视频 6087.92-6092.42秒
2025-07-08 17:09:48 [INFO] 处理替换批次 195/252，包含 10 个片段
2025-07-08 17:09:48 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:09:49 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:09:49 [INFO] 替换片段 199: 使用辅助视频 6092.42-6095.38秒
2025-07-08 17:09:50 [INFO] 替换片段 314: 使用辅助视频 6095.38-6097.67秒
2025-07-08 17:09:50 [INFO] 替换片段 438: 使用辅助视频 6097.67-6101.58秒
2025-07-08 17:09:51 [INFO] 替换片段 391: 使用辅助视频 6101.58-6104.38秒
2025-07-08 17:09:51 [INFO] 替换片段 392: 使用辅助视频 6104.38-6105.58秒
2025-07-08 17:09:52 [INFO] 替换片段 345: 使用辅助视频 6105.58-6107.58秒
2025-07-08 17:09:52 [INFO] 替换片段 49: 使用辅助视频 6107.58-6110.83秒
2025-07-08 17:09:53 [INFO] 替换片段 392: 使用辅助视频 6110.83-6112.33秒
2025-07-08 17:09:53 [INFO] 替换片段 235: 使用辅助视频 6112.33-6114.79秒
2025-07-08 17:09:54 [INFO] 替换片段 287: 使用辅助视频 6114.79-6118.96秒
2025-07-08 17:09:54 [INFO] 处理替换批次 196/252，包含 10 个片段
2025-07-08 17:09:54 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:09:54 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:09:55 [INFO] 替换片段 202: 使用辅助视频 6118.96-6122.88秒
2025-07-08 17:09:55 [INFO] 替换片段 252: 使用辅助视频 6122.88-6124.17秒
2025-07-08 17:09:56 [INFO] 替换片段 451: 使用辅助视频 6124.17-6125.75秒
2025-07-08 17:09:56 [INFO] 替换片段 162: 使用辅助视频 6125.75-6129.08秒
2025-07-08 17:09:57 [INFO] 替换片段 252: 使用辅助视频 6129.08-6131.58秒
2025-07-08 17:09:57 [INFO] 替换片段 337: 使用辅助视频 6131.58-6133.83秒
2025-07-08 17:09:57 [INFO] 替换片段 147: 使用辅助视频 6133.83-6135.75秒
2025-07-08 17:09:58 [INFO] 替换片段 295: 使用辅助视频 6135.75-6138.67秒
2025-07-08 17:09:58 [INFO] 替换片段 350: 使用辅助视频 6138.67-6142.46秒
2025-07-08 17:09:59 [INFO] 替换片段 373: 使用辅助视频 6142.46-6148.54秒
2025-07-08 17:09:59 [INFO] 处理替换批次 197/252，包含 10 个片段
2025-07-08 17:09:59 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:10:00 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:10:00 [INFO] 替换片段 241: 使用辅助视频 6148.54-6151.92秒
2025-07-08 17:10:01 [INFO] 替换片段 394: 使用辅助视频 6151.92-6154.92秒
2025-07-08 17:10:02 [INFO] 替换片段 453: 使用辅助视频 6154.92-6158.08秒
2025-07-08 17:10:02 [INFO] 替换片段 354: 使用辅助视频 6158.08-6161.29秒
2025-07-08 17:10:03 [INFO] 替换片段 172: 使用辅助视频 6161.29-6162.58秒
2025-07-08 17:10:03 [INFO] 替换片段 102: 使用辅助视频 6162.58-6163.42秒
2025-07-08 17:10:03 [INFO] 替换片段 398: 使用辅助视频 6163.42-6166.33秒
2025-07-08 17:10:04 [INFO] 替换片段 231: 使用辅助视频 6166.33-6172.04秒
2025-07-08 17:10:04 [INFO] 替换片段 398: 使用辅助视频 6172.04-6173.62秒
2025-07-08 17:10:05 [INFO] 替换片段 471: 使用辅助视频 6173.62-6174.62秒
2025-07-08 17:10:05 [INFO] 处理替换批次 198/252，包含 10 个片段
2025-07-08 17:10:05 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:10:05 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:10:06 [INFO] 替换片段 50: 使用辅助视频 6174.62-6176.46秒
2025-07-08 17:10:06 [INFO] 替换片段 48: 使用辅助视频 6176.46-6179.50秒
2025-07-08 17:10:07 [INFO] 替换片段 146: 使用辅助视频 6179.50-6182.46秒
2025-07-08 17:10:07 [INFO] 替换片段 244: 使用辅助视频 6182.46-6185.25秒
2025-07-08 17:10:08 [INFO] 替换片段 238: 使用辅助视频 6185.25-6188.62秒
2025-07-08 17:10:08 [INFO] 替换片段 170: 使用辅助视频 6188.62-6190.83秒
2025-07-08 17:10:09 [INFO] 替换片段 118: 使用辅助视频 6190.83-6196.12秒
2025-07-08 17:10:09 [INFO] 替换片段 208: 使用辅助视频 6196.12-6197.25秒
2025-07-08 17:10:09 [INFO] 替换片段 45: 使用辅助视频 6197.25-6198.04秒
2025-07-08 17:10:10 [INFO] 替换片段 238: 使用辅助视频 6198.04-6200.46秒
2025-07-08 17:10:10 [INFO] 处理替换批次 199/252，包含 10 个片段
2025-07-08 17:10:10 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:10:10 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:10:11 [INFO] 替换片段 292: 使用辅助视频 6200.46-6202.83秒
2025-07-08 17:10:11 [INFO] 替换片段 2: 使用辅助视频 6202.83-6204.04秒
2025-07-08 17:10:11 [INFO] 替换片段 377: 使用辅助视频 6204.04-6205.92秒
2025-07-08 17:10:12 [INFO] 替换片段 189: 使用辅助视频 6205.92-6207.29秒
2025-07-08 17:10:12 [INFO] 替换片段 238: 使用辅助视频 6207.29-6208.58秒
2025-07-08 17:10:12 [INFO] 替换片段 396: 使用辅助视频 6208.58-6210.50秒
2025-07-08 17:10:13 [INFO] 替换片段 310: 使用辅助视频 6210.50-6211.75秒
2025-07-08 17:10:13 [INFO] 替换片段 438: 使用辅助视频 6211.75-6216.08秒
2025-07-08 17:10:13 [INFO] 替换片段 110: 使用辅助视频 6216.08-6217.50秒
2025-07-08 17:10:14 [INFO] 替换片段 213: 使用辅助视频 6217.50-6222.29秒
2025-07-08 17:10:14 [INFO] 处理替换批次 200/252，包含 10 个片段
2025-07-08 17:10:14 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:10:15 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:10:15 [INFO] 替换片段 301: 使用辅助视频 6222.29-6225.83秒
2025-07-08 17:10:16 [INFO] 替换片段 403: 使用辅助视频 6225.83-6228.04秒
2025-07-08 17:10:16 [INFO] 替换片段 113: 使用辅助视频 6228.04-6229.33秒
2025-07-08 17:10:16 [INFO] 替换片段 180: 使用辅助视频 6229.33-6230.96秒
2025-07-08 17:10:17 [INFO] 替换片段 318: 使用辅助视频 6230.96-6232.54秒
2025-07-08 17:10:17 [INFO] 替换片段 27: 使用辅助视频 6232.54-6233.83秒
2025-07-08 17:10:17 [INFO] 替换片段 399: 使用辅助视频 6233.83-6234.67秒
2025-07-08 17:10:17 [INFO] 替换片段 108: 使用辅助视频 6234.67-6235.62秒
2025-07-08 17:10:18 [INFO] 替换片段 477: 使用辅助视频 6235.62-6236.25秒
2025-07-08 17:10:18 [INFO] 替换片段 390: 使用辅助视频 6236.25-6239.62秒
2025-07-08 17:10:18 [INFO] 处理替换批次 201/252，包含 10 个片段
2025-07-08 17:10:18 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:10:18 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:10:19 [INFO] 替换片段 79: 使用辅助视频 6239.62-6242.33秒
2025-07-08 17:10:19 [INFO] 替换片段 237: 使用辅助视频 6242.33-6246.08秒
2025-07-08 17:10:20 [INFO] 替换片段 109: 使用辅助视频 6246.08-6248.08秒
2025-07-08 17:10:20 [INFO] 替换片段 146: 使用辅助视频 6248.08-6250.38秒
2025-07-08 17:10:21 [INFO] 替换片段 278: 使用辅助视频 6250.38-6253.04秒
2025-07-08 17:10:21 [INFO] 替换片段 123: 使用辅助视频 6253.04-6257.96秒
2025-07-08 17:10:22 [INFO] 替换片段 109: 使用辅助视频 6257.96-6261.00秒
2025-07-08 17:10:23 [INFO] 替换片段 440: 使用辅助视频 6261.00-6267.08秒
2025-07-08 17:10:23 [INFO] 替换片段 485: 使用辅助视频 6267.08-6269.71秒
2025-07-08 17:10:24 [INFO] 替换片段 475: 使用辅助视频 6269.71-6271.75秒
2025-07-08 17:10:24 [INFO] 处理替换批次 202/252，包含 10 个片段
2025-07-08 17:10:24 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:10:24 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:10:25 [INFO] 替换片段 96: 使用辅助视频 6271.75-6274.46秒
2025-07-08 17:10:25 [INFO] 替换片段 382: 使用辅助视频 6274.46-6276.62秒
2025-07-08 17:10:26 [INFO] 替换片段 153: 使用辅助视频 6276.62-6278.62秒
2025-07-08 17:10:26 [INFO] 替换片段 103: 使用辅助视频 6278.62-6285.21秒
2025-07-08 17:10:27 [INFO] 替换片段 473: 使用辅助视频 6285.21-6286.25秒
2025-07-08 17:10:27 [INFO] 替换片段 134: 使用辅助视频 6286.25-6287.71秒
2025-07-08 17:10:27 [INFO] 替换片段 187: 使用辅助视频 6287.71-6289.04秒
2025-07-08 17:10:28 [INFO] 替换片段 408: 使用辅助视频 6289.04-6290.92秒
2025-07-08 17:10:28 [INFO] 替换片段 332: 使用辅助视频 6290.92-6293.21秒
2025-07-08 17:10:29 [INFO] 替换片段 55: 使用辅助视频 6293.21-6297.92秒
2025-07-08 17:10:29 [INFO] 处理替换批次 203/252，包含 10 个片段
2025-07-08 17:10:29 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:10:29 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:10:30 [INFO] 替换片段 282: 使用辅助视频 6297.92-6298.96秒
2025-07-08 17:10:30 [INFO] 替换片段 183: 使用辅助视频 6298.96-6302.75秒
2025-07-08 17:10:31 [INFO] 替换片段 347: 使用辅助视频 6302.75-6306.54秒
2025-07-08 17:10:31 [INFO] 替换片段 357: 使用辅助视频 6306.54-6307.92秒
2025-07-08 17:10:32 [INFO] 替换片段 162: 使用辅助视频 6307.92-6308.92秒
2025-07-08 17:10:32 [INFO] 替换片段 51: 使用辅助视频 6308.92-6309.96秒
2025-07-08 17:10:32 [INFO] 替换片段 453: 使用辅助视频 6309.96-6313.54秒
2025-07-08 17:10:33 [INFO] 替换片段 320: 使用辅助视频 6313.54-6318.38秒
2025-07-08 17:10:34 [INFO] 替换片段 384: 使用辅助视频 6318.38-6320.67秒
2025-07-08 17:10:34 [INFO] 替换片段 132: 使用辅助视频 6320.67-6322.38秒
2025-07-08 17:10:34 [INFO] 处理替换批次 204/252，包含 10 个片段
2025-07-08 17:10:34 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:10:35 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:10:35 [INFO] 替换片段 137: 使用辅助视频 6322.38-6323.75秒
2025-07-08 17:10:36 [INFO] 替换片段 398: 使用辅助视频 6323.75-6326.46秒
2025-07-08 17:10:36 [INFO] 替换片段 459: 使用辅助视频 6326.46-6327.92秒
2025-07-08 17:10:36 [INFO] 替换片段 400: 使用辅助视频 6327.92-6330.54秒
2025-07-08 17:10:37 [INFO] 替换片段 357: 使用辅助视频 6330.54-6332.50秒
2025-07-08 17:10:37 [INFO] 替换片段 400: 使用辅助视频 6332.50-6334.42秒
2025-07-08 17:10:38 [INFO] 替换片段 291: 使用辅助视频 6334.42-6336.96秒
2025-07-08 17:10:38 [INFO] 替换片段 423: 使用辅助视频 6336.96-6338.42秒
2025-07-08 17:10:38 [INFO] 替换片段 398: 使用辅助视频 6338.42-6339.62秒
2025-07-08 17:10:39 [INFO] 替换片段 282: 使用辅助视频 6339.62-6340.58秒
2025-07-08 17:10:39 [INFO] 处理替换批次 205/252，包含 10 个片段
2025-07-08 17:10:39 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:10:39 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:10:40 [INFO] 替换片段 398: 使用辅助视频 6340.58-6341.88秒
2025-07-08 17:10:40 [INFO] 替换片段 231: 使用辅助视频 6341.88-6342.62秒
2025-07-08 17:10:40 [INFO] 替换片段 170: 使用辅助视频 6342.62-6343.79秒
2025-07-08 17:10:40 [INFO] 替换片段 384: 使用辅助视频 6343.79-6346.79秒
2025-07-08 17:10:41 [INFO] 替换片段 120: 使用辅助视频 6346.79-6347.96秒
2025-07-08 17:10:41 [INFO] 替换片段 226: 使用辅助视频 6347.96-6349.29秒
2025-07-08 17:10:42 [INFO] 替换片段 204: 使用辅助视频 6349.29-6350.12秒
2025-07-08 17:10:42 [INFO] 替换片段 155: 使用辅助视频 6350.12-6350.96秒
2025-07-08 17:10:42 [INFO] 替换片段 186: 使用辅助视频 6350.96-6352.50秒
2025-07-08 17:10:42 [INFO] 替换片段 479: 使用辅助视频 6352.50-6355.38秒
2025-07-08 17:10:42 [INFO] 处理替换批次 206/252，包含 10 个片段
2025-07-08 17:10:42 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:10:43 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:10:44 [INFO] 替换片段 380: 使用辅助视频 6355.38-6356.79秒
2025-07-08 17:10:44 [INFO] 替换片段 116: 使用辅助视频 6356.79-6357.71秒
2025-07-08 17:10:44 [INFO] 替换片段 234: 使用辅助视频 6357.71-6359.71秒
2025-07-08 17:10:45 [INFO] 替换片段 470: 使用辅助视频 6359.71-6360.96秒
2025-07-08 17:10:45 [INFO] 替换片段 381: 使用辅助视频 6360.96-6361.67秒
2025-07-08 17:10:45 [INFO] 替换片段 10: 使用辅助视频 6361.67-6364.08秒
2025-07-08 17:10:46 [INFO] 替换片段 351: 使用辅助视频 6364.08-6366.50秒
2025-07-08 17:10:46 [INFO] 替换片段 416: 使用辅助视频 6366.50-6368.58秒
2025-07-08 17:10:47 [INFO] 替换片段 261: 使用辅助视频 6368.58-6369.38秒
2025-07-08 17:10:47 [INFO] 替换片段 270: 使用辅助视频 6369.38-6370.96秒
2025-07-08 17:10:47 [INFO] 处理替换批次 207/252，包含 10 个片段
2025-07-08 17:10:47 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:10:48 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:10:48 [INFO] 替换片段 202: 使用辅助视频 6370.96-6373.17秒
2025-07-08 17:10:48 [INFO] 替换片段 153: 使用辅助视频 6373.17-6374.67秒
2025-07-08 17:10:49 [INFO] 替换片段 357: 使用辅助视频 6374.67-6377.88秒
2025-07-08 17:10:49 [INFO] 替换片段 401: 使用辅助视频 6377.88-6379.67秒
2025-07-08 17:10:50 [INFO] 替换片段 358: 使用辅助视频 6379.67-6381.12秒
2025-07-08 17:10:50 [INFO] 替换片段 91: 使用辅助视频 6381.12-6383.62秒
2025-07-08 17:10:51 [INFO] 替换片段 320: 使用辅助视频 6383.62-6385.50秒
2025-07-08 17:10:51 [INFO] 替换片段 185: 使用辅助视频 6385.50-6387.96秒
2025-07-08 17:10:52 [INFO] 替换片段 50: 使用辅助视频 6387.96-6389.21秒
2025-07-08 17:10:52 [INFO] 替换片段 205: 使用辅助视频 6389.21-6390.58秒
2025-07-08 17:10:52 [INFO] 处理替换批次 208/252，包含 10 个片段
2025-07-08 17:10:52 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:10:53 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:10:53 [INFO] 替换片段 141: 使用辅助视频 6390.58-6392.62秒
2025-07-08 17:10:54 [INFO] 替换片段 193: 使用辅助视频 6392.62-6393.92秒
2025-07-08 17:10:54 [INFO] 替换片段 66: 使用辅助视频 6393.92-6395.12秒
2025-07-08 17:10:54 [INFO] 替换片段 289: 使用辅助视频 6395.12-6396.79秒
2025-07-08 17:10:55 [INFO] 替换片段 153: 使用辅助视频 6396.79-6398.38秒
2025-07-08 17:10:55 [INFO] 替换片段 206: 使用辅助视频 6398.38-6400.25秒
2025-07-08 17:10:56 [INFO] 替换片段 476: 使用辅助视频 6400.25-6403.58秒
2025-07-08 17:10:56 [INFO] 替换片段 391: 使用辅助视频 6403.58-6406.08秒
2025-07-08 17:10:57 [INFO] 替换片段 451: 使用辅助视频 6406.08-6407.08秒
2025-07-08 17:10:57 [INFO] 替换片段 103: 使用辅助视频 6407.08-6407.88秒
2025-07-08 17:10:57 [INFO] 处理替换批次 209/252，包含 10 个片段
2025-07-08 17:10:57 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:10:58 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:10:58 [INFO] 替换片段 204: 使用辅助视频 6407.88-6410.75秒
2025-07-08 17:10:59 [INFO] 替换片段 179: 使用辅助视频 6410.75-6411.67秒
2025-07-08 17:10:59 [INFO] 替换片段 404: 使用辅助视频 6411.67-6412.67秒
2025-07-08 17:10:59 [INFO] 替换片段 104: 使用辅助视频 6412.67-6415.17秒
2025-07-08 17:11:00 [INFO] 替换片段 189: 使用辅助视频 6415.17-6417.08秒
2025-07-08 17:11:01 [INFO] 替换片段 459: 使用辅助视频 6417.08-6419.21秒
2025-07-08 17:11:01 [INFO] 替换片段 399: 使用辅助视频 6419.21-6420.12秒
2025-07-08 17:11:01 [INFO] 替换片段 248: 使用辅助视频 6420.12-6421.42秒
2025-07-08 17:11:02 [INFO] 替换片段 59: 使用辅助视频 6421.42-6422.96秒
2025-07-08 17:11:02 [INFO] 替换片段 403: 使用辅助视频 6422.96-6423.71秒
2025-07-08 17:11:02 [INFO] 处理替换批次 210/252，包含 10 个片段
2025-07-08 17:11:02 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:11:03 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:11:03 [INFO] 替换片段 404: 使用辅助视频 6423.71-6424.75秒
2025-07-08 17:11:04 [INFO] 替换片段 103: 使用辅助视频 6424.75-6427.67秒
2025-07-08 17:11:04 [INFO] 替换片段 424: 使用辅助视频 6427.67-6429.29秒
2025-07-08 17:11:05 [INFO] 替换片段 195: 使用辅助视频 6429.29-6431.04秒
2025-07-08 17:11:05 [INFO] 替换片段 302: 使用辅助视频 6431.04-6438.08秒
2025-07-08 17:11:06 [INFO] 替换片段 285: 使用辅助视频 6438.08-6440.12秒
2025-07-08 17:11:06 [INFO] 替换片段 248: 使用辅助视频 6440.12-6442.83秒
2025-07-08 17:11:06 [INFO] 替换片段 388: 使用辅助视频 6446.33-6447.75秒
2025-07-08 17:11:07 [INFO] 替换片段 305: 使用辅助视频 6447.75-6449.29秒
2025-07-08 17:11:07 [INFO] 替换片段 420: 使用辅助视频 6449.29-6451.42秒
2025-07-08 17:11:07 [INFO] 处理替换批次 211/252，包含 10 个片段
2025-07-08 17:11:07 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:11:08 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:11:09 [INFO] 替换片段 258: 使用辅助视频 6451.42-6452.33秒
2025-07-08 17:11:09 [INFO] 替换片段 282: 使用辅助视频 6452.33-6453.00秒
2025-07-08 17:11:09 [INFO] 替换片段 2: 使用辅助视频 6453.00-6454.46秒
2025-07-08 17:11:09 [INFO] 替换片段 390: 使用辅助视频 6454.46-6455.42秒
2025-07-08 17:11:10 [INFO] 替换片段 41: 使用辅助视频 6455.42-6456.25秒
2025-07-08 17:11:10 [INFO] 替换片段 332: 使用辅助视频 6456.25-6457.58秒
2025-07-08 17:11:10 [INFO] 替换片段 420: 使用辅助视频 6459.12-6460.71秒
2025-07-08 17:11:11 [INFO] 替换片段 286: 使用辅助视频 6460.71-6461.67秒
2025-07-08 17:11:11 [INFO] 替换片段 217: 使用辅助视频 6461.67-6462.38秒
2025-07-08 17:11:11 [INFO] 替换片段 146: 使用辅助视频 6462.38-6463.25秒
2025-07-08 17:11:11 [INFO] 处理替换批次 212/252，包含 10 个片段
2025-07-08 17:11:11 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:11:12 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:11:13 [INFO] 替换片段 242: 使用辅助视频 6463.25-6464.75秒
2025-07-08 17:11:13 [INFO] 替换片段 471: 使用辅助视频 6464.75-6465.79秒
2025-07-08 17:11:13 [INFO] 替换片段 18: 使用辅助视频 6465.79-6468.25秒
2025-07-08 17:11:14 [INFO] 替换片段 258: 使用辅助视频 6468.25-6470.29秒
2025-07-08 17:11:14 [INFO] 替换片段 408: 使用辅助视频 6470.29-6472.33秒
2025-07-08 17:11:15 [INFO] 替换片段 201: 使用辅助视频 6472.33-6475.75秒
2025-07-08 17:11:16 [INFO] 替换片段 298: 使用辅助视频 6475.75-6477.38秒
2025-07-08 17:11:16 [INFO] 替换片段 34: 使用辅助视频 6477.38-6479.29秒
2025-07-08 17:11:17 [INFO] 替换片段 459: 使用辅助视频 6479.29-6482.54秒
2025-07-08 17:11:17 [INFO] 替换片段 409: 使用辅助视频 6482.54-6502.79秒
2025-07-08 17:11:18 [INFO] 处理替换批次 213/252，包含 10 个片段
2025-07-08 17:11:18 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:11:19 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:11:19 [INFO] 替换片段 98: 使用辅助视频 6502.79-6505.79秒
2025-07-08 17:11:20 [INFO] 替换片段 290: 使用辅助视频 6505.79-6511.38秒
2025-07-08 17:11:20 [INFO] 替换片段 411: 使用辅助视频 6511.38-6514.04秒
2025-07-08 17:11:21 [INFO] 替换片段 0: 使用辅助视频 6514.04-6514.71秒
2025-07-08 17:11:21 [INFO] 替换片段 445: 使用辅助视频 6514.71-6517.29秒
2025-07-08 17:11:21 [INFO] 替换片段 263: 使用辅助视频 6517.29-6522.21秒
2025-07-08 17:11:22 [INFO] 替换片段 124: 使用辅助视频 6522.21-6526.88秒
2025-07-08 17:11:22 [INFO] 替换片段 307: 使用辅助视频 6526.88-6528.50秒
2025-07-08 17:11:23 [INFO] 替换片段 440: 使用辅助视频 6528.50-6530.12秒
2025-07-08 17:11:23 [INFO] 替换片段 24: 使用辅助视频 6530.12-6531.71秒
2025-07-08 17:11:23 [INFO] 处理替换批次 214/252，包含 10 个片段
2025-07-08 17:11:23 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:11:24 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:11:25 [INFO] 替换片段 310: 使用辅助视频 6531.71-6534.46秒
2025-07-08 17:11:25 [INFO] 替换片段 414: 使用辅助视频 6534.46-6536.25秒
2025-07-08 17:11:26 [INFO] 替换片段 423: 使用辅助视频 6536.25-6538.00秒
2025-07-08 17:11:26 [INFO] 替换片段 303: 使用辅助视频 6538.00-6539.29秒
2025-07-08 17:11:26 [INFO] 替换片段 110: 使用辅助视频 6539.29-6539.96秒
2025-07-08 17:11:27 [INFO] 替换片段 119: 使用辅助视频 6539.96-6540.67秒
2025-07-08 17:11:27 [INFO] 替换片段 30: 使用辅助视频 6540.67-6543.83秒
2025-07-08 17:11:28 [INFO] 替换片段 416: 使用辅助视频 6543.83-6547.75秒
2025-07-08 17:11:29 [INFO] 替换片段 403: 使用辅助视频 6547.75-6550.42秒
2025-07-08 17:11:29 [INFO] 替换片段 242: 使用辅助视频 6550.42-6552.88秒
2025-07-08 17:11:29 [INFO] 处理替换批次 215/252，包含 10 个片段
2025-07-08 17:11:29 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:11:30 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:11:30 [INFO] 替换片段 172: 使用辅助视频 6552.88-6556.67秒
2025-07-08 17:11:31 [INFO] 替换片段 131: 使用辅助视频 6556.67-6559.21秒
2025-07-08 17:11:32 [INFO] 替换片段 305: 使用辅助视频 6559.21-6563.71秒
2025-07-08 17:11:32 [INFO] 替换片段 417: 使用辅助视频 6563.71-6565.21秒
2025-07-08 17:11:33 [INFO] 替换片段 104: 使用辅助视频 6565.21-6566.67秒
2025-07-08 17:11:33 [INFO] 替换片段 347: 使用辅助视频 6566.67-6568.42秒
2025-07-08 17:11:33 [INFO] 替换片段 294: 使用辅助视频 6568.42-6572.38秒
2025-07-08 17:11:35 [INFO] 替换片段 257: 使用辅助视频 6572.38-6575.00秒
2025-07-08 17:11:35 [INFO] 替换片段 390: 使用辅助视频 6575.00-6581.50秒
2025-07-08 17:11:36 [INFO] 替换片段 347: 使用辅助视频 6581.50-6584.75秒
2025-07-08 17:11:36 [INFO] 处理替换批次 216/252，包含 10 个片段
2025-07-08 17:11:36 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:11:37 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:11:37 [INFO] 替换片段 294: 使用辅助视频 6584.75-6586.62秒
2025-07-08 17:11:38 [INFO] 替换片段 478: 使用辅助视频 6586.62-6592.58秒
2025-07-08 17:11:38 [INFO] 替换片段 363: 使用辅助视频 6592.58-6595.04秒
2025-07-08 17:11:39 [INFO] 替换片段 300: 使用辅助视频 6595.04-6597.08秒
2025-07-08 17:11:39 [INFO] 替换片段 118: 使用辅助视频 6597.08-6599.46秒
2025-07-08 17:11:40 [INFO] 替换片段 177: 使用辅助视频 6599.46-6601.00秒
2025-07-08 17:11:40 [INFO] 替换片段 162: 使用辅助视频 6601.00-6601.71秒
2025-07-08 17:11:41 [INFO] 替换片段 177: 使用辅助视频 6601.71-6602.33秒
2025-07-08 17:11:41 [INFO] 替换片段 177: 使用辅助视频 6602.33-6612.58秒
2025-07-08 17:11:41 [INFO] 替换片段 349: 使用辅助视频 6612.58-6613.79秒
2025-07-08 17:11:41 [INFO] 处理替换批次 217/252，包含 10 个片段
2025-07-08 17:11:41 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:11:42 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:11:43 [INFO] 替换片段 231: 使用辅助视频 6613.79-6617.08秒
2025-07-08 17:11:44 [INFO] 替换片段 110: 使用辅助视频 6617.08-6618.38秒
2025-07-08 17:11:44 [INFO] 替换片段 116: 使用辅助视频 6618.38-6621.83秒
2025-07-08 17:11:45 [INFO] 替换片段 370: 使用辅助视频 6621.83-6624.46秒
2025-07-08 17:11:46 [INFO] 替换片段 414: 使用辅助视频 6624.46-6627.46秒
2025-07-08 17:11:46 [INFO] 替换片段 407: 使用辅助视频 6627.46-6633.50秒
2025-07-08 17:11:47 [INFO] 替换片段 198: 使用辅助视频 6633.50-6639.67秒
2025-07-08 17:11:47 [INFO] 替换片段 318: 使用辅助视频 6639.67-6642.75秒
2025-07-08 17:11:48 [INFO] 替换片段 407: 使用辅助视频 6642.75-6645.67秒
2025-07-08 17:11:49 [INFO] 替换片段 177: 使用辅助视频 6645.67-6648.83秒
2025-07-08 17:11:49 [INFO] 处理替换批次 218/252，包含 10 个片段
2025-07-08 17:11:49 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:11:50 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:11:50 [INFO] 替换片段 339: 使用辅助视频 6648.83-6652.83秒
2025-07-08 17:11:51 [INFO] 替换片段 445: 使用辅助视频 6652.83-6656.12秒
2025-07-08 17:11:52 [INFO] 替换片段 467: 使用辅助视频 6656.12-6656.79秒
2025-07-08 17:11:52 [INFO] 替换片段 255: 使用辅助视频 6656.79-6657.75秒
2025-07-08 17:11:52 [INFO] 替换片段 391: 使用辅助视频 6657.75-6659.38秒
2025-07-08 17:11:53 [INFO] 替换片段 109: 使用辅助视频 6659.38-6663.04秒
2025-07-08 17:11:54 [INFO] 替换片段 142: 使用辅助视频 6663.04-6666.29秒
2025-07-08 17:11:54 [INFO] 替换片段 240: 使用辅助视频 6666.29-6670.75秒
2025-07-08 17:11:55 [INFO] 替换片段 418: 使用辅助视频 6670.75-6676.42秒
2025-07-08 17:11:55 [INFO] 替换片段 117: 使用辅助视频 6676.42-6677.79秒
2025-07-08 17:11:55 [INFO] 处理替换批次 219/252，包含 10 个片段
2025-07-08 17:11:55 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:11:56 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:11:57 [INFO] 替换片段 193: 使用辅助视频 6677.79-6681.79秒
2025-07-08 17:11:58 [INFO] 替换片段 132: 使用辅助视频 6681.79-6683.50秒
2025-07-08 17:11:58 [INFO] 替换片段 96: 使用辅助视频 6683.50-6688.54秒
2025-07-08 17:11:58 [INFO] 替换片段 119: 使用辅助视频 6688.54-6692.04秒
2025-07-08 17:11:59 [INFO] 替换片段 294: 使用辅助视频 6692.04-6693.54秒
2025-07-08 17:12:00 [INFO] 替换片段 203: 使用辅助视频 6693.54-6694.62秒
2025-07-08 17:12:00 [INFO] 替换片段 371: 使用辅助视频 6694.62-6697.25秒
2025-07-08 17:12:01 [INFO] 替换片段 391: 使用辅助视频 6697.25-6701.46秒
2025-07-08 17:12:01 [INFO] 替换片段 350: 使用辅助视频 6701.46-6705.33秒
2025-07-08 17:12:02 [INFO] 替换片段 424: 使用辅助视频 6705.33-6706.88秒
2025-07-08 17:12:02 [INFO] 处理替换批次 220/252，包含 10 个片段
2025-07-08 17:12:02 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:12:03 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:12:03 [INFO] 替换片段 226: 使用辅助视频 6706.88-6708.96秒
2025-07-08 17:12:04 [INFO] 替换片段 32: 使用辅助视频 6708.96-6710.42秒
2025-07-08 17:12:04 [INFO] 替换片段 320: 使用辅助视频 6710.42-6711.71秒
2025-07-08 17:12:05 [INFO] 替换片段 458: 使用辅助视频 6711.71-6712.50秒
2025-07-08 17:12:05 [INFO] 替换片段 248: 使用辅助视频 6712.50-6713.54秒
2025-07-08 17:12:05 [INFO] 替换片段 472: 使用辅助视频 6713.54-6719.67秒
2025-07-08 17:12:05 [INFO] 替换片段 239: 使用辅助视频 6719.67-6723.46秒
2025-07-08 17:12:06 [INFO] 替换片段 120: 使用辅助视频 6723.46-6725.08秒
2025-07-08 17:12:07 [INFO] 替换片段 368: 使用辅助视频 6725.08-6726.92秒
2025-07-08 17:12:07 [INFO] 替换片段 221: 使用辅助视频 6726.92-6728.62秒
2025-07-08 17:12:07 [INFO] 处理替换批次 221/252，包含 10 个片段
2025-07-08 17:12:07 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:12:08 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:12:09 [INFO] 替换片段 102: 使用辅助视频 6728.62-6730.00秒
2025-07-08 17:12:09 [INFO] 替换片段 108: 使用辅助视频 6730.00-6731.12秒
2025-07-08 17:12:09 [INFO] 替换片段 208: 使用辅助视频 6731.12-6733.33秒
2025-07-08 17:12:10 [INFO] 替换片段 98: 使用辅助视频 6733.33-6734.79秒
2025-07-08 17:12:10 [INFO] 替换片段 420: 使用辅助视频 6734.79-6736.08秒
2025-07-08 17:12:10 [INFO] 替换片段 145: 使用辅助视频 6736.08-6739.58秒
2025-07-08 17:12:11 [INFO] 替换片段 131: 使用辅助视频 6739.58-6740.83秒
2025-07-08 17:12:12 [INFO] 替换片段 66: 使用辅助视频 6740.83-6742.08秒
2025-07-08 17:12:12 [INFO] 替换片段 229: 使用辅助视频 6742.08-6743.08秒
2025-07-08 17:12:12 [INFO] 替换片段 437: 使用辅助视频 6743.08-6743.83秒
2025-07-08 17:12:12 [INFO] 处理替换批次 222/252，包含 10 个片段
2025-07-08 17:12:12 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:12:13 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:12:14 [INFO] 替换片段 242: 使用辅助视频 6743.83-6744.88秒
2025-07-08 17:12:14 [INFO] 替换片段 337: 使用辅助视频 6744.88-6747.00秒
2025-07-08 17:12:14 [INFO] 替换片段 467: 使用辅助视频 6747.00-6750.88秒
2025-07-08 17:12:15 [INFO] 替换片段 254: 使用辅助视频 6750.88-6752.75秒
2025-07-08 17:12:15 [INFO] 替换片段 407: 使用辅助视频 6752.75-6755.12秒
2025-07-08 17:12:16 [INFO] 替换片段 115: 使用辅助视频 6755.12-6756.71秒
2025-07-08 17:12:16 [INFO] 替换片段 470: 使用辅助视频 6756.71-6763.04秒
2025-07-08 17:12:17 [INFO] 替换片段 116: 使用辅助视频 6763.04-6766.17秒
2025-07-08 17:12:17 [INFO] 替换片段 70: 使用辅助视频 6766.17-6767.58秒
2025-07-08 17:12:18 [INFO] 替换片段 411: 使用辅助视频 6767.58-6768.83秒
2025-07-08 17:12:18 [INFO] 处理替换批次 223/252，包含 10 个片段
2025-07-08 17:12:18 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:12:19 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:12:19 [INFO] 替换片段 471: 使用辅助视频 6768.83-6770.04秒
2025-07-08 17:12:19 [INFO] 替换片段 421: 使用辅助视频 6770.04-6772.12秒
2025-07-08 17:12:19 [INFO] 替换片段 404: 使用辅助视频 6772.12-6772.92秒
2025-07-08 17:12:20 [INFO] 替换片段 310: 使用辅助视频 6772.92-6775.04秒
2025-07-08 17:12:20 [INFO] 替换片段 221: 使用辅助视频 6775.04-6777.46秒
2025-07-08 17:12:21 [INFO] 替换片段 38: 使用辅助视频 6777.46-6780.29秒
2025-07-08 17:12:21 [INFO] 替换片段 302: 使用辅助视频 6780.29-6782.12秒
2025-07-08 17:12:22 [INFO] 替换片段 57: 使用辅助视频 6782.12-6784.38秒
2025-07-08 17:12:22 [INFO] 替换片段 373: 使用辅助视频 6784.38-6786.96秒
2025-07-08 17:12:23 [INFO] 替换片段 294: 使用辅助视频 6786.96-6789.21秒
2025-07-08 17:12:23 [INFO] 处理替换批次 224/252，包含 10 个片段
2025-07-08 17:12:23 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:12:24 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:12:24 [INFO] 替换片段 338: 使用辅助视频 6789.21-6792.42秒
2025-07-08 17:12:25 [INFO] 替换片段 471: 使用辅助视频 6792.42-6794.96秒
2025-07-08 17:12:25 [INFO] 替换片段 404: 使用辅助视频 6794.96-6798.67秒
2025-07-08 17:12:26 [INFO] 替换片段 294: 使用辅助视频 6798.67-6802.50秒
2025-07-08 17:12:27 [INFO] 替换片段 246: 使用辅助视频 6802.50-6805.08秒
2025-07-08 17:12:28 [INFO] 替换片段 192: 使用辅助视频 6805.08-6807.04秒
2025-07-08 17:12:28 [INFO] 替换片段 376: 使用辅助视频 6807.04-6808.58秒
2025-07-08 17:12:28 [INFO] 替换片段 102: 使用辅助视频 6808.58-6812.38秒
2025-07-08 17:12:29 [INFO] 替换片段 452: 使用辅助视频 6812.38-6814.04秒
2025-07-08 17:12:29 [INFO] 替换片段 102: 使用辅助视频 6814.04-6819.71秒
2025-07-08 17:12:30 [INFO] 处理替换批次 225/252，包含 10 个片段
2025-07-08 17:12:30 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:12:30 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:12:31 [INFO] 替换片段 254: 使用辅助视频 6819.71-6824.42秒
2025-07-08 17:12:31 [INFO] 替换片段 179: 使用辅助视频 6824.42-6830.46秒
2025-07-08 17:12:32 [INFO] 替换片段 440: 使用辅助视频 6830.46-6832.33秒
2025-07-08 17:12:32 [INFO] 替换片段 350: 使用辅助视频 6832.33-6837.00秒
2025-07-08 17:12:32 [INFO] 替换片段 149: 使用辅助视频 6837.00-6843.04秒
2025-07-08 17:12:33 [INFO] 替换片段 179: 使用辅助视频 6843.04-6844.96秒
2025-07-08 17:12:33 [INFO] 替换片段 170: 使用辅助视频 6844.96-6859.04秒
2025-07-08 17:12:34 [INFO] 替换片段 412: 使用辅助视频 6859.04-6861.17秒
2025-07-08 17:12:34 [INFO] 替换片段 430: 使用辅助视频 6861.17-6875.83秒
2025-07-08 17:12:34 [INFO] 替换片段 404: 使用辅助视频 6875.83-6877.62秒
2025-07-08 17:12:34 [INFO] 处理替换批次 226/252，包含 10 个片段
2025-07-08 17:12:34 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:12:35 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:12:36 [INFO] 替换片段 236: 使用辅助视频 6877.62-6879.12秒
2025-07-08 17:12:36 [INFO] 替换片段 292: 使用辅助视频 6879.12-6880.96秒
2025-07-08 17:12:36 [INFO] 替换片段 264: 使用辅助视频 6880.96-6887.29秒
2025-07-08 17:12:37 [INFO] 替换片段 352: 使用辅助视频 6887.29-6893.29秒
2025-07-08 17:12:37 [INFO] 替换片段 34: 使用辅助视频 6918.33-6923.04秒
2025-07-08 17:12:38 [INFO] 替换片段 429: 使用辅助视频 6923.04-6930.83秒
2025-07-08 17:12:38 [INFO] 替换片段 432: 使用辅助视频 6930.83-6931.62秒
2025-07-08 17:12:38 [INFO] 替换片段 241: 使用辅助视频 6931.62-6932.50秒
2025-07-08 17:12:38 [INFO] 替换片段 432: 使用辅助视频 6932.50-6933.17秒
2025-07-08 17:12:39 [INFO] 替换片段 433: 使用辅助视频 6933.17-6934.38秒
2025-07-08 17:12:39 [INFO] 处理替换批次 227/252，包含 10 个片段
2025-07-08 17:12:39 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:12:39 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:12:40 [INFO] 替换片段 434: 使用辅助视频 6934.38-6935.33秒
2025-07-08 17:12:40 [INFO] 替换片段 349: 使用辅助视频 6935.33-6936.12秒
2025-07-08 17:12:40 [INFO] 替换片段 436: 使用辅助视频 6936.12-6938.29秒
2025-07-08 17:12:41 [INFO] 替换片段 437: 使用辅助视频 6938.29-6939.04秒
2025-07-08 17:12:41 [INFO] 替换片段 438: 使用辅助视频 6939.04-6941.00秒
2025-07-08 17:12:41 [INFO] 替换片段 439: 使用辅助视频 6941.00-6944.08秒
2025-07-08 17:12:42 [INFO] 替换片段 40: 使用辅助视频 6944.08-6946.04秒
2025-07-08 17:12:42 [INFO] 替换片段 226: 使用辅助视频 6946.04-6947.62秒
2025-07-08 17:12:42 [INFO] 替换片段 374: 使用辅助视频 6947.62-6950.12秒
2025-07-08 17:12:43 [INFO] 替换片段 197: 使用辅助视频 6950.12-6954.75秒
2025-07-08 17:12:43 [INFO] 处理替换批次 228/252，包含 10 个片段
2025-07-08 17:12:43 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:12:44 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:12:44 [INFO] 替换片段 378: 使用辅助视频 6954.75-6956.92秒
2025-07-08 17:12:45 [INFO] 替换片段 475: 使用辅助视频 6956.92-6958.75秒
2025-07-08 17:12:45 [INFO] 替换片段 443: 使用辅助视频 6958.75-6961.96秒
2025-07-08 17:12:46 [INFO] 替换片段 391: 使用辅助视频 6961.96-6964.50秒
2025-07-08 17:12:46 [INFO] 替换片段 444: 使用辅助视频 6964.50-6966.62秒
2025-07-08 17:12:47 [INFO] 替换片段 294: 使用辅助视频 6966.62-6968.12秒
2025-07-08 17:12:47 [INFO] 替换片段 108: 使用辅助视频 6968.12-6969.88秒
2025-07-08 17:12:48 [INFO] 替换片段 321: 使用辅助视频 6969.88-6971.08秒
2025-07-08 17:12:48 [INFO] 替换片段 320: 使用辅助视频 6971.08-6973.29秒
2025-07-08 17:12:48 [INFO] 替换片段 213: 使用辅助视频 6973.29-6975.21秒
2025-07-08 17:12:48 [INFO] 处理替换批次 229/252，包含 10 个片段
2025-07-08 17:12:48 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:12:49 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:12:50 [INFO] 替换片段 439: 使用辅助视频 6975.21-6977.33秒
2025-07-08 17:12:50 [INFO] 替换片段 141: 使用辅助视频 6977.33-6979.21秒
2025-07-08 17:12:51 [INFO] 替换片段 423: 使用辅助视频 6979.21-6982.17秒
2025-07-08 17:12:51 [INFO] 替换片段 389: 使用辅助视频 6982.17-6983.42秒
2025-07-08 17:12:51 [INFO] 替换片段 245: 使用辅助视频 6983.42-6984.38秒
2025-07-08 17:12:52 [INFO] 替换片段 199: 使用辅助视频 6984.38-6986.42秒
2025-07-08 17:12:52 [INFO] 替换片段 439: 使用辅助视频 6986.42-6987.62秒
2025-07-08 17:12:53 [INFO] 替换片段 439: 使用辅助视频 6987.62-6989.62秒
2025-07-08 17:12:53 [INFO] 替换片段 139: 使用辅助视频 6989.62-6991.00秒
2025-07-08 17:12:53 [INFO] 替换片段 420: 使用辅助视频 6991.00-6992.21秒
2025-07-08 17:12:53 [INFO] 处理替换批次 230/252，包含 10 个片段
2025-07-08 17:12:53 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:12:54 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:12:55 [INFO] 替换片段 475: 使用辅助视频 6992.21-6993.79秒
2025-07-08 17:12:55 [INFO] 替换片段 445: 使用辅助视频 6993.79-6997.79秒
2025-07-08 17:12:56 [INFO] 替换片段 381: 使用辅助视频 6997.79-6998.50秒
2025-07-08 17:12:56 [INFO] 替换片段 120: 使用辅助视频 6998.50-7000.46秒
2025-07-08 17:12:56 [INFO] 替换片段 10: 使用辅助视频 7000.46-7002.12秒
2025-07-08 17:12:57 [INFO] 替换片段 364: 使用辅助视频 7002.12-7004.29秒
2025-07-08 17:12:57 [INFO] 替换片段 302: 使用辅助视频 7004.29-7005.92秒
2025-07-08 17:12:58 [INFO] 替换片段 254: 使用辅助视频 7005.92-7007.46秒
2025-07-08 17:12:58 [INFO] 替换片段 421: 使用辅助视频 7007.46-7008.71秒
2025-07-08 17:12:58 [INFO] 替换片段 98: 使用辅助视频 7008.71-7014.71秒
2025-07-08 17:12:58 [INFO] 处理替换批次 231/252，包含 10 个片段
2025-07-08 17:12:58 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:12:59 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:13:00 [INFO] 替换片段 395: 使用辅助视频 7014.71-7016.17秒
2025-07-08 17:13:00 [INFO] 替换片段 447: 使用辅助视频 7016.17-7018.08秒
2025-07-08 17:13:00 [INFO] 替换片段 380: 使用辅助视频 7018.08-7018.92秒
2025-07-08 17:13:01 [INFO] 替换片段 40: 使用辅助视频 7018.92-7021.21秒
2025-07-08 17:13:01 [INFO] 替换片段 155: 使用辅助视频 7021.21-7022.88秒
2025-07-08 17:13:02 [INFO] 替换片段 141: 使用辅助视频 7022.88-7026.25秒
2025-07-08 17:13:02 [INFO] 替换片段 169: 使用辅助视频 7026.25-7027.58秒
2025-07-08 17:13:03 [INFO] 替换片段 141: 使用辅助视频 7027.58-7028.96秒
2025-07-08 17:13:03 [INFO] 替换片段 223: 使用辅助视频 7028.96-7032.25秒
2025-07-08 17:13:04 [INFO] 替换片段 226: 使用辅助视频 7032.25-7034.08秒
2025-07-08 17:13:04 [INFO] 处理替换批次 232/252，包含 10 个片段
2025-07-08 17:13:04 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:13:05 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:13:05 [INFO] 替换片段 12: 使用辅助视频 7034.08-7036.50秒
2025-07-08 17:13:06 [INFO] 替换片段 199: 使用辅助视频 7036.50-7039.75秒
2025-07-08 17:13:07 [INFO] 替换片段 293: 使用辅助视频 7039.75-7042.88秒
2025-07-08 17:13:08 [INFO] 替换片段 351: 使用辅助视频 7042.88-7044.58秒
2025-07-08 17:13:08 [INFO] 替换片段 33: 使用辅助视频 7044.58-7046.12秒
2025-07-08 17:13:08 [INFO] 替换片段 137: 使用辅助视频 7046.12-7046.96秒
2025-07-08 17:13:09 [INFO] 替换片段 452: 使用辅助视频 7046.96-7048.33秒
2025-07-08 17:13:09 [INFO] 替换片段 203: 使用辅助视频 7048.33-7049.46秒
2025-07-08 17:13:09 [INFO] 替换片段 185: 使用辅助视频 7049.46-7052.29秒
2025-07-08 17:13:10 [INFO] 替换片段 174: 使用辅助视频 7052.29-7058.38秒
2025-07-08 17:13:10 [INFO] 处理替换批次 233/252，包含 10 个片段
2025-07-08 17:13:10 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:13:11 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:13:11 [INFO] 替换片段 395: 使用辅助视频 7058.38-7059.88秒
2025-07-08 17:13:12 [INFO] 替换片段 414: 使用辅助视频 7059.88-7062.12秒
2025-07-08 17:13:12 [INFO] 替换片段 263: 使用辅助视频 7062.12-7071.71秒
2025-07-08 17:13:13 [INFO] 替换片段 203: 使用辅助视频 7071.71-7082.96秒
2025-07-08 17:13:13 [INFO] 替换片段 357: 使用辅助视频 7082.96-7088.00秒
2025-07-08 17:13:14 [INFO] 替换片段 338: 使用辅助视频 7088.00-7091.38秒
2025-07-08 17:13:14 [INFO] 替换片段 206: 使用辅助视频 7091.38-7094.54秒
2025-07-08 17:13:15 [INFO] 替换片段 198: 使用辅助视频 7094.54-7095.58秒
2025-07-08 17:13:15 [INFO] 替换片段 211: 使用辅助视频 7095.58-7096.67秒
2025-07-08 17:13:15 [INFO] 替换片段 91: 使用辅助视频 7096.67-7099.12秒
2025-07-08 17:13:16 [INFO] 处理替换批次 234/252，包含 10 个片段
2025-07-08 17:13:16 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:13:17 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:13:17 [INFO] 替换片段 70: 使用辅助视频 7099.12-7122.25秒
2025-07-08 17:13:18 [INFO] 替换片段 321: 使用辅助视频 7122.25-7127.21秒
2025-07-08 17:13:18 [INFO] 替换片段 402: 使用辅助视频 7127.21-7128.88秒
2025-07-08 17:13:19 [INFO] 替换片段 478: 使用辅助视频 7128.88-7130.38秒
2025-07-08 17:13:19 [INFO] 替换片段 208: 使用辅助视频 7130.38-7131.54秒
2025-07-08 17:13:19 [INFO] 替换片段 121: 使用辅助视频 7131.54-7132.75秒
2025-07-08 17:13:20 [INFO] 替换片段 458: 使用辅助视频 7132.75-7134.42秒
2025-07-08 17:13:20 [INFO] 替换片段 208: 使用辅助视频 7134.42-7137.04秒
2025-07-08 17:13:21 [INFO] 替换片段 282: 使用辅助视频 7137.04-7138.17秒
2025-07-08 17:13:21 [INFO] 替换片段 134: 使用辅助视频 7138.17-7139.79秒
2025-07-08 17:13:21 [INFO] 处理替换批次 235/252，包含 10 个片段
2025-07-08 17:13:21 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:13:22 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:13:22 [INFO] 替换片段 460: 使用辅助视频 7139.79-7143.21秒
2025-07-08 17:13:23 [INFO] 替换片段 147: 使用辅助视频 7143.21-7145.12秒
2025-07-08 17:13:24 [INFO] 替换片段 389: 使用辅助视频 7145.12-7146.21秒
2025-07-08 17:13:24 [INFO] 替换片段 10: 使用辅助视频 7146.21-7149.71秒
2025-07-08 17:13:25 [INFO] 替换片段 108: 使用辅助视频 7149.71-7150.96秒
2025-07-08 17:13:25 [INFO] 替换片段 463: 使用辅助视频 7150.96-7155.67秒
2025-07-08 17:13:25 [INFO] 替换片段 82: 使用辅助视频 7155.67-7161.29秒
2025-07-08 17:13:26 [INFO] 替换片段 247: 使用辅助视频 7161.29-7162.79秒
2025-07-08 17:13:26 [INFO] 替换片段 364: 使用辅助视频 7162.79-7164.21秒
2025-07-08 17:13:27 [INFO] 替换片段 242: 使用辅助视频 7164.21-7165.17秒
2025-07-08 17:13:27 [INFO] 处理替换批次 236/252，包含 10 个片段
2025-07-08 17:13:27 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:13:28 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:13:28 [INFO] 替换片段 58: 使用辅助视频 7165.17-7169.92秒
2025-07-08 17:13:28 [INFO] 替换片段 463: 使用辅助视频 7169.92-7170.67秒
2025-07-08 17:13:28 [INFO] 替换片段 464: 使用辅助视频 7170.67-7171.96秒
2025-07-08 17:13:29 [INFO] 替换片段 198: 使用辅助视频 7171.96-7174.54秒
2025-07-08 17:13:29 [INFO] 替换片段 466: 使用辅助视频 7174.54-7181.08秒
2025-07-08 17:13:30 [INFO] 替换片段 38: 使用辅助视频 7181.08-7186.54秒
2025-07-08 17:13:30 [INFO] 替换片段 468: 使用辅助视频 7186.54-7188.25秒
2025-07-08 17:13:31 [INFO] 替换片段 255: 使用辅助视频 7188.25-7191.29秒
2025-07-08 17:13:31 [INFO] 替换片段 336: 使用辅助视频 7191.29-7192.71秒
2025-07-08 17:13:32 [INFO] 替换片段 199: 使用辅助视频 7192.71-7195.96秒
2025-07-08 17:13:32 [INFO] 处理替换批次 237/252，包含 10 个片段
2025-07-08 17:13:32 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:13:33 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:13:33 [INFO] 替换片段 472: 使用辅助视频 7195.96-7198.04秒
2025-07-08 17:13:34 [INFO] 替换片段 473: 使用辅助视频 7198.04-7199.54秒
2025-07-08 17:13:34 [INFO] 替换片段 474: 使用辅助视频 7199.54-7201.12秒
2025-07-08 17:13:34 [INFO] 替换片段 475: 使用辅助视频 7201.12-7203.75秒
2025-07-08 17:13:35 [INFO] 替换片段 476: 使用辅助视频 7203.75-7210.17秒
2025-07-08 17:13:35 [INFO] 替换片段 174: 使用辅助视频 7210.17-7214.42秒
2025-07-08 17:13:36 [INFO] 替换片段 203: 使用辅助视频 7225.75-7255.58秒
2025-07-08 17:13:36 [INFO] 替换片段 107: 使用辅助视频 7255.58-7260.58秒
2025-07-08 17:13:36 [INFO] 替换片段 478: 使用辅助视频 7297.38-7317.38秒
2025-07-08 17:13:37 [INFO] 替换片段 450: 使用辅助视频 7317.38-7320.75秒
2025-07-08 17:13:37 [INFO] 处理替换批次 238/252，包含 10 个片段
2025-07-08 17:13:37 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:13:38 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:13:38 [INFO] 替换片段 480: 使用辅助视频 7330.92-7342.12秒
2025-07-08 17:13:39 [INFO] 替换片段 377: 使用辅助视频 7342.12-7349.08秒
2025-07-08 17:13:39 [INFO] 替换片段 261: 使用辅助视频 7349.08-7352.08秒
2025-07-08 17:13:40 [INFO] 替换片段 291: 使用辅助视频 7352.08-7361.17秒
2025-07-08 17:13:41 [INFO] 替换片段 399: 使用辅助视频 7361.17-7364.96秒
2025-07-08 17:13:41 [INFO] 替换片段 68: 使用辅助视频 7364.96-7371.83秒
2025-07-08 17:13:42 [INFO] 替换片段 483: 使用辅助视频 7371.83-7376.92秒
2025-07-08 17:13:42 [INFO] 替换片段 484: 使用辅助视频 7376.92-7380.04秒
2025-07-08 17:13:43 [INFO] 替换片段 227: 使用辅助视频 7380.04-7389.42秒
2025-07-08 17:13:44 [INFO] 替换片段 242: 使用辅助视频 7389.42-7394.79秒
2025-07-08 17:13:44 [INFO] 处理替换批次 239/252，包含 10 个片段
2025-07-08 17:13:44 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:13:45 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:13:45 [INFO] 替换片段 45: 使用辅助视频 7394.79-7400.38秒
2025-07-08 17:13:46 [INFO] 替换片段 374: 使用辅助视频 7400.38-7403.58秒
2025-07-08 17:13:47 [INFO] 替换片段 190: 使用辅助视频 7403.58-7420.54秒
2025-07-08 17:13:47 [INFO] 替换片段 384: 使用辅助视频 7420.54-7421.67秒
2025-07-08 17:13:47 [INFO] 替换片段 153: 使用辅助视频 7421.67-7422.46秒
2025-07-08 17:13:48 [INFO] 替换片段 24: 使用辅助视频 7422.46-7424.29秒
2025-07-08 17:13:48 [INFO] 替换片段 263: 使用辅助视频 7424.29-7426.58秒
2025-07-08 17:13:49 [INFO] 替换片段 128: 使用辅助视频 7426.58-7433.42秒
2025-07-08 17:13:49 [INFO] 替换片段 379: 使用辅助视频 7433.42-7436.79秒
2025-07-08 17:13:50 [INFO] 替换片段 217: 使用辅助视频 7436.79-7439.50秒
2025-07-08 17:13:50 [INFO] 处理替换批次 240/252，包含 10 个片段
2025-07-08 17:13:50 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:13:51 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:13:51 [INFO] 替换片段 158: 使用辅助视频 7439.50-7441.92秒
2025-07-08 17:13:52 [INFO] 替换片段 179: 使用辅助视频 7441.92-7443.92秒
2025-07-08 17:13:52 [INFO] 替换片段 60: 使用辅助视频 7443.92-7445.67秒
2025-07-08 17:13:53 [INFO] 替换片段 420: 使用辅助视频 7445.67-7447.67秒
2025-07-08 17:13:53 [INFO] 替换片段 371: 使用辅助视频 7447.67-7453.17秒
2025-07-08 17:13:54 [INFO] 替换片段 420: 使用辅助视频 7453.17-7457.79秒
2025-07-08 17:13:54 [INFO] 替换片段 335: 使用辅助视频 7457.79-7461.67秒
2025-07-08 17:13:55 [INFO] 替换片段 118: 使用辅助视频 7461.67-7466.08秒
2025-07-08 17:13:56 [INFO] 替换片段 226: 使用辅助视频 7466.08-7469.25秒
2025-07-08 17:13:57 [INFO] 替换片段 218: 使用辅助视频 7469.25-7478.75秒
2025-07-08 17:13:57 [INFO] 处理替换批次 241/252，包含 10 个片段
2025-07-08 17:13:57 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:13:58 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:13:58 [INFO] 替换片段 476: 使用辅助视频 7478.75-7487.38秒
2025-07-08 17:13:59 [INFO] 替换片段 49: 使用辅助视频 7487.38-7490.29秒
2025-07-08 17:13:59 [INFO] 替换片段 108: 使用辅助视频 7490.29-7496.75秒
2025-07-08 17:14:00 [INFO] 替换片段 402: 使用辅助视频 7496.75-7499.17秒
2025-07-08 17:14:00 [INFO] 替换片段 400: 使用辅助视频 7499.17-7500.92秒
2025-07-08 17:14:01 [INFO] 替换片段 235: 使用辅助视频 7500.92-7505.62秒
2025-07-08 17:14:01 [INFO] 替换片段 137: 使用辅助视频 7505.62-7508.83秒
2025-07-08 17:14:02 [INFO] 替换片段 412: 使用辅助视频 7508.83-7510.00秒
2025-07-08 17:14:02 [INFO] 替换片段 212: 使用辅助视频 7510.00-7511.54秒
2025-07-08 17:14:02 [INFO] 替换片段 96: 使用辅助视频 7511.54-7517.21秒
2025-07-08 17:14:02 [INFO] 处理替换批次 242/252，包含 10 个片段
2025-07-08 17:14:02 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:14:03 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:14:04 [INFO] 替换片段 246: 使用辅助视频 7517.21-7521.38秒
2025-07-08 17:14:05 [INFO] 替换片段 179: 使用辅助视频 7521.38-7524.92秒
2025-07-08 17:14:06 [INFO] 替换片段 310: 使用辅助视频 7524.92-7527.38秒
2025-07-08 17:14:07 [INFO] 替换片段 108: 使用辅助视频 7527.38-7529.79秒
2025-07-08 17:14:07 [INFO] 替换片段 146: 使用辅助视频 7529.79-7531.92秒
2025-07-08 17:14:08 [INFO] 替换片段 104: 使用辅助视频 7531.92-7537.92秒
2025-07-08 17:14:08 [INFO] 替换片段 146: 使用辅助视频 7537.92-7539.88秒
2025-07-08 17:14:09 [INFO] 替换片段 235: 使用辅助视频 7539.88-7541.50秒
2025-07-08 17:14:09 [INFO] 替换片段 254: 使用辅助视频 7541.50-7544.58秒
2025-07-08 17:14:10 [INFO] 替换片段 365: 使用辅助视频 7544.58-7551.83秒
2025-07-08 17:14:10 [INFO] 处理替换批次 243/252，包含 10 个片段
2025-07-08 17:14:10 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:14:11 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:14:11 [INFO] 替换片段 257: 使用辅助视频 7551.83-7555.46秒
2025-07-08 17:14:12 [INFO] 替换片段 471: 使用辅助视频 7555.46-7558.50秒
2025-07-08 17:14:13 [INFO] 替换片段 34: 使用辅助视频 7558.50-7560.33秒
2025-07-08 17:14:13 [INFO] 替换片段 128: 使用辅助视频 7560.33-7564.42秒
2025-07-08 17:14:14 [INFO] 替换片段 388: 使用辅助视频 7564.42-7566.58秒
2025-07-08 17:14:14 [INFO] 替换片段 439: 使用辅助视频 7566.58-7569.58秒
2025-07-08 17:14:15 [INFO] 替换片段 230: 使用辅助视频 7569.58-7574.04秒
2025-07-08 17:14:15 [INFO] 替换片段 246: 使用辅助视频 7574.04-7578.58秒
2025-07-08 17:14:16 [INFO] 替换片段 410: 使用辅助视频 7578.58-7580.38秒
2025-07-08 17:14:16 [INFO] 替换片段 209: 使用辅助视频 7580.38-7581.79秒
2025-07-08 17:14:16 [INFO] 处理替换批次 244/252，包含 10 个片段
2025-07-08 17:14:16 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:14:17 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:14:18 [INFO] 替换片段 32: 使用辅助视频 7581.79-7583.04秒
2025-07-08 17:14:18 [INFO] 替换片段 296: 使用辅助视频 7583.04-7591.71秒
2025-07-08 17:14:18 [INFO] 替换片段 461: 使用辅助视频 7591.71-7595.88秒
2025-07-08 17:14:19 [INFO] 替换片段 247: 使用辅助视频 7595.88-7598.29秒
2025-07-08 17:14:20 [INFO] 替换片段 403: 使用辅助视频 7598.29-7603.33秒
2025-07-08 17:14:20 [INFO] 替换片段 189: 使用辅助视频 7603.33-7605.29秒
2025-07-08 17:14:21 [INFO] 替换片段 247: 使用辅助视频 7605.29-7606.79秒
2025-07-08 17:14:21 [INFO] 替换片段 235: 使用辅助视频 7606.79-7608.83秒
2025-07-08 17:14:22 [INFO] 替换片段 150: 使用辅助视频 7608.83-7610.33秒
2025-07-08 17:14:22 [INFO] 替换片段 223: 使用辅助视频 7610.33-7617.62秒
2025-07-08 17:14:22 [INFO] 处理替换批次 245/252，包含 10 个片段
2025-07-08 17:14:22 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:14:23 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:14:24 [INFO] 替换片段 235: 使用辅助视频 7617.62-7621.71秒
2025-07-08 17:14:25 [INFO] 替换片段 133: 使用辅助视频 7621.71-7625.04秒
2025-07-08 17:14:26 [INFO] 替换片段 303: 使用辅助视频 7625.04-7626.17秒
2025-07-08 17:14:26 [INFO] 替换片段 401: 使用辅助视频 7626.17-7627.71秒
2025-07-08 17:14:26 [INFO] 替换片段 113: 使用辅助视频 7627.71-7633.29秒
2025-07-08 17:14:27 [INFO] 替换片段 104: 使用辅助视频 7633.29-7637.79秒
2025-07-08 17:14:27 [INFO] 替换片段 117: 使用辅助视频 7637.79-7639.17秒
2025-07-08 17:14:28 [INFO] 替换片段 454: 使用辅助视频 7639.17-7642.75秒
2025-07-08 17:14:28 [INFO] 替换片段 450: 使用辅助视频 7642.75-7647.67秒
2025-07-08 17:14:29 [INFO] 替换片段 155: 使用辅助视频 7647.67-7651.04秒
2025-07-08 17:14:29 [INFO] 处理替换批次 246/252，包含 10 个片段
2025-07-08 17:14:29 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:14:30 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:14:30 [INFO] 替换片段 137: 使用辅助视频 7651.04-7653.75秒
2025-07-08 17:14:31 [INFO] 替换片段 398: 使用辅助视频 7653.75-7656.12秒
2025-07-08 17:14:32 [INFO] 替换片段 118: 使用辅助视频 7656.12-7658.42秒
2025-07-08 17:14:32 [INFO] 替换片段 437: 使用辅助视频 7658.42-7660.38秒
2025-07-08 17:14:33 [INFO] 替换片段 445: 使用辅助视频 7660.38-7665.00秒
2025-07-08 17:14:33 [INFO] 替换片段 164: 使用辅助视频 7665.00-7667.42秒
2025-07-08 17:14:34 [INFO] 替换片段 350: 使用辅助视频 7667.42-7669.42秒
2025-07-08 17:14:34 [INFO] 替换片段 104: 使用辅助视频 7669.42-7671.79秒
2025-07-08 17:14:35 [INFO] 替换片段 350: 使用辅助视频 7671.79-7674.67秒
2025-07-08 17:14:36 [INFO] 替换片段 279: 使用辅助视频 7674.67-7678.71秒
2025-07-08 17:14:36 [INFO] 处理替换批次 247/252，包含 10 个片段
2025-07-08 17:14:36 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:14:37 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:14:37 [INFO] 替换片段 97: 使用辅助视频 7678.71-7696.96秒
2025-07-08 17:14:38 [INFO] 替换片段 1: 使用辅助视频 7696.96-7698.50秒
2025-07-08 17:14:38 [INFO] 替换片段 60: 使用辅助视频 7698.50-7701.96秒
2025-07-08 17:14:39 [INFO] 替换片段 471: 使用辅助视频 7701.96-7703.75秒
2025-07-08 17:14:40 [INFO] 替换片段 352: 使用辅助视频 7703.75-7705.92秒
2025-07-08 17:14:40 [INFO] 替换片段 362: 使用辅助视频 7705.92-7712.62秒
2025-07-08 17:14:41 [INFO] 替换片段 417: 使用辅助视频 7712.62-7716.08秒
2025-07-08 17:14:42 [INFO] 替换片段 404: 使用辅助视频 7716.08-7730.38秒
2025-07-08 17:14:42 [INFO] 替换片段 120: 使用辅助视频 7730.38-7737.08秒
2025-07-08 17:14:43 [INFO] 替换片段 13: 使用辅助视频 7737.08-7740.04秒
2025-07-08 17:14:43 [INFO] 处理替换批次 248/252，包含 10 个片段
2025-07-08 17:14:43 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:14:43 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:14:44 [INFO] 替换片段 37: 使用辅助视频 7740.04-7742.88秒
2025-07-08 17:14:44 [INFO] 替换片段 357: 使用辅助视频 7742.88-7744.46秒
2025-07-08 17:14:45 [INFO] 替换片段 300: 使用辅助视频 7744.46-7748.04秒
2025-07-08 17:14:46 [INFO] 替换片段 179: 使用辅助视频 7748.04-7753.88秒
2025-07-08 17:14:46 [INFO] 替换片段 338: 使用辅助视频 7753.88-7757.25秒
2025-07-08 17:14:47 [INFO] 替换片段 113: 使用辅助视频 7757.25-7760.79秒
2025-07-08 17:14:48 [INFO] 替换片段 291: 使用辅助视频 7760.79-7764.25秒
2025-07-08 17:14:48 [INFO] 替换片段 338: 使用辅助视频 7776.96-7779.67秒
2025-07-08 17:14:49 [INFO] 替换片段 217: 使用辅助视频 7779.67-7780.96秒
2025-07-08 17:14:50 [INFO] 替换片段 131: 使用辅助视频 7780.96-7783.08秒
2025-07-08 17:14:50 [INFO] 处理替换批次 249/252，包含 10 个片段
2025-07-08 17:14:50 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:14:51 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:14:51 [INFO] 替换片段 203: 使用辅助视频 7783.08-7784.08秒
2025-07-08 17:14:51 [INFO] 替换片段 121: 使用辅助视频 7784.08-7786.71秒
2025-07-08 17:14:52 [INFO] 替换片段 203: 使用辅助视频 7786.71-7787.62秒
2025-07-08 17:14:52 [INFO] 替换片段 226: 使用辅助视频 7787.62-7788.46秒
2025-07-08 17:14:53 [INFO] 替换片段 39: 使用辅助视频 7788.46-7794.67秒
2025-07-08 17:14:53 [INFO] 替换片段 50: 使用辅助视频 7794.67-7805.38秒
2025-07-08 17:14:54 [INFO] 替换片段 197: 使用辅助视频 7805.38-7812.54秒
2025-07-08 17:14:55 [INFO] 替换片段 174: 使用辅助视频 7812.54-7840.29秒
2025-07-08 17:14:55 [INFO] 替换片段 371: 使用辅助视频 7840.29-7842.54秒
2025-07-08 17:14:55 [INFO] 替换片段 235: 使用辅助视频 7842.54-7852.00秒
2025-07-08 17:14:56 [INFO] 处理替换批次 250/252，包含 10 个片段
2025-07-08 17:14:56 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:14:56 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:14:57 [INFO] 替换片段 120: 使用辅助视频 7852.00-7855.75秒
2025-07-08 17:14:58 [INFO] 替换片段 216: 使用辅助视频 7855.75-7863.83秒
2025-07-08 17:14:58 [INFO] 替换片段 123: 使用辅助视频 7863.83-7866.29秒
2025-07-08 17:14:59 [INFO] 替换片段 96: 使用辅助视频 7866.29-7873.92秒
2025-07-08 17:15:00 [INFO] 替换片段 294: 使用辅助视频 7873.92-7875.83秒
2025-07-08 17:15:00 [INFO] 替换片段 188: 使用辅助视频 7875.83-7878.88秒
2025-07-08 17:15:01 [INFO] 替换片段 158: 使用辅助视频 7878.88-7893.83秒
2025-07-08 17:15:01 [INFO] 替换片段 254: 使用辅助视频 7893.83-7896.96秒
2025-07-08 17:15:02 [INFO] 替换片段 420: 使用辅助视频 7896.96-7901.33秒
2025-07-08 17:15:03 [INFO] 替换片段 16: 使用辅助视频 7901.33-7906.29秒
2025-07-08 17:15:03 [INFO] 处理替换批次 251/252，包含 10 个片段
2025-07-08 17:15:03 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:15:04 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:15:04 [INFO] 替换片段 288: 使用辅助视频 7906.29-7915.92秒
2025-07-08 17:15:05 [INFO] 替换片段 420: 使用辅助视频 7915.92-7920.08秒
2025-07-08 17:15:05 [INFO] 替换片段 347: 使用辅助视频 7920.08-7929.46秒
2025-07-08 17:15:06 [INFO] 替换片段 150: 使用辅助视频 7929.46-7934.71秒
2025-07-08 17:15:06 [INFO] 替换片段 98: 使用辅助视频 7934.71-7951.46秒
2025-07-08 17:15:06 [INFO] 替换片段 176: 使用辅助视频 7951.46-7961.46秒
2025-07-08 17:15:07 [INFO] 替换片段 203: 使用辅助视频 8108.38-8110.50秒
2025-07-08 17:15:07 [INFO] 替换片段 408: 使用辅助视频 8110.50-8111.92秒
2025-07-08 17:15:07 [INFO] 替换片段 230: 使用辅助视频 8111.92-8113.33秒
2025-07-08 17:15:08 [INFO] 替换片段 350: 使用辅助视频 8113.33-8114.46秒
2025-07-08 17:15:08 [INFO] 处理替换批次 252/252，包含 5 个片段
2025-07-08 17:15:08 [INFO] 加载辅助视频: D:/25125/Videos/video_test/01_y.mp4
2025-07-08 17:15:08 [INFO] 辅助视频加载成功，时长: 8142.29秒
2025-07-08 17:15:08 [INFO] 替换片段 388: 使用辅助视频 8114.46-8115.71秒
2025-07-08 17:15:08 [INFO] 替换片段 478: 使用辅助视频 8115.71-8117.38秒
2025-07-08 17:15:09 [INFO] 替换片段 10: 使用辅助视频 8117.38-8119.54秒
2025-07-08 17:15:09 [INFO] 替换片段 453: 使用辅助视频 8119.54-8121.42秒
2025-07-08 17:15:09 [INFO] 替换片段 410: 使用辅助视频 8121.42-8128.33秒
2025-07-08 17:15:09 [INFO] 开始合成 487 个视频片段...
2025-07-08 17:15:09 [INFO] 有效片段数量: 487
2025-07-08 17:15:09 [INFO] 使用内存优化方法合成视频...
2025-07-08 17:15:09 [INFO] 合并 487 个有效片段，总时长: 1621.83秒
2025-07-08 17:15:09 [INFO] 视频片段数量或总时长较大，使用降低分辨率的合并策略
2025-07-08 17:15:09 [INFO] 降低视频分辨率以节省内存...
2025-07-08 17:15:09 [INFO] 使用缩放因子: 0.7，处理 487 个有效片段
2025-07-08 17:15:09 [INFO] 处理片段 0 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 1 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 2 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 3 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 4 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 5 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 6 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 7 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 8 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 9 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 10 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 11 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 12 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 13 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 14 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 15 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 16 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 17 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 18 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 19 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 20 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 21 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 22 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 23 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 24 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 25 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 26 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 27 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 28 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 29 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 30 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 31 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 32 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 33 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 34 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 35 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 36 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 37 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 38 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 39 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 40 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 41 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 42 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 43 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 44 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 45 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 46 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 47 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 48 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 49 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 50 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 51 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 52 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 53 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 54 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 55 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 56 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 57 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 58 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 59 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 60 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 61 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 62 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 63 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 64 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 65 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 66 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 67 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 68 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 69 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 70 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 71 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 72 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 73 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 74 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 75 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 76 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 77 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 78 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 79 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 80 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 81 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 82 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 83 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 84 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 85 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 86 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 87 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 88 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 89 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 90 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 91 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 92 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 93 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 94 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 95 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 96 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 97 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 98 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 99 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 100 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 101 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 102 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 103 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 104 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 105 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 106 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 107 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 108 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 109 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 110 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 111 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 112 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 113 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 114 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 115 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 116 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 117 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 118 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 119 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 120 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 121 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 122 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 123 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 124 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 125 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 126 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 127 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 128 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 129 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 130 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 131 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 132 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 133 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 134 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 135 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 136 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 137 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 138 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 139 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 140 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 141 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 142 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 143 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 144 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 145 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 146 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 147 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 148 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 149 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 150 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 151 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 152 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 153 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 154 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 155 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 156 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 157 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 158 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 159 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 160 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 161 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 162 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 163 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 164 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 165 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 166 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 167 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 168 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 169 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 170 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 171 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 172 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 173 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 174 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 175 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 176 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 177 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 178 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 179 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 180 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 181 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 182 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 183 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 184 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 185 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 186 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 187 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 188 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 189 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 190 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 191 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 192 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 193 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 194 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 195 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 196 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 197 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 198 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 199 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 200 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 201 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 202 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 203 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 204 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 205 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 206 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 207 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 208 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 209 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 210 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 211 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 212 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 213 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 214 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 215 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 216 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 217 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 218 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 219 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 220 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 221 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 222 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 223 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 224 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 225 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 226 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 227 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 228 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 229 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 230 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 231 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 232 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 233 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 234 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 235 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 236 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 237 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 238 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 239 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 240 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 241 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 242 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 243 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 244 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 245 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 246 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 247 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 248 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 249 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 250 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 251 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 252 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 253 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 254 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 255 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 256 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 257 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 258 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 259 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 260 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 261 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 262 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 263 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 264 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 265 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 266 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 267 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 268 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 269 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 270 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 271 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 272 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 273 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 274 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 275 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 276 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 277 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 278 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 279 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 280 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 281 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 282 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 283 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 284 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 285 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 286 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 287 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 288 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 289 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 290 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 291 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 292 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 293 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 294 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 295 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 296 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 297 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 298 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 299 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 300 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 301 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 302 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 303 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 304 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 305 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 306 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 307 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 308 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 309 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 310 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 311 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 312 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 313 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 314 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 315 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 316 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 317 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 318 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 319 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 320 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 321 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 322 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 323 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 324 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 325 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 326 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 327 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 328 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 329 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 330 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 331 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 332 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 333 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 334 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 335 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 336 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 337 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 338 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 339 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 340 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 341 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 342 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 343 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 344 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 345 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 346 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 347 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 348 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 349 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 350 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 351 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 352 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 353 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 354 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 355 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 356 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 357 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 358 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 359 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 360 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 361 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 362 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 363 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 364 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 365 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 366 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 367 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 368 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 369 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 370 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 371 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 372 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 373 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 374 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 375 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 376 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 377 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 378 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 379 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 380 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 381 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 382 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 383 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 384 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 385 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 386 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 387 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 388 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 389 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 390 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 391 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 392 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 393 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 394 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 395 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 396 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 397 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 398 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 399 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 400 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 401 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 402 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 403 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 404 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 405 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 406 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 407 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 408 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 409 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 410 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 411 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 412 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 413 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 414 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 415 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 416 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 417 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 418 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 419 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 420 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 421 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 422 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 423 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 424 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 425 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 426 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 427 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 428 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 429 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 430 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 431 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 432 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 433 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 434 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 435 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 436 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 437 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 438 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 439 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 440 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 441 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 442 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 443 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 444 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 445 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 446 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 447 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 448 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 449 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 450 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 451 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 452 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 453 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 454 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 455 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 456 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 457 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 458 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 459 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 460 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 461 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 462 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 463 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 464 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 465 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 466 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 467 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 468 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 469 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 470 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 471 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 472 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 473 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 474 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 475 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 476 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 477 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 478 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 479 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 480 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 481 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 482 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 483 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 484 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 485 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 处理片段 486 失败: 'NoneType' object has no attribute 'get_frame'，尝试使用原始片段
2025-07-08 17:15:09 [INFO] 合并降低分辨率的批次 1/163
2025-07-08 17:15:09 [INFO] 合并降低分辨率的批次 2/163
2025-07-08 17:15:09 [INFO] 合并降低分辨率的批次 3/163
2025-07-08 17:15:10 [INFO] 合并降低分辨率的批次 4/163
2025-07-08 17:15:10 [INFO] 合并降低分辨率的批次 5/163
2025-07-08 17:15:10 [INFO] 合并降低分辨率的批次 6/163
2025-07-08 17:15:10 [INFO] 合并降低分辨率的批次 7/163
2025-07-08 17:15:10 [INFO] 合并降低分辨率的批次 8/163
2025-07-08 17:15:10 [INFO] 合并降低分辨率的批次 9/163
2025-07-08 17:15:10 [INFO] 合并降低分辨率的批次 10/163
2025-07-08 17:15:10 [INFO] 合并降低分辨率的批次 11/163
2025-07-08 17:15:10 [INFO] 合并降低分辨率的批次 12/163
2025-07-08 17:15:10 [INFO] 合并降低分辨率的批次 13/163
2025-07-08 17:15:10 [INFO] 合并降低分辨率的批次 14/163
2025-07-08 17:15:10 [INFO] 合并降低分辨率的批次 15/163
2025-07-08 17:15:10 [INFO] 合并降低分辨率的批次 16/163
2025-07-08 17:15:10 [INFO] 合并降低分辨率的批次 17/163
2025-07-08 17:15:10 [INFO] 合并降低分辨率的批次 18/163
2025-07-08 17:15:10 [INFO] 合并降低分辨率的批次 19/163
2025-07-08 17:15:10 [INFO] 合并降低分辨率的批次 20/163
2025-07-08 17:15:10 [INFO] 合并降低分辨率的批次 21/163
2025-07-08 17:15:11 [INFO] 合并降低分辨率的批次 22/163
2025-07-08 17:15:11 [INFO] 合并降低分辨率的批次 23/163
2025-07-08 17:15:11 [INFO] 合并降低分辨率的批次 24/163
2025-07-08 17:15:11 [INFO] 合并降低分辨率的批次 25/163
2025-07-08 17:15:11 [INFO] 合并降低分辨率的批次 26/163
2025-07-08 17:15:11 [INFO] 合并降低分辨率的批次 27/163
2025-07-08 17:15:11 [INFO] 合并降低分辨率的批次 28/163
2025-07-08 17:15:11 [INFO] 合并降低分辨率的批次 29/163
2025-07-08 17:15:11 [INFO] 合并降低分辨率的批次 30/163
2025-07-08 17:15:11 [INFO] 合并降低分辨率的批次 31/163
2025-07-08 17:15:11 [INFO] 合并降低分辨率的批次 32/163
2025-07-08 17:15:11 [INFO] 合并降低分辨率的批次 33/163
2025-07-08 17:15:11 [INFO] 合并降低分辨率的批次 34/163
2025-07-08 17:15:11 [INFO] 合并降低分辨率的批次 35/163
2025-07-08 17:15:11 [INFO] 合并降低分辨率的批次 36/163
2025-07-08 17:15:12 [INFO] 合并降低分辨率的批次 37/163
2025-07-08 17:15:12 [INFO] 合并降低分辨率的批次 38/163
2025-07-08 17:15:12 [INFO] 合并降低分辨率的批次 39/163
2025-07-08 17:15:12 [INFO] 合并降低分辨率的批次 40/163
2025-07-08 17:15:12 [INFO] 合并降低分辨率的批次 41/163
2025-07-08 17:15:12 [INFO] 合并降低分辨率的批次 42/163
2025-07-08 17:15:12 [INFO] 合并降低分辨率的批次 43/163
2025-07-08 17:15:12 [INFO] 合并降低分辨率的批次 44/163
2025-07-08 17:15:12 [INFO] 合并降低分辨率的批次 45/163
2025-07-08 17:15:12 [INFO] 合并降低分辨率的批次 46/163
2025-07-08 17:15:12 [INFO] 合并降低分辨率的批次 47/163
2025-07-08 17:15:12 [INFO] 合并降低分辨率的批次 48/163
2025-07-08 17:15:13 [INFO] 合并降低分辨率的批次 49/163
2025-07-08 17:15:13 [INFO] 合并降低分辨率的批次 50/163
2025-07-08 17:15:13 [INFO] 合并降低分辨率的批次 51/163
2025-07-08 17:15:13 [INFO] 合并降低分辨率的批次 52/163
2025-07-08 17:15:13 [INFO] 合并降低分辨率的批次 53/163
2025-07-08 17:15:13 [INFO] 合并降低分辨率的批次 54/163
2025-07-08 17:15:13 [INFO] 合并降低分辨率的批次 55/163
2025-07-08 17:15:14 [INFO] 合并降低分辨率的批次 56/163
2025-07-08 17:15:14 [INFO] 合并降低分辨率的批次 57/163
2025-07-08 17:15:14 [INFO] 合并降低分辨率的批次 58/163
2025-07-08 17:15:14 [INFO] 合并降低分辨率的批次 59/163
2025-07-08 17:15:14 [INFO] 合并降低分辨率的批次 60/163
2025-07-08 17:15:14 [INFO] 合并降低分辨率的批次 61/163
2025-07-08 17:15:14 [INFO] 合并降低分辨率的批次 62/163
2025-07-08 17:15:14 [INFO] 合并降低分辨率的批次 63/163
2025-07-08 17:15:14 [INFO] 合并降低分辨率的批次 64/163
2025-07-08 17:15:15 [INFO] 合并降低分辨率的批次 65/163
2025-07-08 17:15:15 [INFO] 合并降低分辨率的批次 66/163
2025-07-08 17:15:15 [INFO] 合并降低分辨率的批次 67/163
2025-07-08 17:15:15 [INFO] 合并降低分辨率的批次 68/163
2025-07-08 17:15:15 [INFO] 合并降低分辨率的批次 69/163
2025-07-08 17:15:15 [INFO] 合并降低分辨率的批次 70/163
2025-07-08 17:15:15 [INFO] 合并降低分辨率的批次 71/163
2025-07-08 17:15:15 [INFO] 合并降低分辨率的批次 72/163
2025-07-08 17:15:15 [INFO] 合并降低分辨率的批次 73/163
2025-07-08 17:15:16 [INFO] 合并降低分辨率的批次 74/163
2025-07-08 17:15:16 [INFO] 合并降低分辨率的批次 75/163
2025-07-08 17:15:16 [INFO] 合并降低分辨率的批次 76/163
2025-07-08 17:15:16 [INFO] 合并降低分辨率的批次 77/163
2025-07-08 17:15:16 [INFO] 合并降低分辨率的批次 78/163
2025-07-08 17:15:16 [INFO] 合并降低分辨率的批次 79/163
2025-07-08 17:15:16 [INFO] 合并降低分辨率的批次 80/163
2025-07-08 17:15:16 [INFO] 合并降低分辨率的批次 81/163
2025-07-08 17:15:16 [INFO] 合并降低分辨率的批次 82/163
2025-07-08 17:15:17 [INFO] 合并降低分辨率的批次 83/163
2025-07-08 17:15:17 [INFO] 合并降低分辨率的批次 84/163
2025-07-08 17:15:17 [INFO] 合并降低分辨率的批次 85/163
2025-07-08 17:15:17 [INFO] 合并降低分辨率的批次 86/163
2025-07-08 17:15:17 [INFO] 合并降低分辨率的批次 87/163
2025-07-08 17:15:17 [INFO] 合并降低分辨率的批次 88/163
2025-07-08 17:15:17 [INFO] 合并降低分辨率的批次 89/163
2025-07-08 17:15:17 [INFO] 合并降低分辨率的批次 90/163
2025-07-08 17:15:17 [INFO] 合并降低分辨率的批次 91/163
2025-07-08 17:15:17 [INFO] 合并降低分辨率的批次 92/163
2025-07-08 17:15:18 [INFO] 合并降低分辨率的批次 93/163
2025-07-08 17:15:18 [INFO] 合并降低分辨率的批次 94/163
2025-07-08 17:15:18 [INFO] 合并降低分辨率的批次 95/163
2025-07-08 17:15:18 [INFO] 合并降低分辨率的批次 96/163
2025-07-08 17:15:18 [INFO] 合并降低分辨率的批次 97/163
2025-07-08 17:15:18 [INFO] 合并降低分辨率的批次 98/163
2025-07-08 17:15:18 [INFO] 合并降低分辨率的批次 99/163
2025-07-08 17:15:19 [INFO] 合并降低分辨率的批次 100/163
2025-07-08 17:15:19 [INFO] 合并降低分辨率的批次 101/163
2025-07-08 17:15:19 [INFO] 合并降低分辨率的批次 102/163
2025-07-08 17:15:19 [INFO] 合并降低分辨率的批次 103/163
2025-07-08 17:15:19 [INFO] 合并降低分辨率的批次 104/163
2025-07-08 17:15:19 [INFO] 合并降低分辨率的批次 105/163
2025-07-08 17:15:19 [INFO] 合并降低分辨率的批次 106/163
2025-07-08 17:15:19 [INFO] 合并降低分辨率的批次 107/163
2025-07-08 17:15:19 [INFO] 合并降低分辨率的批次 108/163
2025-07-08 17:15:19 [INFO] 合并降低分辨率的批次 109/163
2025-07-08 17:15:19 [INFO] 合并降低分辨率的批次 110/163
2025-07-08 17:15:20 [INFO] 合并降低分辨率的批次 111/163
2025-07-08 17:15:20 [INFO] 合并降低分辨率的批次 112/163
2025-07-08 17:15:20 [INFO] 合并降低分辨率的批次 113/163
2025-07-08 17:15:20 [INFO] 合并降低分辨率的批次 114/163
2025-07-08 17:15:20 [INFO] 合并降低分辨率的批次 115/163
2025-07-08 17:15:20 [INFO] 合并降低分辨率的批次 116/163
2025-07-08 17:15:20 [INFO] 合并降低分辨率的批次 117/163
2025-07-08 17:15:20 [INFO] 合并降低分辨率的批次 118/163
2025-07-08 17:15:20 [INFO] 合并降低分辨率的批次 119/163
2025-07-08 17:15:20 [INFO] 合并降低分辨率的批次 120/163
2025-07-08 17:15:20 [INFO] 合并降低分辨率的批次 121/163
2025-07-08 17:15:21 [INFO] 合并降低分辨率的批次 122/163
2025-07-08 17:15:21 [INFO] 合并降低分辨率的批次 123/163
2025-07-08 17:15:21 [INFO] 合并降低分辨率的批次 124/163
2025-07-08 17:15:21 [INFO] 合并降低分辨率的批次 125/163
2025-07-08 17:15:21 [INFO] 合并降低分辨率的批次 126/163
2025-07-08 17:15:21 [INFO] 合并降低分辨率的批次 127/163
2025-07-08 17:15:21 [INFO] 合并降低分辨率的批次 128/163
2025-07-08 17:15:21 [INFO] 合并降低分辨率的批次 129/163
2025-07-08 17:15:21 [INFO] 合并降低分辨率的批次 130/163
2025-07-08 17:15:21 [INFO] 合并降低分辨率的批次 131/163
2025-07-08 17:15:21 [INFO] 合并降低分辨率的批次 132/163
2025-07-08 17:15:21 [INFO] 合并降低分辨率的批次 133/163
2025-07-08 17:15:21 [INFO] 合并降低分辨率的批次 134/163
2025-07-08 17:15:21 [INFO] 合并降低分辨率的批次 135/163
2025-07-08 17:15:22 [INFO] 合并降低分辨率的批次 136/163
2025-07-08 17:15:22 [INFO] 合并降低分辨率的批次 137/163
2025-07-08 17:15:22 [INFO] 合并降低分辨率的批次 138/163
2025-07-08 17:15:22 [INFO] 合并降低分辨率的批次 139/163
2025-07-08 17:15:22 [INFO] 合并降低分辨率的批次 140/163
2025-07-08 17:15:22 [INFO] 合并降低分辨率的批次 141/163
2025-07-08 17:15:22 [INFO] 合并降低分辨率的批次 142/163
2025-07-08 17:15:22 [INFO] 合并降低分辨率的批次 143/163
2025-07-08 17:15:22 [INFO] 合并降低分辨率的批次 144/163
2025-07-08 17:15:22 [INFO] 合并降低分辨率的批次 145/163
2025-07-08 17:15:22 [INFO] 合并降低分辨率的批次 146/163
2025-07-08 17:15:22 [INFO] 合并降低分辨率的批次 147/163
2025-07-08 17:15:23 [INFO] 合并降低分辨率的批次 148/163
2025-07-08 17:15:23 [INFO] 合并降低分辨率的批次 149/163
2025-07-08 17:15:23 [INFO] 合并降低分辨率的批次 150/163
2025-07-08 17:15:23 [INFO] 合并降低分辨率的批次 151/163
2025-07-08 17:15:23 [INFO] 合并降低分辨率的批次 152/163
2025-07-08 17:15:23 [INFO] 合并降低分辨率的批次 153/163
2025-07-08 17:15:23 [INFO] 合并降低分辨率的批次 154/163
2025-07-08 17:15:23 [INFO] 批次合并失败，尝试使用chain方法: Unable to allocate 11.8 MiB for an array with shape (1543680, 1) and data type float64
2025-07-08 17:15:23 [INFO] 降低分辨率合并失败: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:15:23 [INFO] 尝试使用chain方法合并原始片段...
2025-07-08 17:15:23 [INFO] 内存友好合并失败: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:15:23 [INFO] 尝试使用chain方法合并...
2025-07-08 17:15:23 [INFO] chain方法合并失败: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:15:23 [INFO] 视频合成过程失败: 视频合并失败: 'NoneType' object has no attribute 'get_frame', 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:15:23 [INFO] 处理过程中发生错误: 视频合成失败: 视频合并失败: 'NoneType' object has no attribute 'get_frame', 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:15:23 [INFO] Error in replace_and_concatenate_videos: 视频合成失败: 视频合并失败: 'NoneType' object has no attribute 'get_frame', 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:15:24 [INFO] Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4737, in _concatenate_with_reduced_resolution
    merged = concatenate_videoclips(batch, method="compose")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 98, in concatenate_videoclips
    result = CompositeVideoClip( [c.set_start(t).set_position('center')
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\CompositeVideoClip.py", line 102, in __init__
    self.mask = CompositeVideoClip(maskclips,self.size, ismask=True,
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\CompositeVideoClip.py", line 79, in __init__
    self.bg = ColorClip(size, color=self.bg_color)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\VideoClip.py", line 1014, in __init__
    ImageClip.__init__(self, np.tile(color, w * h).reshape(shape),
                             ^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\numpy\lib\shape_base.py", line 1272, in tile
    c = c.reshape(-1, n).repeat(nrep, 0)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
numpy.core._exceptions._ArrayMemoryError: Unable to allocate 11.8 MiB for an array with shape (1543680, 1) and data type float64

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4741, in _concatenate_with_reduced_resolution
    merged = concatenate_videoclips(batch, method="chain")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 91, in concatenate_videoclips
    result = VideoClip(ismask = ismask, make_frame = make_frame)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\VideoClip.py", line 86, in __init__
    self.size = self.get_frame(0).shape[:2][::-1]
                ^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 83, in make_frame
    return clips[i].get_frame(t - tt[i])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 136, in <lambda>
    newclip = self.set_make_frame(lambda t: fun(self.get_frame, t))
                                            ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 187, in <lambda>
    return self.fl(lambda gf, t: gf(t_func(t)), apply_to,
                                 ^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\VideoFileClip.py", line 113, in <lambda>
    self.make_frame = lambda t: self.reader.get_frame(t)
                                ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4595, in _memory_efficient_concatenate
    return self._concatenate_with_reduced_resolution(actual_clips)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4759, in _concatenate_with_reduced_resolution
    return concatenate_videoclips(video_clips, method="chain")
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 91, in concatenate_videoclips
    result = VideoClip(ismask = ismask, make_frame = make_frame)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\VideoClip.py", line 86, in __init__
    self.size = self.get_frame(0).shape[:2][::-1]
                ^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 83, in make_frame
    return clips[i].get_frame(t - tt[i])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 136, in <lambda>
    newclip = self.set_make_frame(lambda t: fun(self.get_frame, t))
                                            ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 187, in <lambda>
    return self.fl(lambda gf, t: gf(t_func(t)), apply_to,
                                 ^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\VideoFileClip.py", line 113, in <lambda>
    self.make_frame = lambda t: self.reader.get_frame(t)
                                ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4666, in _memory_efficient_concatenate
    return concatenate_videoclips(actual_clips, method="chain")
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 91, in concatenate_videoclips
    result = VideoClip(ismask = ismask, make_frame = make_frame)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\VideoClip.py", line 86, in __init__
    self.size = self.get_frame(0).shape[:2][::-1]
                ^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 83, in make_frame
    return clips[i].get_frame(t - tt[i])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 136, in <lambda>
    newclip = self.set_make_frame(lambda t: fun(self.get_frame, t))
                                            ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 187, in <lambda>
    return self.fl(lambda gf, t: gf(t_func(t)), apply_to,
                                 ^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\VideoFileClip.py", line 113, in <lambda>
    self.make_frame = lambda t: self.reader.get_frame(t)
                                ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1039, in replace_and_concatenate_videos
    final_video = self._memory_efficient_concatenate(valid_clips)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4669, in _memory_efficient_concatenate
    raise Exception(f"视频合并失败: {str(e)}, {str(e2)}")
Exception: 视频合并失败: 'NoneType' object has no attribute 'get_frame', 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1089, in replace_and_concatenate_videos
    raise Exception(f"视频合成失败: {concat_error}")
Exception: 视频合成失败: 视频合并失败: 'NoneType' object has no attribute 'get_frame', 'NoneType' object has no attribute 'get_frame'

2025-07-08 17:15:24 [INFO] 优化版本的视频替换和合并失败: 视频合成失败: 视频合并失败: 'NoneType' object has no attribute 'get_frame', 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:15:24 [INFO] Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4737, in _concatenate_with_reduced_resolution
    merged = concatenate_videoclips(batch, method="compose")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 98, in concatenate_videoclips
    result = CompositeVideoClip( [c.set_start(t).set_position('center')
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\CompositeVideoClip.py", line 102, in __init__
    self.mask = CompositeVideoClip(maskclips,self.size, ismask=True,
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\CompositeVideoClip.py", line 79, in __init__
    self.bg = ColorClip(size, color=self.bg_color)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\VideoClip.py", line 1014, in __init__
    ImageClip.__init__(self, np.tile(color, w * h).reshape(shape),
                             ^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\numpy\lib\shape_base.py", line 1272, in tile
    c = c.reshape(-1, n).repeat(nrep, 0)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
numpy.core._exceptions._ArrayMemoryError: Unable to allocate 11.8 MiB for an array with shape (1543680, 1) and data type float64

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4741, in _concatenate_with_reduced_resolution
    merged = concatenate_videoclips(batch, method="chain")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 91, in concatenate_videoclips
    result = VideoClip(ismask = ismask, make_frame = make_frame)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\VideoClip.py", line 86, in __init__
    self.size = self.get_frame(0).shape[:2][::-1]
                ^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 83, in make_frame
    return clips[i].get_frame(t - tt[i])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 136, in <lambda>
    newclip = self.set_make_frame(lambda t: fun(self.get_frame, t))
                                            ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 187, in <lambda>
    return self.fl(lambda gf, t: gf(t_func(t)), apply_to,
                                 ^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\VideoFileClip.py", line 113, in <lambda>
    self.make_frame = lambda t: self.reader.get_frame(t)
                                ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4595, in _memory_efficient_concatenate
    return self._concatenate_with_reduced_resolution(actual_clips)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4759, in _concatenate_with_reduced_resolution
    return concatenate_videoclips(video_clips, method="chain")
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 91, in concatenate_videoclips
    result = VideoClip(ismask = ismask, make_frame = make_frame)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\VideoClip.py", line 86, in __init__
    self.size = self.get_frame(0).shape[:2][::-1]
                ^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 83, in make_frame
    return clips[i].get_frame(t - tt[i])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 136, in <lambda>
    newclip = self.set_make_frame(lambda t: fun(self.get_frame, t))
                                            ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 187, in <lambda>
    return self.fl(lambda gf, t: gf(t_func(t)), apply_to,
                                 ^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\VideoFileClip.py", line 113, in <lambda>
    self.make_frame = lambda t: self.reader.get_frame(t)
                                ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4666, in _memory_efficient_concatenate
    return concatenate_videoclips(actual_clips, method="chain")
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 91, in concatenate_videoclips
    result = VideoClip(ismask = ismask, make_frame = make_frame)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\VideoClip.py", line 86, in __init__
    self.size = self.get_frame(0).shape[:2][::-1]
                ^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 83, in make_frame
    return clips[i].get_frame(t - tt[i])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 136, in <lambda>
    newclip = self.set_make_frame(lambda t: fun(self.get_frame, t))
                                            ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 187, in <lambda>
    return self.fl(lambda gf, t: gf(t_func(t)), apply_to,
                                 ^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\VideoFileClip.py", line 113, in <lambda>
    self.make_frame = lambda t: self.reader.get_frame(t)
                                ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1039, in replace_and_concatenate_videos
    final_video = self._memory_efficient_concatenate(valid_clips)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4669, in _memory_efficient_concatenate
    raise Exception(f"视频合并失败: {str(e)}, {str(e2)}")
Exception: 视频合并失败: 'NoneType' object has no attribute 'get_frame', 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\performance_optimizer.py", line 267, in replace_and_concatenate_videos_optimized
    self.base_processor.replace_and_concatenate_videos(
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1089, in replace_and_concatenate_videos
    raise Exception(f"视频合成失败: {concat_error}")
Exception: 视频合成失败: 视频合并失败: 'NoneType' object has no attribute 'get_frame', 'NoneType' object has no attribute 'get_frame'

2025-07-08 17:15:24 [INFO] 尝试使用备用方法处理...
2025-07-08 17:15:24 [INFO] 减少匹配数量从 2515 到 10
2025-07-08 17:15:24 [INFO] 加载主视频: D:/25125/Videos/video_test/02_t.mp4
2025-07-08 17:15:25 [INFO] 处理过程中发生错误: [WinError 1455] 页面文件太小，无法完成操作。
2025-07-08 17:15:25 [INFO] Error in replace_and_concatenate_videos: [WinError 1455] 页面文件太小，无法完成操作。
2025-07-08 17:15:25 [INFO] Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4737, in _concatenate_with_reduced_resolution
    merged = concatenate_videoclips(batch, method="compose")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 98, in concatenate_videoclips
    result = CompositeVideoClip( [c.set_start(t).set_position('center')
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\CompositeVideoClip.py", line 102, in __init__
    self.mask = CompositeVideoClip(maskclips,self.size, ismask=True,
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\CompositeVideoClip.py", line 79, in __init__
    self.bg = ColorClip(size, color=self.bg_color)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\VideoClip.py", line 1014, in __init__
    ImageClip.__init__(self, np.tile(color, w * h).reshape(shape),
                             ^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\numpy\lib\shape_base.py", line 1272, in tile
    c = c.reshape(-1, n).repeat(nrep, 0)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
numpy.core._exceptions._ArrayMemoryError: Unable to allocate 11.8 MiB for an array with shape (1543680, 1) and data type float64

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4741, in _concatenate_with_reduced_resolution
    merged = concatenate_videoclips(batch, method="chain")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 91, in concatenate_videoclips
    result = VideoClip(ismask = ismask, make_frame = make_frame)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\VideoClip.py", line 86, in __init__
    self.size = self.get_frame(0).shape[:2][::-1]
                ^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 83, in make_frame
    return clips[i].get_frame(t - tt[i])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 136, in <lambda>
    newclip = self.set_make_frame(lambda t: fun(self.get_frame, t))
                                            ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 187, in <lambda>
    return self.fl(lambda gf, t: gf(t_func(t)), apply_to,
                                 ^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\VideoFileClip.py", line 113, in <lambda>
    self.make_frame = lambda t: self.reader.get_frame(t)
                                ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4595, in _memory_efficient_concatenate
    return self._concatenate_with_reduced_resolution(actual_clips)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4759, in _concatenate_with_reduced_resolution
    return concatenate_videoclips(video_clips, method="chain")
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 91, in concatenate_videoclips
    result = VideoClip(ismask = ismask, make_frame = make_frame)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\VideoClip.py", line 86, in __init__
    self.size = self.get_frame(0).shape[:2][::-1]
                ^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 83, in make_frame
    return clips[i].get_frame(t - tt[i])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 136, in <lambda>
    newclip = self.set_make_frame(lambda t: fun(self.get_frame, t))
                                            ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 187, in <lambda>
    return self.fl(lambda gf, t: gf(t_func(t)), apply_to,
                                 ^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\VideoFileClip.py", line 113, in <lambda>
    self.make_frame = lambda t: self.reader.get_frame(t)
                                ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4666, in _memory_efficient_concatenate
    return concatenate_videoclips(actual_clips, method="chain")
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 91, in concatenate_videoclips
    result = VideoClip(ismask = ismask, make_frame = make_frame)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\VideoClip.py", line 86, in __init__
    self.size = self.get_frame(0).shape[:2][::-1]
                ^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 83, in make_frame
    return clips[i].get_frame(t - tt[i])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 136, in <lambda>
    newclip = self.set_make_frame(lambda t: fun(self.get_frame, t))
                                            ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 187, in <lambda>
    return self.fl(lambda gf, t: gf(t_func(t)), apply_to,
                                 ^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\VideoFileClip.py", line 113, in <lambda>
    self.make_frame = lambda t: self.reader.get_frame(t)
                                ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1039, in replace_and_concatenate_videos
    final_video = self._memory_efficient_concatenate(valid_clips)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4669, in _memory_efficient_concatenate
    raise Exception(f"视频合并失败: {str(e)}, {str(e2)}")
Exception: 视频合并失败: 'NoneType' object has no attribute 'get_frame', 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\performance_optimizer.py", line 267, in replace_and_concatenate_videos_optimized
    self.base_processor.replace_and_concatenate_videos(
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1089, in replace_and_concatenate_videos
    raise Exception(f"视频合成失败: {concat_error}")
Exception: 视频合成失败: 视频合并失败: 'NoneType' object has no attribute 'get_frame', 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 921, in replace_and_concatenate_videos
    main_clip = VideoFileClip(main_video_path)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\VideoFileClip.py", line 88, in __init__
    self.reader = FFMPEG_VideoReader(filename, pix_fmt=pix_fmt,
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\ffmpeg_reader.py", line 35, in __init__
    infos = ffmpeg_parse_infos(filename, print_infos, check_duration,
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\ffmpeg_reader.py", line 257, in ffmpeg_parse_infos
    proc = sp.Popen(cmd, **popen_params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\subprocess.py", line 1026, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "D:\environment\python\3.12\Lib\subprocess.py", line 1538, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
OSError: [WinError 1455] 页面文件太小，无法完成操作。

2025-07-08 17:15:25 [INFO] 备用方法也失败: [WinError 1455] 页面文件太小，无法完成操作。
2025-07-08 17:15:25 [INFO] Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4737, in _concatenate_with_reduced_resolution
    merged = concatenate_videoclips(batch, method="compose")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 98, in concatenate_videoclips
    result = CompositeVideoClip( [c.set_start(t).set_position('center')
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\CompositeVideoClip.py", line 102, in __init__
    self.mask = CompositeVideoClip(maskclips,self.size, ismask=True,
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\CompositeVideoClip.py", line 79, in __init__
    self.bg = ColorClip(size, color=self.bg_color)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\VideoClip.py", line 1014, in __init__
    ImageClip.__init__(self, np.tile(color, w * h).reshape(shape),
                             ^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\numpy\lib\shape_base.py", line 1272, in tile
    c = c.reshape(-1, n).repeat(nrep, 0)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
numpy.core._exceptions._ArrayMemoryError: Unable to allocate 11.8 MiB for an array with shape (1543680, 1) and data type float64

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4741, in _concatenate_with_reduced_resolution
    merged = concatenate_videoclips(batch, method="chain")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 91, in concatenate_videoclips
    result = VideoClip(ismask = ismask, make_frame = make_frame)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\VideoClip.py", line 86, in __init__
    self.size = self.get_frame(0).shape[:2][::-1]
                ^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 83, in make_frame
    return clips[i].get_frame(t - tt[i])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 136, in <lambda>
    newclip = self.set_make_frame(lambda t: fun(self.get_frame, t))
                                            ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 187, in <lambda>
    return self.fl(lambda gf, t: gf(t_func(t)), apply_to,
                                 ^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\VideoFileClip.py", line 113, in <lambda>
    self.make_frame = lambda t: self.reader.get_frame(t)
                                ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4595, in _memory_efficient_concatenate
    return self._concatenate_with_reduced_resolution(actual_clips)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4759, in _concatenate_with_reduced_resolution
    return concatenate_videoclips(video_clips, method="chain")
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 91, in concatenate_videoclips
    result = VideoClip(ismask = ismask, make_frame = make_frame)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\VideoClip.py", line 86, in __init__
    self.size = self.get_frame(0).shape[:2][::-1]
                ^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 83, in make_frame
    return clips[i].get_frame(t - tt[i])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 136, in <lambda>
    newclip = self.set_make_frame(lambda t: fun(self.get_frame, t))
                                            ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 187, in <lambda>
    return self.fl(lambda gf, t: gf(t_func(t)), apply_to,
                                 ^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\VideoFileClip.py", line 113, in <lambda>
    self.make_frame = lambda t: self.reader.get_frame(t)
                                ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4666, in _memory_efficient_concatenate
    return concatenate_videoclips(actual_clips, method="chain")
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 91, in concatenate_videoclips
    result = VideoClip(ismask = ismask, make_frame = make_frame)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\VideoClip.py", line 86, in __init__
    self.size = self.get_frame(0).shape[:2][::-1]
                ^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 83, in make_frame
    return clips[i].get_frame(t - tt[i])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 136, in <lambda>
    newclip = self.set_make_frame(lambda t: fun(self.get_frame, t))
                                            ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 187, in <lambda>
    return self.fl(lambda gf, t: gf(t_func(t)), apply_to,
                                 ^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\VideoFileClip.py", line 113, in <lambda>
    self.make_frame = lambda t: self.reader.get_frame(t)
                                ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1039, in replace_and_concatenate_videos
    final_video = self._memory_efficient_concatenate(valid_clips)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4669, in _memory_efficient_concatenate
    raise Exception(f"视频合并失败: {str(e)}, {str(e2)}")
Exception: 视频合并失败: 'NoneType' object has no attribute 'get_frame', 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\performance_optimizer.py", line 267, in replace_and_concatenate_videos_optimized
    self.base_processor.replace_and_concatenate_videos(
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1089, in replace_and_concatenate_videos
    raise Exception(f"视频合成失败: {concat_error}")
Exception: 视频合成失败: 视频合并失败: 'NoneType' object has no attribute 'get_frame', 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\performance_optimizer.py", line 292, in replace_and_concatenate_videos_optimized
    return self.base_processor.replace_and_concatenate_videos(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 921, in replace_and_concatenate_videos
    main_clip = VideoFileClip(main_video_path)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\VideoFileClip.py", line 88, in __init__
    self.reader = FFMPEG_VideoReader(filename, pix_fmt=pix_fmt,
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\ffmpeg_reader.py", line 35, in __init__
    infos = ffmpeg_parse_infos(filename, print_infos, check_duration,
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\ffmpeg_reader.py", line 257, in ffmpeg_parse_infos
    proc = sp.Popen(cmd, **popen_params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\subprocess.py", line 1026, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "D:\environment\python\3.12\Lib\subprocess.py", line 1538, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
OSError: [WinError 1455] 页面文件太小，无法完成操作。

2025-07-08 17:15:25 [INFO] 批量处理大型视频失败: 视频替换和合并失败: 视频合成失败: 视频合并失败: 'NoneType' object has no attribute 'get_frame', 'NoneType' object has no attribute 'get_frame', 备用方法: [WinError 1455] 页面文件太小，无法完成操作。
2025-07-08 17:15:25 [INFO] Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4737, in _concatenate_with_reduced_resolution
    merged = concatenate_videoclips(batch, method="compose")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 98, in concatenate_videoclips
    result = CompositeVideoClip( [c.set_start(t).set_position('center')
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\CompositeVideoClip.py", line 102, in __init__
    self.mask = CompositeVideoClip(maskclips,self.size, ismask=True,
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\CompositeVideoClip.py", line 79, in __init__
    self.bg = ColorClip(size, color=self.bg_color)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\VideoClip.py", line 1014, in __init__
    ImageClip.__init__(self, np.tile(color, w * h).reshape(shape),
                             ^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\numpy\lib\shape_base.py", line 1272, in tile
    c = c.reshape(-1, n).repeat(nrep, 0)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
numpy.core._exceptions._ArrayMemoryError: Unable to allocate 11.8 MiB for an array with shape (1543680, 1) and data type float64

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4741, in _concatenate_with_reduced_resolution
    merged = concatenate_videoclips(batch, method="chain")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 91, in concatenate_videoclips
    result = VideoClip(ismask = ismask, make_frame = make_frame)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\VideoClip.py", line 86, in __init__
    self.size = self.get_frame(0).shape[:2][::-1]
                ^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 83, in make_frame
    return clips[i].get_frame(t - tt[i])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 136, in <lambda>
    newclip = self.set_make_frame(lambda t: fun(self.get_frame, t))
                                            ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 187, in <lambda>
    return self.fl(lambda gf, t: gf(t_func(t)), apply_to,
                                 ^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\VideoFileClip.py", line 113, in <lambda>
    self.make_frame = lambda t: self.reader.get_frame(t)
                                ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4595, in _memory_efficient_concatenate
    return self._concatenate_with_reduced_resolution(actual_clips)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4759, in _concatenate_with_reduced_resolution
    return concatenate_videoclips(video_clips, method="chain")
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 91, in concatenate_videoclips
    result = VideoClip(ismask = ismask, make_frame = make_frame)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\VideoClip.py", line 86, in __init__
    self.size = self.get_frame(0).shape[:2][::-1]
                ^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 83, in make_frame
    return clips[i].get_frame(t - tt[i])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 136, in <lambda>
    newclip = self.set_make_frame(lambda t: fun(self.get_frame, t))
                                            ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 187, in <lambda>
    return self.fl(lambda gf, t: gf(t_func(t)), apply_to,
                                 ^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\VideoFileClip.py", line 113, in <lambda>
    self.make_frame = lambda t: self.reader.get_frame(t)
                                ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4666, in _memory_efficient_concatenate
    return concatenate_videoclips(actual_clips, method="chain")
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 91, in concatenate_videoclips
    result = VideoClip(ismask = ismask, make_frame = make_frame)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\VideoClip.py", line 86, in __init__
    self.size = self.get_frame(0).shape[:2][::-1]
                ^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\compositing\concatenate.py", line 83, in make_frame
    return clips[i].get_frame(t - tt[i])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 136, in <lambda>
    newclip = self.set_make_frame(lambda t: fun(self.get_frame, t))
                                            ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 187, in <lambda>
    return self.fl(lambda gf, t: gf(t_func(t)), apply_to,
                                 ^^^^^^^^^^^^^
  File "<decorator-gen-11>", line 2, in get_frame
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\decorators.py", line 89, in wrapper
    return f(*new_a, **new_kw)
           ^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\Clip.py", line 93, in get_frame
    return self.make_frame(t)
           ^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\VideoFileClip.py", line 113, in <lambda>
    self.make_frame = lambda t: self.reader.get_frame(t)
                                ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1039, in replace_and_concatenate_videos
    final_video = self._memory_efficient_concatenate(valid_clips)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4669, in _memory_efficient_concatenate
    raise Exception(f"视频合并失败: {str(e)}, {str(e2)}")
Exception: 视频合并失败: 'NoneType' object has no attribute 'get_frame', 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\performance_optimizer.py", line 267, in replace_and_concatenate_videos_optimized
    self.base_processor.replace_and_concatenate_videos(
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1089, in replace_and_concatenate_videos
    raise Exception(f"视频合成失败: {concat_error}")
Exception: 视频合成失败: 视频合并失败: 'NoneType' object has no attribute 'get_frame', 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\performance_optimizer.py", line 292, in replace_and_concatenate_videos_optimized
    return self.base_processor.replace_and_concatenate_videos(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 921, in replace_and_concatenate_videos
    main_clip = VideoFileClip(main_video_path)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\VideoFileClip.py", line 88, in __init__
    self.reader = FFMPEG_VideoReader(filename, pix_fmt=pix_fmt,
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\ffmpeg_reader.py", line 35, in __init__
    infos = ffmpeg_parse_infos(filename, print_infos, check_duration,
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\moviepy\video\io\ffmpeg_reader.py", line 257, in ffmpeg_parse_infos
    proc = sp.Popen(cmd, **popen_params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\subprocess.py", line 1026, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
  File "D:\environment\python\3.12\Lib\subprocess.py", line 1538, in _execute_child
    hp, ht, pid, tid = _winapi.CreateProcess(executable, args,
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
OSError: [WinError 1455] 页面文件太小，无法完成操作。

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\performance_optimizer.py", line 367, in batch_process_large_video
    self.replace_and_concatenate_videos_optimized(
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\performance_optimizer.py", line 299, in replace_and_concatenate_videos_optimized
    raise Exception(f"视频替换和合并失败: {e}, 备用方法: {backup_error}")
Exception: 视频替换和合并失败: 视频合成失败: 视频合并失败: 'NoneType' object has no attribute 'get_frame', 'NoneType' object has no attribute 'get_frame', 备用方法: [WinError 1455] 页面文件太小，无法完成操作。

2025-07-08 17:15:26 [INFO] Error in split_video_into_scenes: 场景检测失败: <method 'retrieve' of 'cv2.VideoCapture' objects> returned a result with an exception set
2025-07-08 17:15:27 [INFO] numpy.core._exceptions._ArrayMemoryError: Unable to allocate 2.22 MiB for an array with shape (720, 1080, 3) and data type uint8

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 541, in split_video_into_scenes
    scene_manager.detect_scenes(video)
  File "D:\environment\python\3.12\Lib\site-packages\scenedetect\scene_manager.py", line 1362, in detect_scenes
    raise self._exception_info[1].with_traceback(self._exception_info[2])
  File "D:\environment\python\3.12\Lib\site-packages\scenedetect\scene_manager.py", line 1385, in _decode_thread
    frame_im = video.read()
               ^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\scenedetect\backends\opencv.py", line 303, in read
    _, frame = self._cap.retrieve()
               ^^^^^^^^^^^^^^^^^^^^
SystemError: <method 'retrieve' of 'cv2.VideoCapture' objects> returned a result with an exception set

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 543, in split_video_into_scenes
    raise Exception(f"场景检测失败: {str(e)}")
Exception: 场景检测失败: <method 'retrieve' of 'cv2.VideoCapture' objects> returned a result with an exception set

2025-07-08 17:15:27 [INFO] Error in split_video_into_scenes: 场景检测失败: <method 'retrieve' of 'cv2.VideoCapture' objects> returned a result with an exception set
2025-07-08 17:15:27 [INFO] numpy.core._exceptions._ArrayMemoryError: Unable to allocate 2.22 MiB for an array with shape (720, 1080, 3) and data type uint8

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 541, in split_video_into_scenes
    scene_manager.detect_scenes(video)
  File "D:\environment\python\3.12\Lib\site-packages\scenedetect\scene_manager.py", line 1362, in detect_scenes
    raise self._exception_info[1].with_traceback(self._exception_info[2])
  File "D:\environment\python\3.12\Lib\site-packages\scenedetect\scene_manager.py", line 1385, in _decode_thread
    frame_im = video.read()
               ^^^^^^^^^^^^
  File "D:\environment\python\3.12\Lib\site-packages\scenedetect\backends\opencv.py", line 303, in read
    _, frame = self._cap.retrieve()
               ^^^^^^^^^^^^^^^^^^^^
SystemError: <method 'retrieve' of 'cv2.VideoCapture' objects> returned a result with an exception set

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\performance_optimizer.py", line 148, in split_video_into_scenes
    return self.base_processor.split_video_into_scenes(video_path, threshold)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 543, in split_video_into_scenes
    raise Exception(f"场景检测失败: {str(e)}")
Exception: 场景检测失败: <method 'retrieve' of 'cv2.VideoCapture' objects> returned a result with an exception set

2025-07-08 17:16:01 [INFO] 🚪 用户选择完全退出程序
2025-07-08 17:20:01 [INFO] ✅ 激活验证模块加载成功
2025-07-08 17:20:03 [INFO] ✅ 单实例应用初始化成功
2025-07-08 17:20:03 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 17:20:03 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-08 17:20:03 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-08 17:20:04 [INFO] ✅ 已更新最后验证时间
2025-07-08 17:20:04 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-08 17:20:04 [INFO] ✅ 本地激活配置验证通过
2025-07-08 17:20:04 [INFO] ✅ 本地激活验证通过
2025-07-08 17:20:04 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 17:20:43 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-07-08 17:20:44 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-08 17:20:45 [INFO] 未检测到CUDA设备
2025-07-08 17:20:45 [INFO] VideoProcessor初始化:
2025-07-08 17:20:45 [INFO]   CPU线程数: 8
2025-07-08 17:20:45 [INFO]   GPU支持: 否
2025-07-08 17:20:45 [INFO]   GPU加速: 禁用
2025-07-08 17:20:45 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-08 17:20:45 [INFO] Error in split_video_into_scenes: cannot import name 'ContentDetector' from 'scenedetect.scene_detector' (D:\environment\python\3.12\Lib\site-packages\scenedetect\scene_detector.py)
2025-07-08 17:20:45 [INFO] Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 522, in split_video_into_scenes
    from scenedetect.scene_detector import ContentDetector as CustomContentDetector
ImportError: cannot import name 'ContentDetector' from 'scenedetect.scene_detector' (D:\environment\python\3.12\Lib\site-packages\scenedetect\scene_detector.py). Did you mean: 'SceneDetector'?

2025-07-08 17:20:45 [INFO] Error in split_video_into_scenes: cannot import name 'ContentDetector' from 'scenedetect.scene_detector' (D:\environment\python\3.12\Lib\site-packages\scenedetect\scene_detector.py)
2025-07-08 17:20:45 [INFO] Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\performance_optimizer.py", line 148, in split_video_into_scenes
    return self.base_processor.split_video_into_scenes(video_path, threshold)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 522, in split_video_into_scenes
    from scenedetect.scene_detector import ContentDetector as CustomContentDetector
ImportError: cannot import name 'ContentDetector' from 'scenedetect.scene_detector' (D:\environment\python\3.12\Lib\site-packages\scenedetect\scene_detector.py). Did you mean: 'SceneDetector'?

2025-07-08 17:21:25 [INFO] 🚪 用户选择完全退出程序
2025-07-08 17:25:38 [INFO] ✅ 激活验证模块加载成功
2025-07-08 17:25:39 [INFO] ✅ 单实例应用初始化成功
2025-07-08 17:25:39 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 17:25:40 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-08 17:25:40 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-08 17:25:40 [INFO] ✅ 已更新最后验证时间
2025-07-08 17:25:40 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-08 17:25:40 [INFO] ✅ 本地激活配置验证通过
2025-07-08 17:25:40 [INFO] ✅ 本地激活验证通过
2025-07-08 17:25:40 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 17:26:08 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-07-08 17:26:09 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-08 17:26:09 [INFO] 未检测到CUDA设备
2025-07-08 17:26:09 [INFO] VideoProcessor初始化:
2025-07-08 17:26:09 [INFO]   CPU线程数: 8
2025-07-08 17:26:09 [INFO]   GPU支持: 否
2025-07-08 17:26:09 [INFO]   GPU加速: 禁用
2025-07-08 17:26:09 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-08 17:26:09 [INFO] 使用PySceneDetect版本: 0.6.6
2025-07-08 17:26:09 [INFO] 成功导入ContentDetector(方法1)
2025-07-08 17:26:09 [INFO] 视频文件大小: 13.66 MB
2025-07-08 17:26:09 [INFO] 视频信息: 30.0 FPS, 1669 帧, 55.63 秒
2025-07-08 17:26:09 [INFO] 尝试使用PySceneDetect原生方法打开视频...
2025-07-08 17:26:09 [INFO] 开始执行场景检测...
2025-07-08 17:26:17 [INFO] 场景检测完成，耗时 8.34 秒
2025-07-08 17:26:17 [INFO] 检测到 19 个场景
2025-07-08 17:26:17 [INFO] 使用PySceneDetect版本: 0.6.6
2025-07-08 17:26:17 [INFO] 成功导入ContentDetector(方法1)
2025-07-08 17:26:17 [INFO] 视频文件大小: 28.10 MB
2025-07-08 17:26:17 [INFO] 视频信息: 30.0 FPS, 1198 帧, 39.93 秒
2025-07-08 17:26:17 [INFO] 尝试使用PySceneDetect原生方法打开视频...
2025-07-08 17:26:17 [INFO] 开始执行场景检测...
2025-07-08 17:26:22 [INFO] 场景检测完成，耗时 4.61 秒
2025-07-08 17:26:22 [INFO] 检测到 2 个场景
2025-07-08 17:26:22 [INFO] 正在并行提取主视频特征...
2025-07-08 17:26:32 [INFO] 正在并行提取辅助视频特征...
2025-07-08 17:26:33 [INFO] 正在并行计算相似度矩阵...
2025-07-08 17:26:33 [INFO] 正在查找最佳匹配...
2025-07-08 17:33:12 [INFO] ✅ 激活验证模块加载成功
2025-07-08 17:33:14 [WARNING] ⚠️ 应用程序已在运行，激活现有窗口
2025-07-08 17:26:33 [INFO] 加载主视频: D:/25125/Videos/555588888888.mp4
2025-07-08 17:26:34 [INFO] 主视频加载成功，时长: 55.66秒
2025-07-08 17:26:34 [INFO] 处理 19 个主视频片段...
2025-07-08 17:26:34 [INFO] 保留主视频片段 0: 0.00-2.73秒
2025-07-08 17:26:36 [INFO] 保留主视频片段 1: 2.73-5.47秒
2025-07-08 17:26:37 [INFO] 保留主视频片段 2: 5.47-6.73秒
2025-07-08 17:26:38 [INFO] 保留主视频片段 3: 6.73-8.43秒
2025-07-08 17:26:38 [INFO] 保留主视频片段 4: 8.43-13.27秒
2025-07-08 17:26:39 [INFO] 保留主视频片段 5: 13.27-14.67秒
2025-07-08 17:26:39 [INFO] 保留主视频片段 6: 14.67-18.23秒
2025-07-08 17:26:39 [INFO] 保留主视频片段 7: 18.23-21.87秒
2025-07-08 17:26:40 [INFO] 保留主视频片段 8: 21.87-23.43秒
2025-07-08 17:26:40 [INFO] 保留主视频片段 9: 23.43-26.17秒
2025-07-08 17:26:41 [INFO] 保留主视频片段 10: 26.17-29.17秒
2025-07-08 17:26:43 [INFO] 保留主视频片段 11: 29.17-32.87秒
2025-07-08 17:26:43 [INFO] 保留主视频片段 12: 32.87-36.40秒
2025-07-08 17:26:43 [INFO] 保留主视频片段 13: 36.40-37.93秒
2025-07-08 17:26:44 [INFO] 保留主视频片段 14: 37.93-41.53秒
2025-07-08 17:26:44 [INFO] 保留主视频片段 15: 41.53-46.63秒
2025-07-08 17:26:45 [INFO] 保留主视频片段 16: 46.63-49.27秒
2025-07-08 17:26:46 [INFO] 保留主视频片段 17: 49.27-53.33秒
2025-07-08 17:26:46 [INFO] 保留主视频片段 18: 53.33-55.63秒
2025-07-08 17:26:46 [INFO] 处理 0 个替换片段...
2025-07-08 17:26:46 [INFO] 开始合成 19 个视频片段...
2025-07-08 17:26:46 [INFO] 有效片段数量: 19
2025-07-08 17:26:46 [INFO] 使用内存优化方法合成视频...
2025-07-08 17:26:46 [INFO] 合并 19 个有效片段，总时长: 55.63秒
2025-07-08 17:26:46 [INFO] 使用分批合并方法 (19 个片段)
2025-07-08 17:26:46 [INFO] 合并批次 1/10
2025-07-08 17:26:46 [INFO] 合并批次 2/10
2025-07-08 17:26:46 [INFO] 合并批次 3/10
2025-07-08 17:26:47 [INFO] 合并批次 4/10
2025-07-08 17:26:47 [INFO] 合并批次 5/10
2025-07-08 17:26:47 [INFO] 合并批次 6/10
2025-07-08 17:26:47 [INFO] 合并批次 7/10
2025-07-08 17:26:47 [INFO] 合并批次 8/10
2025-07-08 17:26:47 [INFO] 合并批次 9/10
2025-07-08 17:26:47 [INFO] 最终合并 10 个批次
2025-07-08 17:26:47 [INFO] 递归合并: 分为 5 和 5 个片段
2025-07-08 17:26:47 [INFO] 合并 5 个有效片段，总时长: 26.17秒
2025-07-08 17:26:47 [INFO] 使用分批合并方法 (5 个片段)
2025-07-08 17:26:47 [INFO] 合并批次 1/3
2025-07-08 17:26:47 [INFO] 合并批次 2/3
2025-07-08 17:26:47 [INFO] 最终合并 3 个批次
2025-07-08 17:26:48 [INFO] 合并 5 个有效片段，总时长: 29.47秒
2025-07-08 17:26:48 [INFO] 使用分批合并方法 (5 个片段)
2025-07-08 17:26:48 [INFO] 合并批次 1/3
2025-07-08 17:26:48 [INFO] 合并批次 2/3
2025-07-08 17:26:48 [INFO] 最终合并 3 个批次
2025-07-08 17:26:48 [INFO] 视频合成成功，总时长: 55.63秒
2025-07-08 17:26:48 [INFO] 开始写入视频文件: D:/25125/Videos/wteset.mp4
2025-07-08 17:26:48 [WARNING] 视频写入失败，重试 1/5: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:26:50 [WARNING] 视频写入失败，重试 2/5: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:26:52 [WARNING] 视频写入失败，重试 3/5: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:26:55 [WARNING] 视频写入失败，重试 4/5: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:26:57 [ERROR] 视频写入最终失败: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:26:57 [INFO] 尝试使用FFmpeg备用方法写入视频...
2025-07-08 17:26:57 [WARNING] 提取音频失败，将创建无声视频: 'CompositeAudioClip' object has no attribute 'fps'
2025-07-08 17:26:57 [ERROR] 备用方法保存临时视频失败: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:26:57 [INFO] 安全写入失败: 输出文件未生成: D:/25125/Videos/wteset.mp4
2025-07-08 17:26:57 [INFO] 尝试使用备用方法写入...
2025-07-08 17:26:57 [INFO] 尝试使用FFmpeg备用方法写入视频...
2025-07-08 17:26:57 [WARNING] 提取音频失败，将创建无声视频: 'CompositeAudioClip' object has no attribute 'fps'
2025-07-08 17:26:57 [ERROR] 备用方法保存临时视频失败: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:26:57 [INFO] 视频合成过程失败: 所有写入方式都失败: 标准=输出文件未生成: D:/25125/Videos/wteset.mp4, 备用=备用写入方法失败
2025-07-08 17:26:57 [INFO] 处理过程中发生错误: 视频合成失败: 所有写入方式都失败: 标准=输出文件未生成: D:/25125/Videos/wteset.mp4, 备用=备用写入方法失败
2025-07-08 17:26:57 [INFO] Error in replace_and_concatenate_videos: 视频合成失败: 所有写入方式都失败: 标准=输出文件未生成: D:/25125/Videos/wteset.mp4, 备用=备用写入方法失败
2025-07-08 17:26:57 [INFO] Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1312, in replace_and_concatenate_videos
    raise Exception(f"输出文件未生成: {output_path}")
Exception: 输出文件未生成: D:/25125/Videos/wteset.mp4

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1329, in replace_and_concatenate_videos
    raise Exception("备用写入方法失败")
Exception: 备用写入方法失败

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1334, in replace_and_concatenate_videos
    raise Exception(f"所有写入方式都失败: 标准={write_error}, 备用={fallback_error}")
Exception: 所有写入方式都失败: 标准=输出文件未生成: D:/25125/Videos/wteset.mp4, 备用=备用写入方法失败

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1341, in replace_and_concatenate_videos
    raise Exception(f"视频合成失败: {concat_error}")
Exception: 视频合成失败: 所有写入方式都失败: 标准=输出文件未生成: D:/25125/Videos/wteset.mp4, 备用=备用写入方法失败

2025-07-08 17:26:57 [INFO] Error in replace_and_concatenate_videos: 视频合成失败: 所有写入方式都失败: 标准=输出文件未生成: D:/25125/Videos/wteset.mp4, 备用=备用写入方法失败
2025-07-08 17:32:40 [INFO] 📦 程序已最小化到系统托盘
2025-07-08 17:33:14 [INFO] 📢 收到激活信号，显示主窗口
2025-07-08 17:33:16 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_dec98734_1751966808519_6ebec454.m4a
2025-07-08 17:33:17 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-08 17:33:17 [INFO] 未检测到CUDA设备
2025-07-08 17:33:17 [INFO] VideoProcessor初始化:
2025-07-08 17:33:17 [INFO]   CPU线程数: 8
2025-07-08 17:33:17 [INFO]   GPU支持: 否
2025-07-08 17:33:17 [INFO]   GPU加速: 禁用
2025-07-08 17:33:17 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-08 17:33:17 [INFO] 使用PySceneDetect版本: 0.6.6
2025-07-08 17:33:17 [INFO] 成功导入ContentDetector(方法1)
2025-07-08 17:33:17 [INFO] 视频文件大小: 13.66 MB
2025-07-08 17:33:17 [INFO] 视频信息: 30.0 FPS, 1669 帧, 55.63 秒
2025-07-08 17:33:17 [INFO] 尝试使用PySceneDetect原生方法打开视频...
2025-07-08 17:33:17 [INFO] 开始执行场景检测...
2025-07-08 17:33:27 [INFO] 场景检测完成，耗时 9.40 秒
2025-07-08 17:33:27 [INFO] 检测到 19 个场景
2025-07-08 17:33:27 [INFO] 使用PySceneDetect版本: 0.6.6
2025-07-08 17:33:27 [INFO] 成功导入ContentDetector(方法1)
2025-07-08 17:33:27 [INFO] 视频文件大小: 28.10 MB
2025-07-08 17:33:27 [INFO] 视频信息: 30.0 FPS, 1198 帧, 39.93 秒
2025-07-08 17:33:27 [INFO] 尝试使用PySceneDetect原生方法打开视频...
2025-07-08 17:33:27 [INFO] 开始执行场景检测...
2025-07-08 17:33:33 [INFO] 场景检测完成，耗时 6.20 秒
2025-07-08 17:33:33 [INFO] 检测到 2 个场景
2025-07-08 17:33:33 [INFO] 正在并行提取主视频特征...
2025-07-08 17:33:45 [INFO] 正在并行提取辅助视频特征...
2025-07-08 17:33:47 [INFO] 正在并行计算相似度矩阵...
2025-07-08 17:33:47 [INFO] 正在查找最佳匹配...
2025-07-08 17:33:48 [INFO] 加载主视频: D:/25125/Videos/555588888888.mp4
2025-07-08 17:33:48 [INFO] 主视频加载成功，时长: 55.66秒
2025-07-08 17:33:48 [INFO] 处理 19 个主视频片段...
2025-07-08 17:33:48 [INFO] 保留主视频片段 0: 0.00-2.73秒
2025-07-08 17:33:49 [INFO] 保留主视频片段 1: 2.73-5.47秒
2025-07-08 17:33:50 [INFO] 保留主视频片段 2: 5.47-6.73秒
2025-07-08 17:33:51 [INFO] 保留主视频片段 3: 6.73-8.43秒
2025-07-08 17:33:51 [INFO] 保留主视频片段 4: 8.43-13.27秒
2025-07-08 17:33:52 [INFO] 保留主视频片段 5: 13.27-14.67秒
2025-07-08 17:33:52 [INFO] 保留主视频片段 6: 14.67-18.23秒
2025-07-08 17:33:52 [INFO] 保留主视频片段 7: 18.23-21.87秒
2025-07-08 17:33:53 [INFO] 保留主视频片段 8: 21.87-23.43秒
2025-07-08 17:33:53 [INFO] 保留主视频片段 9: 23.43-26.17秒
2025-07-08 17:33:54 [INFO] 保留主视频片段 10: 26.17-29.17秒
2025-07-08 17:33:55 [INFO] 保留主视频片段 11: 29.17-32.87秒
2025-07-08 17:33:56 [INFO] 保留主视频片段 12: 32.87-36.40秒
2025-07-08 17:33:56 [INFO] 保留主视频片段 13: 36.40-37.93秒
2025-07-08 17:33:56 [INFO] 保留主视频片段 14: 37.93-41.53秒
2025-07-08 17:33:57 [INFO] 保留主视频片段 15: 41.53-46.63秒
2025-07-08 17:33:57 [INFO] 保留主视频片段 16: 46.63-49.27秒
2025-07-08 17:33:58 [INFO] 保留主视频片段 17: 49.27-53.33秒
2025-07-08 17:33:58 [INFO] 保留主视频片段 18: 53.33-55.63秒
2025-07-08 17:33:58 [INFO] 处理 0 个替换片段...
2025-07-08 17:33:58 [INFO] 开始合成 19 个视频片段...
2025-07-08 17:33:58 [INFO] 有效片段数量: 19
2025-07-08 17:33:58 [INFO] 使用内存优化方法合成视频...
2025-07-08 17:33:58 [INFO] 合并 19 个有效片段，总时长: 55.63秒
2025-07-08 17:33:58 [INFO] 使用分批合并方法 (19 个片段)
2025-07-08 17:33:58 [INFO] 合并批次 1/10
2025-07-08 17:33:59 [INFO] 合并批次 2/10
2025-07-08 17:33:59 [INFO] 合并批次 3/10
2025-07-08 17:33:59 [INFO] 合并批次 4/10
2025-07-08 17:33:59 [INFO] 合并批次 5/10
2025-07-08 17:33:59 [INFO] 合并批次 6/10
2025-07-08 17:33:59 [INFO] 合并批次 7/10
2025-07-08 17:33:59 [INFO] 合并批次 8/10
2025-07-08 17:33:59 [INFO] 合并批次 9/10
2025-07-08 17:33:59 [INFO] 最终合并 10 个批次
2025-07-08 17:33:59 [INFO] 递归合并: 分为 5 和 5 个片段
2025-07-08 17:33:59 [INFO] 合并 5 个有效片段，总时长: 26.17秒
2025-07-08 17:33:59 [INFO] 使用分批合并方法 (5 个片段)
2025-07-08 17:33:59 [INFO] 合并批次 1/3
2025-07-08 17:33:59 [INFO] 合并批次 2/3
2025-07-08 17:33:59 [INFO] 最终合并 3 个批次
2025-07-08 17:33:59 [INFO] 合并 5 个有效片段，总时长: 29.47秒
2025-07-08 17:33:59 [INFO] 使用分批合并方法 (5 个片段)
2025-07-08 17:33:59 [INFO] 合并批次 1/3
2025-07-08 17:33:59 [INFO] 合并批次 2/3
2025-07-08 17:33:59 [INFO] 最终合并 3 个批次
2025-07-08 17:34:00 [INFO] 视频合成成功，总时长: 55.63秒
2025-07-08 17:34:00 [INFO] 开始写入视频文件: D:/25125/Videos/wteset.mp4
2025-07-08 17:34:00 [WARNING] 视频写入失败，重试 1/5: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:34:02 [WARNING] 视频写入失败，重试 2/5: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:34:04 [WARNING] 视频写入失败，重试 3/5: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:34:06 [WARNING] 视频写入失败，重试 4/5: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:34:08 [ERROR] 视频写入最终失败: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:34:08 [INFO] 尝试使用FFmpeg备用方法写入视频...
2025-07-08 17:34:08 [WARNING] 提取音频失败，将创建无声视频: 'CompositeAudioClip' object has no attribute 'fps'
2025-07-08 17:34:08 [ERROR] 备用方法保存临时视频失败: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:34:08 [INFO] 安全写入失败: 输出文件未生成: D:/25125/Videos/wteset.mp4
2025-07-08 17:34:08 [INFO] 尝试使用备用方法写入...
2025-07-08 17:34:08 [INFO] 尝试使用FFmpeg备用方法写入视频...
2025-07-08 17:34:08 [WARNING] 提取音频失败，将创建无声视频: 'CompositeAudioClip' object has no attribute 'fps'
2025-07-08 17:34:08 [ERROR] 备用方法保存临时视频失败: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:34:08 [INFO] 视频合成过程失败: 所有写入方式都失败: 标准=输出文件未生成: D:/25125/Videos/wteset.mp4, 备用=备用写入方法失败
2025-07-08 17:34:08 [INFO] 处理过程中发生错误: 视频合成失败: 所有写入方式都失败: 标准=输出文件未生成: D:/25125/Videos/wteset.mp4, 备用=备用写入方法失败
2025-07-08 17:34:08 [INFO] Error in replace_and_concatenate_videos: 视频合成失败: 所有写入方式都失败: 标准=输出文件未生成: D:/25125/Videos/wteset.mp4, 备用=备用写入方法失败
2025-07-08 17:34:08 [INFO] Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1312, in replace_and_concatenate_videos
    log_info(f"警告: 创建替换片段失败 {main_idx}: {clip_error}")
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: 输出文件未生成: D:/25125/Videos/wteset.mp4

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1329, in replace_and_concatenate_videos
    if clip is not None and hasattr(clip, 'duration') and clip.duration > 0:
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: 备用写入方法失败

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1334, in replace_and_concatenate_videos
    if not valid_clips:
                ^^^^^^^^
Exception: 所有写入方式都失败: 标准=输出文件未生成: D:/25125/Videos/wteset.mp4, 备用=备用写入方法失败

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1341, in replace_and_concatenate_videos
    log_info("使用内存优化方法合成视频...")
Exception: 视频合成失败: 所有写入方式都失败: 标准=输出文件未生成: D:/25125/Videos/wteset.mp4, 备用=备用写入方法失败

2025-07-08 17:34:08 [INFO] Error in replace_and_concatenate_videos: 视频合成失败: 所有写入方式都失败: 标准=输出文件未生成: D:/25125/Videos/wteset.mp4, 备用=备用写入方法失败
2025-07-08 17:34:08 [INFO] Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1312, in replace_and_concatenate_videos
    log_info(f"警告: 创建替换片段失败 {main_idx}: {clip_error}")
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: 输出文件未生成: D:/25125/Videos/wteset.mp4

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1329, in replace_and_concatenate_videos
    if clip is not None and hasattr(clip, 'duration') and clip.duration > 0:
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Exception: 备用写入方法失败

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1334, in replace_and_concatenate_videos
    if not valid_clips:
                ^^^^^^^^
Exception: 所有写入方式都失败: 标准=输出文件未生成: D:/25125/Videos/wteset.mp4, 备用=备用写入方法失败

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\performance_optimizer.py", line 236, in replace_and_concatenate_videos
    return self.base_processor.replace_and_concatenate_videos(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1341, in replace_and_concatenate_videos
    log_info("使用内存优化方法合成视频...")
Exception: 视频合成失败: 所有写入方式都失败: 标准=输出文件未生成: D:/25125/Videos/wteset.mp4, 备用=备用写入方法失败

2025-07-08 17:34:14 [INFO] 📦 窗口已最小化到任务栏
2025-07-08 17:34:14 [INFO] 📦 窗口已最小化到任务栏
2025-07-08 17:34:37 [INFO] ✅ 激活验证模块加载成功
2025-07-08 17:34:39 [INFO] ✅ 单实例应用初始化成功
2025-07-08 17:34:39 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 17:34:39 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-08 17:34:39 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-08 17:34:40 [INFO] ✅ 已更新最后验证时间
2025-07-08 17:34:40 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-08 17:34:40 [INFO] ✅ 本地激活配置验证通过
2025-07-08 17:34:40 [INFO] ✅ 本地激活验证通过
2025-07-08 17:34:40 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 17:35:13 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-07-08 17:35:13 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-08 17:35:13 [INFO] 未检测到CUDA设备
2025-07-08 17:35:13 [INFO] VideoProcessor初始化:
2025-07-08 17:35:13 [INFO]   CPU线程数: 8
2025-07-08 17:35:13 [INFO]   GPU支持: 否
2025-07-08 17:35:13 [INFO]   GPU加速: 禁用
2025-07-08 17:35:13 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-08 17:35:13 [INFO] 使用PySceneDetect版本: 0.6.6
2025-07-08 17:35:13 [INFO] 成功导入ContentDetector(方法1)
2025-07-08 17:35:13 [INFO] 视频文件大小: 13.66 MB
2025-07-08 17:35:13 [INFO] 视频信息: 30.0 FPS, 1669 帧, 55.63 秒
2025-07-08 17:35:13 [INFO] 尝试使用PySceneDetect原生方法打开视频...
2025-07-08 17:35:13 [INFO] 开始执行场景检测...
2025-07-08 17:35:16 [INFO] 📦 窗口已最小化到任务栏
2025-07-08 17:35:16 [INFO] 📦 窗口已最小化到任务栏
2025-07-08 17:35:20 [INFO] 场景检测完成，耗时 6.91 秒
2025-07-08 17:35:20 [INFO] 检测到 19 个场景
2025-07-08 17:35:20 [INFO] 使用PySceneDetect版本: 0.6.6
2025-07-08 17:35:20 [INFO] 成功导入ContentDetector(方法1)
2025-07-08 17:35:20 [INFO] 视频文件大小: 28.10 MB
2025-07-08 17:35:20 [INFO] 视频信息: 30.0 FPS, 1198 帧, 39.93 秒
2025-07-08 17:35:20 [INFO] 尝试使用PySceneDetect原生方法打开视频...
2025-07-08 17:35:20 [INFO] 开始执行场景检测...
2025-07-08 17:35:29 [INFO] 场景检测完成，耗时 8.30 秒
2025-07-08 17:35:29 [INFO] 检测到 2 个场景
2025-07-08 17:35:29 [INFO] 正在并行提取主视频特征...
2025-07-08 17:35:49 [INFO] 正在并行提取辅助视频特征...
2025-07-08 17:35:51 [INFO] 正在并行计算相似度矩阵...
2025-07-08 17:35:51 [INFO] 正在查找最佳匹配...
2025-07-08 17:35:51 [INFO] 输出目录检查通过: D:\25125\Videos, 可用空间: 3.30GB
2025-07-08 17:35:51 [INFO] 最终输出路径: D:\25125\Videos\wwresr.mp4
2025-07-08 17:35:52 [INFO] 加载主视频: D:/25125/Videos/555588888888.mp4
2025-07-08 17:35:52 [INFO] 主视频加载成功，时长: 55.66秒
2025-07-08 17:35:52 [INFO] 处理 19 个主视频片段...
2025-07-08 17:35:52 [INFO] 保留主视频片段 0: 0.00-2.73秒
2025-07-08 17:35:53 [INFO] 保留主视频片段 1: 2.73-5.47秒
2025-07-08 17:35:54 [INFO] 保留主视频片段 2: 5.47-6.73秒
2025-07-08 17:35:54 [INFO] 保留主视频片段 3: 6.73-8.43秒
2025-07-08 17:35:55 [INFO] 保留主视频片段 4: 8.43-13.27秒
2025-07-08 17:35:55 [INFO] 保留主视频片段 5: 13.27-14.67秒
2025-07-08 17:35:56 [INFO] 保留主视频片段 6: 14.67-18.23秒
2025-07-08 17:35:56 [INFO] 保留主视频片段 7: 18.23-21.87秒
2025-07-08 17:35:56 [INFO] 保留主视频片段 8: 21.87-23.43秒
2025-07-08 17:35:57 [INFO] 保留主视频片段 9: 23.43-26.17秒
2025-07-08 17:35:59 [INFO] 保留主视频片段 10: 26.17-29.17秒
2025-07-08 17:36:00 [INFO] 保留主视频片段 11: 29.17-32.87秒
2025-07-08 17:36:01 [INFO] 保留主视频片段 12: 32.87-36.40秒
2025-07-08 17:36:01 [INFO] 保留主视频片段 13: 36.40-37.93秒
2025-07-08 17:36:02 [INFO] 保留主视频片段 14: 37.93-41.53秒
2025-07-08 17:36:02 [INFO] 保留主视频片段 15: 41.53-46.63秒
2025-07-08 17:36:02 [INFO] 保留主视频片段 16: 46.63-49.27秒
2025-07-08 17:36:04 [INFO] 保留主视频片段 17: 49.27-53.33秒
2025-07-08 17:36:04 [INFO] 保留主视频片段 18: 53.33-55.63秒
2025-07-08 17:36:04 [INFO] 处理 0 个替换片段...
2025-07-08 17:36:04 [INFO] 开始合成 19 个视频片段...
2025-07-08 17:36:04 [INFO] 有效片段数量: 19
2025-07-08 17:36:04 [INFO] 使用内存优化方法合成视频...
2025-07-08 17:36:04 [INFO] 合并 19 个有效片段，总时长: 55.63秒
2025-07-08 17:36:04 [INFO] 使用分批合并方法 (19 个片段)
2025-07-08 17:36:04 [INFO] 合并批次 1/10
2025-07-08 17:36:04 [INFO] 合并批次 2/10
2025-07-08 17:36:04 [INFO] 合并批次 3/10
2025-07-08 17:36:04 [INFO] 合并批次 4/10
2025-07-08 17:36:04 [INFO] 合并批次 5/10
2025-07-08 17:36:05 [INFO] 合并批次 6/10
2025-07-08 17:36:05 [INFO] 合并批次 7/10
2025-07-08 17:36:05 [INFO] 合并批次 8/10
2025-07-08 17:36:05 [INFO] 合并批次 9/10
2025-07-08 17:36:05 [INFO] 最终合并 10 个批次
2025-07-08 17:36:05 [INFO] 递归合并: 分为 5 和 5 个片段
2025-07-08 17:36:05 [INFO] 合并 5 个有效片段，总时长: 26.17秒
2025-07-08 17:36:05 [INFO] 使用分批合并方法 (5 个片段)
2025-07-08 17:36:05 [INFO] 合并批次 1/3
2025-07-08 17:36:05 [INFO] 合并批次 2/3
2025-07-08 17:36:05 [INFO] 最终合并 3 个批次
2025-07-08 17:36:05 [INFO] 合并 5 个有效片段，总时长: 29.47秒
2025-07-08 17:36:05 [INFO] 使用分批合并方法 (5 个片段)
2025-07-08 17:36:05 [INFO] 合并批次 1/3
2025-07-08 17:36:05 [INFO] 合并批次 2/3
2025-07-08 17:36:05 [INFO] 最终合并 3 个批次
2025-07-08 17:36:06 [INFO] 视频合成成功，总时长: 55.63秒
2025-07-08 17:36:06 [INFO] 开始写入视频文件: D:\25125\Videos\wwresr.mp4
2025-07-08 17:36:06 [WARNING] 视频写入失败，重试 1/5: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:36:08 [WARNING] 视频写入失败，重试 2/5: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:36:10 [WARNING] 视频写入失败，重试 3/5: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:36:12 [WARNING] 视频写入失败，重试 4/5: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:36:14 [ERROR] 视频写入最终失败: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:36:14 [INFO] 尝试使用FFmpeg备用方法写入视频...
2025-07-08 17:36:14 [WARNING] 提取音频失败，将创建无声视频: 'CompositeAudioClip' object has no attribute 'fps'
2025-07-08 17:36:15 [ERROR] 备用方法保存临时视频失败: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:36:15 [INFO] 安全写入失败: 输出文件未生成: D:\25125\Videos\wwresr.mp4
2025-07-08 17:36:15 [INFO] 尝试使用备用方法写入...
2025-07-08 17:36:15 [INFO] 尝试使用FFmpeg备用方法写入视频...
2025-07-08 17:36:15 [WARNING] 提取音频失败，将创建无声视频: 'CompositeAudioClip' object has no attribute 'fps'
2025-07-08 17:36:15 [ERROR] 备用方法保存临时视频失败: 'NoneType' object has no attribute 'get_frame'
2025-07-08 17:36:15 [INFO] 视频合成过程失败: 所有写入方式都失败: 标准=输出文件未生成: D:\25125\Videos\wwresr.mp4, 备用=备用写入方法失败
2025-07-08 17:36:15 [INFO] 处理过程中发生错误: 视频合成失败: 所有写入方式都失败: 标准=输出文件未生成: D:\25125\Videos\wwresr.mp4, 备用=备用写入方法失败
2025-07-08 17:36:15 [INFO] Error in replace_and_concatenate_videos: 视频合成失败: 所有写入方式都失败: 标准=输出文件未生成: D:\25125\Videos\wwresr.mp4, 备用=备用写入方法失败
2025-07-08 17:36:15 [INFO] Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1363, in replace_and_concatenate_videos
    raise Exception(f"输出文件未生成: {output_path}")
Exception: 输出文件未生成: D:\25125\Videos\wwresr.mp4

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1380, in replace_and_concatenate_videos
    raise Exception("备用写入方法失败")
Exception: 备用写入方法失败

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1385, in replace_and_concatenate_videos
    raise Exception(f"所有写入方式都失败: 标准={write_error}, 备用={fallback_error}")
Exception: 所有写入方式都失败: 标准=输出文件未生成: D:\25125\Videos\wwresr.mp4, 备用=备用写入方法失败

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1392, in replace_and_concatenate_videos
    raise Exception(f"视频合成失败: {concat_error}")
Exception: 视频合成失败: 所有写入方式都失败: 标准=输出文件未生成: D:\25125\Videos\wwresr.mp4, 备用=备用写入方法失败

2025-07-08 17:36:15 [INFO] Error in replace_and_concatenate_videos: 视频合成失败: 所有写入方式都失败: 标准=输出文件未生成: D:\25125\Videos\wwresr.mp4, 备用=备用写入方法失败
2025-07-08 17:36:15 [INFO] Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1363, in replace_and_concatenate_videos
    raise Exception(f"输出文件未生成: {output_path}")
Exception: 输出文件未生成: D:\25125\Videos\wwresr.mp4

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1380, in replace_and_concatenate_videos
    raise Exception("备用写入方法失败")
Exception: 备用写入方法失败

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1385, in replace_and_concatenate_videos
    raise Exception(f"所有写入方式都失败: 标准={write_error}, 备用={fallback_error}")
Exception: 所有写入方式都失败: 标准=输出文件未生成: D:\25125\Videos\wwresr.mp4, 备用=备用写入方法失败

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\performance_optimizer.py", line 236, in replace_and_concatenate_videos
    return self.base_processor.replace_and_concatenate_videos(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1392, in replace_and_concatenate_videos
    raise Exception(f"视频合成失败: {concat_error}")
Exception: 视频合成失败: 所有写入方式都失败: 标准=输出文件未生成: D:\25125\Videos\wwresr.mp4, 备用=备用写入方法失败

2025-07-08 17:40:27 [INFO] ✅ 激活验证模块加载成功
2025-07-08 17:40:28 [INFO] ✅ 单实例应用初始化成功
2025-07-08 17:40:28 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 17:40:29 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-08 17:40:29 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-08 17:40:29 [INFO] ✅ 已更新最后验证时间
2025-07-08 17:40:29 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-08 17:40:29 [INFO] ✅ 本地激活配置验证通过
2025-07-08 17:40:29 [INFO] ✅ 本地激活验证通过
2025-07-08 17:40:29 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 17:40:58 [INFO] Error initializing OptimizedVideoProcessor: expected an indented block after 'else' statement on line 697 (video_processor.py, line 698)
2025-07-08 17:41:31 [INFO] Error initializing OptimizedVideoProcessor: expected an indented block after 'else' statement on line 697 (video_processor.py, line 698)
2025-07-08 17:41:34 [INFO] 🚪 用户选择完全退出程序
2025-07-08 17:49:08 [INFO] ✅ 激活验证模块加载成功
2025-07-08 17:49:10 [INFO] ✅ 单实例应用初始化成功
2025-07-08 17:49:10 [INFO] ✅ 成功设置应用程序图标: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 17:49:11 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-08 17:49:11 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-08 17:49:12 [INFO] ✅ 已更新最后验证时间
2025-07-08 17:49:12 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-08 17:49:12 [INFO] ✅ 本地激活配置验证通过
2025-07-08 17:49:12 [INFO] ✅ 本地激活验证通过
2025-07-08 17:49:12 [INFO] ✅ 成功加载窗口图标: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 17:49:21 [INFO] 🚪 用户选择完全退出程序
2025-07-08 17:49:33 [INFO] ✅ 激活验证模块加载成功
2025-07-08 17:49:34 [INFO] ✅ 单实例应用初始化成功
2025-07-08 17:49:34 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 17:49:35 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-08 17:49:35 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-08 17:49:37 [INFO] ✅ 已更新最后验证时间
2025-07-08 17:49:37 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-08 17:49:37 [INFO] ✅ 本地激活配置验证通过
2025-07-08 17:49:37 [INFO] ✅ 本地激活验证通过
2025-07-08 17:49:37 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 17:50:12 [INFO] Error initializing OptimizedVideoProcessor: expected 'except' or 'finally' block (video_processor.py, line 3930)
2025-07-08 17:51:12 [INFO] 📦 窗口已最小化到任务栏
2025-07-08 17:51:12 [INFO] 📦 窗口已最小化到任务栏
2025-07-08 17:51:36 [INFO] 🚪 用户选择完全退出程序
2025-07-08 20:26:46 [INFO] ✅ 激活验证模块加载成功
2025-07-08 20:26:48 [INFO] ✅ 单实例应用初始化成功
2025-07-08 20:26:48 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 20:26:48 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-08 20:26:48 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-08 20:26:49 [INFO] ✅ 已更新最后验证时间
2025-07-08 20:26:49 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-08 20:26:49 [INFO] ✅ 本地激活配置验证通过
2025-07-08 20:26:49 [INFO] ✅ 本地激活验证通过
2025-07-08 20:26:49 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 20:28:18 [INFO] Error initializing OptimizedVideoProcessor: unexpected indent (video_processor.py, line 3924)
2025-07-08 20:28:23 [INFO] 🚪 用户选择完全退出程序
2025-07-08 20:32:01 [INFO] ✅ 激活验证模块加载成功
2025-07-08 20:32:03 [INFO] ✅ 单实例应用初始化成功
2025-07-08 20:32:03 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 20:32:03 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-08 20:32:03 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-08 20:32:03 [INFO] ✅ 已更新最后验证时间
2025-07-08 20:32:03 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-08 20:32:03 [INFO] ✅ 本地激活配置验证通过
2025-07-08 20:32:03 [INFO] ✅ 本地激活验证通过
2025-07-08 20:32:03 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 20:32:26 [INFO] Error initializing OptimizedVideoProcessor: expected an indented block after 'else' statement on line 697 (video_processor.py, line 698)
2025-07-08 20:32:54 [INFO] 🚪 用户选择完全退出程序
2025-07-08 21:22:34 [INFO] ✅ 激活验证模块加载成功
2025-07-08 21:22:36 [INFO] ✅ 单实例应用初始化成功
2025-07-08 21:22:36 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 21:22:36 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-08 21:22:36 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-08 21:22:37 [INFO] ✅ 已更新最后验证时间
2025-07-08 21:22:37 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-08 21:22:37 [INFO] ✅ 本地激活配置验证通过
2025-07-08 21:22:37 [INFO] ✅ 本地激活验证通过
2025-07-08 21:22:37 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 21:23:00 [INFO] Error initializing OptimizedVideoProcessor: expected an indented block after 'else' statement on line 732 (video_processor.py, line 733)
2025-07-08 21:23:13 [INFO] 📦 窗口已最小化到任务栏
2025-07-08 21:23:13 [INFO] 📦 窗口已最小化到任务栏
2025-07-08 21:24:44 [INFO] ✅ 激活验证模块加载成功
2025-07-08 21:24:45 [INFO] ✅ 单实例应用初始化成功
2025-07-08 21:24:45 [INFO] ✅ 成功设置应用程序图标: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 21:24:46 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-08 21:24:46 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-08 21:24:46 [INFO] ✅ 已更新最后验证时间
2025-07-08 21:24:46 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-08 21:24:46 [INFO] ✅ 本地激活配置验证通过
2025-07-08 21:24:46 [INFO] ✅ 本地激活验证通过
2025-07-08 21:24:46 [INFO] ✅ 成功加载窗口图标: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 21:24:57 [INFO] ✅ 激活验证模块加载成功
2025-07-08 21:24:58 [INFO] ✅ 单实例应用初始化成功
2025-07-08 21:24:58 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 21:24:59 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-08 21:24:59 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-08 21:24:59 [INFO] ✅ 已更新最后验证时间
2025-07-08 21:24:59 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-08 21:24:59 [INFO] ✅ 本地激活配置验证通过
2025-07-08 21:24:59 [INFO] ✅ 本地激活验证通过
2025-07-08 21:24:59 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 21:25:25 [INFO] Error initializing OptimizedVideoProcessor: expected an indented block after 'try' statement on line 1156 (video_processor.py, line 1157)
2025-07-08 21:25:36 [INFO] 📦 窗口已最小化到任务栏
2025-07-08 21:25:36 [INFO] 📦 窗口已最小化到任务栏
2025-07-08 21:25:49 [INFO] 🚪 用户选择完全退出程序
2025-07-08 21:44:45 [INFO] ✅ 激活验证模块加载成功
2025-07-08 21:44:46 [INFO] ✅ 单实例应用初始化成功
2025-07-08 21:44:46 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 21:44:47 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-08 21:44:47 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-08 21:44:48 [INFO] ✅ 已更新最后验证时间
2025-07-08 21:44:48 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-08 21:44:48 [INFO] ✅ 本地激活配置验证通过
2025-07-08 21:44:48 [INFO] ✅ 本地激活验证通过
2025-07-08 21:44:48 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 21:45:21 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-07-08 21:45:21 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-08 21:45:22 [INFO] 未检测到CUDA设备
2025-07-08 21:45:22 [INFO] VideoProcessor初始化:
2025-07-08 21:45:22 [INFO]   CPU线程数: 8
2025-07-08 21:45:22 [INFO]   GPU支持: 否
2025-07-08 21:45:22 [INFO]   GPU加速: 禁用
2025-07-08 21:45:22 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-08 21:45:22 [INFO] 使用PySceneDetect版本: 0.6.6
2025-07-08 21:45:22 [INFO] 成功导入ContentDetector(方法1)
2025-07-08 21:45:22 [INFO] 视频文件大小: 13.66 MB
2025-07-08 21:45:22 [INFO] 视频信息: 30.0 FPS, 1669 帧, 55.63 秒
2025-07-08 21:45:22 [INFO] 尝试使用PySceneDetect原生方法打开视频...
2025-07-08 21:45:22 [INFO] 开始执行场景检测...
2025-07-08 21:45:31 [INFO] 场景检测完成，耗时 9.75 秒
2025-07-08 21:45:31 [INFO] 检测到 19 个场景
2025-07-08 21:45:32 [INFO] 使用PySceneDetect版本: 0.6.6
2025-07-08 21:45:32 [INFO] 成功导入ContentDetector(方法1)
2025-07-08 21:45:32 [INFO] 视频文件大小: 28.10 MB
2025-07-08 21:45:32 [INFO] 视频信息: 30.0 FPS, 1198 帧, 39.93 秒
2025-07-08 21:45:32 [INFO] 尝试使用PySceneDetect原生方法打开视频...
2025-07-08 21:45:32 [INFO] 开始执行场景检测...
2025-07-08 21:45:42 [INFO] 场景检测完成，耗时 10.06 秒
2025-07-08 21:45:42 [INFO] 检测到 2 个场景
2025-07-08 21:45:42 [INFO] 正在并行提取主视频特征...
2025-07-08 21:46:03 [INFO] 正在并行提取辅助视频特征...
2025-07-08 21:46:05 [INFO] 正在并行计算相似度矩阵...
2025-07-08 21:46:05 [INFO] 正在查找最佳匹配...
2025-07-08 21:46:05 [INFO] 使用临时路径处理视频: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\temp_output_d877d087_1751982365275_e1b38e26.mp4
2025-07-08 21:46:05 [INFO] 最终输出路径: D:/25125/Videos/www111.mp4
2025-07-08 21:46:05 [INFO] 加载主视频: D:/25125/Videos/555588888888.mp4
2025-07-08 21:46:06 [INFO] 主视频加载成功，时长: 55.66秒
2025-07-08 21:46:06 [INFO] 处理 19 个主视频片段...
2025-07-08 21:46:06 [INFO] 保留主视频片段 0: 0.00-2.73秒
2025-07-08 21:46:07 [INFO] 保留主视频片段 1: 2.73-5.47秒
2025-07-08 21:46:08 [INFO] 保留主视频片段 2: 5.47-6.73秒
2025-07-08 21:46:08 [INFO] 保留主视频片段 3: 6.73-8.43秒
2025-07-08 21:46:09 [INFO] 保留主视频片段 4: 8.43-13.27秒
2025-07-08 21:46:09 [INFO] 保留主视频片段 5: 13.27-14.67秒
2025-07-08 21:46:10 [INFO] 保留主视频片段 6: 14.67-18.23秒
2025-07-08 21:46:10 [INFO] 保留主视频片段 7: 18.23-21.87秒
2025-07-08 21:46:11 [INFO] 保留主视频片段 8: 21.87-23.43秒
2025-07-08 21:46:11 [INFO] 保留主视频片段 9: 23.43-26.17秒
2025-07-08 21:46:12 [INFO] 保留主视频片段 10: 26.17-29.17秒
2025-07-08 21:46:13 [INFO] 保留主视频片段 11: 29.17-32.87秒
2025-07-08 21:46:14 [INFO] 保留主视频片段 12: 32.87-36.40秒
2025-07-08 21:46:14 [INFO] 保留主视频片段 13: 36.40-37.93秒
2025-07-08 21:46:15 [INFO] 保留主视频片段 14: 37.93-41.53秒
2025-07-08 21:46:15 [INFO] 保留主视频片段 15: 41.53-46.63秒
2025-07-08 21:46:15 [INFO] 保留主视频片段 16: 46.63-49.27秒
2025-07-08 21:46:16 [INFO] 保留主视频片段 17: 49.27-53.33秒
2025-07-08 21:46:16 [INFO] 保留主视频片段 18: 53.33-55.63秒
2025-07-08 21:46:16 [INFO] 处理 0 个替换片段...
2025-07-08 21:46:16 [INFO] 开始合成 19 个视频片段...
2025-07-08 21:46:16 [INFO] 有效片段数量: 19
2025-07-08 21:46:16 [INFO] 尝试方法1：使用内存优化方法合成视频...
2025-07-08 21:46:16 [INFO] 合并 19 个有效片段，总时长: 55.63秒
2025-07-08 21:46:16 [INFO] 使用分批合并方法 (19 个片段)
2025-07-08 21:46:17 [INFO] 合并批次 1/10
2025-07-08 21:46:17 [INFO] 合并批次 2/10
2025-07-08 21:46:17 [INFO] 合并批次 3/10
2025-07-08 21:46:17 [INFO] 合并批次 4/10
2025-07-08 21:46:17 [INFO] 合并批次 5/10
2025-07-08 21:46:17 [INFO] 合并批次 6/10
2025-07-08 21:46:17 [INFO] 合并批次 7/10
2025-07-08 21:46:17 [INFO] 合并批次 8/10
2025-07-08 21:46:17 [INFO] 合并批次 9/10
2025-07-08 21:46:17 [INFO] 最终合并 10 个批次
2025-07-08 21:46:17 [INFO] 递归合并: 分为 5 和 5 个片段
2025-07-08 21:46:17 [INFO] 合并 5 个有效片段，总时长: 26.17秒
2025-07-08 21:46:17 [INFO] 使用分批合并方法 (5 个片段)
2025-07-08 21:46:17 [INFO] 合并批次 1/3
2025-07-08 21:46:17 [INFO] 合并批次 2/3
2025-07-08 21:46:17 [INFO] 最终合并 3 个批次
2025-07-08 21:46:17 [INFO] 合并 5 个有效片段，总时长: 29.47秒
2025-07-08 21:46:17 [INFO] 使用分批合并方法 (5 个片段)
2025-07-08 21:46:17 [INFO] 合并批次 1/3
2025-07-08 21:46:17 [INFO] 合并批次 2/3
2025-07-08 21:46:17 [INFO] 最终合并 3 个批次
2025-07-08 21:46:17 [INFO] 视频合成成功，总时长: 55.63秒
2025-07-08 21:46:18 [WARNING] 视频写入失败，重试 1/5: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:46:20 [WARNING] 视频写入失败，重试 2/5: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:46:22 [WARNING] 视频写入失败，重试 3/5: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:46:24 [WARNING] 视频写入失败，重试 4/5: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:46:26 [ERROR] 视频写入最终失败: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:46:26 [INFO] 尝试使用FFmpeg备用方法写入视频...
2025-07-08 21:46:26 [WARNING] 提取音频失败，将创建无声视频: 'CompositeAudioClip' object has no attribute 'fps'
2025-07-08 21:46:26 [ERROR] 备用方法保存临时视频失败: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:46:26 [INFO] 方法1失败: 临时输出文件无效或过小: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\temp_output_d877d087_1751982365275_e1b38e26.mp4
2025-07-08 21:46:26 [INFO] 尝试方法2：使用FFmpeg直接连接...
2025-07-08 21:46:26 [INFO] 保存片段 0 到 D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\segment_0_d877d087_1751982386832_457e3d25.mp4
2025-07-08 21:46:26 [INFO] 方法1失败: 临时输出文件无效或过小: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\temp_output_d877d087_1751982365275_e1b38e26.mp4; 方法2失败: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:46:27 [INFO] 处理过程中发生错误: 视频合成过程失败: 视频合成失败: 方法1失败: 临时输出文件无效或过小: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\temp_output_d877d087_1751982365275_e1b38e26.mp4; 方法2失败: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:46:27 [INFO] Error in replace_and_concatenate_videos: 视频合成过程失败: 视频合成失败: 方法1失败: 临时输出文件无效或过小: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\temp_output_d877d087_1751982365275_e1b38e26.mp4; 方法2失败: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:46:27 [INFO] Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1432, in replace_and_concatenate_videos
    raise Exception(f"视频合成失败: {concat_error if concat_error else '未知原因'}")
Exception: 视频合成失败: 方法1失败: 临时输出文件无效或过小: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\temp_output_d877d087_1751982365275_e1b38e26.mp4; 方法2失败: 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1469, in replace_and_concatenate_videos
    raise Exception(f"视频合成过程失败: {str(e)}")
Exception: 视频合成过程失败: 视频合成失败: 方法1失败: 临时输出文件无效或过小: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\temp_output_d877d087_1751982365275_e1b38e26.mp4; 方法2失败: 'NoneType' object has no attribute 'get_frame'

2025-07-08 21:46:27 [INFO] Error in replace_and_concatenate_videos: 视频合成过程失败: 视频合成失败: 方法1失败: 临时输出文件无效或过小: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\temp_output_d877d087_1751982365275_e1b38e26.mp4; 方法2失败: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:46:27 [INFO] Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1432, in replace_and_concatenate_videos
    raise Exception(f"视频合成失败: {concat_error if concat_error else '未知原因'}")
Exception: 视频合成失败: 方法1失败: 临时输出文件无效或过小: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\temp_output_d877d087_1751982365275_e1b38e26.mp4; 方法2失败: 'NoneType' object has no attribute 'get_frame'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\performance_optimizer.py", line 236, in replace_and_concatenate_videos
    return self.base_processor.replace_and_concatenate_videos(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1469, in replace_and_concatenate_videos
    raise Exception(f"视频合成过程失败: {str(e)}")
Exception: 视频合成过程失败: 视频合成失败: 方法1失败: 临时输出文件无效或过小: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\temp_output_d877d087_1751982365275_e1b38e26.mp4; 方法2失败: 'NoneType' object has no attribute 'get_frame'

2025-07-08 21:49:19 [INFO] 🚪 用户选择完全退出程序
2025-07-08 21:56:34 [INFO] ✅ 激活验证模块加载成功
2025-07-08 21:56:36 [INFO] ✅ 单实例应用初始化成功
2025-07-08 21:56:36 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 21:56:37 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-08 21:56:37 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-08 21:56:37 [INFO] ✅ 已更新最后验证时间
2025-07-08 21:56:37 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-08 21:56:37 [INFO] ✅ 本地激活配置验证通过
2025-07-08 21:56:37 [INFO] ✅ 本地激活验证通过
2025-07-08 21:56:37 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-08 21:57:10 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-07-08 21:57:11 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-08 21:57:11 [INFO] 未检测到CUDA设备
2025-07-08 21:57:11 [INFO] VideoProcessor初始化:
2025-07-08 21:57:11 [INFO]   CPU线程数: 8
2025-07-08 21:57:11 [INFO]   GPU支持: 否
2025-07-08 21:57:11 [INFO]   GPU加速: 禁用
2025-07-08 21:57:11 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-08 21:57:11 [INFO] 使用PySceneDetect版本: 0.6.6
2025-07-08 21:57:11 [INFO] 成功导入ContentDetector(方法1)
2025-07-08 21:57:11 [INFO] 视频文件大小: 13.66 MB
2025-07-08 21:57:11 [INFO] 视频信息: 30.0 FPS, 1669 帧, 55.63 秒
2025-07-08 21:57:11 [INFO] 尝试使用PySceneDetect原生方法打开视频...
2025-07-08 21:57:11 [INFO] 开始执行场景检测...
2025-07-08 21:57:22 [INFO] 场景检测完成，耗时 10.86 秒
2025-07-08 21:57:22 [INFO] 检测到 19 个场景
2025-07-08 21:57:22 [INFO] 使用PySceneDetect版本: 0.6.6
2025-07-08 21:57:22 [INFO] 成功导入ContentDetector(方法1)
2025-07-08 21:57:22 [INFO] 视频文件大小: 0.60 MB
2025-07-08 21:57:22 [INFO] 视频信息: 30.0 FPS, 241 帧, 8.03 秒
2025-07-08 21:57:22 [INFO] 尝试使用PySceneDetect原生方法打开视频...
2025-07-08 21:57:22 [INFO] 开始执行场景检测...
2025-07-08 21:57:23 [INFO] 场景检测完成，耗时 0.27 秒
2025-07-08 21:57:23 [INFO] 场景列表为空，使用备用方法
2025-07-08 21:57:23 [INFO] 生成备用场景列表: 视频时长 8.03 秒, 生成 10 个场景
2025-07-08 21:57:23 [INFO] 生成了 5 个备用场景
2025-07-08 21:57:23 [INFO] 正在并行提取主视频特征...
2025-07-08 21:57:44 [INFO] 正在并行提取辅助视频特征...
2025-07-08 21:57:44 [INFO] 正在并行计算相似度矩阵...
2025-07-08 21:57:44 [INFO] 正在查找最佳匹配...
2025-07-08 21:57:44 [INFO] 使用临时路径处理视频: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\temp_output_9ef2f86d_1751983064992_8f401b48.mp4
2025-07-08 21:57:45 [INFO] 最终输出路径: D:/25125/Videos/www11.mp4
2025-07-08 21:57:45 [INFO] 加载主视频: D:/25125/Videos/555588888888.mp4
2025-07-08 21:57:46 [INFO] 主视频加载成功，时长: 55.66秒
2025-07-08 21:57:46 [INFO] 处理 19 个主视频片段...
2025-07-08 21:57:46 [INFO] 保留主视频片段 0: 0.00-2.73秒
2025-07-08 21:57:47 [INFO] 保留主视频片段 1: 2.73-5.47秒
2025-07-08 21:57:47 [INFO] 保留主视频片段 2: 5.47-6.73秒
2025-07-08 21:57:48 [INFO] 保留主视频片段 3: 6.73-8.43秒
2025-07-08 21:57:48 [INFO] 保留主视频片段 4: 8.43-13.27秒
2025-07-08 21:57:49 [INFO] 保留主视频片段 5: 13.27-14.67秒
2025-07-08 21:57:49 [INFO] 保留主视频片段 6: 14.67-18.23秒
2025-07-08 21:57:50 [INFO] 保留主视频片段 7: 18.23-21.87秒
2025-07-08 21:57:50 [INFO] 保留主视频片段 8: 21.87-23.43秒
2025-07-08 21:57:51 [INFO] 保留主视频片段 9: 23.43-26.17秒
2025-07-08 21:57:52 [INFO] 保留主视频片段 10: 26.17-29.17秒
2025-07-08 21:57:53 [INFO] 保留主视频片段 11: 29.17-32.87秒
2025-07-08 21:57:53 [INFO] 保留主视频片段 12: 32.87-36.40秒
2025-07-08 21:57:53 [INFO] 保留主视频片段 13: 36.40-37.93秒
2025-07-08 21:57:54 [INFO] 保留主视频片段 14: 37.93-41.53秒
2025-07-08 21:57:54 [INFO] 保留主视频片段 15: 41.53-46.63秒
2025-07-08 21:57:55 [INFO] 保留主视频片段 16: 46.63-49.27秒
2025-07-08 21:57:56 [INFO] 保留主视频片段 17: 49.27-53.33秒
2025-07-08 21:57:56 [INFO] 保留主视频片段 18: 53.33-55.63秒
2025-07-08 21:57:56 [INFO] 处理 0 个替换片段...
2025-07-08 21:57:56 [INFO] 开始合成 19 个视频片段...
2025-07-08 21:57:56 [INFO] 有效片段数量: 19
2025-07-08 21:57:56 [INFO] 尝试方法1：使用内存优化方法合成视频...
2025-07-08 21:57:56 [INFO] 跳过获取帧失败的片段 0: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过获取帧失败的片段 1: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过获取帧失败的片段 2: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过获取帧失败的片段 3: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过获取帧失败的片段 4: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过获取帧失败的片段 5: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过获取帧失败的片段 6: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过获取帧失败的片段 7: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过获取帧失败的片段 8: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过获取帧失败的片段 9: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过获取帧失败的片段 10: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过获取帧失败的片段 11: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过获取帧失败的片段 12: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过获取帧失败的片段 13: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过获取帧失败的片段 14: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过获取帧失败的片段 15: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过获取帧失败的片段 16: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过获取帧失败的片段 17: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过获取帧失败的片段 18: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 内存友好合并失败: 所有片段都无效，无法合并
2025-07-08 21:57:56 [INFO] 尝试使用chain方法合并...
2025-07-08 21:57:56 [INFO] chain方法合并失败: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 方法1失败: 视频合并失败: 所有片段都无效，无法合并, 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 尝试方法2：使用FFmpeg直接连接...
2025-07-08 21:57:56 [INFO] 跳过验证失败的片段 0: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过验证失败的片段 1: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过验证失败的片段 2: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过验证失败的片段 3: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过验证失败的片段 4: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过验证失败的片段 5: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过验证失败的片段 6: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过验证失败的片段 7: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过验证失败的片段 8: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过验证失败的片段 9: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过验证失败的片段 10: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过验证失败的片段 11: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过验证失败的片段 12: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过验证失败的片段 13: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过验证失败的片段 14: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过验证失败的片段 15: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过验证失败的片段 16: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过验证失败的片段 17: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 跳过验证失败的片段 18: 'NoneType' object has no attribute 'get_frame'
2025-07-08 21:57:56 [INFO] 方法1失败: 视频合并失败: 所有片段都无效，无法合并, 'NoneType' object has no attribute 'get_frame'; 方法2失败: 没有有效的片段文件可以合并
2025-07-08 21:57:56 [INFO] 处理过程中发生错误: 视频合成过程失败: 视频合成失败: 方法1失败: 视频合并失败: 所有片段都无效，无法合并, 'NoneType' object has no attribute 'get_frame'; 方法2失败: 没有有效的片段文件可以合并
2025-07-08 21:57:56 [INFO] Error in replace_and_concatenate_videos: 视频合成过程失败: 视频合成失败: 方法1失败: 视频合并失败: 所有片段都无效，无法合并, 'NoneType' object has no attribute 'get_frame'; 方法2失败: 没有有效的片段文件可以合并
2025-07-08 21:57:56 [INFO] Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1497, in replace_and_concatenate_videos
    raise Exception(f"视频合成失败: {concat_error if concat_error else '未知原因'}")
Exception: 视频合成失败: 方法1失败: 视频合并失败: 所有片段都无效，无法合并, 'NoneType' object has no attribute 'get_frame'; 方法2失败: 没有有效的片段文件可以合并

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1534, in replace_and_concatenate_videos
    raise Exception(f"视频合成过程失败: {str(e)}")
Exception: 视频合成过程失败: 视频合成失败: 方法1失败: 视频合并失败: 所有片段都无效，无法合并, 'NoneType' object has no attribute 'get_frame'; 方法2失败: 没有有效的片段文件可以合并

2025-07-08 21:57:56 [INFO] Error in replace_and_concatenate_videos: 视频合成过程失败: 视频合成失败: 方法1失败: 视频合并失败: 所有片段都无效，无法合并, 'NoneType' object has no attribute 'get_frame'; 方法2失败: 没有有效的片段文件可以合并
2025-07-08 21:57:56 [INFO] Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1497, in replace_and_concatenate_videos
    raise Exception(f"视频合成失败: {concat_error if concat_error else '未知原因'}")
Exception: 视频合成失败: 方法1失败: 视频合并失败: 所有片段都无效，无法合并, 'NoneType' object has no attribute 'get_frame'; 方法2失败: 没有有效的片段文件可以合并

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\performance_optimizer.py", line 236, in replace_and_concatenate_videos
    return self.base_processor.replace_and_concatenate_videos(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 1534, in replace_and_concatenate_videos
    raise Exception(f"视频合成过程失败: {str(e)}")
Exception: 视频合成过程失败: 视频合成失败: 方法1失败: 视频合并失败: 所有片段都无效，无法合并, 'NoneType' object has no attribute 'get_frame'; 方法2失败: 没有有效的片段文件可以合并

2025-07-08 22:01:16 [INFO] 📦 窗口已最小化到任务栏
2025-07-08 22:01:16 [INFO] 📦 窗口已最小化到任务栏
2025-07-08 23:44:25 [INFO] 🚪 用户选择完全退出程序
