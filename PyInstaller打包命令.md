# 🎬 余下视频乱剪工具 PyInstaller 打包指令

## 🚀 快速打包命令

### 1. 基础打包命令（推荐）
```bash
pyinstaller --onefile --windowed --icon=img/logo.ico enhanced_video_deduplication_gui.py
```

### 2. 完整打包命令（包含所有资源）
```bash
pyinstaller --onefile --windowed --icon=img/logo.ico \
  --add-data "img;img" \
  --add-data "config.ini;." \
  --add-data "config_example.ini;." \
  --add-data "README.md;." \
  --add-data "余下视频乱剪工具v2.0完整说明书.md;." \
  --hidden-import cv2 \
  --hidden-import numpy \
  --hidden-import moviepy.editor \
  --hidden-import moviepy.video.io.VideoFileClip \
  --hidden-import moviepy.audio.io.AudioFileClip \
  --hidden-import scenedetect \
  --hidden-import scenedetect.detectors \
  --hidden-import scenedetect.detectors.content_detector \
  --hidden-import PyQt5.QtCore \
  --hidden-import PyQt5.QtGui \
  --hidden-import PyQt5.QtWidgets \
  --hidden-import PyQt5.QtNetwork \
  --hidden-import PIL \
  --hidden-import PIL.Image \
  --hidden-import imageio \
  --hidden-import imageio_ffmpeg \
  --hidden-import tqdm \
  --hidden-import pandas \
  --hidden-import psutil \
  --hidden-import performance_optimizer \
  --hidden-import video_processor \
  --hidden-import multi_rectangle_selector \
  --hidden-import alternative_subtitle_system \
  --exclude-module tkinter \
  --exclude-module unittest \
  --exclude-module test \
  --exclude-module tests \
  --exclude-module pytest \
  --name "余下视频乱剪工具" \
  enhanced_video_deduplication_gui.py
```

### 3. Linux/Mac 打包命令
```bash
pyinstaller --onefile --windowed --icon=img/logo.png \
  --add-data "img:img" \
  --add-data "config.ini:." \
  --add-data "config_example.ini:." \
  --add-data "README.md:." \
  --add-data "余下视频乱剪工具v2.0完整说明书.md:." \
  --hidden-import cv2 \
  --hidden-import numpy \
  --hidden-import moviepy.editor \
  --hidden-import scenedetect \
  --hidden-import PyQt5.QtNetwork \
  --hidden-import performance_optimizer \
  --hidden-import video_processor \
  --hidden-import multi_rectangle_selector \
  --name "余下视频乱剪工具" \
  enhanced_video_deduplication_gui.py
```

## 🛠️ 自动化打包方案

### 方案1：使用自动化脚本（推荐）
```bash
# Windows
打包程序.bat

# Linux/Mac
chmod +x 打包程序.sh
./打包程序.sh
```

### 方案2：使用Python脚本
```bash
# 生成配置文件
python build_spec.py

# 执行打包
python build.py

# 或者直接使用生成的spec文件
pyinstaller 余下视频乱剪工具.spec
```

## 📋 打包前准备

### 1. 安装PyInstaller
```bash
pip install pyinstaller
```

### 2. 检查依赖
```bash
pip install -r requirements.txt
```

### 3. 测试程序
```bash
python enhanced_video_deduplication_gui.py
```

## 🎯 打包参数说明

| 参数 | 说明 |
|------|------|
| `--onefile` | 打包成单个可执行文件 |
| `--windowed` | 不显示控制台窗口（GUI程序） |
| `--icon` | 设置程序图标 |
| `--add-data` | 添加数据文件到打包中 |
| `--hidden-import` | 手动指定需要导入的模块 |
| `--exclude-module` | 排除不需要的模块 |
| `--name` | 设置可执行文件名称 |
| `--clean` | 清理临时文件 |
| `--noconfirm` | 不询问覆盖确认 |

## 🔧 常见问题解决

### 1. 模块导入错误
如果打包后运行出现模块导入错误，添加对应的 `--hidden-import` 参数：
```bash
--hidden-import 模块名
```

### 2. 文件缺失错误
如果程序需要额外的文件，使用 `--add-data` 参数：
```bash
# Windows
--add-data "源路径;目标路径"

# Linux/Mac
--add-data "源路径:目标路径"
```

### 3. 图标显示问题
确保图标文件存在且格式正确：
- Windows: 使用 `.ico` 格式
- Linux/Mac: 使用 `.png` 格式

### 4. 文件过大问题
使用以下参数减小文件大小：
```bash
--exclude-module tkinter
--exclude-module unittest
--exclude-module test
--exclude-module matplotlib
--exclude-module jupyter
```

## 📦 打包后测试

### 1. 基本功能测试
- [ ] 程序能正常启动
- [ ] 界面显示正常
- [ ] 图标显示正确
- [ ] 基本功能可用

### 2. 高级功能测试
- [ ] 视频处理功能
- [ ] 字幕移除功能
- [ ] 文件选择功能
- [ ] 设置保存功能

### 3. 兼容性测试
- [ ] 在不同Windows版本测试
- [ ] 在没有Python环境的机器测试
- [ ] 在不同硬件配置测试

## 💡 优化建议

### 1. 减小文件大小
- 使用虚拟环境打包
- 排除不必要的模块
- 使用UPX压缩（可选）

### 2. 提高启动速度
- 减少隐藏导入
- 优化代码结构
- 使用延迟导入

### 3. 增强兼容性
- 测试不同操作系统
- 包含必要的运行时库
- 提供详细的系统要求说明

## 🎉 打包完成后

打包完成后，可执行文件位于 `dist/` 目录中：
- Windows: `余下视频乱剪工具.exe`
- Linux/Mac: `余下视频乱剪工具`

建议创建发布包，包含：
- 可执行文件
- 说明文档
- 配置文件示例
- 启动脚本
