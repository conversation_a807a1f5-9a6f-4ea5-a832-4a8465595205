@echo off
chcp 65001 >nul
title 余下视频乱剪工具 - 自动打包程序

echo.
echo ========================================
echo 🎬 余下视频乱剪工具 v2.0 自动打包程序
echo ========================================
echo.

echo 📋 打包选项:
echo 1. 完整打包 (推荐)
echo 2. 快速打包 (不清理缓存)
echo 3. 仅生成配置文件
echo 4. 手动打包命令
echo 5. 退出
echo.

set /p choice=请选择打包方式 (1-5): 

if "%choice%"=="1" goto full_build
if "%choice%"=="2" goto quick_build
if "%choice%"=="3" goto spec_only
if "%choice%"=="4" goto manual_commands
if "%choice%"=="5" goto exit
goto invalid_choice

:full_build
echo.
echo 🚀 开始完整打包...
python build.py
goto end

:quick_build
echo.
echo ⚡ 开始快速打包...
python build.py --no-clean
goto end

:spec_only
echo.
echo 📝 生成配置文件...
python build_spec.py
echo.
echo ✅ 配置文件已生成
echo 💡 可以使用以下命令进行打包:
echo pyinstaller 余下视频乱剪工具.spec
goto end

:manual_commands
echo.
echo 📋 手动打包命令参考:
echo.
echo 🔹 基础打包命令:
echo pyinstaller --onefile --windowed --icon=img/logo.ico enhanced_video_deduplication_gui.py
echo.
echo 🔹 完整打包命令:
echo pyinstaller --onefile --windowed --icon=img/logo.ico ^
echo   --add-data "img;img" ^
echo   --add-data "config.ini;." ^
echo   --add-data "README.md;." ^
echo   --hidden-import cv2 ^
echo   --hidden-import numpy ^
echo   --hidden-import moviepy.editor ^
echo   --hidden-import scenedetect ^
echo   --hidden-import PyQt5.QtNetwork ^
echo   enhanced_video_deduplication_gui.py
echo.
echo 🔹 使用spec文件打包:
echo python build_spec.py
echo pyinstaller 余下视频乱剪工具.spec
echo.
goto end

:invalid_choice
echo.
echo ❌ 无效选择，请重新运行程序
goto end

:end
echo.
echo 📁 输出目录: dist/
echo 📦 可执行文件: dist/余下视频乱剪工具.exe
echo.
echo 💡 打包完成后建议:
echo 1. 测试可执行文件是否正常运行
echo 2. 检查所有功能是否完整
echo 3. 在不同电脑上测试兼容性
echo.
pause

:exit
echo 退出程序
exit /b 0
