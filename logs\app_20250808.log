2025-08-08 09:43:54 [INFO] ✅ 激活验证模块加载成功
2025-08-08 09:43:57 [INFO] ✅ 单实例应用初始化成功
2025-08-08 09:43:57 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-08-08 09:43:58 [INFO] 🔑 机器码: B609-6939-8E74-6EFF
2025-08-08 09:43:58 [INFO] 📁 配置文件路径: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\activation_config.json
2025-08-08 09:43:58 [INFO] 🔍 检查本地保存的激活码: 71C9-4CBE-4B7F-48BB
2025-08-08 09:43:58 [INFO] 🔍 与服务器验证激活码有效性
2025-08-08 09:43:58 [INFO] ✅ 服务器验证成功: 设备已激活
2025-08-08 09:43:58 [INFO] ✅ 激活码验证通过
2025-08-08 09:43:58 [INFO] ✅ 本地激活验证通过
2025-08-08 09:43:58 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-08-08 09:44:01 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-08-08 09:44:01 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-08-08 09:44:01 [INFO] 未检测到CUDA设备
2025-08-08 09:44:01 [INFO] VideoProcessor初始化:
2025-08-08 09:44:01 [INFO]   CPU线程数: 8
2025-08-08 09:44:01 [INFO]   GPU支持: 否
2025-08-08 09:44:01 [INFO]   GPU加速: 禁用
2025-08-08 09:44:01 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-08-08 09:44:01 [INFO] ✅ FFmpeg已配置: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-08-08 09:44:01 [INFO] 🔍 测试FFmpeg安装...
2025-08-08 09:44:01 [INFO] ✅ FFmpeg安装正确: ffmpeg version 7.0.2-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
2025-08-08 09:44:01 [INFO] ✅ 支持PCM音频编码
2025-08-08 09:44:01 [INFO] ✅ FFmpeg测试通过
2025-08-08 09:44:37 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-08-08 09:44:37 [INFO] 未检测到CUDA设备
2025-08-08 09:44:37 [INFO] VideoProcessor初始化:
2025-08-08 09:44:37 [INFO]   CPU线程数: 4
2025-08-08 09:44:37 [INFO]   GPU支持: 否
2025-08-08 09:44:37 [INFO]   GPU加速: 禁用
2025-08-08 09:44:37 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-08-08 09:44:37 [INFO] 使用PySceneDetect版本: 0.6.6
2025-08-08 09:44:37 [INFO] 成功导入ContentDetector(方法1)
2025-08-08 09:44:37 [INFO] 视频文件大小: 50.26 MB
2025-08-08 09:44:37 [INFO] 视频信息: 30.0 FPS, 7362 帧, 245.40 秒
2025-08-08 09:44:37 [INFO] 尝试使用PySceneDetect原生方法打开视频...
2025-08-08 09:44:37 [INFO] 开始执行场景检测...
