#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频兼容性检查工具
用于诊断和解决视频加载问题
"""

import os
import sys
import subprocess
import json
import time

# 简单的日志函数
def log_info(msg):
    print(f"[INFO] {msg}")

def log_error(msg):
    print(f"[ERROR] {msg}")

def log_warning(msg):
    print(f"[WARNING] {msg}")

class VideoCompatibilityChecker:
    def __init__(self):
        self.ffmpeg_path = self._find_ffmpeg_path()
    
    def _find_ffmpeg_path(self):
        """查找FFmpeg路径"""
        try:
            # 尝试直接调用ffmpeg
            result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                return 'ffmpeg'
        except:
            pass
        
        # 尝试常见路径
        common_paths = [
            'C:/ffmpeg/bin/ffmpeg.exe',
            'C:/Program Files/ffmpeg/bin/ffmpeg.exe',
            '/usr/bin/ffmpeg',
            '/usr/local/bin/ffmpeg'
        ]
        
        for path in common_paths:
            if os.path.exists(path):
                return path
        
        return None
    
    def check_video_file(self, video_path):
        """
        全面检查视频文件
        :param video_path: 视频文件路径
        :return: 检查结果字典
        """
        result = {
            'file_exists': False,
            'file_size': 0,
            'file_readable': False,
            'ffmpeg_info': None,
            'moviepy_loadable': False,
            'issues': [],
            'recommendations': []
        }
        
        print(f"\n🔍 检查视频文件: {video_path}")
        print("=" * 60)
        
        # 1. 检查文件是否存在
        if not os.path.exists(video_path):
            result['issues'].append("文件不存在")
            print("❌ 文件不存在")
            return result
        
        result['file_exists'] = True
        print("✅ 文件存在")
        
        # 2. 检查文件大小
        try:
            file_size = os.path.getsize(video_path)
            result['file_size'] = file_size
            file_size_mb = file_size / (1024 * 1024)
            print(f"📏 文件大小: {file_size_mb:.2f} MB")
            
            if file_size == 0:
                result['issues'].append("文件大小为0")
                print("❌ 文件大小为0")
            elif file_size_mb > 1000:
                result['issues'].append("文件过大（>1GB）")
                result['recommendations'].append("考虑压缩视频或分段处理")
                print(f"⚠️ 文件较大: {file_size_mb:.2f} MB")
        except Exception as e:
            result['issues'].append(f"无法获取文件大小: {str(e)}")
            print(f"❌ 无法获取文件大小: {str(e)}")
        
        # 3. 检查文件可读性
        try:
            with open(video_path, 'rb') as f:
                f.read(1024)  # 尝试读取前1KB
            result['file_readable'] = True
            print("✅ 文件可读")
        except Exception as e:
            result['issues'].append(f"文件不可读: {str(e)}")
            print(f"❌ 文件不可读: {str(e)}")
        
        # 4. 使用FFmpeg检查
        if self.ffmpeg_path:
            ffmpeg_info = self._check_with_ffmpeg(video_path)
            result['ffmpeg_info'] = ffmpeg_info
            
            if ffmpeg_info:
                print("✅ FFmpeg可以识别视频")
                print(f"   时长: {ffmpeg_info.get('duration', 'unknown')}秒")
                print(f"   分辨率: {ffmpeg_info.get('width', 'unknown')}x{ffmpeg_info.get('height', 'unknown')}")
                print(f"   编码: {ffmpeg_info.get('codec', 'unknown')}")
                print(f"   帧率: {ffmpeg_info.get('fps', 'unknown')}")
                
                # 检查编码兼容性
                codec = ffmpeg_info.get('codec', '').lower()
                if codec in ['h264', 'h.264', 'avc']:
                    print("✅ 编码格式兼容性良好")
                elif codec in ['h265', 'hevc']:
                    result['recommendations'].append("H.265编码可能导致加载缓慢，建议转换为H.264")
                    print("⚠️ H.265编码可能导致加载缓慢")
                else:
                    result['recommendations'].append(f"未知编码格式 {codec}，建议转换为H.264")
                    print(f"⚠️ 未知编码格式: {codec}")
            else:
                result['issues'].append("FFmpeg无法识别视频格式")
                print("❌ FFmpeg无法识别视频格式")
        else:
            result['issues'].append("未找到FFmpeg")
            print("❌ 未找到FFmpeg")
        
        # 5. 使用MoviePy检查
        moviepy_result = self._check_with_moviepy(video_path)
        result['moviepy_loadable'] = moviepy_result['success']
        
        if moviepy_result['success']:
            print("✅ MoviePy可以加载视频")
            print(f"   加载耗时: {moviepy_result['load_time']:.2f}秒")
        else:
            result['issues'].append(f"MoviePy加载失败: {moviepy_result['error']}")
            print(f"❌ MoviePy加载失败: {moviepy_result['error']}")
            
            if moviepy_result['load_time'] > 60:
                result['recommendations'].append("视频加载时间过长，建议优化视频格式")
        
        return result
    
    def _check_with_ffmpeg(self, video_path):
        """使用FFmpeg检查视频"""
        try:
            ffprobe_path = self.ffmpeg_path.replace('ffmpeg', 'ffprobe')
            cmd = [
                ffprobe_path,
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                video_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                info = json.loads(result.stdout)
                
                # 提取视频流信息
                video_streams = [s for s in info.get('streams', []) if s.get('codec_type') == 'video']
                if video_streams:
                    video_stream = video_streams[0]
                    duration = float(info.get('format', {}).get('duration', 0))
                    
                    return {
                        'duration': duration,
                        'width': video_stream.get('width', 0),
                        'height': video_stream.get('height', 0),
                        'fps': eval(video_stream.get('r_frame_rate', '0/1')) if video_stream.get('r_frame_rate') else 0,
                        'codec': video_stream.get('codec_name', 'unknown')
                    }
            
            return None
            
        except Exception as e:
            print(f"FFmpeg检查失败: {str(e)}")
            return None
    
    def _check_with_moviepy(self, video_path):
        """使用MoviePy检查视频"""
        start_time = time.time()
        try:
            from moviepy.editor import VideoFileClip
            
            # 尝试加载视频
            clip = VideoFileClip(video_path, audio=False, verbose=False)
            
            # 验证基本属性
            if clip and hasattr(clip, 'duration') and clip.duration > 0:
                load_time = time.time() - start_time
                clip.close()
                return {'success': True, 'load_time': load_time, 'error': None}
            else:
                if clip:
                    clip.close()
                return {'success': False, 'load_time': time.time() - start_time, 'error': '视频无效或时长为0'}
                
        except Exception as e:
            load_time = time.time() - start_time
            return {'success': False, 'load_time': load_time, 'error': str(e)}
    
    def generate_recommendations(self, result):
        """生成修复建议"""
        print("\n💡 修复建议:")
        print("=" * 60)
        
        if not result['issues']:
            print("✅ 视频文件没有发现问题")
            return
        
        for issue in result['issues']:
            print(f"❌ 问题: {issue}")
        
        print("\n🔧 建议解决方案:")
        
        if result['recommendations']:
            for i, rec in enumerate(result['recommendations'], 1):
                print(f"{i}. {rec}")
        
        # 通用建议
        if not result['moviepy_loadable']:
            print(f"{len(result['recommendations']) + 1}. 尝试使用视频转换工具转换为标准MP4格式")
            print(f"{len(result['recommendations']) + 2}. 使用FFmpeg命令转换: ffmpeg -i input.mp4 -c:v libx264 -c:a aac output.mp4")
        
        if result['file_size'] > 500 * 1024 * 1024:  # 500MB
            print(f"{len(result['recommendations']) + 3}. 考虑压缩视频以减少文件大小")

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法: python video_compatibility_checker.py <视频文件路径>")
        print("示例: python video_compatibility_checker.py D:/25125/Videos/www333.mp4")
        sys.exit(1)
    
    video_path = sys.argv[1]
    
    print("🎬 视频兼容性检查工具")
    print("=" * 80)
    
    checker = VideoCompatibilityChecker()
    result = checker.check_video_file(video_path)
    checker.generate_recommendations(result)
    
    print("\n" + "=" * 80)
    if result['moviepy_loadable']:
        print("🎉 视频文件兼容性良好，应该可以正常加载")
    else:
        print("⚠️ 视频文件存在兼容性问题，需要处理后才能使用")

if __name__ == "__main__":
    main()
