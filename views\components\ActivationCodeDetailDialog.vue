<template>
  <el-dialog
    title="激活码详情"
    :visible.sync="dialogVisible"
    width="700px"
    :close-on-click-modal="false"
    @open="loadData"
  >
    <div v-loading="loading" class="detail-content">
      <div v-if="codeInfo" class="info-sections">
        <!-- 基本信息 -->
        <el-card class="info-card" shadow="never">
          <div slot="header" class="card-header">
            <span>基本信息</span>
            <el-tag
              :type="getStatusTagType(codeInfo.status)"
              size="small"
            >
              {{ getStatusText(codeInfo.status) }}
            </el-tag>
          </div>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>激活码：</label>
                <div class="value">
                  <span class="code-text">{{ codeInfo.activationCode }}</span>
                  <el-button
                    type="text"
                    icon="el-icon-copy-document"
                    @click="copyToClipboard(codeInfo.activationCode)"
                  >
                    复制
                  </el-button>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>用户ID：</label>
                <div class="value">{{ codeInfo.userId || '未设置' }}</div>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>最大激活次数：</label>
                <div class="value">{{ codeInfo.maxActivations }}</div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>当前激活次数：</label>
                <div class="value">
                  <span :class="{ 'text-warning': codeInfo.currentActivations >= codeInfo.maxActivations }">
                    {{ codeInfo.currentActivations }}
                  </span>
                </div>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>创建时间：</label>
                <div class="value">{{ formatDateTime(codeInfo.createTime) }}</div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>过期时间：</label>
                <div class="value">
                  <span v-if="codeInfo.expireTime" :class="{ 'text-danger': isExpired(codeInfo.expireTime) }">
                    {{ formatDateTime(codeInfo.expireTime) }}
                  </span>
                  <span v-else class="text-muted">永不过期</span>
                </div>
              </div>
            </el-col>
          </el-row>
          
          <div v-if="codeInfo.remark" class="info-item">
            <label>备注：</label>
            <div class="value">{{ codeInfo.remark }}</div>
          </div>
        </el-card>
        
        <!-- 激活信息 -->
        <el-card v-if="codeInfo.status === 1" class="info-card" shadow="never">
          <div slot="header" class="card-header">
            <span>激活信息</span>
          </div>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>绑定机器码：</label>
                <div class="value">
                  <span class="code-text">{{ codeInfo.machineCode || '未绑定' }}</span>
                  <el-button
                    v-if="codeInfo.machineCode"
                    type="text"
                    icon="el-icon-copy-document"
                    @click="copyToClipboard(codeInfo.machineCode)"
                  >
                    复制
                  </el-button>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>激活时间：</label>
                <div class="value">{{ formatDateTime(codeInfo.activationTime) }}</div>
              </div>
            </el-col>
          </el-row>
          
          <div class="machine-info-section">
            <el-button
              type="text"
              icon="el-icon-monitor"
              @click="loadMachineInfo"
              :loading="machineLoading"
            >
              查看设备详细信息
            </el-button>
          </div>
        </el-card>
        
        <!-- 设备详细信息 -->
        <el-card v-if="machineInfo" class="info-card" shadow="never">
          <div slot="header" class="card-header">
            <span>设备详细信息</span>
          </div>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>计算机名称：</label>
                <div class="value">{{ machineInfo.computerName || '未知' }}</div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>操作系统：</label>
                <div class="value">{{ machineInfo.operatingSystem || '未知' }}</div>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>处理器：</label>
                <div class="value">{{ machineInfo.processor || '未知' }}</div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>MAC地址：</label>
                <div class="value">{{ machineInfo.macAddress || '未知' }}</div>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>首次激活：</label>
                <div class="value">{{ formatDateTime(machineInfo.firstActivationTime) }}</div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>最后激活：</label>
                <div class="value">{{ formatDateTime(machineInfo.lastActivationTime) }}</div>
              </div>
            </el-col>
          </el-row>
          
          <div class="info-item">
            <label>激活次数：</label>
            <div class="value">{{ machineInfo.activationCount || 0 }}</div>
          </div>
        </el-card>
        
        <!-- 状态提示 -->
        <div v-if="getStatusWarning(codeInfo)" class="status-warning">
          <el-alert
            :title="getStatusWarning(codeInfo)"
            :type="getWarningType(codeInfo)"
            show-icon
            :closable="false"
          />
        </div>
      </div>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import activationApi from '@/api/activation'

export default {
  name: 'ActivationCodeDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    activationCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      machineLoading: false,
      codeInfo: null,
      machineInfo: null
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (!val) {
        this.resetData()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    // 加载数据
    async loadData() {
      if (!this.activationCode) return
      
      this.loading = true
      try {
        const response = await activationApi.getActivationCodeInfo(this.activationCode)
        if (response.success) {
          this.codeInfo = response.activationCode
        } else {
          this.$message.error(response.message || '获取激活码信息失败')
        }
      } catch (error) {
        console.error('加载激活码信息失败:', error)
        this.$message.error('获取激活码信息失败')
      } finally {
        this.loading = false
      }
    },
    
    // 加载设备信息
    async loadMachineInfo() {
      if (!this.codeInfo || !this.codeInfo.machineCode) return
      
      this.machineLoading = true
      try {
        const response = await activationApi.checkMachineActivation(this.codeInfo.machineCode)
        if (response.success) {
          this.machineInfo = response.machineInfo
        } else {
          this.$message.error(response.message || '获取设备信息失败')
        }
      } catch (error) {
        console.error('加载设备信息失败:', error)
        this.$message.error('获取设备信息失败')
      } finally {
        this.machineLoading = false
      }
    },
    
    // 重置数据
    resetData() {
      this.codeInfo = null
      this.machineInfo = null
    },
    
    // 复制到剪贴板
    async copyToClipboard(text) {
      try {
        await navigator.clipboard.writeText(text)
        this.$message.success('已复制到剪贴板')
      } catch (error) {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success('已复制到剪贴板')
      }
    },
    
    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        0: 'info',     // 未激活
        1: 'success',  // 已激活
        2: 'danger'    // 已禁用
      }
      return typeMap[status] || 'info'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        0: '未激活',
        1: '已激活',
        2: '已禁用'
      }
      return textMap[status] || '未知'
    },
    
    // 获取状态警告
    getStatusWarning(codeInfo) {
      if (!codeInfo) return null
      
      if (codeInfo.status === 2) {
        return '此激活码已被禁用，无法使用'
      }
      
      if (this.isExpired(codeInfo.expireTime)) {
        return '此激活码已过期，无法使用'
      }
      
      if (codeInfo.currentActivations >= codeInfo.maxActivations) {
        return '此激活码已达到最大激活次数限制'
      }
      
      return null
    },
    
    // 获取警告类型
    getWarningType(codeInfo) {
      if (!codeInfo) return 'info'
      
      if (codeInfo.status === 2 || this.isExpired(codeInfo.expireTime)) {
        return 'error'
      }
      
      if (codeInfo.currentActivations >= codeInfo.maxActivations) {
        return 'warning'
      }
      
      return 'info'
    },
    
    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      return new Date(dateTime).toLocaleString('zh-CN')
    },
    
    // 检查是否过期
    isExpired(expireTime) {
      if (!expireTime) return false
      return new Date(expireTime) < new Date()
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-content {
  .info-sections {
    .info-card {
      margin-bottom: 20px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: bold;
      }
    }
    
    .info-item {
      margin-bottom: 15px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      label {
        display: block;
        font-weight: bold;
        color: #606266;
        margin-bottom: 5px;
        font-size: 14px;
      }
      
      .value {
        color: #303133;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 10px;
        
        .code-text {
          font-family: 'Courier New', monospace;
          background-color: #F5F7FA;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 13px;
        }
      }
    }
    
    .machine-info-section {
      margin-top: 15px;
      padding-top: 15px;
      border-top: 1px solid #EBEEF5;
    }
    
    .status-warning {
      margin-top: 20px;
    }
  }
  
  .text-muted {
    color: #C0C4CC;
  }
  
  .text-danger {
    color: #F56C6C;
  }
  
  .text-warning {
    color: #E6A23C;
  }
}
</style>
