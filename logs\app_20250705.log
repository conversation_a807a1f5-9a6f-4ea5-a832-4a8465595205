2025-07-05 13:43:30 [INFO] ✅ 这是一条信息日志
2025-07-05 13:43:30 [WARNING] ⚠️ 这是一条警告日志
2025-07-05 13:43:30 [ERROR] ❌ 这是一条错误日志
2025-07-05 13:43:30 [DEBUG] 🔍 这是一条调试日志
2025-07-05 13:43:30 [INFO] 📝 这是safe_print输出
2025-07-05 13:43:30 [ERROR] 捕获到测试异常: 测试异常
Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\test_logger_system.py", line 43, in test_logger_functions
    raise ValueError("测试异常")
ValueError: 测试异常

2025-07-05 13:43:30 [INFO] 🧪 这是GUI回调测试消息
2025-07-05 13:44:07 [INFO] ✅ 这是一条信息日志
2025-07-05 13:44:07 [WARNING] ⚠️ 这是一条警告日志
2025-07-05 13:44:07 [ERROR] ❌ 这是一条错误日志
2025-07-05 13:44:07 [DEBUG] 🔍 这是一条调试日志
2025-07-05 13:44:07 [INFO] 📝 这是safe_print输出
2025-07-05 13:44:07 [ERROR] 捕获到测试异常: 测试异常
Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\test_logger_system.py", line 43, in test_logger_functions
    raise ValueError("测试异常")
ValueError: 测试异常

2025-07-05 13:44:07 [INFO] 🧪 这是GUI回调测试消息
2025-07-05 13:46:21 [INFO] ✅ 激活验证模块加载成功
2025-07-05 13:46:30 [INFO] ✅ 激活验证模块加载成功
2025-07-05 13:46:42 [INFO] ✅ 激活验证模块加载成功
2025-07-05 13:46:46 [WARNING] ⚠️ 应用程序已在运行，激活现有窗口
2025-07-05 13:47:15 [INFO] ✅ 激活验证模块加载成功
2025-07-05 13:47:15 [INFO] ✅ 单实例应用初始化成功
2025-07-05 13:47:15 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 13:47:16 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-05 13:47:16 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-05 13:47:16 [INFO] ✅ 已更新最后验证时间
2025-07-05 13:47:16 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-05 13:47:16 [INFO] ✅ 本地激活配置验证通过
2025-07-05 13:47:16 [INFO] ✅ 本地激活验证通过
2025-07-05 13:47:16 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 13:47:40 [INFO] ✅ 日志系统测试成功
2025-07-05 13:47:40 [INFO] ✅ 激活验证模块加载成功
2025-07-05 13:48:32 [INFO] ✅ 日志系统测试成功
2025-07-05 13:48:32 [INFO] ✅ 激活验证模块加载成功
2025-07-05 13:48:33 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-07-05 13:49:32 [INFO] ✅ 激活验证模块加载成功
2025-07-05 13:49:33 [WARNING] ⚠️ 应用程序已在运行，激活现有窗口
2025-07-05 13:49:33 [INFO] 📢 收到激活信号，显示主窗口
2025-07-05 13:49:40 [INFO] 📦 窗口已最小化到任务栏
2025-07-05 13:49:40 [INFO] 📦 程序已最小化到任务栏
2025-07-05 13:49:52 [INFO] ✅ 激活验证模块加载成功
2025-07-05 13:49:53 [INFO] ✅ 单实例应用初始化成功
2025-07-05 13:49:53 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 13:49:54 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-05 13:49:54 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-05 13:49:54 [INFO] ✅ 已更新最后验证时间
2025-07-05 13:49:54 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-05 13:49:54 [INFO] ✅ 本地激活配置验证通过
2025-07-05 13:49:54 [INFO] ✅ 本地激活验证通过
2025-07-05 13:49:54 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 13:50:03 [INFO] 📦 窗口已最小化到任务栏
2025-07-05 13:50:03 [INFO] 📋 程序已最小化到任务栏
2025-07-05 13:50:10 [INFO] 📋 程序已最小化到任务栏
2025-07-05 13:50:12 [INFO] 📦 窗口已最小化到任务栏
2025-07-05 14:03:37 [INFO] 📜 创建启动脚本...
2025-07-05 14:03:37 [INFO]   创建Shell启动脚本: 启动程序.sh
2025-07-05 14:04:12 [INFO] 📜 创建启动脚本...
2025-07-05 14:04:12 [INFO]   创建Shell启动脚本: 启动程序.sh
2025-07-05 14:09:16 [INFO] ✅ 激活验证模块加载成功
2025-07-05 14:09:17 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-07-05 14:09:47 [INFO] ✅ 激活验证模块加载成功
2025-07-05 14:09:48 [INFO] ✅ 单实例应用初始化成功
2025-07-05 14:09:48 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 14:09:49 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-05 14:09:49 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-05 14:09:49 [INFO] ✅ 已更新最后验证时间
2025-07-05 14:09:49 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-05 14:09:49 [INFO] ✅ 本地激活配置验证通过
2025-07-05 14:09:49 [INFO] ✅ 本地激活验证通过
2025-07-05 14:09:49 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 14:17:06 [INFO] 📜 创建启动脚本...
2025-07-05 14:17:06 [INFO]   创建Shell启动脚本: 启动程序.sh
2025-07-05 14:51:56 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-07-05 14:51:58 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-05 14:51:58 [INFO] 未检测到CUDA设备
2025-07-05 14:51:58 [INFO] 创建临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-05 14:51:58 [INFO] VideoProcessor初始化:
2025-07-05 14:51:58 [INFO]   CPU线程数: 8
2025-07-05 14:51:58 [INFO]   GPU支持: 否
2025-07-05 14:51:58 [INFO]   GPU加速: 禁用
2025-07-05 14:51:58 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-05 14:51:58 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-05 14:51:58 [INFO] 未检测到CUDA设备
2025-07-05 14:51:58 [INFO] VideoProcessor初始化:
2025-07-05 14:51:58 [INFO]   CPU线程数: 8
2025-07-05 14:51:58 [INFO]   GPU支持: 否
2025-07-05 14:51:58 [INFO]   GPU加速: 禁用
2025-07-05 14:51:58 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-05 14:51:58 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-05 14:51:58 [INFO] 未检测到CUDA设备
2025-07-05 14:51:58 [INFO] VideoProcessor初始化:
2025-07-05 14:51:58 [INFO]   CPU线程数: 8
2025-07-05 14:51:58 [INFO]   GPU支持: 否
2025-07-05 14:51:58 [INFO]   GPU加速: 禁用
2025-07-05 14:51:58 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-05 14:51:58 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-05 14:51:59 [INFO] 未检测到CUDA设备
2025-07-05 14:51:59 [INFO] VideoProcessor初始化:
2025-07-05 14:51:59 [INFO]   CPU线程数: 8
2025-07-05 14:51:59 [INFO]   GPU支持: 否
2025-07-05 14:51:59 [INFO]   GPU加速: 禁用
2025-07-05 14:51:59 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-05 14:51:59 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\cleanup_test_4a76b8db_1751698319092_f3183289_0.tmp
2025-07-05 14:51:59 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\cleanup_test_4a76b8db_1751698319095_f5e0fdda_1.tmp
2025-07-05 14:51:59 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\cleanup_test_4a76b8db_1751698319096_e8280308_2.tmp
2025-07-05 14:51:59 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-05 14:51:59 [INFO] 未检测到CUDA设备
2025-07-05 14:51:59 [INFO] VideoProcessor初始化:
2025-07-05 14:51:59 [INFO]   CPU线程数: 8
2025-07-05 14:51:59 [INFO]   GPU支持: 否
2025-07-05 14:51:59 [INFO]   GPU加速: 禁用
2025-07-05 14:51:59 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-05 14:56:37 [INFO] ✅ 激活验证模块加载成功
2025-07-05 14:56:37 [WARNING] ⚠️ 应用程序已在运行，激活现有窗口
2025-07-05 14:56:57 [INFO] ✅ 激活验证模块加载成功
2025-07-05 14:56:58 [WARNING] ⚠️ 应用程序已在运行，激活现有窗口
2025-07-05 14:57:27 [INFO] ✅ 激活验证模块加载成功
2025-07-05 14:57:28 [WARNING] ⚠️ 应用程序已在运行，激活现有窗口
2025-07-05 14:59:05 [INFO] ✅ 激活验证模块加载成功
2025-07-05 14:59:06 [INFO] ✅ 单实例应用初始化成功
2025-07-05 14:59:06 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 14:59:07 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-05 14:59:07 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-05 14:59:07 [INFO] ✅ 已更新最后验证时间
2025-07-05 14:59:07 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-05 14:59:07 [INFO] ✅ 本地激活配置验证通过
2025-07-05 14:59:07 [INFO] ✅ 本地激活验证通过
2025-07-05 14:59:07 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 14:59:34 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-07-05 14:59:35 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-05 14:59:35 [INFO] 未检测到CUDA设备
2025-07-05 14:59:35 [INFO] VideoProcessor初始化:
2025-07-05 14:59:35 [INFO]   CPU线程数: 8
2025-07-05 14:59:35 [INFO]   GPU支持: 否
2025-07-05 14:59:35 [INFO]   GPU加速: 禁用
2025-07-05 14:59:35 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-05 14:59:45 [INFO] 视频写入成功: D:/25125/Videos\555588888888_split\555588888888_scene_001.mp4
2025-07-05 14:59:45 [WARNING] 检测到subprocess None错误，重试 1/3
2025-07-05 14:59:47 [WARNING] 检测到subprocess None错误，重试 2/3
2025-07-05 14:59:48 [WARNING] 检测到subprocess None错误，重试 3/3
2025-07-05 14:59:49 [ERROR] 场景 2 写入失败，跳过
2025-07-05 14:59:49 [WARNING] 检测到subprocess None错误，重试 1/3
2025-07-05 14:59:50 [WARNING] 检测到subprocess None错误，重试 2/3
2025-07-05 14:59:51 [WARNING] 检测到subprocess None错误，重试 3/3
2025-07-05 14:59:52 [ERROR] 场景 3 写入失败，跳过
2025-07-05 14:59:53 [WARNING] 检测到subprocess None错误，重试 1/3
2025-07-05 14:59:54 [WARNING] 检测到subprocess None错误，重试 2/3
2025-07-05 14:59:55 [WARNING] 检测到subprocess None错误，重试 3/3
2025-07-05 14:59:56 [ERROR] 场景 4 写入失败，跳过
2025-07-05 14:59:56 [WARNING] 检测到subprocess None错误，重试 1/3
2025-07-05 14:59:57 [WARNING] 检测到subprocess None错误，重试 2/3
2025-07-05 14:59:58 [WARNING] 检测到subprocess None错误，重试 3/3
2025-07-05 14:59:59 [ERROR] 场景 5 写入失败，跳过
2025-07-05 15:00:00 [WARNING] 检测到subprocess None错误，重试 1/3
2025-07-05 15:00:01 [WARNING] 检测到subprocess None错误，重试 2/3
2025-07-05 15:00:02 [WARNING] 检测到subprocess None错误，重试 3/3
2025-07-05 15:00:03 [ERROR] 场景 6 写入失败，跳过
2025-07-05 15:00:03 [WARNING] 检测到subprocess None错误，重试 1/3
2025-07-05 15:00:04 [WARNING] 检测到subprocess None错误，重试 2/3
2025-07-05 15:00:05 [WARNING] 检测到subprocess None错误，重试 3/3
2025-07-05 15:00:06 [ERROR] 场景 7 写入失败，跳过
2025-07-05 15:00:07 [WARNING] 检测到subprocess None错误，重试 1/3
2025-07-05 15:00:08 [WARNING] 检测到subprocess None错误，重试 2/3
2025-07-05 15:00:09 [WARNING] 检测到subprocess None错误，重试 3/3
2025-07-05 15:00:10 [ERROR] 场景 8 写入失败，跳过
2025-07-05 15:00:10 [WARNING] 检测到subprocess None错误，重试 1/3
2025-07-05 15:00:11 [WARNING] 检测到subprocess None错误，重试 2/3
2025-07-05 15:00:13 [WARNING] 检测到subprocess None错误，重试 3/3
2025-07-05 15:00:14 [ERROR] 场景 9 写入失败，跳过
2025-07-05 15:00:14 [WARNING] 检测到subprocess None错误，重试 1/3
2025-07-05 15:00:15 [WARNING] 检测到subprocess None错误，重试 2/3
2025-07-05 15:00:15 [INFO] 📦 窗口已最小化到任务栏
2025-07-05 15:00:15 [INFO] 📦 窗口已最小化到任务栏
2025-07-05 15:00:16 [WARNING] 检测到subprocess None错误，重试 3/3
2025-07-05 15:00:17 [ERROR] 场景 10 写入失败，跳过
2025-07-05 15:00:18 [WARNING] 检测到subprocess None错误，重试 1/3
2025-07-05 15:00:19 [WARNING] 检测到subprocess None错误，重试 2/3
2025-07-05 15:00:20 [WARNING] 检测到subprocess None错误，重试 3/3
2025-07-05 15:00:21 [ERROR] 场景 11 写入失败，跳过
2025-07-05 15:00:21 [WARNING] 检测到subprocess None错误，重试 1/3
2025-07-05 15:00:22 [WARNING] 检测到subprocess None错误，重试 2/3
2025-07-05 15:00:24 [WARNING] 检测到subprocess None错误，重试 3/3
2025-07-05 15:00:25 [ERROR] 场景 12 写入失败，跳过
2025-07-05 15:00:28 [INFO] 视频写入成功: D:/25125/Videos\555588888888_split\555588888888_scene_013.mp4
2025-07-05 15:00:28 [WARNING] 检测到subprocess None错误，重试 1/3
2025-07-05 15:00:30 [WARNING] 检测到subprocess None错误，重试 2/3
2025-07-05 15:00:31 [WARNING] 检测到subprocess None错误，重试 3/3
2025-07-05 15:00:32 [ERROR] 场景 14 写入失败，跳过
2025-07-05 15:00:32 [WARNING] 检测到subprocess None错误，重试 1/3
2025-07-05 15:00:33 [WARNING] 检测到subprocess None错误，重试 2/3
2025-07-05 15:00:34 [WARNING] 检测到subprocess None错误，重试 3/3
2025-07-05 15:00:36 [ERROR] 场景 15 写入失败，跳过
2025-07-05 15:00:36 [WARNING] 检测到subprocess None错误，重试 1/3
2025-07-05 15:00:37 [WARNING] 检测到subprocess None错误，重试 2/3
2025-07-05 15:00:38 [WARNING] 检测到subprocess None错误，重试 3/3
2025-07-05 15:00:39 [ERROR] 场景 16 写入失败，跳过
2025-07-05 15:00:40 [WARNING] 检测到subprocess None错误，重试 1/3
2025-07-05 15:00:41 [WARNING] 检测到subprocess None错误，重试 2/3
2025-07-05 15:00:42 [WARNING] 检测到subprocess None错误，重试 3/3
2025-07-05 15:00:43 [ERROR] 场景 17 写入失败，跳过
2025-07-05 15:00:43 [WARNING] 检测到subprocess None错误，重试 1/3
2025-07-05 15:00:44 [WARNING] 检测到subprocess None错误，重试 2/3
2025-07-05 15:00:45 [WARNING] 检测到subprocess None错误，重试 3/3
2025-07-05 15:00:46 [INFO] 📦 窗口已最小化到任务栏
2025-07-05 15:00:46 [ERROR] 场景 18 写入失败，跳过
2025-07-05 15:00:47 [WARNING] 检测到subprocess None错误，重试 1/3
2025-07-05 15:00:48 [WARNING] 检测到subprocess None错误，重试 2/3
2025-07-05 15:00:48 [INFO] 📦 窗口已最小化到任务栏
2025-07-05 15:00:49 [WARNING] 检测到subprocess None错误，重试 3/3
2025-07-05 15:00:50 [ERROR] 场景 19 写入失败，跳过
2025-07-05 15:01:20 [INFO] 📦 窗口已最小化到任务栏
2025-07-05 15:02:55 [INFO] 📦 窗口已最小化到任务栏
2025-07-05 15:04:37 [INFO] ✅ 激活验证模块加载成功
2025-07-05 15:04:38 [INFO] ✅ 单实例应用初始化成功
2025-07-05 15:04:38 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 15:04:39 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-05 15:04:39 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-05 15:04:39 [INFO] ✅ 已更新最后验证时间
2025-07-05 15:04:39 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-05 15:04:39 [INFO] ✅ 本地激活配置验证通过
2025-07-05 15:04:39 [INFO] ✅ 本地激活验证通过
2025-07-05 15:04:39 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 15:05:00 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-07-05 15:05:00 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-05 15:05:01 [INFO] 未检测到CUDA设备
2025-07-05 15:05:01 [INFO] VideoProcessor初始化:
2025-07-05 15:05:01 [INFO]   CPU线程数: 8
2025-07-05 15:05:01 [INFO]   GPU支持: 否
2025-07-05 15:05:01 [INFO]   GPU加速: 禁用
2025-07-05 15:05:01 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-05 15:05:12 [INFO] 视频写入成功: D:/25125/Videos\555588888888_split\555588888888_scene_001.mp4
2025-07-05 15:05:12 [INFO] ✅ 场景 1 处理成功
2025-07-05 15:05:13 [WARNING] 检测到subprocess None错误，重试 1/5
2025-07-05 15:05:17 [WARNING] 检测到subprocess None错误，重试 2/5
2025-07-05 15:05:21 [WARNING] 检测到subprocess None错误，重试 3/5
2025-07-05 15:05:27 [WARNING] 检测到subprocess None错误，重试 4/5
2025-07-05 15:05:34 [WARNING] 检测到subprocess None错误，重试 5/5
2025-07-05 15:05:41 [ERROR] ❌ 场景 2 写入失败，跳过 (连续失败: 1)
2025-07-05 15:05:42 [WARNING] 检测到subprocess None错误，重试 1/5
2025-07-05 15:05:46 [WARNING] 检测到subprocess None错误，重试 2/5
2025-07-05 15:05:51 [WARNING] 检测到subprocess None错误，重试 3/5
2025-07-05 15:05:56 [WARNING] 检测到subprocess None错误，重试 4/5
2025-07-05 15:06:03 [WARNING] 检测到subprocess None错误，重试 5/5
2025-07-05 15:06:11 [ERROR] ❌ 场景 3 写入失败，跳过 (连续失败: 2)
2025-07-05 15:06:11 [WARNING] 检测到subprocess None错误，重试 1/5
2025-07-05 15:06:15 [WARNING] 检测到subprocess None错误，重试 2/5
2025-07-05 15:06:19 [WARNING] 检测到subprocess None错误，重试 3/5
2025-07-05 15:06:25 [WARNING] 检测到subprocess None错误，重试 4/5
2025-07-05 15:06:47 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-07-05 15:06:49 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-05 15:06:49 [INFO] 未检测到CUDA设备
2025-07-05 15:06:49 [INFO] VideoProcessor初始化:
2025-07-05 15:06:49 [INFO]   CPU线程数: 8
2025-07-05 15:06:49 [INFO]   GPU支持: 否
2025-07-05 15:06:49 [INFO]   GPU加速: 禁用
2025-07-05 15:06:49 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-05 15:06:49 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-05 15:06:49 [INFO] 未检测到CUDA设备
2025-07-05 15:06:49 [INFO] VideoProcessor初始化:
2025-07-05 15:06:49 [INFO]   CPU线程数: 8
2025-07-05 15:06:49 [INFO]   GPU支持: 否
2025-07-05 15:06:49 [INFO]   GPU加速: 禁用
2025-07-05 15:06:49 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-05 15:06:49 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\test_0_d716ae28_1751699209379_f7c9c7cd.tmp
2025-07-05 15:06:49 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\test_1_d716ae28_1751699209380_2fee04cf.tmp
2025-07-05 15:06:49 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\test_2_d716ae28_1751699209381_4f77889d.tmp
2025-07-05 15:06:49 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\test_3_d716ae28_1751699209381_cffb17cf.tmp
2025-07-05 15:06:49 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\test_4_d716ae28_1751699209382_77044ec5.tmp
2025-07-05 15:06:49 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-05 15:06:49 [INFO] 未检测到CUDA设备
2025-07-05 15:06:49 [INFO] VideoProcessor初始化:
2025-07-05 15:06:49 [INFO]   CPU线程数: 8
2025-07-05 15:06:49 [INFO]   GPU支持: 否
2025-07-05 15:06:49 [INFO]   GPU加速: 禁用
2025-07-05 15:06:49 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-05 15:06:50 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-05 15:06:50 [INFO] 未检测到CUDA设备
2025-07-05 15:06:50 [INFO] VideoProcessor初始化:
2025-07-05 15:06:50 [INFO]   CPU线程数: 8
2025-07-05 15:06:50 [INFO]   GPU支持: 否
2025-07-05 15:06:50 [INFO]   GPU加速: 禁用
2025-07-05 15:06:50 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-05 15:06:50 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-05 15:06:50 [INFO] 未检测到CUDA设备
2025-07-05 15:06:50 [INFO] VideoProcessor初始化:
2025-07-05 15:06:50 [INFO]   CPU线程数: 8
2025-07-05 15:06:50 [INFO]   GPU支持: 否
2025-07-05 15:06:50 [INFO]   GPU加速: 禁用
2025-07-05 15:06:50 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-05 15:06:50 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-05 15:06:50 [INFO] 未检测到CUDA设备
2025-07-05 15:06:50 [INFO] VideoProcessor初始化:
2025-07-05 15:06:50 [INFO]   CPU线程数: 8
2025-07-05 15:06:50 [INFO]   GPU支持: 否
2025-07-05 15:06:50 [INFO]   GPU加速: 禁用
2025-07-05 15:06:50 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-05 15:06:50 [WARNING] 检测到subprocess None错误，重试 1/5
2025-07-05 15:06:54 [WARNING] 检测到subprocess None错误，重试 2/5
2025-07-05 15:06:58 [WARNING] 检测到subprocess None错误，重试 3/5
2025-07-05 15:07:04 [WARNING] 检测到subprocess None错误，重试 4/5
2025-07-05 15:07:10 [WARNING] 检测到subprocess None错误，重试 5/5
2025-07-05 15:14:43 [INFO] 📜 创建启动脚本...
2025-07-05 15:14:43 [INFO]   创建Shell启动脚本: 启动程序.sh
2025-07-05 15:38:15 [INFO] ✅ 激活验证模块加载成功
2025-07-05 15:38:17 [WARNING] ⚠️ 应用程序已在运行，激活现有窗口
2025-07-05 15:38:40 [INFO] ✅ 激活验证模块加载成功
2025-07-05 15:38:41 [INFO] ✅ 单实例应用初始化成功
2025-07-05 15:38:41 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 15:38:42 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-05 15:38:42 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-05 15:38:42 [INFO] ✅ 已更新最后验证时间
2025-07-05 15:38:42 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-05 15:38:42 [INFO] ✅ 本地激活配置验证通过
2025-07-05 15:38:42 [INFO] ✅ 本地激活验证通过
2025-07-05 15:38:42 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 15:39:39 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-07-05 15:39:39 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-05 15:39:39 [INFO] 未检测到CUDA设备
2025-07-05 15:39:39 [INFO] VideoProcessor初始化:
2025-07-05 15:39:39 [INFO]   CPU线程数: 8
2025-07-05 15:39:39 [INFO]   GPU支持: 否
2025-07-05 15:39:39 [INFO]   GPU加速: 禁用
2025-07-05 15:39:39 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-05 15:40:09 [INFO] 视频写入成功: D:/25125/Videos\高校班费管理系统_split\高校班费管理系统_scene_001.mp4
2025-07-05 15:40:09 [INFO] ✅ 场景 1 处理成功
2025-07-05 15:40:10 [INFO] 视频写入成功: D:/25125/Videos\高校班费管理系统_split\高校班费管理系统_scene_002.mp4
2025-07-05 15:40:10 [INFO] ✅ 场景 2 处理成功
2025-07-05 15:40:11 [WARNING] 检测到subprocess None错误，重试 1/5
2025-07-05 15:40:11 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_2d74b696_1751699113105_ede46e1e.m4a
2025-07-05 15:40:11 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_2d74b696_1751699142423_dd464bae.m4a
2025-07-05 15:40:11 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_2d74b696_1751699171399_a7ef6cd1.m4a
2025-07-05 15:40:11 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_cb38e76a_1751698785751_57c0fd75.m4a
2025-07-05 15:40:11 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_cb38e76a_1751698789552_e6d0e900.m4a
2025-07-05 15:40:11 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_cb38e76a_1751698793046_a1a6e120.m4a
2025-07-05 15:40:11 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_cb38e76a_1751698796551_188430b8.m4a
2025-07-05 15:40:11 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_cb38e76a_1751698800087_6320af0a.m4a
2025-07-05 15:40:11 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_cb38e76a_1751698803554_e97cb69a.m4a
2025-07-05 15:40:11 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_cb38e76a_1751698807140_c1629ce8.m4a
2025-07-05 15:40:11 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_cb38e76a_1751698810750_d1513b44.m4a
2025-07-05 15:40:11 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_cb38e76a_1751698814371_fe9138e6.m4a
2025-07-05 15:40:11 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_cb38e76a_1751698818082_aa332d32.m4a
2025-07-05 15:40:11 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_cb38e76a_1751698821805_649e30ba.m4a
2025-07-05 15:40:11 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_cb38e76a_1751698828472_0e5cfba9.m4a
2025-07-05 15:40:11 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_cb38e76a_1751698832761_43c8dd36.m4a
2025-07-05 15:40:11 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_cb38e76a_1751698836351_daffc9e4.m4a
2025-07-05 15:40:11 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_cb38e76a_1751698839995_439278f9.m4a
2025-07-05 15:40:11 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_cb38e76a_1751698843555_784bf0b4.m4a
2025-07-05 15:40:11 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_cb38e76a_1751698847117_3f92ea00.m4a
2025-07-05 15:40:15 [WARNING] 检测到subprocess None错误，重试 2/5
2025-07-05 15:40:15 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_47ab8a54_1751701210990_bb3fdfff.m4a
2025-07-05 15:40:22 [WARNING] 检测到subprocess None错误，重试 3/5
2025-07-05 15:40:22 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_47ab8a54_1751701210990_bb3fdfff.m4a
2025-07-05 15:40:35 [WARNING] 检测到subprocess None错误，重试 4/5
2025-07-05 15:40:35 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_47ab8a54_1751701210990_bb3fdfff.m4a
2025-07-05 15:41:01 [WARNING] 检测到subprocess None错误，重试 5/5
2025-07-05 15:41:01 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_47ab8a54_1751701210990_bb3fdfff.m4a
2025-07-05 15:41:45 [INFO] 📦 窗口已最小化到任务栏
2025-07-05 15:41:45 [INFO] 📦 窗口已最小化到任务栏
2025-07-05 15:41:49 [INFO] 📦 窗口已最小化到任务栏
2025-07-05 15:41:50 [INFO] 尝试使用FFmpeg备用方法写入视频...
2025-07-05 15:41:50 [WARNING] 提取音频失败，将创建无声视频: 'NoneType' object has no attribute 'stdout'
2025-07-05 15:41:51 [INFO] 📦 窗口已最小化到任务栏
2025-07-05 15:41:52 [INFO] 执行FFmpeg命令: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe -i D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\fallback_input_47ab8a54_1751701310253_1e1a1094.mp4 -c copy -y D:/25125/Videos\高校班费管理系统_split\高校班费管理系统_scene_003.mp4
2025-07-05 15:41:52 [INFO] FFmpeg备用方法成功: D:/25125/Videos\高校班费管理系统_split\高校班费管理系统_scene_003.mp4
2025-07-05 15:41:52 [INFO] ✅ 场景 3 处理成功
2025-07-05 15:41:52 [WARNING] 检测到subprocess None错误，重试 1/5
2025-07-05 15:41:52 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_47ab8a54_1751701210990_bb3fdfff.m4a
2025-07-05 15:41:56 [WARNING] 检测到subprocess None错误，重试 2/5
2025-07-05 15:41:56 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_47ab8a54_1751701312542_591d166c.m4a
2025-07-05 15:42:03 [WARNING] 检测到subprocess None错误，重试 3/5
2025-07-05 15:42:04 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_47ab8a54_1751701312542_591d166c.m4a
2025-07-05 15:42:17 [WARNING] 检测到subprocess None错误，重试 4/5
2025-07-05 15:42:17 [DEBUG] 清理临时文件: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\scene_audio_47ab8a54_1751701312542_591d166c.m4a
2025-07-05 15:46:26 [INFO] ✅ 激活验证模块加载成功
2025-07-05 15:46:27 [INFO] ✅ 单实例应用初始化成功
2025-07-05 15:46:27 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 15:46:28 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-05 15:46:28 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-05 15:46:28 [INFO] ✅ 已更新最后验证时间
2025-07-05 15:46:28 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-05 15:46:28 [INFO] ✅ 本地激活配置验证通过
2025-07-05 15:46:28 [INFO] ✅ 本地激活验证通过
2025-07-05 15:46:28 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 15:47:03 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-07-05 15:47:03 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-05 15:47:03 [INFO] 未检测到CUDA设备
2025-07-05 15:47:03 [INFO] VideoProcessor初始化:
2025-07-05 15:47:03 [INFO]   CPU线程数: 8
2025-07-05 15:47:03 [INFO]   GPU支持: 否
2025-07-05 15:47:03 [INFO]   GPU加速: 禁用
2025-07-05 15:47:03 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-05 15:47:20 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-05 15:51:35 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-05 15:51:35 [INFO] 未检测到CUDA设备
2025-07-05 15:51:35 [INFO] VideoProcessor初始化:
2025-07-05 15:51:35 [INFO]   CPU线程数: 8
2025-07-05 15:51:35 [INFO]   GPU支持: 否
2025-07-05 15:51:35 [INFO]   GPU加速: 禁用
2025-07-05 15:51:35 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-05 15:51:35 [INFO] 未检测到CUDA设备
2025-07-05 15:51:35 [INFO] 自动选择 CPU 多线程模式
2025-07-05 15:51:35 [INFO] 性能模式: auto, GPU: False, 线程数: 8, 低内存模式: False
2025-07-05 15:51:35 [INFO] 开始智能混剪 2 个视频...
2025-07-05 15:51:35 [INFO] 混剪程度: extreme
2025-07-05 15:51:35 [INFO] 画面变化检测: 启用
2025-07-05 15:51:35 [INFO] 开始生成第 1 个混剪视频...
2025-07-05 15:51:35 [INFO] 目标时长: 20秒，计划片段数: 3
2025-07-05 15:51:35 [INFO] 混剪程度: extreme (片段时长: 1.5-6.0秒)
2025-07-05 15:51:38 [INFO] 场景检测完成: MJ助手使用演示_video_only.mp4 - 发现 3 个场景变化点
2025-07-05 15:51:39 [INFO] 基于场景变化提取片段: MJ助手使用演示_video_only.mp4 (起始: 20.0s)
2025-07-05 15:51:39 [INFO] 添加片段 1: MJ助手使用演示_video_only.mp4 (3.6秒)
2025-07-05 15:51:41 [INFO] 检测到场景变化: 3.7s (差异: 48.3)
2025-07-05 15:51:41 [INFO] 检测到场景变化: 5.6s (差异: 94.1)
2025-07-05 15:51:42 [INFO] 检测到场景变化: 7.4s (差异: 84.1)
2025-07-05 15:51:42 [INFO] 检测到场景变化: 9.3s (差异: 69.7)
2025-07-05 15:51:43 [INFO] 检测到场景变化: 14.8s (差异: 50.3)
2025-07-05 15:51:44 [INFO] 检测到场景变化: 16.7s (差异: 30.6)
2025-07-05 15:51:44 [INFO] 场景检测完成: 555588888888.mp4 - 发现 6 个场景变化点
2025-07-05 15:51:44 [INFO] 基于场景变化提取片段: 555588888888.mp4 (起始: 3.7s)
2025-07-05 15:51:44 [INFO] 添加片段 2: 555588888888.mp4 (2.2秒)
2025-07-05 15:51:47 [INFO] 场景检测完成: MJ助手使用演示_video_only.mp4 - 发现 3 个场景变化点
2025-07-05 15:51:47 [INFO] 基于场景变化提取片段: MJ助手使用演示_video_only.mp4 (起始: 25.8s)
2025-07-05 15:51:47 [INFO] 添加片段 3: MJ助手使用演示_video_only.mp4 (14.2秒)
2025-07-05 15:51:47 [INFO] 共生成 3 个片段，总时长: 20.0秒
2025-07-05 15:51:47 [INFO] 验证后有效片段数: 3
2025-07-05 15:51:47 [INFO] 添加转场效果: fade
2025-07-05 15:51:48 [INFO] 开始渲染视频: D:/25125/Videos/wttttt\mixed_video_01.mp4
2025-07-05 15:52:06 [INFO] ✅ 第 1 个混剪视频生成完成: D:/25125/Videos/wttttt\mixed_video_01.mp4
2025-07-05 15:52:06 [INFO] 开始生成第 2 个混剪视频...
2025-07-05 15:52:06 [INFO] 目标时长: 20秒，计划片段数: 3
2025-07-05 15:52:06 [INFO] 混剪程度: extreme (片段时长: 1.5-6.0秒)
2025-07-05 15:52:10 [INFO] 场景检测完成: MJ助手使用演示_video_only.mp4 - 发现 3 个场景变化点
2025-07-05 15:52:10 [INFO] 基于场景变化提取片段: MJ助手使用演示_video_only.mp4 (起始: 30.0s)
2025-07-05 15:52:10 [INFO] 添加片段 1: MJ助手使用演示_video_only.mp4 (4.9秒)
2025-07-05 15:52:11 [INFO] 检测到场景变化: 3.7s (差异: 48.3)
2025-07-05 15:52:12 [INFO] 检测到场景变化: 5.6s (差异: 94.1)
2025-07-05 15:52:12 [INFO] 检测到场景变化: 7.4s (差异: 84.1)
2025-07-05 15:52:13 [INFO] 检测到场景变化: 9.3s (差异: 69.7)
2025-07-05 15:52:14 [INFO] 检测到场景变化: 14.8s (差异: 50.3)
2025-07-05 15:52:15 [INFO] 检测到场景变化: 16.7s (差异: 30.6)
2025-07-05 15:52:15 [INFO] 场景检测完成: 555588888888.mp4 - 发现 6 个场景变化点
2025-07-05 15:52:15 [INFO] 基于场景变化提取片段: 555588888888.mp4 (起始: 9.3s)
2025-07-05 15:52:15 [INFO] 添加片段 2: 555588888888.mp4 (5.0秒)
2025-07-05 15:52:18 [INFO] 场景检测完成: MJ助手使用演示_video_only.mp4 - 发现 3 个场景变化点
2025-07-05 15:52:18 [INFO] 基于场景变化提取片段: MJ助手使用演示_video_only.mp4 (起始: 29.8s)
2025-07-05 15:52:18 [INFO] 添加片段 3: MJ助手使用演示_video_only.mp4 (10.2秒)
2025-07-05 15:52:18 [INFO] 共生成 3 个片段，总时长: 20.0秒
2025-07-05 15:52:18 [INFO] 验证后有效片段数: 3
2025-07-05 15:52:18 [INFO] 添加转场效果: fade
2025-07-05 15:52:18 [INFO] 开始渲染视频: D:/25125/Videos/wttttt\mixed_video_02.mp4
2025-07-05 15:52:37 [INFO] ✅ 第 2 个混剪视频生成完成: D:/25125/Videos/wttttt\mixed_video_02.mp4
2025-07-05 15:52:37 [INFO] 开始生成第 3 个混剪视频...
2025-07-05 15:52:37 [INFO] 目标时长: 20秒，计划片段数: 3
2025-07-05 15:52:37 [INFO] 混剪程度: extreme (片段时长: 1.5-6.0秒)
2025-07-05 15:52:38 [INFO] 检测到场景变化: 3.7s (差异: 48.3)
2025-07-05 15:52:38 [INFO] 检测到场景变化: 5.6s (差异: 94.1)
2025-07-05 15:52:39 [INFO] 检测到场景变化: 7.4s (差异: 84.1)
2025-07-05 15:52:39 [INFO] 检测到场景变化: 9.3s (差异: 69.7)
2025-07-05 15:52:41 [INFO] 检测到场景变化: 14.8s (差异: 50.3)
2025-07-05 15:52:41 [INFO] 检测到场景变化: 16.7s (差异: 30.6)
2025-07-05 15:52:41 [INFO] 场景检测完成: 555588888888.mp4 - 发现 6 个场景变化点
2025-07-05 15:52:41 [INFO] 基于场景变化提取片段: 555588888888.mp4 (起始: 16.7s)
2025-07-05 15:52:41 [INFO] 添加片段 1: 555588888888.mp4 (3.2秒)
2025-07-05 15:52:45 [INFO] 场景检测完成: MJ助手使用演示_video_only.mp4 - 发现 3 个场景变化点
2025-07-05 15:52:45 [INFO] 基于场景变化提取片段: MJ助手使用演示_video_only.mp4 (起始: 30.0s)
2025-07-05 15:52:45 [INFO] 添加片段 2: MJ助手使用演示_video_only.mp4 (2.8秒)
2025-07-05 15:52:47 [INFO] 检测到场景变化: 3.7s (差异: 48.3)
2025-07-05 15:52:47 [INFO] 检测到场景变化: 5.6s (差异: 94.1)
2025-07-05 15:52:47 [INFO] 检测到场景变化: 7.4s (差异: 84.1)
2025-07-05 15:52:48 [INFO] 检测到场景变化: 9.3s (差异: 69.7)
2025-07-05 15:52:49 [INFO] 检测到场景变化: 14.8s (差异: 50.3)
2025-07-05 15:52:50 [INFO] 检测到场景变化: 16.7s (差异: 30.6)
2025-07-05 15:52:50 [INFO] 场景检测完成: 555588888888.mp4 - 发现 6 个场景变化点
2025-07-05 15:52:50 [INFO] 基于场景变化提取片段: 555588888888.mp4 (起始: 14.8s)
2025-07-05 15:52:50 [INFO] 添加片段 3: 555588888888.mp4 (14.0秒)
2025-07-05 15:52:50 [INFO] 共生成 3 个片段，总时长: 20.0秒
2025-07-05 15:52:50 [INFO] 验证后有效片段数: 3
2025-07-05 15:52:50 [INFO] 添加转场效果: fade
2025-07-05 15:52:50 [INFO] 开始渲染视频: D:/25125/Videos/wttttt\mixed_video_03.mp4
2025-07-05 15:53:11 [INFO] ✅ 第 3 个混剪视频生成完成: D:/25125/Videos/wttttt\mixed_video_03.mp4
2025-07-05 15:53:11 [INFO] 开始生成第 4 个混剪视频...
2025-07-05 15:53:11 [INFO] 目标时长: 20秒，计划片段数: 3
2025-07-05 15:53:11 [INFO] 混剪程度: extreme (片段时长: 1.5-6.0秒)
2025-07-05 15:53:13 [INFO] 检测到场景变化: 3.7s (差异: 48.3)
2025-07-05 15:53:13 [INFO] 检测到场景变化: 5.6s (差异: 94.1)
2025-07-05 15:53:14 [INFO] 检测到场景变化: 7.4s (差异: 84.1)
2025-07-05 15:53:14 [INFO] 检测到场景变化: 9.3s (差异: 69.7)
2025-07-05 15:53:16 [INFO] 检测到场景变化: 14.8s (差异: 50.3)
2025-07-05 15:53:16 [INFO] 检测到场景变化: 16.7s (差异: 30.6)
2025-07-05 15:53:16 [INFO] 场景检测完成: 555588888888.mp4 - 发现 6 个场景变化点
2025-07-05 15:53:16 [INFO] 基于场景变化提取片段: 555588888888.mp4 (起始: 3.7s)
2025-07-05 15:53:16 [INFO] 添加片段 1: 555588888888.mp4 (4.6秒)
2025-07-05 15:53:20 [INFO] 场景检测完成: MJ助手使用演示_video_only.mp4 - 发现 3 个场景变化点
2025-07-05 15:53:20 [INFO] 基于场景变化提取片段: MJ助手使用演示_video_only.mp4 (起始: 10.0s)
2025-07-05 15:53:20 [INFO] 添加片段 2: MJ助手使用演示_video_only.mp4 (1.9秒)
2025-07-05 15:53:24 [INFO] 检测到场景变化: 3.7s (差异: 48.3)
2025-07-05 15:53:25 [INFO] 检测到场景变化: 5.6s (差异: 94.1)
2025-07-05 15:53:25 [INFO] 检测到场景变化: 7.4s (差异: 84.1)
2025-07-05 15:53:26 [INFO] 检测到场景变化: 9.3s (差异: 69.7)
2025-07-05 15:53:28 [INFO] 检测到场景变化: 14.8s (差异: 50.3)
2025-07-05 15:53:30 [INFO] 检测到场景变化: 16.7s (差异: 30.6)
2025-07-05 15:53:30 [INFO] 场景检测完成: 555588888888.mp4 - 发现 6 个场景变化点
2025-07-05 15:53:31 [INFO] 基于场景变化提取片段: 555588888888.mp4 (起始: 5.6s)
2025-07-05 15:53:31 [INFO] 添加片段 3: 555588888888.mp4 (13.5秒)
2025-07-05 15:53:31 [INFO] 共生成 3 个片段，总时长: 20.0秒
2025-07-05 15:53:31 [INFO] 验证后有效片段数: 3
2025-07-05 15:53:31 [INFO] 添加转场效果: fade
2025-07-05 15:53:31 [INFO] 开始渲染视频: D:/25125/Videos/wttttt\mixed_video_04.mp4
2025-07-05 15:53:59 [INFO] ✅ 第 4 个混剪视频生成完成: D:/25125/Videos/wttttt\mixed_video_04.mp4
2025-07-05 15:53:59 [INFO] 智能混剪完成，共生成 4 个视频
2025-07-05 15:55:01 [INFO] 📦 窗口已最小化到任务栏
2025-07-05 15:55:01 [INFO] 📦 窗口已最小化到任务栏
2025-07-05 15:55:26 [INFO] 📦 窗口已最小化到任务栏
2025-07-05 15:55:30 [INFO] 📦 窗口已最小化到任务栏
2025-07-05 15:55:40 [INFO] 📦 窗口已最小化到任务栏
2025-07-05 15:56:10 [INFO] 📦 窗口已最小化到任务栏
2025-07-05 15:56:12 [INFO] 📦 窗口已最小化到任务栏
2025-07-05 16:02:05 [INFO] ✅ 激活验证模块加载成功
2025-07-05 16:02:06 [INFO] ✅ 单实例应用初始化成功
2025-07-05 16:02:06 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 16:02:06 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-05 16:02:06 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-05 16:02:06 [INFO] ✅ 已更新最后验证时间
2025-07-05 16:02:06 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-05 16:02:06 [INFO] ✅ 本地激活配置验证通过
2025-07-05 16:02:06 [INFO] ✅ 本地激活验证通过
2025-07-05 16:02:06 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 16:03:41 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-07-05 16:03:42 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-05 16:03:42 [INFO] 未检测到CUDA设备
2025-07-05 16:03:42 [INFO] VideoProcessor初始化:
2025-07-05 16:03:42 [INFO]   CPU线程数: 8
2025-07-05 16:03:42 [INFO]   GPU支持: 否
2025-07-05 16:03:42 [INFO]   GPU加速: 禁用
2025-07-05 16:03:42 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-05 16:03:42 [INFO] 未检测到CUDA设备
2025-07-05 16:03:42 [INFO] 自动选择 CPU 多线程模式
2025-07-05 16:03:42 [INFO] 性能模式: auto, GPU: False, 线程数: 8, 低内存模式: False
2025-07-05 16:03:42 [INFO] 智能混剪失败: VideoProcessor.smart_video_mixing() got an unexpected keyword argument 'scene_detection_sensitivity'
2025-07-05 16:03:42 [INFO] Traceback (most recent call last):
  File "D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0/enhanced_video_deduplication_gui.py", line 1066, in run
    result_paths = processor.smart_video_mixing(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: VideoProcessor.smart_video_mixing() got an unexpected keyword argument 'scene_detection_sensitivity'

2025-07-05 16:08:08 [INFO] ✅ 激活验证模块加载成功
2025-07-05 16:08:10 [INFO] ✅ 单实例应用初始化成功
2025-07-05 16:08:10 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 16:08:11 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-05 16:08:11 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-05 16:08:11 [INFO] ✅ 已更新最后验证时间
2025-07-05 16:08:11 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-05 16:08:11 [INFO] ✅ 本地激活配置验证通过
2025-07-05 16:08:11 [INFO] ✅ 本地激活验证通过
2025-07-05 16:08:11 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 16:09:23 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-07-05 16:09:23 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-05 16:09:24 [INFO] 未检测到CUDA设备
2025-07-05 16:09:24 [INFO] VideoProcessor初始化:
2025-07-05 16:09:24 [INFO]   CPU线程数: 8
2025-07-05 16:09:24 [INFO]   GPU支持: 否
2025-07-05 16:09:24 [INFO]   GPU加速: 禁用
2025-07-05 16:09:24 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-07-05 16:09:24 [INFO] 未检测到CUDA设备
2025-07-05 16:09:24 [INFO] 自动选择 CPU 多线程模式
2025-07-05 16:09:24 [INFO] 性能模式: auto, GPU: False, 线程数: 8, 低内存模式: False
2025-07-05 16:09:24 [INFO] 开始智能混剪 2 个视频...
2025-07-05 16:09:24 [INFO] 混剪程度: medium
2025-07-05 16:09:24 [INFO] 画面变化检测: 启用
2025-07-05 16:09:24 [INFO] 使用随机种子: 1751702964
2025-07-05 16:09:24 [INFO] 开始生成第 1 个混剪视频...
2025-07-05 16:09:24 [INFO] 目标时长: 20秒，计划片段数: 2
2025-07-05 16:09:24 [INFO] 混剪程度: medium (片段时长: 5.0-20.0秒)
2025-07-05 16:09:25 [INFO] 检测到场景变化: 3.7s (差异: 48.3)
2025-07-05 16:09:26 [INFO] 检测到场景变化: 5.6s (差异: 94.1)
2025-07-05 16:09:26 [INFO] 检测到场景变化: 7.4s (差异: 84.1)
2025-07-05 16:09:27 [INFO] 检测到场景变化: 9.3s (差异: 69.7)
2025-07-05 16:09:28 [INFO] 检测到场景变化: 14.8s (差异: 50.3)
2025-07-05 16:09:29 [INFO] 检测到场景变化: 16.7s (差异: 30.6)
2025-07-05 16:09:29 [INFO] 场景检测完成: 555588888888.mp4 - 发现 6 个场景变化点
2025-07-05 16:09:29 [INFO] 基于场景变化提取片段: 555588888888.mp4 (起始: 7.4s, 阈值: 30.0)
2025-07-05 16:09:29 [INFO] 添加片段 1: 555588888888.mp4 (5.0秒)
2025-07-05 16:09:32 [INFO] 场景检测完成: MJ助手使用演示.mp4 - 发现 3 个场景变化点
2025-07-05 16:09:32 [INFO] 基于场景变化提取片段: MJ助手使用演示.mp4 (起始: 25.0s, 阈值: 30.0)
2025-07-05 16:09:32 [INFO] 添加片段 2: MJ助手使用演示.mp4 (15.0秒)
2025-07-05 16:09:32 [INFO] 共生成 2 个片段，总时长: 20.0秒
2025-07-05 16:09:32 [INFO] 验证后有效片段数: 2
2025-07-05 16:09:32 [INFO] 开始渲染视频: D:/25125/Videos/wttttt\mixed_video_01.mp4
2025-07-05 16:09:50 [INFO] ✅ 第 1 个混剪视频生成完成: D:/25125/Videos/wttttt\mixed_video_01.mp4
2025-07-05 16:09:50 [INFO] 使用随机种子: 1751703964
2025-07-05 16:09:50 [INFO] 开始生成第 2 个混剪视频...
2025-07-05 16:09:50 [INFO] 目标时长: 20秒，计划片段数: 2
2025-07-05 16:09:50 [INFO] 混剪程度: medium (片段时长: 5.0-20.0秒)
2025-07-05 16:09:52 [INFO] 检测到场景变化: 3.7s (差异: 48.3)
2025-07-05 16:09:52 [INFO] 检测到场景变化: 5.6s (差异: 94.1)
2025-07-05 16:09:53 [INFO] 检测到场景变化: 7.4s (差异: 84.1)
2025-07-05 16:09:53 [INFO] 检测到场景变化: 9.3s (差异: 69.7)
2025-07-05 16:09:56 [INFO] 检测到场景变化: 14.8s (差异: 50.3)
2025-07-05 16:09:56 [INFO] 检测到场景变化: 16.7s (差异: 30.6)
2025-07-05 16:09:56 [INFO] 场景检测完成: 555588888888.mp4 - 发现 6 个场景变化点
2025-07-05 16:09:56 [INFO] 基于场景变化提取片段: 555588888888.mp4 (起始: 16.7s, 阈值: 30.0)
2025-07-05 16:09:56 [INFO] 添加片段 1: 555588888888.mp4 (9.6秒)
2025-07-05 16:10:00 [INFO] 场景检测完成: MJ助手使用演示.mp4 - 发现 3 个场景变化点
2025-07-05 16:10:00 [INFO] 基于场景变化提取片段: MJ助手使用演示.mp4 (起始: 29.5s, 阈值: 30.0)
2025-07-05 16:10:00 [INFO] 添加片段 2: MJ助手使用演示.mp4 (10.4秒)
2025-07-05 16:10:00 [INFO] 共生成 2 个片段，总时长: 20.0秒
2025-07-05 16:10:00 [INFO] 验证后有效片段数: 2
2025-07-05 16:10:00 [INFO] 开始渲染视频: D:/25125/Videos/wttttt\mixed_video_02.mp4
2025-07-05 16:10:17 [INFO] ✅ 第 2 个混剪视频生成完成: D:/25125/Videos/wttttt\mixed_video_02.mp4
2025-07-05 16:10:17 [INFO] 使用随机种子: 1751704964
2025-07-05 16:10:17 [INFO] 开始生成第 3 个混剪视频...
2025-07-05 16:10:17 [INFO] 目标时长: 20秒，计划片段数: 2
2025-07-05 16:10:17 [INFO] 混剪程度: medium (片段时长: 5.0-20.0秒)
2025-07-05 16:10:23 [INFO] 场景检测完成: MJ助手使用演示.mp4 - 发现 3 个场景变化点
2025-07-05 16:10:23 [INFO] 基于场景变化提取片段: MJ助手使用演示.mp4 (起始: 30.0s, 阈值: 30.0)
2025-07-05 16:10:23 [INFO] 添加片段 1: MJ助手使用演示.mp4 (5.5秒)
2025-07-05 16:10:24 [INFO] 检测到场景变化: 3.7s (差异: 48.3)
2025-07-05 16:10:25 [INFO] 检测到场景变化: 5.6s (差异: 94.1)
2025-07-05 16:10:25 [INFO] 检测到场景变化: 7.4s (差异: 84.1)
2025-07-05 16:10:26 [INFO] 检测到场景变化: 9.3s (差异: 69.7)
2025-07-05 16:10:27 [INFO] 检测到场景变化: 14.8s (差异: 50.3)
2025-07-05 16:10:28 [INFO] 检测到场景变化: 16.7s (差异: 30.6)
2025-07-05 16:10:28 [INFO] 场景检测完成: 555588888888.mp4 - 发现 6 个场景变化点
2025-07-05 16:10:28 [INFO] 基于场景变化提取片段: 555588888888.mp4 (起始: 14.8s, 阈值: 30.0)
2025-07-05 16:10:28 [INFO] 添加片段 2: 555588888888.mp4 (14.5秒)
2025-07-05 16:10:28 [INFO] 共生成 2 个片段，总时长: 20.0秒
2025-07-05 16:10:28 [INFO] 验证后有效片段数: 2
2025-07-05 16:10:28 [INFO] 开始渲染视频: D:/25125/Videos/wttttt\mixed_video_03.mp4
2025-07-05 16:10:46 [INFO] ✅ 第 3 个混剪视频生成完成: D:/25125/Videos/wttttt\mixed_video_03.mp4
2025-07-05 16:10:46 [INFO] 使用随机种子: 1751705964
2025-07-05 16:10:46 [INFO] 开始生成第 4 个混剪视频...
2025-07-05 16:10:46 [INFO] 目标时长: 20秒，计划片段数: 2
2025-07-05 16:10:46 [INFO] 混剪程度: medium (片段时长: 5.0-20.0秒)
2025-07-05 16:10:49 [INFO] 检测到场景变化: 3.7s (差异: 48.3)
2025-07-05 16:10:50 [INFO] 检测到场景变化: 5.6s (差异: 94.1)
2025-07-05 16:10:50 [INFO] 检测到场景变化: 7.4s (差异: 84.1)
2025-07-05 16:10:51 [INFO] 检测到场景变化: 9.3s (差异: 69.7)
2025-07-05 16:10:52 [INFO] 检测到场景变化: 14.8s (差异: 50.3)
2025-07-05 16:10:52 [INFO] 检测到场景变化: 16.7s (差异: 30.6)
2025-07-05 16:10:52 [INFO] 场景检测完成: 555588888888.mp4 - 发现 6 个场景变化点
2025-07-05 16:10:53 [INFO] 基于场景变化提取片段: 555588888888.mp4 (起始: 7.4s, 阈值: 30.0)
2025-07-05 16:10:53 [INFO] 添加片段 1: 555588888888.mp4 (6.8秒)
2025-07-05 16:10:56 [INFO] 场景检测完成: MJ助手使用演示.mp4 - 发现 3 个场景变化点
2025-07-05 16:10:56 [INFO] 基于场景变化提取片段: MJ助手使用演示.mp4 (起始: 20.0s, 阈值: 30.0)
2025-07-05 16:10:56 [INFO] 添加片段 2: MJ助手使用演示.mp4 (13.2秒)
2025-07-05 16:10:56 [INFO] 共生成 2 个片段，总时长: 20.0秒
2025-07-05 16:10:56 [INFO] 验证后有效片段数: 2
2025-07-05 16:10:56 [INFO] 开始渲染视频: D:/25125/Videos/wttttt\mixed_video_04.mp4
2025-07-05 16:11:15 [INFO] ✅ 第 4 个混剪视频生成完成: D:/25125/Videos/wttttt\mixed_video_04.mp4
2025-07-05 16:11:15 [INFO] 使用随机种子: 1751706964
2025-07-05 16:11:15 [INFO] 开始生成第 5 个混剪视频...
2025-07-05 16:11:15 [INFO] 目标时长: 20秒，计划片段数: 2
2025-07-05 16:11:15 [INFO] 混剪程度: medium (片段时长: 5.0-20.0秒)
2025-07-05 16:11:16 [INFO] 检测到场景变化: 3.7s (差异: 48.3)
2025-07-05 16:11:17 [INFO] 检测到场景变化: 5.6s (差异: 94.1)
2025-07-05 16:11:17 [INFO] 检测到场景变化: 7.4s (差异: 84.1)
2025-07-05 16:11:18 [INFO] 检测到场景变化: 9.3s (差异: 69.7)
2025-07-05 16:11:19 [INFO] 检测到场景变化: 14.8s (差异: 50.3)
2025-07-05 16:11:20 [INFO] 检测到场景变化: 16.7s (差异: 30.6)
2025-07-05 16:11:20 [INFO] 场景检测完成: 555588888888.mp4 - 发现 6 个场景变化点
2025-07-05 16:11:20 [INFO] 基于场景变化提取片段: 555588888888.mp4 (起始: 16.7s, 阈值: 30.0)
2025-07-05 16:11:20 [INFO] 添加片段 1: 555588888888.mp4 (8.0秒)
2025-07-05 16:11:23 [INFO] 场景检测完成: MJ助手使用演示.mp4 - 发现 3 个场景变化点
2025-07-05 16:11:23 [INFO] 基于场景变化提取片段: MJ助手使用演示.mp4 (起始: 28.0s, 阈值: 30.0)
2025-07-05 16:11:23 [INFO] 添加片段 2: MJ助手使用演示.mp4 (12.0秒)
2025-07-05 16:11:23 [INFO] 共生成 2 个片段，总时长: 20.0秒
2025-07-05 16:11:23 [INFO] 验证后有效片段数: 2
2025-07-05 16:11:23 [INFO] 开始渲染视频: D:/25125/Videos/wttttt\mixed_video_05.mp4
2025-07-05 16:11:40 [INFO] ✅ 第 5 个混剪视频生成完成: D:/25125/Videos/wttttt\mixed_video_05.mp4
2025-07-05 16:11:40 [INFO] 智能混剪完成，共生成 5 个视频
2025-07-05 16:42:49 [INFO] 📜 创建启动脚本...
2025-07-05 16:42:49 [INFO]   创建Shell启动脚本: 启动程序.sh
2025-07-05 16:46:27 [INFO] 📢 收到激活信号，显示主窗口
2025-07-05 21:46:56 [INFO] ✅ 激活验证模块加载成功
2025-07-05 21:46:57 [WARNING] ⚠️ 应用程序已在运行，激活现有窗口
2025-07-05 21:47:20 [INFO] ✅ 激活验证模块加载成功
2025-07-05 21:47:21 [WARNING] ⚠️ 应用程序已在运行，激活现有窗口
2025-07-05 21:47:30 [INFO] ✅ 激活验证模块加载成功
2025-07-05 21:47:31 [WARNING] ⚠️ 应用程序已在运行，激活现有窗口
2025-07-05 21:48:53 [INFO] ✅ 激活验证模块加载成功
2025-07-05 21:48:54 [WARNING] ⚠️ 应用程序已在运行，激活现有窗口
2025-07-05 21:49:36 [INFO] ✅ 激活验证模块加载成功
2025-07-05 21:49:37 [INFO] ✅ 单实例应用初始化成功
2025-07-05 21:49:37 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 21:49:38 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-05 21:49:38 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-05 21:49:38 [INFO] ✅ 已更新最后验证时间
2025-07-05 21:49:38 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-05 21:49:38 [INFO] ✅ 本地激活配置验证通过
2025-07-05 21:49:38 [INFO] ✅ 本地激活验证通过
2025-07-05 21:49:38 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 21:49:48 [INFO] ✅ 激活验证模块加载成功
2025-07-05 21:49:49 [WARNING] ⚠️ 应用程序已在运行，激活现有窗口
2025-07-05 21:49:49 [INFO] 📢 收到激活信号，显示主窗口
2025-07-05 21:50:02 [INFO] ✅ 激活验证模块加载成功
2025-07-05 21:50:03 [INFO] ✅ 单实例应用初始化成功
2025-07-05 21:50:03 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 21:50:03 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-05 21:50:03 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-05 21:50:03 [INFO] ✅ 已更新最后验证时间
2025-07-05 21:50:03 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-05 21:50:03 [INFO] ✅ 本地激活配置验证通过
2025-07-05 21:50:03 [INFO] ✅ 本地激活验证通过
2025-07-05 21:50:03 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 21:50:21 [INFO] ✅ 激活验证模块加载成功
2025-07-05 21:50:22 [WARNING] ⚠️ 应用程序已在运行，激活现有窗口
2025-07-05 21:50:22 [INFO] 📢 收到激活信号，显示主窗口
2025-07-05 21:57:39 [INFO] ✅ 激活验证模块加载成功
2025-07-05 21:57:40 [INFO] ✅ 单实例应用初始化成功
2025-07-05 21:57:40 [INFO] ✅ 成功设置应用程序图标: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 21:57:41 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-05 21:57:41 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-05 21:57:41 [INFO] ✅ 已更新最后验证时间
2025-07-05 21:57:41 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-05 21:57:41 [INFO] ✅ 本地激活配置验证通过
2025-07-05 21:57:41 [INFO] ✅ 本地激活验证通过
2025-07-05 21:57:41 [INFO] ✅ 成功加载窗口图标: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 21:57:46 [INFO] 📦 程序已最小化到系统托盘
2025-07-05 21:58:07 [INFO] ✅ 激活验证模块加载成功
2025-07-05 21:58:08 [WARNING] ⚠️ 应用程序已在运行，激活现有窗口
2025-07-05 21:58:08 [INFO] 📢 收到激活信号，显示主窗口
2025-07-05 21:58:21 [INFO] ✅ 激活验证模块加载成功
2025-07-05 21:58:23 [INFO] ✅ 单实例应用初始化成功
2025-07-05 21:58:23 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 21:58:23 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-05 21:58:23 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-05 21:58:23 [INFO] ✅ 已更新最后验证时间
2025-07-05 21:58:23 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-05 21:58:23 [INFO] ✅ 本地激活配置验证通过
2025-07-05 21:58:23 [INFO] ✅ 本地激活验证通过
2025-07-05 21:58:23 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 21:58:28 [INFO] 🚪 用户选择完全退出程序
2025-07-05 21:58:31 [INFO] ✅ 激活验证模块加载成功
2025-07-05 21:58:33 [INFO] ✅ 单实例应用初始化成功
2025-07-05 21:58:33 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 21:58:33 [INFO] 🔑 机器码: F1CE-69ED-3A25-1420
2025-07-05 21:58:33 [INFO] 🔍 验证本地保存的激活码: B379-9B3D-D4C5-4484
2025-07-05 21:58:33 [INFO] ✅ 已更新最后验证时间
2025-07-05 21:58:33 [INFO] ✅ 服务器验证成功: 设备已激活
2025-07-05 21:58:33 [INFO] ✅ 本地激活配置验证通过
2025-07-05 21:58:33 [INFO] ✅ 本地激活验证通过
2025-07-05 21:58:33 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-07-05 21:58:39 [INFO] 📦 程序已最小化到系统托盘
2025-07-05 21:58:45 [INFO] 🚪 用户选择完全退出程序
