# 软件最小化行为优化

## 概述

根据用户需求，软件最小化后图标现在会保留在任务栏中，而不是自动隐藏到系统托盘。用户可以通过多种方式控制软件的最小化行为。

## 功能特性

### 1. 🔧 最小化行为选项

软件现在提供三种最小化方式：

#### 📋 最小化到任务栏（默认）
- 软件最小化后图标保留在任务栏中
- 用户可以直接点击任务栏图标恢复窗口
- 这是现在的默认行为

#### 📦 最小化到系统托盘
- 软件隐藏到系统托盘，任务栏图标消失
- 用户可以通过双击托盘图标恢复窗口
- 通过托盘菜单可以控制软件

#### ❌ 完全退出程序
- 彻底关闭软件，释放所有资源

### 2. 🎛️ 用户控制选项

#### 关闭窗口时的选择
当用户点击窗口关闭按钮时，会弹出选择对话框：

```
请选择关闭方式：

• 点击 'Yes' - 最小化到任务栏
• 点击 'No' - 最小化到系统托盘  
• 点击 'Cancel' - 完全退出程序
```

#### 系统托盘菜单选项
右键点击系统托盘图标可以看到：
- 🔍 显示主窗口
- 📋 最小化到任务栏
- 📦 最小化到托盘
- ❌ 退出程序

### 3. 🔄 窗口恢复功能

#### 从任务栏恢复
- 直接点击任务栏图标
- 窗口会自动恢复并获得焦点

#### 从系统托盘恢复
- 双击系统托盘图标
- 右键菜单选择"显示主窗口"

## 技术实现

### 1. 窗口标志设置

<augment_code_snippet path="enhanced_video_deduplication_gui.py" mode="EXCERPT">
```python
def init_ui(self):
    # 设置窗口标志，确保在任务栏中正确显示
    self.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | 
                       Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)
    
    # 确保窗口在任务栏中显示
    self.setAttribute(Qt.WA_ShowWithoutActivating, False)
    self.setAttribute(Qt.WA_DeleteOnClose, False)
```
</augment_code_snippet>

### 2. 最小化行为控制

<augment_code_snippet path="enhanced_video_deduplication_gui.py" mode="EXCERPT">
```python
def changeEvent(self, event):
    """处理窗口状态变化事件"""
    if event.type() == event.WindowStateChange:
        if self.isMinimized():
            # 最小化时保持在任务栏，不隐藏到托盘
            self.setWindowState(Qt.WindowMinimized)
            print("📦 窗口已最小化到任务栏")
            event.accept()
            return
    super().changeEvent(event)
```
</augment_code_snippet>

### 3. 关闭事件处理

<augment_code_snippet path="enhanced_video_deduplication_gui.py" mode="EXCERPT">
```python
def closeEvent(self, event):
    """重写关闭事件，提供最小化选项"""
    if hasattr(self, 'tray_icon') and self.tray_icon.isVisible():
        # 询问用户选择关闭方式
        reply = QMessageBox.question(
            self,
            "关闭选项",
            "请选择关闭方式：\n\n"
            "• 点击 'Yes' - 最小化到任务栏\n"
            "• 点击 'No' - 最小化到系统托盘\n"
            "• 点击 'Cancel' - 完全退出程序",
            QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
            QMessageBox.Yes
        )
        
        if reply == QMessageBox.Yes:
            # 最小化到任务栏
            self.showMinimized()
            event.ignore()
        elif reply == QMessageBox.No:
            # 最小化到系统托盘
            self.hide()
            event.ignore()
        else:
            # 完全退出程序
            event.accept()
```
</augment_code_snippet>

### 4. 应用程序属性设置

<augment_code_snippet path="enhanced_video_deduplication_gui.py" mode="EXCERPT">
```python
def main():
    # 设置应用程序属性，确保任务栏图标正确显示
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_DisableWindowContextHelpButton, True)

    # 设置应用程序不在最后一个窗口关闭时退出（支持系统托盘）
    app.setQuitOnLastWindowClosed(False)
```
</augment_code_snippet>

## 用户体验改进

### 1. 🎯 默认行为优化
- **新行为**：最小化时默认保留在任务栏
- **旧行为**：最小化时自动隐藏到系统托盘
- **优势**：符合用户习惯，更容易找到和恢复软件

### 2. 🎛️ 灵活的控制选项
- 用户可以根据需要选择不同的最小化方式
- 提供清晰的选择界面和说明
- 支持快捷操作和菜单操作

### 3. 🔄 智能窗口恢复
- 自动检测窗口状态
- 确保窗口正确恢复并获得焦点
- 支持多种恢复方式

## 兼容性说明

### 系统要求
- Windows 7 及以上版本
- 支持系统托盘功能的桌面环境

### 向后兼容
- 保留原有的系统托盘功能
- 用户仍可以选择使用托盘模式
- 不影响现有的快捷键和操作习惯

## 使用建议

### 日常使用
1. **推荐**：使用默认的"最小化到任务栏"模式
2. **优势**：快速访问，符合Windows使用习惯
3. **场景**：适合需要频繁切换软件的用户

### 后台运行
1. **推荐**：使用"最小化到系统托盘"模式
2. **优势**：节省任务栏空间，静默运行
3. **场景**：适合长时间后台运行的场景

### 完全退出
1. **推荐**：在不需要软件时选择完全退出
2. **优势**：释放系统资源，彻底关闭
3. **场景**：适合临时使用或系统资源紧张时

## 故障排除

### 任务栏图标不显示
1. 检查窗口标志设置
2. 确认应用程序属性配置
3. 重启软件尝试

### 无法从任务栏恢复
1. 尝试双击任务栏图标
2. 使用系统托盘菜单恢复
3. 检查窗口状态设置

### 系统托盘功能异常
1. 确认系统支持托盘功能
2. 检查托盘图标设置
3. 重新初始化托盘组件

## 更新日志

### v2.0 更新内容
- ✅ 修改默认最小化行为为保留任务栏图标
- ✅ 添加用户选择关闭方式的对话框
- ✅ 优化窗口恢复逻辑
- ✅ 增强任务栏图标显示稳定性
- ✅ 保留系统托盘功能作为备选方案
- ✅ 改进用户体验和操作流畅性
