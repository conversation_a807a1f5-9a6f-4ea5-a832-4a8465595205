package com.example.activation.entity;

import java.time.LocalDateTime;

/**
 * 激活码实体类
 */
public class ActivationCode {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 激活码
     */
    private String activationCode;
    
    /**
     * 机器码
     */
    private String machineCode;
    
    /**
     * 用户标识（可选）
     */
    private String userId;
    
    /**
     * 激活状态：0-未激活，1-已激活，2-已禁用
     */
    private Integer status;
    
    /**
     * 激活时间
     */
    private LocalDateTime activationTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 过期时间
     */
    private LocalDateTime expireTime;
    
    /**
     * 最大激活次数
     */
    private Integer maxActivations;
    
    /**
     * 当前激活次数
     */
    private Integer currentActivations;
    
    /**
     * 备注信息
     */
    private String remark;
    
    // 构造函数
    public ActivationCode() {}
    
    public ActivationCode(String activationCode, String machineCode) {
        this.activationCode = activationCode;
        this.machineCode = machineCode;
        this.status = 0;
        this.currentActivations = 0;
        this.maxActivations = 1;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getActivationCode() {
        return activationCode;
    }
    
    public void setActivationCode(String activationCode) {
        this.activationCode = activationCode;
    }
    
    public String getMachineCode() {
        return machineCode;
    }
    
    public void setMachineCode(String machineCode) {
        this.machineCode = machineCode;
    }
    
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public LocalDateTime getActivationTime() {
        return activationTime;
    }
    
    public void setActivationTime(LocalDateTime activationTime) {
        this.activationTime = activationTime;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    public LocalDateTime getExpireTime() {
        return expireTime;
    }
    
    public void setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
    }
    
    public Integer getMaxActivations() {
        return maxActivations;
    }
    
    public void setMaxActivations(Integer maxActivations) {
        this.maxActivations = maxActivations;
    }
    
    public Integer getCurrentActivations() {
        return currentActivations;
    }
    
    public void setCurrentActivations(Integer currentActivations) {
        this.currentActivations = currentActivations;
    }
    
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
    
    /**
     * 检查激活码是否已过期
     */
    public boolean isExpired() {
        return expireTime != null && LocalDateTime.now().isAfter(expireTime);
    }
    
    /**
     * 检查是否可以激活
     */
    public boolean canActivate() {
        return status == 0 && !isExpired() && currentActivations < maxActivations;
    }
    
    /**
     * 检查是否已激活
     */
    public boolean isActivated() {
        return status == 1;
    }
    
    /**
     * 检查是否已禁用
     */
    public boolean isDisabled() {
        return status == 2;
    }
    
    @Override
    public String toString() {
        return "ActivationCode{" +
                "id=" + id +
                ", activationCode='" + activationCode + '\'' +
                ", machineCode='" + machineCode + '\'' +
                ", userId='" + userId + '\'' +
                ", status=" + status +
                ", activationTime=" + activationTime +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", expireTime=" + expireTime +
                ", maxActivations=" + maxActivations +
                ", currentActivations=" + currentActivations +
                ", remark='" + remark + '\'' +
                '}';
    }
}
