# 机器详细信息收集和发送功能

## 概述

本功能在客户端进行激活码验证时，自动收集并发送机器的详细硬件信息到后端服务器，用于更精确的设备识别和管理。

## 功能特性

### 收集的机器信息

客户端会自动收集以下硬件信息：

1. **CPU信息**
   - CPU ID (cpuId): 处理器唯一标识符
   - 处理器型号 (processor): 详细的处理器信息

2. **主板信息**
   - 主板序列号 (motherboardSerial): 主板唯一序列号
   - BIOS序列号 (biosSerial): BIOS固件序列号

3. **存储信息**
   - 硬盘序列号 (diskSerial): 主硬盘序列号

4. **网络信息**
   - MAC地址 (macAddress): 网络适配器物理地址

5. **系统信息**
   - 计算机名称 (computerName): 系统计算机名
   - 操作系统 (operatingSystem): 操作系统类型
   - Windows产品ID (windowsProductId): Windows系统产品标识
   - 机器GUID (machineGuid): Windows系统机器GUID

### 字段名称映射

客户端使用Python风格的下划线命名，后端使用Java风格的驼峰命名。系统会自动进行字段名称转换：

| 客户端字段 (Python) | 后端字段 (Java) | 说明 |
|-------------------|----------------|------|
| cpu_id | cpuId | CPU标识符 |
| motherboard_serial | motherboardSerial | 主板序列号 |
| bios_serial | biosSerial | BIOS序列号 |
| disk_serial | diskSerial | 硬盘序列号 |
| mac_address | macAddress | MAC地址 |
| windows_product_id | windowsProductId | Windows产品ID |
| machine_guid | machineGuid | 机器GUID |
| computer_name | computerName | 计算机名称 |
| system | operatingSystem | 操作系统 |
| processor | processor | 处理器信息 |

## 实现细节

### 客户端实现

#### 1. 机器信息收集 (`machine_code_generator.py`)

```python
def collect_hardware_info(self) -> dict:
    """收集所有硬件信息"""
    info = {
        'cpu_id': self.get_cpu_id(),
        'motherboard_serial': self.get_motherboard_serial(),
        'bios_serial': self.get_bios_serial(),
        'disk_serial': self.get_disk_serial(),
        'mac_address': self.get_mac_address(),
        'windows_product_id': self.get_windows_product_id(),
        'machine_guid': self.get_machine_guid(),
        'computer_name': platform.node(),
        'system': platform.system(),
        'processor': platform.processor()
    }
    
    # 过滤掉空值
    return {k: v for k, v in info.items() if v}
```

#### 2. API请求处理 (`api_manager.py`)

```python
def verify_activation(self, activation_code: str, machine_code: str, 
                     machine_details: Optional[Dict] = None) -> Dict[str, Any]:
    """验证激活码"""
    data = {
        'activationCode': activation_code,
        'machineCode': machine_code,
        'timestamp': int(time.time()),
        'clientVersion': '2.0'
    }
    
    if machine_details:
        # 转换字段名称从Python风格到Java风格
        converted_details = {}
        field_mapping = {
            'cpu_id': 'cpuId',
            'motherboard_serial': 'motherboardSerial',
            # ... 其他映射
        }
        
        for python_key, java_key in field_mapping.items():
            if python_key in machine_details and machine_details[python_key]:
                converted_details[java_key] = machine_details[python_key]
        
        data['machineDetails'] = converted_details
        
    return self._make_request('POST', '/verify', data)
```

#### 3. 激活对话框集成 (`activation_dialog.py`)

```python
def verify_activation(self):
    """验证激活码"""
    # ... 其他验证逻辑
    
    # 收集机器详细信息
    try:
        machine_generator = MachineCodeGenerator()
        machine_details = machine_generator.collect_hardware_info()
        print(f"📊 收集到机器详细信息: {len(machine_details)} 项")
    except Exception as e:
        print(f"⚠️ 收集机器详细信息失败: {e}")
        machine_details = None

    # 启动验证线程
    self.activation_thread = ActivationThread(
        self.machine_code,
        activation_code,
        self.server_url,
        machine_details  # 传递机器详细信息
    )
```

### 后端实现

#### 1. 控制器接收 (`ActivationController.java`)

```java
@PostMapping("/verify")
public ResponseEntity<Map<String, Object>> verifyActivation(@RequestBody Map<String, Object> request) {
    String activationCode = (String) request.get("activationCode");
    String machineCode = (String) request.get("machineCode");
    
    // 获取机器详细信息（可选）
    @SuppressWarnings("unchecked")
    Map<String, String> machineDetails = (Map<String, String>) request.get("machineDetails");
    
    Map<String, Object> result = activationService.verifyActivation(activationCode, machineCode, machineDetails);
    
    return ResponseEntity.ok(result);
}
```

#### 2. 服务层处理 (`ActivationService.java`)

```java
private void updateOrCreateMachineInfo(String machineCode, Map<String, String> machineDetails) {
    // ... 查找或创建机器信息
    
    // 设置机器详细信息
    if (machineDetails != null) {
        machineInfo.setComputerName(machineDetails.get("computerName"));
        machineInfo.setOperatingSystem(machineDetails.get("operatingSystem"));
        machineInfo.setProcessor(machineDetails.get("processor"));
        machineInfo.setCpuId(machineDetails.get("cpuId"));
        machineInfo.setMotherboardSerial(machineDetails.get("motherboardSerial"));
        machineInfo.setBiosSerial(machineDetails.get("biosSerial"));
        machineInfo.setDiskSerial(machineDetails.get("diskSerial"));
        machineInfo.setMacAddress(machineDetails.get("macAddress"));
        machineInfo.setWindowsProductId(machineDetails.get("windowsProductId"));
        machineInfo.setMachineGuid(machineDetails.get("machineGuid"));
    }
    
    // 保存到数据库
    machineInfoMapper.insert(machineInfo);
}
```

## 使用方式

### 自动收集

机器详细信息的收集是完全自动的，用户在进行激活码验证时无需任何额外操作：

1. 用户输入激活码
2. 点击"验证"按钮
3. 系统自动收集机器详细信息
4. 将激活码、机器码和机器详细信息一起发送到服务器
5. 服务器验证并保存机器信息

### 错误处理

- 如果机器详细信息收集失败，系统会继续进行激活验证，只是不会发送详细信息
- 收集过程中的错误会在控制台输出，但不会影响用户体验
- 后端会优雅处理缺失的机器详细信息

## 测试

使用 `test_machine_details.py` 脚本可以测试机器详细信息的收集功能：

```bash
python test_machine_details.py
```

测试内容包括：
1. 机器详细信息收集
2. 字段名称映射
3. API数据格式验证

## 安全性

- 收集的信息仅用于设备识别和管理
- 不收集任何个人敏感信息
- 所有信息通过HTTPS加密传输
- 后端严格验证数据格式和内容

## 兼容性

- 支持Windows系统
- 自动适配不同硬件配置
- 对于无法获取的硬件信息会自动跳过
- 向后兼容不支持机器详细信息的旧版本后端
