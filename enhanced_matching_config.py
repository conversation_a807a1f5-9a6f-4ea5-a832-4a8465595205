#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强相似度匹配配置文件
针对不同类型的视频内容提供优化的匹配参数
"""

# 默认增强匹配配置
DEFAULT_ENHANCED_CONFIG = {
    'feature_types': ['subtitle_robust', 'phash', 'color_hist', 'edge', 'motion'],
    'feature_weights': {
        'subtitle_robust': 0.35,  # 字幕鲁棒特征权重最高
        'phash': 0.25,           # 感知哈希
        'color_hist': 0.20,      # 颜色直方图
        'edge': 0.15,            # 边缘特征
        'motion': 0.05           # 运动特征
    },
    'similarity_threshold': 0.75,
    'enhanced_accuracy': True,
    'num_frames': 12,
    'scene_threshold': 25.0
}

# 针对不同视频类型的优化配置
VIDEO_TYPE_CONFIGS = {
    # 电影/电视剧类型（有字幕，剪辑较多）
    'movie_tv': {
        'feature_types': ['subtitle_robust', 'phash', 'color_hist', 'edge'],
        'feature_weights': {
            'subtitle_robust': 0.40,  # 字幕处理权重更高
            'phash': 0.30,           # 结构相似度重要
            'color_hist': 0.20,      # 颜色匹配
            'edge': 0.10             # 边缘特征
        },
        'similarity_threshold': 0.70,  # 稍低阈值，因为剪辑差异大
        'scene_threshold': 20.0,       # 更细的场景分割
        'num_frames': 15              # 更多帧数提高精度
    },
    
    # 纪录片类型（字幕较少，内容连续性强）
    'documentary': {
        'feature_types': ['phash', 'color_hist', 'edge', 'motion', 'subtitle_robust'],
        'feature_weights': {
            'phash': 0.35,           # 结构相似度最重要
            'color_hist': 0.25,      # 颜色分布重要
            'edge': 0.20,            # 边缘特征
            'motion': 0.15,          # 运动特征较重要
            'subtitle_robust': 0.05  # 字幕权重较低
        },
        'similarity_threshold': 0.80,  # 较高阈值
        'scene_threshold': 30.0,       # 较粗的场景分割
        'num_frames': 10
    },
    
    # 新闻类型（字幕多，场景变化快）
    'news': {
        'feature_types': ['subtitle_robust', 'color_hist', 'phash', 'edge'],
        'feature_weights': {
            'subtitle_robust': 0.45,  # 字幕处理最重要
            'color_hist': 0.25,      # 颜色匹配重要
            'phash': 0.20,           # 结构相似度
            'edge': 0.10             # 边缘特征
        },
        'similarity_threshold': 0.65,  # 较低阈值，适应快速变化
        'scene_threshold': 15.0,       # 很细的场景分割
        'num_frames': 8               # 较少帧数，提高速度
    },
    
    # 体育类型（运动多，字幕少）
    'sports': {
        'feature_types': ['motion', 'phash', 'color_hist', 'edge', 'subtitle_robust'],
        'feature_weights': {
            'motion': 0.35,          # 运动特征最重要
            'phash': 0.25,           # 结构相似度
            'color_hist': 0.20,      # 颜色匹配
            'edge': 0.15,            # 边缘特征
            'subtitle_robust': 0.05  # 字幕权重很低
        },
        'similarity_threshold': 0.75,
        'scene_threshold': 25.0,
        'num_frames': 12
    },
    
    # 动画类型（颜色鲜明，风格化强）
    'animation': {
        'feature_types': ['color_hist', 'phash', 'edge', 'subtitle_robust'],
        'feature_weights': {
            'color_hist': 0.40,      # 颜色最重要
            'phash': 0.30,           # 结构相似度
            'edge': 0.20,            # 边缘特征重要
            'subtitle_robust': 0.10  # 字幕处理
        },
        'similarity_threshold': 0.80,  # 较高阈值，动画风格一致性强
        'scene_threshold': 35.0,       # 较粗场景分割
        'num_frames': 10
    },
    
    # 音乐视频类型（视觉效果多，变化快）
    'music_video': {
        'feature_types': ['color_hist', 'motion', 'phash', 'edge'],
        'feature_weights': {
            'color_hist': 0.35,      # 颜色效果重要
            'motion': 0.30,          # 运动效果重要
            'phash': 0.20,           # 结构相似度
            'edge': 0.15             # 边缘特征
        },
        'similarity_threshold': 0.70,  # 中等阈值
        'scene_threshold': 20.0,       # 细场景分割
        'num_frames': 15              # 更多帧数捕捉变化
    },
    
    # 教育类型（字幕多，内容稳定）
    'educational': {
        'feature_types': ['subtitle_robust', 'phash', 'color_hist', 'edge'],
        'feature_weights': {
            'subtitle_robust': 0.40,  # 字幕处理重要
            'phash': 0.30,           # 结构稳定
            'color_hist': 0.20,      # 颜色匹配
            'edge': 0.10             # 边缘特征
        },
        'similarity_threshold': 0.75,
        'scene_threshold': 30.0,      # 较粗场景分割，内容连续
        'num_frames': 12
    }
}

# 自动检测视频类型的关键词
VIDEO_TYPE_KEYWORDS = {
    'movie_tv': ['movie', 'film', 'tv', 'series', 'episode', '电影', '电视剧', '剧集'],
    'documentary': ['documentary', 'doc', 'record', '纪录片', '记录'],
    'news': ['news', 'report', 'broadcast', '新闻', '报道', '播报'],
    'sports': ['sport', 'game', 'match', 'football', 'basketball', '体育', '比赛', '足球', '篮球'],
    'animation': ['animation', 'cartoon', 'anime', '动画', '卡通', '动漫'],
    'music_video': ['music', 'mv', 'song', 'concert', '音乐', '歌曲', '演唱会'],
    'educational': ['education', 'tutorial', 'lesson', 'course', '教育', '教程', '课程', '学习']
}

def detect_video_type(video_path):
    """
    根据文件名自动检测视频类型
    :param video_path: 视频文件路径
    :return: 检测到的视频类型，如果无法检测则返回None
    """
    filename = os.path.basename(video_path).lower()
    
    for video_type, keywords in VIDEO_TYPE_KEYWORDS.items():
        for keyword in keywords:
            if keyword.lower() in filename:
                return video_type
    
    return None

def get_config_for_video_type(video_type):
    """
    获取指定视频类型的配置
    :param video_type: 视频类型
    :return: 配置字典
    """
    if video_type in VIDEO_TYPE_CONFIGS:
        config = DEFAULT_ENHANCED_CONFIG.copy()
        config.update(VIDEO_TYPE_CONFIGS[video_type])
        return config
    else:
        return DEFAULT_ENHANCED_CONFIG.copy()

def get_auto_config(main_video_path, aux_video_path=None):
    """
    自动获取最适合的配置
    :param main_video_path: 主视频路径
    :param aux_video_path: 辅助视频路径（可选）
    :return: 配置字典
    """
    # 优先根据主视频检测类型
    video_type = detect_video_type(main_video_path)
    
    # 如果主视频检测不到，尝试辅助视频
    if not video_type and aux_video_path:
        video_type = detect_video_type(aux_video_path)
    
    config = get_config_for_video_type(video_type)
    
    # 添加检测到的类型信息
    config['detected_type'] = video_type or 'unknown'
    
    return config

def print_config_info(config):
    """
    打印配置信息
    :param config: 配置字典
    """
    print(f"📋 匹配配置信息:")
    print(f"   检测类型: {config.get('detected_type', 'unknown')}")
    print(f"   特征类型: {', '.join(config['feature_types'])}")
    print(f"   相似度阈值: {config['similarity_threshold']:.2f}")
    print(f"   场景阈值: {config['scene_threshold']:.1f}")
    print(f"   提取帧数: {config['num_frames']}")
    print(f"   特征权重:")
    for feature, weight in config['feature_weights'].items():
        print(f"     {feature}: {weight:.2f}")

# 使用示例
if __name__ == "__main__":
    import os
    
    # 测试自动配置
    test_videos = [
        "movie_sample.mp4",
        "news_report.mp4", 
        "sports_game.mp4",
        "animation_clip.mp4"
    ]
    
    for video in test_videos:
        print(f"\n测试视频: {video}")
        config = get_auto_config(video)
        print_config_info(config)
