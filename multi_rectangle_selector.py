#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多矩形字幕区域选择器
支持在视频帧上绘制和管理多个矩形选择区域
"""

import sys
import cv2
import numpy as np
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                             QLabel, QSlider, QListWidget, QListWidgetItem,
                             QMessageBox, QFrame, QGroupBox, QScrollArea)
from PyQt5.QtCore import Qt, pyqtSignal, QPoint
from PyQt5.QtGui import QPixmap, QImage, QPainter, QPen, QColor, QFont

# 导入日志系统
try:
    from logger_system import log_info, log_error, log_warning, log_debug, safe_print
except ImportError:
    # 备用函数
    def log_info(msg): print(msg)
    def log_error(msg): print(f"ERROR: {msg}")
    def log_warning(msg): print(f"WARNING: {msg}")
    def log_debug(msg): print(f"DEBUG: {msg}")
    def safe_print(*args, **kwargs): print(*args, **kwargs)

class MultiRectangleSelector(QDialog):
    """多矩形字幕区域选择器"""
    regions_selected = pyqtSignal(list)  # 发送选定的多个区域列表
    
    def __init__(self, video_path, parent=None):
        super().__init__(parent)
        self.video_path = video_path
        self.current_frame = None
        self.scaled_frame = None
        self.scale_factor = 1.0
        self.cap = None
        
        # 矩形选择相关
        self.rectangles = []  # 存储所有矩形 [(x1, y1, x2, y2), ...]
        self.current_rect = None  # 当前正在绘制的矩形
        self.selected_rect_index = -1  # 当前选中的矩形索引
        self.is_drawing = False
        self.is_moving = False
        self.is_resizing = False
        self.start_point = None
        self.resize_handle = None  # 调整大小的控制点

        # 图像显示相关
        self.image_offset_x = 0  # 图像在QLabel中的X偏移
        self.image_offset_y = 0  # 图像在QLabel中的Y偏移
        self.image_display_width = 0  # 图像显示宽度
        self.image_display_height = 0  # 图像显示高度

        # 操作模式
        self.operation_mode = "draw"  # draw, select, move, resize
        
        # 颜色列表用于区分不同矩形
        self.colors = [
            QColor(255, 0, 0),    # 红色
            QColor(0, 255, 0),    # 绿色
            QColor(0, 0, 255),    # 蓝色
            QColor(255, 255, 0),  # 黄色
            QColor(255, 0, 255),  # 紫色
            QColor(0, 255, 255),  # 青色
            QColor(255, 128, 0),  # 橙色
            QColor(128, 0, 255),  # 紫罗兰
        ]
        
        self.init_ui()
        self.load_video_frame()

        # 设置焦点策略以接收键盘事件
        self.setFocusPolicy(Qt.StrongFocus)
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("多矩形字幕区域选择器")
        self.setGeometry(100, 100, 1200, 800)
        
        # 主布局
        main_layout = QHBoxLayout(self)
        
        # 左侧：视频显示区域
        left_widget = QFrame()
        left_layout = QVBoxLayout(left_widget)
        
        # 视频帧显示标签
        self.frame_label = QLabel()
        self.frame_label.setMinimumSize(640, 480)
        self.frame_label.setStyleSheet("""
            QLabel {
                border: 2px solid #3498db;
                border-radius: 8px;
                background-color: #f8f9fa;
            }
        """)
        self.frame_label.setAlignment(Qt.AlignCenter)
        self.frame_label.mousePressEvent = self.mouse_press_event
        self.frame_label.mouseMoveEvent = self.mouse_move_event
        self.frame_label.mouseReleaseEvent = self.mouse_release_event
        left_layout.addWidget(self.frame_label)
        
        # 帧选择滑块
        frame_control_layout = QHBoxLayout()
        frame_control_layout.addWidget(QLabel("选择帧:"))
        
        self.frame_slider = QSlider(Qt.Horizontal)
        self.frame_slider.setMinimum(0)
        self.frame_slider.setMaximum(100)
        self.frame_slider.setValue(0)
        self.frame_slider.valueChanged.connect(self.change_frame)
        frame_control_layout.addWidget(self.frame_slider)
        
        self.frame_label_text = QLabel("帧 0")
        frame_control_layout.addWidget(self.frame_label_text)
        
        left_layout.addLayout(frame_control_layout)
        
        # 操作说明
        instruction_label = QLabel("""
        <b>操作说明:</b><br>
        • <b>绘制矩形:</b> 在空白区域拖拽鼠标<br>
        • <b>选择矩形:</b> 点击矩形选中（黄色控制点）<br>
        • <b>移动矩形:</b> 拖拽矩形内部区域<br>
        • <b>调整大小:</b> 拖拽黄色控制点<br>
        • <b>删除矩形:</b> 选中后按Delete键或点击列表项<br>
        • <b>快捷键:</b> Delete删除, Esc取消选择
        """)
        instruction_label.setStyleSheet("""
            QLabel {
                background-color: #e3f2fd;
                padding: 10px;
                border-radius: 6px;
                font-size: 12px;
            }
        """)
        left_layout.addWidget(instruction_label)
        
        main_layout.addWidget(left_widget, 2)
        
        # 右侧：控制面板
        right_widget = QFrame()
        right_widget.setMaximumWidth(300)
        right_layout = QVBoxLayout(right_widget)
        
        # 状态显示
        status_group = QGroupBox("操作状态")
        status_layout = QVBoxLayout(status_group)

        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #2c3e50;
                padding: 5px;
                background-color: #ecf0f1;
                border-radius: 4px;
            }
        """)
        status_layout.addWidget(self.status_label)

        self.mode_label = QLabel("模式: 绘制")
        self.mode_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: #7f8c8d;
                padding: 3px;
            }
        """)
        status_layout.addWidget(self.mode_label)

        right_layout.addWidget(status_group)

        # 矩形列表
        list_group = QGroupBox("选择的矩形区域")
        list_layout = QVBoxLayout(list_group)
        
        self.rectangle_list = QListWidget()
        self.rectangle_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #ddd;
                border-radius: 6px;
                background-color: white;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:selected {
                background-color: #e3f2fd;
            }
        """)
        self.rectangle_list.itemClicked.connect(self.on_rectangle_item_clicked)
        list_layout.addWidget(self.rectangle_list)
        
        # 清除所有按钮
        clear_btn = QPushButton("🗑️ 清除所有矩形")
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        clear_btn.clicked.connect(self.clear_all_rectangles)
        list_layout.addWidget(clear_btn)
        
        right_layout.addWidget(list_group)
        
        # 按钮区域
        button_layout = QVBoxLayout()
        
        # 确认按钮
        confirm_btn = QPushButton("✅ 确认选择")
        confirm_btn.setStyleSheet("""
            QPushButton {
                background-color: #4caf50;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        confirm_btn.clicked.connect(self.confirm_selection)
        button_layout.addWidget(confirm_btn)
        
        # 取消按钮
        cancel_btn = QPushButton("❌ 取消")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #757575;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #616161;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        right_layout.addLayout(button_layout)
        right_layout.addStretch()
        
        main_layout.addWidget(right_widget, 1)
    
    def load_video_frame(self):
        """加载视频帧"""
        try:
            self.cap = cv2.VideoCapture(self.video_path)
            if not self.cap.isOpened():
                QMessageBox.critical(self, "错误", "无法打开视频文件")
                return
            
            # 设置滑块最大值
            frame_count = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.frame_slider.setMaximum(frame_count - 1)
            
            # 加载第一帧
            self.change_frame(0)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载视频失败: {str(e)}")
    
    def change_frame(self, frame_number):
        """切换到指定帧"""
        if not self.cap:
            return
        
        try:
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            ret, frame = self.cap.read()
            
            if ret:
                self.current_frame = frame
                self.display_frame()
                self.frame_label_text.setText(f"帧 {frame_number}")
            
        except Exception as e:
            log_info(f"切换帧失败: {str(e)}")
    
    def display_frame(self):
        """显示当前帧"""
        if self.current_frame is None:
            return

        # 转换为RGB格式
        rgb_frame = cv2.cvtColor(self.current_frame, cv2.COLOR_BGR2RGB)
        h, w, ch = rgb_frame.shape

        # 计算缩放比例以适应显示区域
        label_size = self.frame_label.size()
        scale_w = label_size.width() / w
        scale_h = label_size.height() / h
        self.scale_factor = min(scale_w, scale_h, 1.0)  # 不放大，只缩小

        # 缩放图像
        new_w = int(w * self.scale_factor)
        new_h = int(h * self.scale_factor)
        self.scaled_frame = cv2.resize(rgb_frame, (new_w, new_h))

        # 计算图像在QLabel中的偏移（居中显示）
        self.image_display_width = new_w
        self.image_display_height = new_h
        self.image_offset_x = (label_size.width() - new_w) // 2
        self.image_offset_y = (label_size.height() - new_h) // 2

        # 转换为QImage并显示
        self.update_display()

    def mouse_to_image_coords(self, mouse_x, mouse_y):
        """将鼠标坐标转换为图像坐标"""
        # 减去图像偏移
        image_x = mouse_x - self.image_offset_x
        image_y = mouse_y - self.image_offset_y

        # 检查是否在图像范围内
        if (image_x < 0 or image_x >= self.image_display_width or
            image_y < 0 or image_y >= self.image_display_height):
            return None, None

        # 转换为原始图像坐标
        orig_x = int(image_x / self.scale_factor)
        orig_y = int(image_y / self.scale_factor)

        return orig_x, orig_y

    def image_to_display_coords(self, orig_x, orig_y):
        """将原始图像坐标转换为显示坐标"""
        display_x = int(orig_x * self.scale_factor) + self.image_offset_x
        display_y = int(orig_y * self.scale_factor) + self.image_offset_y
        return display_x, display_y

    def get_rect_at_point(self, x, y):
        """获取指定点处的矩形索引"""
        orig_x, orig_y = self.mouse_to_image_coords(x, y)
        if orig_x is None:
            return -1

        for i, (x1, y1, x2, y2) in enumerate(self.rectangles):
            if x1 <= orig_x <= x2 and y1 <= orig_y <= y2:
                return i
        return -1

    def get_resize_handle(self, x, y, rect_index):
        """获取调整大小的控制点"""
        if rect_index < 0 or rect_index >= len(self.rectangles):
            return None

        x1, y1, x2, y2 = self.rectangles[rect_index]
        dx1, dy1 = self.image_to_display_coords(x1, y1)
        dx2, dy2 = self.image_to_display_coords(x2, y2)

        handle_size = 8

        # 检查各个控制点
        handles = {
            'top-left': (dx1, dy1),
            'top-right': (dx2, dy1),
            'bottom-left': (dx1, dy2),
            'bottom-right': (dx2, dy2),
            'top': ((dx1 + dx2) // 2, dy1),
            'bottom': ((dx1 + dx2) // 2, dy2),
            'left': (dx1, (dy1 + dy2) // 2),
            'right': (dx2, (dy1 + dy2) // 2)
        }

        for handle, (hx, hy) in handles.items():
            if abs(x - hx) <= handle_size and abs(y - hy) <= handle_size:
                return handle

        return None

    def update_display(self):
        """更新显示，包括绘制矩形"""
        if self.scaled_frame is None:
            return

        # 创建一个足够大的画布来容纳图像和偏移
        label_size = self.frame_label.size()
        canvas = np.zeros((label_size.height(), label_size.width(), 3), dtype=np.uint8)
        canvas.fill(64)  # 深灰色背景

        # 将缩放后的图像放置到画布的正确位置
        h, w = self.scaled_frame.shape[:2]
        y_start = self.image_offset_y
        y_end = y_start + h
        x_start = self.image_offset_x
        x_end = x_start + w

        if (y_start >= 0 and y_end <= canvas.shape[0] and
            x_start >= 0 and x_end <= canvas.shape[1]):
            canvas[y_start:y_end, x_start:x_end] = self.scaled_frame

        # 创建QImage
        q_image = QImage(canvas.data, canvas.shape[1], canvas.shape[0],
                        canvas.shape[1] * 3, QImage.Format_RGB888)
        pixmap = QPixmap.fromImage(q_image)

        # 在pixmap上绘制矩形
        painter = QPainter(pixmap)

        # 绘制已确认的矩形
        for i, rect in enumerate(self.rectangles):
            x1, y1, x2, y2 = rect
            dx1, dy1 = self.image_to_display_coords(x1, y1)
            dx2, dy2 = self.image_to_display_coords(x2, y2)

            # 选择颜色和样式
            color = self.colors[i % len(self.colors)]
            if i == self.selected_rect_index:
                pen = QPen(color, 4)  # 选中的矩形更粗
            else:
                pen = QPen(color, 2)

            painter.setPen(pen)
            painter.drawRect(dx1, dy1, dx2 - dx1, dy2 - dy1)

            # 绘制矩形编号
            painter.setFont(QFont("Arial", 12, QFont.Bold))
            painter.setPen(QPen(QColor(255, 255, 255)))
            painter.drawText(dx1 + 5, dy1 + 20, f"{i+1}")

            # 如果是选中的矩形，绘制调整大小的控制点
            if i == self.selected_rect_index:
                self.draw_resize_handles(painter, dx1, dy1, dx2, dy2)

        # 绘制当前正在绘制的矩形
        if self.current_rect:
            pen = QPen(QColor(255, 255, 255), 2, Qt.DashLine)
            painter.setPen(pen)
            x1, y1, x2, y2 = self.current_rect
            painter.drawRect(x1, y1, x2 - x1, y2 - y1)

        painter.end()
        self.frame_label.setPixmap(pixmap)

    def draw_resize_handles(self, painter, x1, y1, x2, y2):
        """绘制调整大小的控制点"""
        handle_size = 6
        handle_color = QColor(255, 255, 0)  # 黄色控制点

        painter.setPen(QPen(handle_color, 2))
        painter.setBrush(handle_color)

        # 八个控制点
        handles = [
            (x1, y1),  # 左上
            (x2, y1),  # 右上
            (x1, y2),  # 左下
            (x2, y2),  # 右下
            ((x1 + x2) // 2, y1),  # 上中
            ((x1 + x2) // 2, y2),  # 下中
            (x1, (y1 + y2) // 2),  # 左中
            (x2, (y1 + y2) // 2),  # 右中
        ]

        for hx, hy in handles:
            painter.drawEllipse(hx - handle_size//2, hy - handle_size//2,
                              handle_size, handle_size)
    
    def mouse_press_event(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            mouse_x, mouse_y = event.pos().x(), event.pos().y()

            # 检查是否点击在现有矩形上
            clicked_rect = self.get_rect_at_point(mouse_x, mouse_y)

            if clicked_rect >= 0:
                # 点击在现有矩形上
                self.selected_rect_index = clicked_rect

                # 检查是否点击在调整大小的控制点上
                self.resize_handle = self.get_resize_handle(mouse_x, mouse_y, clicked_rect)

                if self.resize_handle:
                    # 开始调整大小
                    self.is_resizing = True
                    self.operation_mode = "resize"
                else:
                    # 开始移动矩形
                    self.is_moving = True
                    self.operation_mode = "move"

                self.start_point = event.pos()
                self.update_display()
                self.update_status()
            else:
                # 点击在空白区域，开始绘制新矩形
                self.selected_rect_index = -1
                self.is_drawing = True
                self.operation_mode = "draw"
                self.start_point = event.pos()
                self.current_rect = None
                self.update_display()
                self.update_status("开始绘制新矩形...")
    
    def mouse_move_event(self, event):
        """鼠标移动事件"""
        mouse_x, mouse_y = event.pos().x(), event.pos().y()

        if self.is_drawing and self.start_point:
            # 绘制新矩形
            x1 = min(self.start_point.x(), mouse_x)
            y1 = min(self.start_point.y(), mouse_y)
            x2 = max(self.start_point.x(), mouse_x)
            y2 = max(self.start_point.y(), mouse_y)

            self.current_rect = (x1, y1, x2, y2)
            self.update_display()

        elif self.is_moving and self.selected_rect_index >= 0:
            # 移动矩形
            if self.start_point:
                dx = mouse_x - self.start_point.x()
                dy = mouse_y - self.start_point.y()

                # 获取原始矩形坐标
                x1, y1, x2, y2 = self.rectangles[self.selected_rect_index]

                # 计算移动量（转换为原始图像坐标）
                dx_orig = dx / self.scale_factor
                dy_orig = dy / self.scale_factor

                # 更新矩形位置
                new_x1 = x1 + dx_orig
                new_y1 = y1 + dy_orig
                new_x2 = x2 + dx_orig
                new_y2 = y2 + dy_orig

                # 确保矩形在图像范围内
                if self.current_frame is not None:
                    h, w = self.current_frame.shape[:2]
                    if new_x1 >= 0 and new_x2 <= w and new_y1 >= 0 and new_y2 <= h:
                        self.rectangles[self.selected_rect_index] = (new_x1, new_y1, new_x2, new_y2)
                        self.start_point = event.pos()
                        self.update_display()
                        self.update_rectangle_list()

        elif self.is_resizing and self.selected_rect_index >= 0 and self.resize_handle:
            # 调整矩形大小
            self.resize_rectangle(mouse_x, mouse_y)

        else:
            # 更新鼠标光标
            self.update_cursor(mouse_x, mouse_y)

    def resize_rectangle(self, mouse_x, mouse_y):
        """调整矩形大小"""
        if self.selected_rect_index < 0 or not self.resize_handle:
            return

        # 获取原始坐标
        orig_x, orig_y = self.mouse_to_image_coords(mouse_x, mouse_y)
        if orig_x is None:
            return

        x1, y1, x2, y2 = self.rectangles[self.selected_rect_index]

        # 根据控制点类型调整矩形
        if self.resize_handle == 'top-left':
            new_x1, new_y1 = orig_x, orig_y
            new_x2, new_y2 = x2, y2
        elif self.resize_handle == 'top-right':
            new_x1, new_y1 = x1, orig_y
            new_x2, new_y2 = orig_x, y2
        elif self.resize_handle == 'bottom-left':
            new_x1, new_y1 = orig_x, y1
            new_x2, new_y2 = x2, orig_y
        elif self.resize_handle == 'bottom-right':
            new_x1, new_y1 = x1, y1
            new_x2, new_y2 = orig_x, orig_y
        elif self.resize_handle == 'top':
            new_x1, new_y1 = x1, orig_y
            new_x2, new_y2 = x2, y2
        elif self.resize_handle == 'bottom':
            new_x1, new_y1 = x1, y1
            new_x2, new_y2 = x2, orig_y
        elif self.resize_handle == 'left':
            new_x1, new_y1 = orig_x, y1
            new_x2, new_y2 = x2, y2
        elif self.resize_handle == 'right':
            new_x1, new_y1 = x1, y1
            new_x2, new_y2 = orig_x, y2
        else:
            return

        # 确保矩形有效（左上角在右下角的左上方）
        if new_x1 > new_x2:
            new_x1, new_x2 = new_x2, new_x1
        if new_y1 > new_y2:
            new_y1, new_y2 = new_y2, new_y1

        # 确保矩形有最小尺寸
        min_size = 10
        if new_x2 - new_x1 < min_size or new_y2 - new_y1 < min_size:
            return

        # 确保矩形在图像范围内
        if self.current_frame is not None:
            h, w = self.current_frame.shape[:2]
            new_x1 = max(0, min(new_x1, w - min_size))
            new_y1 = max(0, min(new_y1, h - min_size))
            new_x2 = max(new_x1 + min_size, min(new_x2, w))
            new_y2 = max(new_y1 + min_size, min(new_y2, h))

        # 更新矩形
        self.rectangles[self.selected_rect_index] = (new_x1, new_y1, new_x2, new_y2)
        self.update_display()
        self.update_rectangle_list()

    def update_cursor(self, mouse_x, mouse_y):
        """更新鼠标光标"""
        # 检查是否在矩形上
        rect_index = self.get_rect_at_point(mouse_x, mouse_y)

        if rect_index >= 0:
            # 检查是否在调整大小的控制点上
            handle = self.get_resize_handle(mouse_x, mouse_y, rect_index)

            if handle:
                # 设置调整大小的光标
                if handle in ['top-left', 'bottom-right']:
                    self.frame_label.setCursor(Qt.SizeFDiagCursor)
                elif handle in ['top-right', 'bottom-left']:
                    self.frame_label.setCursor(Qt.SizeBDiagCursor)
                elif handle in ['top', 'bottom']:
                    self.frame_label.setCursor(Qt.SizeVerCursor)
                elif handle in ['left', 'right']:
                    self.frame_label.setCursor(Qt.SizeHorCursor)
            else:
                # 在矩形内，设置移动光标
                self.frame_label.setCursor(Qt.SizeAllCursor)
        else:
            # 不在矩形上，设置默认光标
            self.frame_label.setCursor(Qt.CrossCursor)
    
    def mouse_release_event(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            if self.is_drawing:
                # 完成绘制新矩形
                self.is_drawing = False

                if self.current_rect:
                    # 转换为原始图像坐标
                    x1, y1, x2, y2 = self.current_rect

                    # 计算相对于图像的坐标
                    orig_x1, orig_y1 = self.mouse_to_image_coords(x1, y1)
                    orig_x2, orig_y2 = self.mouse_to_image_coords(x2, y2)

                    if orig_x1 is not None and orig_x2 is not None:
                        # 确保矩形有最小尺寸
                        if abs(orig_x2 - orig_x1) > 10 and abs(orig_y2 - orig_y1) > 10:
                            self.rectangles.append((orig_x1, orig_y1, orig_x2, orig_y2))
                            self.selected_rect_index = len(self.rectangles) - 1
                            self.update_rectangle_list()
                            self.update_status(f"已创建矩形 {len(self.rectangles)}")

                    self.current_rect = None
                    self.operation_mode = "select"
                    self.update_display()
                    self.update_status()

            elif self.is_moving:
                # 完成移动
                self.is_moving = False
                self.operation_mode = "select"

            elif self.is_resizing:
                # 完成调整大小
                self.is_resizing = False
                self.resize_handle = None
                self.operation_mode = "select"

            # 重置起始点
            self.start_point = None

    def keyPressEvent(self, event):
        """键盘按键事件"""
        if event.key() == Qt.Key_Delete:
            # 删除选中的矩形
            if self.selected_rect_index >= 0:
                self.delete_selected_rectangle()
        elif event.key() == Qt.Key_Escape:
            # 取消选择
            self.selected_rect_index = -1
            self.update_display()
        else:
            super().keyPressEvent(event)

    def delete_selected_rectangle(self):
        """删除选中的矩形"""
        if 0 <= self.selected_rect_index < len(self.rectangles):
            del self.rectangles[self.selected_rect_index]
            self.selected_rect_index = -1
            self.update_rectangle_list()
            self.update_display()
    
    def update_rectangle_list(self):
        """更新矩形列表显示"""
        self.rectangle_list.clear()
        
        for i, rect in enumerate(self.rectangles):
            x1, y1, x2, y2 = rect
            width = x2 - x1
            height = y2 - y1
            
            item_text = f"矩形 {i+1}: ({x1}, {y1}) - ({x2}, {y2})\n尺寸: {width} × {height}"
            item = QListWidgetItem(item_text)
            
            # 设置颜色
            color = self.colors[i % len(self.colors)]
            item.setBackground(QColor(color.red(), color.green(), color.blue(), 50))
            
            self.rectangle_list.addItem(item)
    
    def on_rectangle_item_clicked(self, item):
        """点击矩形列表项"""
        row = self.rectangle_list.row(item)

        # 双击删除，单击选择
        if hasattr(self, '_last_click_time') and hasattr(self, '_last_click_row'):
            import time
            current_time = time.time()
            if (current_time - self._last_click_time < 0.5 and
                self._last_click_row == row):
                # 双击删除
                reply = QMessageBox.question(self, "删除矩形",
                                           f"确定要删除矩形 {row + 1} 吗？",
                                           QMessageBox.Yes | QMessageBox.No)

                if reply == QMessageBox.Yes:
                    del self.rectangles[row]
                    self.selected_rect_index = -1
                    self.update_rectangle_list()
                    self.update_display()
                return

        # 单击选择
        self.selected_rect_index = row
        self.update_display()

        # 记录点击时间和行号
        import time
        self._last_click_time = time.time()
        self._last_click_row = row

    def update_status(self, message=None):
        """更新状态显示"""
        if message:
            self.status_label.setText(message)

        # 更新模式显示
        if self.operation_mode == "draw":
            mode_text = "模式: 绘制矩形"
        elif self.operation_mode == "select":
            if self.selected_rect_index >= 0:
                mode_text = f"模式: 已选择矩形 {self.selected_rect_index + 1}"
            else:
                mode_text = "模式: 选择矩形"
        elif self.operation_mode == "move":
            mode_text = f"模式: 移动矩形 {self.selected_rect_index + 1}"
        elif self.operation_mode == "resize":
            mode_text = f"模式: 调整矩形 {self.selected_rect_index + 1} 大小"
        else:
            mode_text = "模式: 未知"

        self.mode_label.setText(mode_text)

        # 更新矩形数量状态
        if not message:
            count = len(self.rectangles)
            if count == 0:
                self.status_label.setText("未选择任何区域")
            else:
                self.status_label.setText(f"已选择 {count} 个矩形区域")
    
    def clear_all_rectangles(self):
        """清除所有矩形"""
        if self.rectangles:
            reply = QMessageBox.question(self, "清除所有矩形", 
                                       "确定要清除所有矩形吗？",
                                       QMessageBox.Yes | QMessageBox.No)
            
            if reply == QMessageBox.Yes:
                self.rectangles.clear()
                self.update_rectangle_list()
                self.update_display()
    
    def confirm_selection(self):
        """确认选择"""
        if not self.rectangles:
            QMessageBox.warning(self, "警告", "请至少选择一个矩形区域！")
            return
        
        # 发送选择的区域
        self.regions_selected.emit(self.rectangles)
        self.accept()
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.cap:
            self.cap.release()
        super().closeEvent(event)
