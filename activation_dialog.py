"""
激活码验证对话框
"""

import sys
import os
import json
import requests
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QPushButton, QTextEdit, QGroupBox,
                             QMessageBox, QProgressBar, QFrame, QApplication)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QIcon, QPainter, QPen, QBrush, QColor

from machine_code_generator import get_machine_code, MachineCodeGenerator, get_hardware_info
from api_manager import api_manager

# 导入日志系统
try:
    from logger_system import log_info, log_error, log_warning, log_debug, safe_print
except ImportError:
    # 备用函数
    def log_info(msg): print(msg)
    def log_error(msg): print(f"ERROR: {msg}")
    def log_warning(msg): print(f"WARNING: {msg}")
    def log_debug(msg): print(f"DEBUG: {msg}")
    def safe_print(*args, **kwargs): print(*args, **kwargs)


class ActivationThread(QThread):
    """激活验证线程"""
    finished = pyqtSignal(bool, str)  # 成功/失败, 消息

    def __init__(self, machine_code, activation_code, server_url, machine_details=None):
        super().__init__()
        self.machine_code = machine_code
        self.activation_code = activation_code
        self.machine_details = machine_details
        # 设置API管理器的服务器URL
        api_manager.set_base_url(server_url)

    def run(self):
        try:
            # 使用API管理器进行激活验证，包含机器详细信息
            result = api_manager.verify_activation(
                self.activation_code,
                self.machine_code,
                self.machine_details
            )

            if result.get('success', False):
                self.finished.emit(True, result.get('message', '激活成功'))
            else:
                self.finished.emit(False, result.get('message', '激活失败'))

        except Exception as e:
            self.finished.emit(False, f"激活验证失败: {str(e)}")


class ActivationDialog(QDialog):
    """激活码验证对话框"""
    
    def __init__(self, parent=None, server_url="http://api_script.pg-code-go.com"):
        super().__init__(parent)
        self.server_url = server_url
        self.machine_code = ""
        self.activation_thread = None
        self.init_ui()
        self.load_machine_code()
        self.load_activation_status()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("软件激活验证")
        self.setFixedSize(700, 500)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)

        # 设置窗口图标和应用程序图标
        self.setup_dialog_icon()

        # 确保对话框在任务栏显示
        # 移除 WindowStaysOnTopHint 以避免影响任务栏显示
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint | Qt.WindowTitleHint)

        # 确保对话框可以获得焦点和在任务栏显示
        self.setAttribute(Qt.WA_ShowWithoutActivating, False)
        self.setAttribute(Qt.WA_DeleteOnClose, False)
        
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel("软件激活验证")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        main_layout.addWidget(title_label)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(line)
        
        # 机器码组
        machine_group = QGroupBox("机器码信息")
        machine_layout = QVBoxLayout()
        
        machine_label = QLabel("您的机器码:")
        machine_label.setStyleSheet("font-weight: bold; color: #34495e;")
        machine_layout.addWidget(machine_label)
        
        self.machine_code_edit = QTextEdit()
        self.machine_code_edit.setMaximumHeight(60)
        self.machine_code_edit.setReadOnly(True)
        self.machine_code_edit.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
            }
        """)
        machine_layout.addWidget(self.machine_code_edit)
        
        # 复制按钮
        copy_btn = QPushButton("复制机器码")
        copy_btn.clicked.connect(self.copy_machine_code)
        copy_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        machine_layout.addWidget(copy_btn)
        
        machine_group.setLayout(machine_layout)
        main_layout.addWidget(machine_group)
        
        # 激活码组
        activation_group = QGroupBox("激活码验证")
        activation_layout = QVBoxLayout()
        
        activation_label = QLabel("请输入激活码:")
        activation_label.setStyleSheet("font-weight: bold; color: #34495e;")
        activation_layout.addWidget(activation_label)
        
        self.activation_code_edit = QLineEdit()
        self.activation_code_edit.setPlaceholderText("请输入您的激活码...")
        self.activation_code_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #dee2e6;
                border-radius: 4px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #007bff;
            }
        """)
        activation_layout.addWidget(self.activation_code_edit)
        
        activation_group.setLayout(activation_layout)
        main_layout.addWidget(activation_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #dee2e6;
                border-radius: 4px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #007bff;
                border-radius: 2px;
            }
        """)
        main_layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #6c757d; font-style: italic;")
        main_layout.addWidget(self.status_label)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        self.verify_btn = QPushButton("验证激活码")
        self.verify_btn.clicked.connect(self.verify_activation)
        self.verify_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        button_layout.addWidget(self.verify_btn)
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        button_layout.addWidget(self.close_btn)
        
        main_layout.addLayout(button_layout)
        
        self.setLayout(main_layout)

    def setup_dialog_icon(self):
        """设置对话框图标"""
        try:
            # 尝试设置窗口图标
            icon_path = os.path.join(os.path.dirname(__file__), 'img', 'logo.ico')
            if os.path.exists(icon_path):
                icon = QIcon(icon_path)
                self.setWindowIcon(icon)
                log_info(f"✅ 激活对话框图标设置成功: {icon_path}")
            else:
                # 创建默认图标
                pixmap = QPixmap(32, 32)
                pixmap.fill(QColor(52, 152, 219))  # 蓝色背景

                # 绘制简单的锁图标
                painter = QPainter(pixmap)
                painter.setRenderHint(QPainter.Antialiasing)
                painter.setPen(QPen(QColor(255, 255, 255), 2))
                painter.setBrush(QBrush(QColor(255, 255, 255)))

                # 绘制锁的主体
                painter.drawRect(10, 16, 12, 10)
                # 绘制锁的环
                painter.setBrush(QBrush())
                painter.drawEllipse(12, 8, 8, 8)

                painter.end()

                icon = QIcon(pixmap)
                self.setWindowIcon(icon)
                log_info(f"✅ 激活对话框默认图标设置成功")

            # 确保应用程序也有图标
            app = QApplication.instance()
            if app and not app.windowIcon().isNull():
                # 如果应用程序已经有图标，使用应用程序的图标
                self.setWindowIcon(app.windowIcon())

        except Exception as e:
            log_warning(f"⚠️ 设置激活对话框图标失败: {e}")

    def load_machine_code(self):
        """加载机器码"""
        try:
            self.machine_code = get_machine_code()
            self.machine_code_edit.setText(self.machine_code)
            self.status_label.setText("机器码已生成，请联系管理员获取激活码")
        except Exception as e:
            self.machine_code_edit.setText(f"机器码生成失败: {str(e)}")
            self.status_label.setText("机器码生成失败")
    
    def copy_machine_code(self):
        """复制机器码到剪贴板"""
        clipboard = QApplication.clipboard()
        clipboard.setText(self.machine_code)
        self.status_label.setText("机器码已复制到剪贴板")
        
        # 3秒后清除状态
        QTimer.singleShot(3000, lambda: self.status_label.setText(""))
    
    def verify_activation(self):
        """验证激活码"""
        activation_code = self.activation_code_edit.text().strip()

        if not activation_code:
            QMessageBox.warning(self, "警告", "请输入激活码")
            return

        # 去除激活码格式验证，允许任何格式的激活码
        # if not self.validate_activation_code_format(activation_code):
        #     QMessageBox.warning(self, "格式错误", "激活码格式不正确\n正确格式：XXXX-XXXX-XXXX-XXXXX")
        #     return

        if not self.machine_code:
            QMessageBox.warning(self, "警告", "机器码获取失败，无法验证")
            return

        # 开始验证
        self.verify_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        self.status_label.setText("正在验证激活码...")

        # 收集机器详细信息
        try:
            machine_generator = MachineCodeGenerator()
            machine_details = machine_generator.collect_hardware_info()
            log_info(f"📊 收集到机器详细信息: {len(machine_details)} 项")
        except Exception as e:
            log_warning(f"⚠️ 收集机器详细信息失败: {e}")
            machine_details = None

        # 启动验证线程
        self.activation_thread = ActivationThread(
            self.machine_code,
            activation_code,
            self.server_url,
            machine_details
        )
        self.activation_thread.finished.connect(self.on_verification_finished)
        self.activation_thread.start()

    def validate_activation_code_format(self, activation_code):
        """验证激活码格式"""
        import re
        # 激活码格式：XXXX-XXXX-XXXX-XXXXX (4-4-4-5位字母数字组合，只接受大写)
        pattern = r'^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{5}$'
        return bool(re.match(pattern, activation_code))
    
    def on_verification_finished(self, success, message):
        """验证完成回调"""
        self.verify_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        if success:
            self.status_label.setText("激活成功!")
            self.status_label.setStyleSheet("color: #28a745; font-weight: bold;")
            
            # 保存激活状态
            self.save_activation_status(True, self.activation_code_edit.text().strip())
            
            QMessageBox.information(self, "激活成功", message)
            self.accept()  # 关闭对话框并返回成功
        else:
            self.status_label.setText("激活失败")
            self.status_label.setStyleSheet("color: #dc3545; font-weight: bold;")
            QMessageBox.warning(self, "激活失败", message)
    
    def get_config_file_path(self):
        """获取配置文件路径，确保在打包后的exe中也能正常工作"""
        try:
            import sys
            import os

            # 如果是打包后的exe程序
            if getattr(sys, 'frozen', False):
                # 使用exe文件所在目录
                app_dir = os.path.dirname(sys.executable)
            else:
                # 开发环境，使用脚本文件所在目录
                app_dir = os.path.dirname(__file__)

            config_file = os.path.join(app_dir, 'activation_config.json')

            # 测试是否可写
            try:
                test_file = os.path.join(app_dir, 'test_write.tmp')
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
                log_info(f"📁 配置文件路径: {config_file}")
                return config_file
            except (PermissionError, OSError):
                # 如果exe目录不可写，使用用户数据目录
                import tempfile
                user_data_dir = os.path.join(tempfile.gettempdir(), 'VideoDeduplicationTool')
                os.makedirs(user_data_dir, exist_ok=True)
                config_file = os.path.join(user_data_dir, 'activation_config.json')
                log_info(f"📁 使用用户数据目录: {config_file}")
                return config_file

        except Exception as e:
            log_error(f"❌ 获取配置文件路径失败: {e}")
            # 回退到当前目录
            return 'activation_config.json'

    def save_activation_status(self, activated, activation_code):
        """保存激活状态"""
        try:
            import datetime
            config_file = self.get_config_file_path()
            config = {
                'activated': activated,
                # 不再保存机器码到配置文件中，机器码应该每次动态生成
                'activation_code': activation_code,
                'activation_time': datetime.datetime.now().isoformat()
            }

            # 确保目录存在
            config_dir = os.path.dirname(config_file)
            if config_dir and not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            log_info(f"✅ 激活状态已保存到: {config_file}")
        except Exception as e:
            log_error(f"❌ 保存激活状态失败: {e}")
            # 尝试保存到当前目录作为备用
            try:
                backup_config = 'activation_config.json'
                with open(backup_config, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)
                log_info(f"✅ 激活状态已保存到备用位置: {backup_config}")
            except Exception as backup_e:
                log_error(f"❌ 备用保存也失败: {backup_e}")
    
    def load_activation_status(self):
        """加载激活状态"""
        try:
            import datetime
            config_file = self.get_config_file_path()
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 检查是否已激活且有激活码
                if config.get('activated') and config.get('activation_code'):
                    activation_code = config.get('activation_code')

                    # 每次都验证激活码是否仍然有效（与服务器验证）
                    log_info(f"🔍 验证本地保存的激活码: {activation_code}")
                    if not self.verify_activation_code_with_server(activation_code):
                        log_error(f"❌ 激活码服务器验证失败，需要重新激活")
                        # 清除无效的本地配置
                        self.clear_local_activation_config()
                        return

                    # 显示已激活状态
                    activation_time_str = config.get('activation_time')
                    if activation_time_str:
                        try:
                            activation_time = datetime.datetime.fromisoformat(activation_time_str)
                            log_info(f"✅ 本地激活配置有效，激活时间: {activation_time}")
                        except ValueError:
                            log_warning(f"⚠️ 激活时间格式无效，但继续使用本地配置")

                    self.activation_code_edit.setText(activation_code)
                    self.status_label.setText("软件已激活")
                    self.status_label.setStyleSheet("color: #28a745; font-weight: bold;")
                    self.verify_btn.setText("重新验证")
                    log_info(f"✅ 本地激活状态验证通过")
                else:
                    log_error(f"❌ 本地激活配置验证失败：未激活或无激活码")
            else:
                log_info(f"📄 本地激活配置文件不存在")
        except Exception as e:
            log_info(f"加载激活状态失败: {e}")

    def verify_activation_code_with_server(self, activation_code):
        """与服务器验证激活码是否有效"""
        try:
            # 设置API管理器的服务器URL
            api_manager.set_base_url(self.server_url)

            # 收集机器详细信息
            try:
                machine_generator = MachineCodeGenerator()
                machine_details = machine_generator.collect_hardware_info()
                log_info(f"📊 服务器验证时收集到机器详细信息: {len(machine_details)} 项")
            except Exception as e:
                log_warning(f"⚠️ 服务器验证时收集机器详细信息失败: {e}")
                machine_details = None

            # 使用API管理器进行验证，包含机器详细信息
            result = api_manager.verify_activation(activation_code, self.machine_code, machine_details)

            success = result.get('success', False)
            if success:
                # 验证服务器返回的时间戳，防止重放攻击
                server_timestamp = result.get('timestamp')
                if server_timestamp:
                    import time
                    current_timestamp = int(time.time())
                    if abs(current_timestamp - server_timestamp) > 300:  # 5分钟容差
                        log_warning(f"⚠️ 时间戳验证失败，可能存在重放攻击")
                        return False

                # 验证成功
                log_info(f"✅ 服务器验证成功: {result.get('message', '')}")
            else:
                log_error(f"❌ 服务器验证失败: {result.get('message', '未知错误')}")

                # 网络连接错误时不允许离线使用
                error_type = result.get('error_type')
                if error_type in ['timeout', 'connection_error', 'request_error']:
                    log_error(f"❌ 网络连接失败，无法验证激活码: {result.get('message')}")
                    log_error("❌ 离线模式已禁用，必须在线验证激活码")
                    return False

            return success

        except Exception as e:
            log_warning(f"⚠️ 激活码服务器验证异常: {e}")
            return False

    def check_offline_grace_period(self):
        """检查离线宽限期（已禁用）"""
        log_error("❌ 离线模式已禁用，必须在线验证激活码")
        return False


    def clear_local_activation_config(self):
        """清除本地激活配置"""
        try:
            config_file = self.get_config_file_path()
            if os.path.exists(config_file):
                os.remove(config_file)
                log_info(f"🗑️ 已清除无效的本地激活配置")
        except Exception as e:
            log_warning(f"⚠️ 清除本地配置失败: {e}")


def show_activation_dialog(parent=None, server_url="http://api_script.pg-code-go.com"):
    """显示激活对话框"""
    dialog = ActivationDialog(parent, server_url)
    return dialog.exec_() == QDialog.Accepted


if __name__ == "__main__":
    app = QApplication(sys.argv)
    dialog = ActivationDialog()
    dialog.show()
    sys.exit(app.exec_())
