<template>
  <div class="activation-management">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <div class="header-left">
        <h2>激活码管理</h2>
        <p class="page-description">管理软件激活码的生成、启用、禁用等操作</p>
      </div>
      <div class="header-right">
        <el-button type="primary" icon="el-icon-plus" @click="showCreateDialog">
          生成激活码
        </el-button>
        <el-button type="warning" icon="el-icon-delete" @click="cleanupExpiredCodes">
          清理过期
        </el-button>
        <el-button type="info" icon="el-icon-refresh" @click="refreshData">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.totalCodes || 0 }}</div>
              <div class="stat-label">总激活码数</div>
            </div>
            <i class="el-icon-tickets stat-icon"></i>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.activatedCodes || 0 }}</div>
              <div class="stat-label">已激活</div>
            </div>
            <i class="el-icon-check stat-icon"></i>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.expiredCodes || 0 }}</div>
              <div class="stat-label">已过期</div>
            </div>
            <i class="el-icon-time stat-icon"></i>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.activeMachines || 0 }}</div>
              <div class="stat-label">活跃设备</div>
            </div>
            <i class="el-icon-monitor stat-icon"></i>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <el-card>
        <el-form :model="searchForm" inline>
          <el-form-item label="激活码">
            <el-input
              v-model="searchForm.activationCode"
              placeholder="请输入激活码"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="用户ID">
            <el-input
              v-model="searchForm.userId"
              placeholder="请输入用户ID"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px">
              <el-option label="未激活" :value="0" />
              <el-option label="已激活" :value="1" />
              <el-option label="已禁用" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleSearch">
              搜索
            </el-button>
            <el-button icon="el-icon-refresh" @click="resetSearch">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 激活码列表 -->
    <div class="table-section">
      <el-card>
        <el-table
          v-loading="tableLoading"
          :data="filteredTableData"
          stripe
          border
          style="width: 100%"
          @sort-change="handleSortChange"
        >
          <el-table-column prop="id" label="ID" width="80" sortable />
          <el-table-column prop="activationCode" label="激活码" width="200" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-tag
                type="info"
                size="small"
                class="clickable-activation-code"
                @click="copyActivationCode(scope.row.activationCode)"
                :title="'点击复制激活码: ' + scope.row.activationCode"
              >
                <i class="el-icon-document-copy" style="margin-right: 4px;"></i>
                {{ scope.row.activationCode }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="userId" label="用户ID" width="120" show-overflow-tooltip />
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template slot-scope="scope">
              <el-tag
                :type="getStatusTagType(scope.row.status)"
                size="small"
              >
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="machineCode" label="机器码" width="180" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-tag
                v-if="scope.row.machineCode"
                type="warning"
                size="small"
                class="clickable-machine-code"
                @click="copyMachineCode(scope.row.machineCode)"
                :title="'点击复制机器码: ' + scope.row.machineCode"
              >
                <i class="el-icon-document-copy" style="margin-right: 4px;"></i>
                {{ scope.row.machineCode }}
              </el-tag>
              <span v-else class="text-muted">未绑定</span>
            </template>
          </el-table-column>
          <el-table-column prop="maxActivations" label="最大激活次数" width="120" align="center" />
          <el-table-column prop="currentActivations" label="当前激活次数" width="120" align="center" />
          <el-table-column prop="createTime" label="创建时间" width="160" sortable>
            <template slot-scope="scope">
              {{ formatDateTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="expireTime" label="过期时间" width="160" sortable>
            <template slot-scope="scope">
              <span v-if="scope.row.expireTime" :class="{ 'text-danger': isExpired(scope.row.expireTime) }">
                {{ formatDateTime(scope.row.expireTime) }}
              </span>
              <span v-else class="text-muted">永不过期</span>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" show-overflow-tooltip />
          <el-table-column label="操作" width="280" fixed="right">
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.status === 2"
                type="success"
                size="mini"
                icon="el-icon-check"
                @click="enableCode(scope.row)"
              >
                启用
              </el-button>
              <el-button
                v-else-if="scope.row.status !== 2"
                type="warning"
                size="mini"
                icon="el-icon-close"
                @click="disableCode(scope.row)"
              >
                禁用
              </el-button>
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-view"
                @click="viewDetails(scope.row)"
              >
                详情
              </el-button>
              <el-button
                type="info"
                size="mini"
                icon="el-icon-edit"
                @click="editCode(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                size="mini"
                icon="el-icon-delete"
                @click="deleteCode(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          />
        </div>
      </el-card>
    </div>

    <!-- 生成激活码对话框 -->
    <CreateActivationCodeDialog
      :visible.sync="createDialogVisible"
      @success="handleCreateSuccess"
    />

    <!-- 激活码详情对话框 -->
    <ActivationCodeDetailDialog
      :visible.sync="detailDialogVisible"
      :activation-code="selectedActivationCode"
    />

    <!-- 编辑激活码对话框 -->
    <EditActivationCodeDialog
      :visible.sync="editDialogVisible"
      :activation-code="selectedActivationCode"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script>
import activationApi from '@/api/activation'
import CreateActivationCodeDialog from './components/CreateActivationCodeDialog'
import ActivationCodeDetailDialog from './components/ActivationCodeDetailDialog'
import EditActivationCodeDialog from './components/EditActivationCodeDialog'

export default {
  name: 'ActivationManagement',
  components: {
    CreateActivationCodeDialog,
    ActivationCodeDetailDialog,
    EditActivationCodeDialog
  },
  data() {
    return {
      // 表格数据
      tableData: [],
      tableLoading: false,
      
      // 统计数据
      statistics: {},
      
      // 搜索表单
      searchForm: {
        activationCode: '',
        userId: '',
        status: null
      },
      
      // 分页
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      
      // 排序
      sortConfig: {
        prop: 'createTime',
        order: 'descending'
      },
      
      // 对话框状态
      createDialogVisible: false,
      detailDialogVisible: false,
      editDialogVisible: false,
      selectedActivationCode: null
    }
  },
  computed: {
    // 过滤后的表格数据
    filteredTableData() {
      let data = [...this.tableData]
      
      // 搜索过滤
      if (this.searchForm.activationCode) {
        data = data.filter(item => 
          item.activationCode.toLowerCase().includes(this.searchForm.activationCode.toLowerCase())
        )
      }
      
      if (this.searchForm.userId) {
        data = data.filter(item => 
          item.userId && item.userId.toLowerCase().includes(this.searchForm.userId.toLowerCase())
        )
      }
      
      if (this.searchForm.status !== null && this.searchForm.status !== '') {
        data = data.filter(item => item.status === this.searchForm.status)
      }
      
      // 排序
      if (this.sortConfig.prop) {
        data.sort((a, b) => {
          const aVal = a[this.sortConfig.prop]
          const bVal = b[this.sortConfig.prop]
          
          if (this.sortConfig.order === 'ascending') {
            return aVal > bVal ? 1 : -1
          } else {
            return aVal < bVal ? 1 : -1
          }
        })
      }
      
      // 更新总数
      this.pagination.total = data.length
      
      // 分页
      const start = (this.pagination.currentPage - 1) * this.pagination.pageSize
      const end = start + this.pagination.pageSize
      
      return data.slice(start, end)
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    // 加载数据
    async loadData() {
      await Promise.all([
        this.loadActivationCodes(),
        this.loadStatistics()
      ])
    },
    
    // 加载激活码列表
    async loadActivationCodes() {
      this.tableLoading = true
      try {
        const response = await activationApi.getActivationCodeList()
        if (response.success) {
          this.tableData = response.data || []
        } else {
          this.$message.error(response.message || '获取激活码列表失败')
        }
      } catch (error) {
        console.error('加载激活码列表失败:', error)
        this.$message.error('获取激活码列表失败')
      } finally {
        this.tableLoading = false
      }
    },
    
    // 加载统计数据
    async loadStatistics() {
      try {
        const response = await activationApi.getActivationStatistics()
        if (response.success) {
          this.statistics = response.data || {}
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },
    
    // 刷新数据
    refreshData() {
      this.loadData()
    },
    
    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1
    },
    
    // 重置搜索
    resetSearch() {
      this.searchForm = {
        activationCode: '',
        userId: '',
        status: null
      }
      this.pagination.currentPage = 1
    },
    
    // 排序变化
    handleSortChange({ prop, order }) {
      this.sortConfig = { prop, order }
    },
    
    // 分页大小变化
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.currentPage = 1
    },
    
    // 当前页变化
    handleCurrentChange(val) {
      this.pagination.currentPage = val
    },
    
    // 显示创建对话框
    showCreateDialog() {
      this.createDialogVisible = true
    },
    
    // 创建成功回调
    handleCreateSuccess() {
      this.loadData()
    },
    
    // 启用激活码
    async enableCode(row) {
      try {
        const response = await activationApi.enableActivationCode(row.activationCode)
        if (response.success) {
          this.$message.success('激活码已启用')
          this.loadData()
        } else {
          this.$message.error(response.message || '启用失败')
        }
      } catch (error) {
        console.error('启用激活码失败:', error)
        this.$message.error('启用失败')
      }
    },
    
    // 禁用激活码
    async disableCode(row) {
      try {
        await this.$confirm('确定要禁用此激活码吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await activationApi.disableActivationCode(row.activationCode)
        if (response.success) {
          this.$message.success('激活码已禁用')
          this.loadData()
        } else {
          this.$message.error(response.message || '禁用失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('禁用激活码失败:', error)
          this.$message.error('禁用失败')
        }
      }
    },
    
    // 查看详情
    viewDetails(row) {
      this.selectedActivationCode = row.activationCode
      this.detailDialogVisible = true
    },

    // 编辑激活码
    editCode(row) {
      this.selectedActivationCode = row.activationCode
      this.editDialogVisible = true
    },

    // 编辑成功回调
    handleEditSuccess() {
      this.loadData()
    },

    // 删除激活码
    async deleteCode(row) {
      try {
        await this.$confirm(
          `确定要删除激活码 "${row.activationCode}" 吗？\n\n此操作不可恢复，如果激活码已绑定设备，相关设备信息也会被删除。`,
          '删除确认',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'error',
            dangerouslyUseHTMLString: false
          }
        )

        const response = await activationApi.deleteActivationCode(row.activationCode)
        if (response.success) {
          this.$message.success('激活码已删除')
          this.loadData()
        } else {
          this.$message.error(response.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除激活码失败:', error)
          this.$message.error('删除失败')
        }
      }
    },
    
    // 清理过期激活码
    async cleanupExpiredCodes() {
      try {
        await this.$confirm('确定要清理所有过期的激活码吗？此操作不可恢复。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await activationApi.cleanupExpiredCodes()
        if (response.success) {
          this.$message.success(`清理完成，共删除 ${response.deletedCount} 个过期激活码`)
          this.loadData()
        } else {
          this.$message.error(response.message || '清理失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('清理过期激活码失败:', error)
          this.$message.error('清理失败')
        }
      }
    },
    
    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        0: 'info',     // 未激活
        1: 'success',  // 已激活
        2: 'danger'    // 已禁用
      }
      return typeMap[status] || 'info'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        0: '未激活',
        1: '已激活',
        2: '已禁用'
      }
      return textMap[status] || '未知'
    },
    
    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      return new Date(dateTime).toLocaleString('zh-CN')
    },
    
    // 检查是否过期
    isExpired(expireTime) {
      if (!expireTime) return false
      return new Date(expireTime) < new Date()
    },

    // 复制激活码到剪贴板
    async copyActivationCode(activationCode) {
      try {
        // 使用现代浏览器的 Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
          await navigator.clipboard.writeText(activationCode)
        } else {
          // 降级方案：使用传统的 document.execCommand
          const textArea = document.createElement('textarea')
          textArea.value = activationCode
          textArea.style.position = 'fixed'
          textArea.style.left = '-999999px'
          textArea.style.top = '-999999px'
          document.body.appendChild(textArea)
          textArea.focus()
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)
        }

        this.$message.success(`激活码已复制到剪贴板: ${activationCode}`)
      } catch (error) {
        console.error('复制激活码失败:', error)
        this.$message.error('复制失败，请手动复制激活码')
      }
    },

    // 复制机器码到剪贴板
    async copyMachineCode(machineCode) {
      try {
        // 使用现代浏览器的 Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
          await navigator.clipboard.writeText(machineCode)
        } else {
          // 降级方案：使用传统的 document.execCommand
          const textArea = document.createElement('textarea')
          textArea.value = machineCode
          textArea.style.position = 'fixed'
          textArea.style.left = '-999999px'
          textArea.style.top = '-999999px'
          document.body.appendChild(textArea)
          textArea.focus()
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)
        }

        this.$message.success(`机器码已复制到剪贴板: ${machineCode}`)
      } catch (error) {
        console.error('复制机器码失败:', error)
        this.$message.error('复制失败，请手动复制机器码')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.activation-management {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .header-left {
      h2 {
        margin: 0 0 5px 0;
        color: #303133;
        font-size: 24px;
        font-weight: 600;
      }

      .page-description {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }

    .header-right {
      .el-button {
        margin-left: 10px;
      }
    }
  }

  .statistics-cards {
    margin-bottom: 20px;

    .stat-card {
      position: relative;
      overflow: hidden;

      .stat-content {
        .stat-number {
          font-size: 28px;
          font-weight: bold;
          color: #409EFF;
          margin-bottom: 5px;
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
        }
      }

      .stat-icon {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 40px;
        color: #E4E7ED;
      }
    }
  }

  .search-section {
    margin-bottom: 20px;
  }

  .table-section {
    .pagination-wrapper {
      margin-top: 20px;
      text-align: right;
    }
  }

  .text-muted {
    color: #C0C4CC;
  }

  .text-danger {
    color: #F56C6C;
  }

  // 可点击的激活码样式
  .clickable-activation-code {
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;

    &:hover {
      background-color: #409EFF !important;
      color: white !important;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
    }

    &:active {
      transform: translateY(0);
    }

    .el-icon-document-copy {
      opacity: 0.7;
      transition: opacity 0.3s ease;
    }

    &:hover .el-icon-document-copy {
      opacity: 1;
    }
  }

  // 可点击的机器码样式
  .clickable-machine-code {
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;

    &:hover {
      background-color: #E6A23C !important;
      color: white !important;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
    }

    &:active {
      transform: translateY(0);
    }

    .el-icon-document-copy {
      opacity: 0.7;
      transition: opacity 0.3s ease;
    }

    &:hover .el-icon-document-copy {
      opacity: 1;
    }
  }
}
</style>
