package com.example.activation.service;

import com.example.activation.entity.ActivationCode;
import com.example.activation.entity.MachineInfo;
import com.example.activation.mapper.ActivationCodeMapper;
import com.example.activation.mapper.MachineInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 激活服务类
 */
@Service
public class ActivationService {
    
    @Autowired
    private ActivationCodeMapper activationCodeMapper;
    
    @Autowired
    private MachineInfoMapper machineInfoMapper;
    
    /**
     * 验证激活码
     */
    @Transactional
    public Map<String, Object> verifyActivation(String activationCode, String machineCode, Map<String, String> machineDetails) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 参数验证
            if (!StringUtils.hasText(activationCode)) {
                result.put("success", false);
                result.put("message", "激活码不能为空");
                return result;
            }
            
            if (!StringUtils.hasText(machineCode)) {
                result.put("success", false);
                result.put("message", "机器码不能为空");
                return result;
            }
            
            // 查询激活码
            ActivationCode code = activationCodeMapper.findByActivationCode(activationCode);
            if (code == null) {
                result.put("success", false);
                result.put("message", "激活码不存在");
                return result;
            }
            
            // 检查激活码状态
            if (code.isDisabled()) {
                result.put("success", false);
                result.put("message", "激活码已被禁用");
                return result;
            }
            
            if (code.isExpired()) {
                result.put("success", false);
                result.put("message", "激活码已过期");
                return result;
            }
            
            // 检查激活次数限制
            if (!code.canActivate()) {
                result.put("success", false);
                result.put("message", "激活码已达到最大激活次数");
                return result;
            }
            
            // 检查机器码绑定
            if (code.isActivated()) {
                // 已激活的激活码，检查机器码是否匹配
                if (!machineCode.equals(code.getMachineCode())) {
                    result.put("success", false);
                    result.put("message", "激活码已绑定其他设备");
                    return result;
                }
                
                // 机器码匹配，验证成功
                result.put("success", true);
                result.put("message", "设备已激活");
                result.put("activationTime", code.getActivationTime());
                return result;
            }
            
            // 首次激活
            LocalDateTime now = LocalDateTime.now();
            
            // 更新激活码状态
            code.setMachineCode(machineCode);
            code.setStatus(1); // 已激活
            code.setActivationTime(now);
            code.setUpdateTime(now);
            code.setCurrentActivations(code.getCurrentActivations() + 1);
            
            activationCodeMapper.update(code);
            
            // 更新或创建机器信息
            updateOrCreateMachineInfo(machineCode, machineDetails);
            
            result.put("success", true);
            result.put("message", "激活成功");
            result.put("activationTime", now);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "激活验证失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 更新或创建机器信息
     */
    private void updateOrCreateMachineInfo(String machineCode, Map<String, String> machineDetails) {
        MachineInfo machineInfo = machineInfoMapper.findByMachineCode(machineCode);
        LocalDateTime now = LocalDateTime.now();
        
        if (machineInfo == null) {
            // 创建新的机器信息
            machineInfo = new MachineInfo(machineCode);
            machineInfo.setActivationStatus(1);
            machineInfo.setFirstActivationTime(now);
            machineInfo.setLastActivationTime(now);
            machineInfo.setActivationCount(1);
            
            // 设置机器详细信息
            if (machineDetails != null) {
                machineInfo.setComputerName(machineDetails.get("computerName"));
                machineInfo.setOperatingSystem(machineDetails.get("operatingSystem"));
                machineInfo.setProcessor(machineDetails.get("processor"));
                machineInfo.setCpuId(machineDetails.get("cpuId"));
                machineInfo.setMotherboardSerial(machineDetails.get("motherboardSerial"));
                machineInfo.setBiosSerial(machineDetails.get("biosSerial"));
                machineInfo.setDiskSerial(machineDetails.get("diskSerial"));
                machineInfo.setMacAddress(machineDetails.get("macAddress"));
                machineInfo.setWindowsProductId(machineDetails.get("windowsProductId"));
                machineInfo.setMachineGuid(machineDetails.get("machineGuid"));
            }
            
            machineInfoMapper.insert(machineInfo);
        } else {
            // 更新现有机器信息
            machineInfo.setActivationStatus(1);
            machineInfo.setLastActivationTime(now);
            machineInfo.setUpdateTime(now);
            machineInfo.incrementActivationCount();
            
            // 更新机器详细信息
            if (machineDetails != null) {
                machineInfo.setComputerName(machineDetails.get("computerName"));
                machineInfo.setOperatingSystem(machineDetails.get("operatingSystem"));
                machineInfo.setProcessor(machineDetails.get("processor"));
                machineInfo.setCpuId(machineDetails.get("cpuId"));
                machineInfo.setMotherboardSerial(machineDetails.get("motherboardSerial"));
                machineInfo.setBiosSerial(machineDetails.get("biosSerial"));
                machineInfo.setDiskSerial(machineDetails.get("diskSerial"));
                machineInfo.setMacAddress(machineDetails.get("macAddress"));
                machineInfo.setWindowsProductId(machineDetails.get("windowsProductId"));
                machineInfo.setMachineGuid(machineDetails.get("machineGuid"));
            }
            
            machineInfoMapper.update(machineInfo);
        }
    }
    
    /**
     * 生成激活码
     */
    public String generateActivationCode() {
        String code;
        do {
            code = UUID.randomUUID().toString().replace("-", "").substring(0, 16).toUpperCase();
            // 格式化为 XXXX-XXXX-XXXX-XXXX
            code = code.substring(0, 4) + "-" + code.substring(4, 8) + "-" + 
                   code.substring(8, 12) + "-" + code.substring(12, 16);
        } while (activationCodeMapper.existsByActivationCode(code));
        
        return code;
    }
    
    /**
     * 创建激活码
     */
    public ActivationCode createActivationCode(String userId, LocalDateTime expireTime, Integer maxActivations, String remark) {
        ActivationCode activationCode = new ActivationCode();
        activationCode.setActivationCode(generateActivationCode());
        activationCode.setUserId(userId);
        activationCode.setStatus(0); // 未激活
        activationCode.setCreateTime(LocalDateTime.now());
        activationCode.setUpdateTime(LocalDateTime.now());
        activationCode.setExpireTime(expireTime);
        activationCode.setMaxActivations(maxActivations != null ? maxActivations : 1);
        activationCode.setCurrentActivations(0);
        activationCode.setRemark(remark);
        
        activationCodeMapper.insert(activationCode);
        return activationCode;
    }
    
    /**
     * 检查机器是否已激活
     */
    public boolean isMachineActivated(String machineCode) {
        return machineInfoMapper.isActivated(machineCode);
    }
    
    /**
     * 获取机器激活信息
     */
    public MachineInfo getMachineInfo(String machineCode) {
        return machineInfoMapper.findByMachineCode(machineCode);
    }
    
    /**
     * 获取激活码信息
     */
    public ActivationCode getActivationCode(String activationCode) {
        return activationCodeMapper.findByActivationCode(activationCode);
    }
    
    /**
     * 禁用激活码
     */
    @Transactional
    public boolean disableActivationCode(String activationCode) {
        ActivationCode code = activationCodeMapper.findByActivationCode(activationCode);
        if (code != null) {
            activationCodeMapper.disableActivationCode(code.getId(), LocalDateTime.now());
            
            // 同时禁用对应的机器
            if (StringUtils.hasText(code.getMachineCode())) {
                MachineInfo machineInfo = machineInfoMapper.findByMachineCode(code.getMachineCode());
                if (machineInfo != null) {
                    machineInfoMapper.disableMachine(machineInfo.getId(), LocalDateTime.now());
                }
            }
            return true;
        }
        return false;
    }
    
    /**
     * 启用激活码
     */
    @Transactional
    public boolean enableActivationCode(String activationCode) {
        ActivationCode code = activationCodeMapper.findByActivationCode(activationCode);
        if (code != null) {
            activationCodeMapper.enableActivationCode(code.getId(), LocalDateTime.now());
            
            // 同时启用对应的机器
            if (StringUtils.hasText(code.getMachineCode())) {
                MachineInfo machineInfo = machineInfoMapper.findByMachineCode(code.getMachineCode());
                if (machineInfo != null) {
                    machineInfoMapper.enableMachine(machineInfo.getId(), LocalDateTime.now());
                }
            }
            return true;
        }
        return false;
    }
    
    /**
     * 获取所有激活码
     */
    public List<ActivationCode> getAllActivationCodes() {
        return activationCodeMapper.findAll();
    }

    /**
     * 删除激活码
     */
    @Transactional
    public boolean deleteActivationCode(String activationCode) {
        try {
            ActivationCode code = activationCodeMapper.findByActivationCode(activationCode);
            if (code == null) {
                return false;
            }

            // 如果激活码已绑定机器，先删除机器信息
            if (StringUtils.hasText(code.getMachineCode())) {
                MachineInfo machineInfo = machineInfoMapper.findByMachineCode(code.getMachineCode());
                if (machineInfo != null) {
                    machineInfoMapper.deleteById(machineInfo.getId());
                }
            }

            // 删除激活码
            activationCodeMapper.deleteById(code.getId());
            return true;
        } catch (Exception e) {
            throw new RuntimeException("删除激活码失败: " + e.getMessage(), e);
        }
    }

    /**
     * 编辑激活码
     */
    @Transactional
    public ActivationCode updateActivationCode(String activationCode, String userId, LocalDateTime expireTime, Integer maxActivations, String remark) {
        try {
            System.out.println("服务层收到编辑请求:");
            System.out.println("激活码: " + activationCode);
            System.out.println("用户ID: " + userId);
            System.out.println("过期时间: " + expireTime);
            System.out.println("最大激活次数: " + maxActivations);
            System.out.println("备注: " + remark);

            ActivationCode code = activationCodeMapper.findByActivationCode(activationCode);
            if (code == null) {
                throw new RuntimeException("激活码不存在");
            }

            System.out.println("找到激活码，当前状态: " + code.getStatus());

            // 检查激活码是否已激活，如果已激活则不允许修改某些关键信息
            if (code.isActivated()) {
                // 已激活的激活码只能修改备注和过期时间
                if (expireTime != null) {
                    code.setExpireTime(expireTime);
                }
                if (remark != null) {
                    code.setRemark(remark);
                }
            } else {
                // 未激活的激活码可以修改所有信息
                if (userId != null) {
                    code.setUserId(userId);
                }
                if (expireTime != null) {
                    code.setExpireTime(expireTime);
                }
                if (maxActivations != null && maxActivations > 0) {
                    code.setMaxActivations(maxActivations);
                }
                if (remark != null) {
                    code.setRemark(remark);
                }
            }

            code.setUpdateTime(LocalDateTime.now());
            activationCodeMapper.update(code);

            return code;
        } catch (Exception e) {
            throw new RuntimeException("编辑激活码失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取所有机器信息
     */
    public List<MachineInfo> getAllMachineInfo() {
        return machineInfoMapper.findAll();
    }
    
    /**
     * 清理过期激活码
     */
    @Transactional
    public int cleanExpiredActivationCodes() {
        return activationCodeMapper.deleteExpiredCodes();
    }
    
    /**
     * 获取激活统计信息
     */
    public Map<String, Object> getActivationStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        stats.put("totalActivationCodes", activationCodeMapper.countAll());
        stats.put("activatedCodes", activationCodeMapper.countByStatus(1));
        stats.put("unactivatedCodes", activationCodeMapper.countByStatus(0));
        stats.put("disabledCodes", activationCodeMapper.countByStatus(2));
        stats.put("expiredCodes", activationCodeMapper.countExpiredCodes());
        
        stats.put("totalMachines", machineInfoMapper.countAll());
        stats.put("activatedMachines", machineInfoMapper.countByActivationStatus(1));
        stats.put("unactivatedMachines", machineInfoMapper.countByActivationStatus(0));
        stats.put("disabledMachines", machineInfoMapper.countByActivationStatus(2));
        
        return stats;
    }
}
