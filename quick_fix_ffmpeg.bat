@echo off
chcp 65001 >nul
echo ========================================
echo     视频混剪工具 FFmpeg 快速修复
echo ========================================
echo.

echo 正在检测FFmpeg配置...
echo.

REM 检查当前目录是否有ffmpeg.exe
if exist "ffmpeg.exe" (
    echo ✅ 发现本地FFmpeg: %cd%\ffmpeg.exe
    echo 正在测试FFmpeg...
    ffmpeg.exe -version >nul 2>&1
    if %errorlevel% == 0 (
        echo ✅ FFmpeg测试通过，配置正常！
        goto :end
    ) else (
        echo ❌ FFmpeg测试失败，文件可能损坏
        goto :download
    )
)

REM 检查系统PATH中是否有ffmpeg
echo 检查系统PATH中的FFmpeg...
ffmpeg -version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ 系统PATH中存在FFmpeg，配置正常！
    goto :end
)

REM 检查常见安装路径
echo 检查常见安装路径...
if exist "C:\ffmpeg\bin\ffmpeg.exe" (
    echo ✅ 发现系统FFmpeg: C:\ffmpeg\bin\ffmpeg.exe
    echo 建议将 C:\ffmpeg\bin 添加到系统PATH环境变量
    goto :end
)

echo ❌ 未找到FFmpeg，需要下载安装
echo.

:download
echo ========================================
echo           开始下载FFmpeg
echo ========================================
echo.
echo 正在从官方源下载FFmpeg精简版...
echo 下载地址: https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip
echo.

REM 使用PowerShell下载FFmpeg
echo 请稍候，正在下载...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip' -OutFile 'ffmpeg_temp.zip'}"

if not exist "ffmpeg_temp.zip" (
    echo ❌ 下载失败，请检查网络连接
    echo.
    echo 手动下载方法：
    echo 1. 访问: https://www.gyan.dev/ffmpeg/builds/
    echo 2. 下载 ffmpeg-release-essentials.zip
    echo 3. 解压后将 ffmpeg.exe 复制到程序目录
    goto :manual
)

echo ✅ 下载完成，正在解压...

REM 使用PowerShell解压文件
powershell -Command "& {Add-Type -AssemblyName System.IO.Compression.FileSystem; $zip = [System.IO.Compression.ZipFile]::OpenRead('%cd%\ffmpeg_temp.zip'); $zip.Entries | Where-Object {$_.Name -eq 'ffmpeg.exe'} | ForEach-Object {[System.IO.Compression.ZipFileExtensions]::ExtractToFile($_, '%cd%\ffmpeg.exe', $true)}; $zip.Dispose()}"

if exist "ffmpeg.exe" (
    echo ✅ FFmpeg解压成功！
    echo 正在测试FFmpeg...
    ffmpeg.exe -version >nul 2>&1
    if %errorlevel% == 0 (
        echo ✅ FFmpeg安装成功，测试通过！
        del "ffmpeg_temp.zip" >nul 2>&1
        goto :success
    ) else (
        echo ❌ FFmpeg测试失败
        goto :manual
    )
) else (
    echo ❌ FFmpeg解压失败
    goto :manual
)

:success
echo.
echo ========================================
echo           安装成功！
echo ========================================
echo.
echo ✅ FFmpeg已成功安装到程序目录
echo ✅ 视频混剪工具现在应该可以正常工作了
echo.
echo 请重新启动视频混剪工具程序
goto :end

:manual
echo.
echo ========================================
echo           手动安装指导
echo ========================================
echo.
echo 自动安装失败，请按照以下步骤手动安装：
echo.
echo 方法1（推荐）：
echo 1. 访问: https://www.gyan.dev/ffmpeg/builds/
echo 2. 下载 ffmpeg-release-essentials.zip
echo 3. 解压后找到 ffmpeg.exe 文件
echo 4. 将 ffmpeg.exe 复制到程序目录: %cd%
echo 5. 重启视频混剪工具
echo.
echo 方法2：
echo 1. 下载完整版FFmpeg: https://ffmpeg.org/download.html
echo 2. 解压到 C:\ffmpeg
echo 3. 将 C:\ffmpeg\bin 添加到系统PATH环境变量
echo 4. 重启计算机
echo.

:end
echo.
echo 按任意键退出...
pause >nul
