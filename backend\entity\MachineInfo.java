package com.example.activation.entity;

import java.time.LocalDateTime;

/**
 * 机器信息实体类
 */
public class MachineInfo {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 机器码
     */
    private String machineCode;
    
    /**
     * 计算机名称
     */
    private String computerName;
    
    /**
     * 操作系统
     */
    private String operatingSystem;
    
    /**
     * 处理器信息
     */
    private String processor;
    
    /**
     * CPU序列号
     */
    private String cpuId;
    
    /**
     * 主板序列号
     */
    private String motherboardSerial;
    
    /**
     * BIOS序列号
     */
    private String biosSerial;
    
    /**
     * 硬盘序列号
     */
    private String diskSerial;
    
    /**
     * MAC地址
     */
    private String macAddress;
    
    /**
     * Windows产品ID
     */
    private String windowsProductId;
    
    /**
     * 机器GUID
     */
    private String machineGuid;
    
    /**
     * 激活状态：0-未激活，1-已激活，2-已禁用
     */
    private Integer activationStatus;
    
    /**
     * 首次激活时间
     */
    private LocalDateTime firstActivationTime;
    
    /**
     * 最后激活时间
     */
    private LocalDateTime lastActivationTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 激活次数
     */
    private Integer activationCount;
    
    /**
     * 备注信息
     */
    private String remark;
    
    // 构造函数
    public MachineInfo() {}
    
    public MachineInfo(String machineCode) {
        this.machineCode = machineCode;
        this.activationStatus = 0;
        this.activationCount = 0;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getMachineCode() {
        return machineCode;
    }
    
    public void setMachineCode(String machineCode) {
        this.machineCode = machineCode;
    }
    
    public String getComputerName() {
        return computerName;
    }
    
    public void setComputerName(String computerName) {
        this.computerName = computerName;
    }
    
    public String getOperatingSystem() {
        return operatingSystem;
    }
    
    public void setOperatingSystem(String operatingSystem) {
        this.operatingSystem = operatingSystem;
    }
    
    public String getProcessor() {
        return processor;
    }
    
    public void setProcessor(String processor) {
        this.processor = processor;
    }
    
    public String getCpuId() {
        return cpuId;
    }
    
    public void setCpuId(String cpuId) {
        this.cpuId = cpuId;
    }
    
    public String getMotherboardSerial() {
        return motherboardSerial;
    }
    
    public void setMotherboardSerial(String motherboardSerial) {
        this.motherboardSerial = motherboardSerial;
    }
    
    public String getBiosSerial() {
        return biosSerial;
    }
    
    public void setBiosSerial(String biosSerial) {
        this.biosSerial = biosSerial;
    }
    
    public String getDiskSerial() {
        return diskSerial;
    }
    
    public void setDiskSerial(String diskSerial) {
        this.diskSerial = diskSerial;
    }
    
    public String getMacAddress() {
        return macAddress;
    }
    
    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }
    
    public String getWindowsProductId() {
        return windowsProductId;
    }
    
    public void setWindowsProductId(String windowsProductId) {
        this.windowsProductId = windowsProductId;
    }
    
    public String getMachineGuid() {
        return machineGuid;
    }
    
    public void setMachineGuid(String machineGuid) {
        this.machineGuid = machineGuid;
    }
    
    public Integer getActivationStatus() {
        return activationStatus;
    }
    
    public void setActivationStatus(Integer activationStatus) {
        this.activationStatus = activationStatus;
    }
    
    public LocalDateTime getFirstActivationTime() {
        return firstActivationTime;
    }
    
    public void setFirstActivationTime(LocalDateTime firstActivationTime) {
        this.firstActivationTime = firstActivationTime;
    }
    
    public LocalDateTime getLastActivationTime() {
        return lastActivationTime;
    }
    
    public void setLastActivationTime(LocalDateTime lastActivationTime) {
        this.lastActivationTime = lastActivationTime;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    public Integer getActivationCount() {
        return activationCount;
    }
    
    public void setActivationCount(Integer activationCount) {
        this.activationCount = activationCount;
    }
    
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
    
    /**
     * 检查是否已激活
     */
    public boolean isActivated() {
        return activationStatus == 1;
    }
    
    /**
     * 检查是否已禁用
     */
    public boolean isDisabled() {
        return activationStatus == 2;
    }
    
    /**
     * 增加激活次数
     */
    public void incrementActivationCount() {
        if (this.activationCount == null) {
            this.activationCount = 0;
        }
        this.activationCount++;
        this.lastActivationTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
        
        if (this.firstActivationTime == null) {
            this.firstActivationTime = LocalDateTime.now();
        }
    }
    
    @Override
    public String toString() {
        return "MachineInfo{" +
                "id=" + id +
                ", machineCode='" + machineCode + '\'' +
                ", computerName='" + computerName + '\'' +
                ", operatingSystem='" + operatingSystem + '\'' +
                ", processor='" + processor + '\'' +
                ", activationStatus=" + activationStatus +
                ", activationCount=" + activationCount +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
