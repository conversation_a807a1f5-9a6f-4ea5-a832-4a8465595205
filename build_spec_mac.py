#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mac专用PyInstaller spec文件生成器
为macOS系统生成优化的打包配置
"""

import os
import sys
from pathlib import Path

def generate_mac_spec():
    """生成Mac专用的spec文件"""
    
    app_name = "余下视频混剪工具"
    app_name_en = "VideoMixingTool"
    version = "2.0"
    main_script = "enhanced_video_deduplication_gui.py"
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-
"""
Mac专用PyInstaller配置文件
自动生成于: {app_name} v{version}
"""

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 应用程序基本信息
APP_NAME = "{app_name}"
APP_NAME_EN = "{app_name_en}"
VERSION = "{version}"
BUNDLE_ID = "com.videomixingtool.app"

# 收集数据文件
datas = []

# 添加应用程序资源
resource_files = [
    ('img', 'img'),
    ('config.ini', '.'),
    ('README.md', '.'),
    ('余下视频混剪工具v2.0完整说明书.md', '.'),
]

for src, dst in resource_files:
    if os.path.exists(src):
        datas.append((src, dst))
        print(f"✅ 添加资源: {{src}} -> {{dst}}")

# 收集第三方库数据文件
third_party_data = [
    'moviepy',
    'scenedetect',
    'cv2',
    'sklearn',
]

for lib in third_party_data:
    try:
        lib_datas = collect_data_files(lib)
        if lib_datas:
            datas.extend(lib_datas)
            print(f"✅ 收集 {{lib}} 数据文件: {{len(lib_datas)}} 个")
    except Exception as e:
        print(f"⚠️ 收集 {{lib}} 数据文件失败: {{e}}")

# 隐藏导入模块
hiddenimports = [
    # 核心依赖
    'cv2',
    'numpy',
    'numpy.core',
    'numpy.core._methods',
    'numpy.lib.format',
    
    # MoviePy相关
    'moviepy',
    'moviepy.editor',
    'moviepy.video.io.VideoFileClip',
    'moviepy.audio.io.AudioFileClip',
    'moviepy.video.fx',
    'moviepy.audio.fx',
    'moviepy.config',
    
    # 场景检测
    'scenedetect',
    'scenedetect.detectors',
    'scenedetect.detectors.content_detector',
    'scenedetect.detectors.threshold_detector',
    'scenedetect.video_manager',
    'scenedetect.scene_manager',
    
    # PyQt5相关
    'PyQt5',
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'PyQt5.QtWidgets',
    'PyQt5.QtNetwork',
    'PyQt5.QtMultimedia',
    'PyQt5.QtMultimediaWidgets',
    'PyQt5.sip',
    
    # 图像处理
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'PIL.ImageDraw',
    'PIL.ImageFont',
    'imageio',
    'imageio.core',
    'imageio.core.util',
    'imageio_ffmpeg',
    
    # 音频处理
    'librosa',
    'librosa.core',
    'librosa.feature',
    'soundfile',
    'scipy',
    'scipy.io',
    'scipy.signal',
    'scipy.ndimage',
    
    # 数据处理
    'pandas',
    'tqdm',
    'sklearn',
    'sklearn.cluster',
    'sklearn.metrics',
    
    # 系统工具
    'psutil',
    'platformdirs',
    'dotenv',
    'numba',
    'numba.core',
    'numba.typed',
    
    # 其他可能需要的模块
    'pkg_resources',
    'pkg_resources.py2_warn',
    'packaging',
    'packaging.version',
    'packaging.specifiers',
    'packaging.requirements',
]

# 自动收集子模块
auto_collect_modules = ['moviepy', 'scenedetect', 'sklearn']
for module in auto_collect_modules:
    try:
        submodules = collect_submodules(module)
        hiddenimports.extend(submodules)
        print(f"✅ 收集 {{module}} 子模块: {{len(submodules)}} 个")
    except Exception as e:
        print(f"⚠️ 收集 {{module}} 子模块失败: {{e}}")

# 排除不需要的模块
excludes = [
    'tkinter',
    'matplotlib.backends._backend_tk',
    'matplotlib.backends.backend_tkagg',
    'test',
    'tests',
    'unittest',
    'doctest',
    'pdb',
    'pydoc',
]

# 二进制文件排除
binaries = []

# 分析配置
block_cipher = None

a = Analysis(
    ['{main_script}'],
    pathex=[os.path.abspath('.')],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 过滤重复文件
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 可执行文件配置
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name=APP_NAME_EN,
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='img/logo.ico' if os.path.exists('img/logo.ico') else None,
)

# macOS App Bundle配置
app = BUNDLE(
    exe,
    name=f'{{APP_NAME_EN}}.app',
    icon='img/logo.ico' if os.path.exists('img/logo.ico') else None,
    bundle_identifier=BUNDLE_ID,
    version=VERSION,
    info_plist={{
        # 基本信息
        'CFBundleName': APP_NAME,
        'CFBundleDisplayName': APP_NAME,
        'CFBundleVersion': VERSION,
        'CFBundleShortVersionString': VERSION,
        'CFBundleIdentifier': BUNDLE_ID,
        'CFBundleExecutable': APP_NAME_EN,
        'CFBundlePackageType': 'APPL',
        'CFBundleSignature': 'VMTL',
        
        # 系统要求
        'LSMinimumSystemVersion': '10.14.0',  # macOS Mojave
        'LSRequiresNativeExecution': True,
        
        # 显示设置
        'NSHighResolutionCapable': True,
        'NSRequiresAquaSystemAppearance': False,  # 支持深色模式
        'LSUIElement': False,  # 在Dock中显示
        'LSBackgroundOnly': False,
        
        # 权限请求
        'NSCameraUsageDescription': '此应用需要访问摄像头以进行视频处理和预览功能',
        'NSMicrophoneUsageDescription': '此应用需要访问麦克风以进行音频处理和录制功能',
        'NSDocumentsFolderUsageDescription': '此应用需要访问文档文件夹以保存和读取视频处理结果',
        'NSDesktopFolderUsageDescription': '此应用需要访问桌面文件夹以保存处理后的视频文件',
        'NSDownloadsFolderUsageDescription': '此应用需要访问下载文件夹以保存和读取视频文件',
        'NSRemovableVolumesUsageDescription': '此应用需要访问外部存储设备以读取和保存视频文件',
        
        # 文件类型关联
        'CFBundleDocumentTypes': [
            {{
                'CFBundleTypeName': 'Video Files',
                'CFBundleTypeRole': 'Editor',
                'CFBundleTypeExtensions': ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm'],
                'CFBundleTypeIconFile': 'logo.ico',
                'CFBundleTypeMIMETypes': ['video/mp4', 'video/avi', 'video/quicktime'],
            }},
            {{
                'CFBundleTypeName': 'Audio Files',
                'CFBundleTypeRole': 'Editor',
                'CFBundleTypeExtensions': ['mp3', 'wav', 'aac', 'm4a', 'flac'],
                'CFBundleTypeIconFile': 'logo.ico',
                'CFBundleTypeMIMETypes': ['audio/mpeg', 'audio/wav', 'audio/aac'],
            }},
        ],
        
        # URL Schemes (可选)
        'CFBundleURLTypes': [
            {{
                'CFBundleURLName': 'VideoMixingTool',
                'CFBundleURLSchemes': ['videomixingtool'],
            }},
        ],
        
        # 应用程序分类
        'LSApplicationCategoryType': 'public.app-category.video',
        
        # 版权信息
        'NSHumanReadableCopyright': f'Copyright © 2024 VideoMixingTool. All rights reserved.',
        
        # 其他设置
        'NSSupportsAutomaticGraphicsSwitching': True,  # 支持自动图形切换
        'NSAppTransportSecurity': {{
            'NSAllowsArbitraryLoads': False,
            'NSExceptionDomains': {{}},
        }},
    }},
)
'''
    
    spec_filename = f"{app_name_en}.spec"
    
    try:
        with open(spec_filename, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        print(f"\n✅ Mac专用spec文件已生成: {spec_filename}")
        print(f"📱 应用程序名称: {app_name}")
        print(f"🆔 Bundle ID: com.videomixingtool.app")
        print(f"📦 版本: {version}")
        print(f"🎯 最低系统要求: macOS 10.14 (Mojave)")
        
        print(f"\n💡 使用方法:")
        print(f"pyinstaller {spec_filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成spec文件失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🍎 Mac专用PyInstaller配置生成器")
    print("=" * 50)
    
    # 检查操作系统
    if sys.platform != "darwin":
        print(f"⚠️ 当前系统: {sys.platform}")
        print("此配置专为macOS优化，但仍可生成")
    
    # 检查主程序文件
    main_script = "enhanced_video_deduplication_gui.py"
    if not os.path.exists(main_script):
        print(f"❌ 未找到主程序文件: {main_script}")
        return False
    
    # 生成spec文件
    success = generate_mac_spec()
    
    if success:
        print("\n🎉 配置文件生成完成!")
        print("\n📋 下一步:")
        print("1. 运行: pyinstaller VideoMixingTool.spec")
        print("2. 或使用: python3 build_mac.py")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
