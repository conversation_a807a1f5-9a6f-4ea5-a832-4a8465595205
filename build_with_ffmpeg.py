#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
带FFmpeg的打包脚本
自动下载FFmpeg并包含在打包的程序中
"""

import os
import sys
import subprocess
import urllib.request
import zipfile
import shutil
import platform
from pathlib import Path

def log_info(msg):
    print(f"[INFO] {msg}")

def log_error(msg):
    print(f"[ERROR] {msg}")

def log_warning(msg):
    print(f"[WARNING] {msg}")

class BuildWithFFmpeg:
    def __init__(self):
        self.project_dir = os.path.dirname(os.path.abspath(__file__))
        self.dist_dir = os.path.join(self.project_dir, 'dist')
        self.ffmpeg_url = "https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip"
        
    def check_requirements(self):
        """检查构建要求"""
        log_info("检查构建要求...")
        
        # 检查PyInstaller
        try:
            import PyInstaller
            log_info(f"✅ PyInstaller已安装: {PyInstaller.__version__}")
        except ImportError:
            log_error("❌ PyInstaller未安装，请运行: pip install pyinstaller")
            return False
        
        # 检查主程序文件
        main_file = os.path.join(self.project_dir, 'enhanced_video_deduplication_gui.py')
        if not os.path.exists(main_file):
            log_error(f"❌ 主程序文件不存在: {main_file}")
            return False
        
        log_info("✅ 构建要求检查通过")
        return True
    
    def download_ffmpeg(self):
        """下载FFmpeg"""
        if platform.system() != "Windows":
            log_warning("⚠️ 自动下载FFmpeg仅支持Windows系统")
            return None
        
        log_info("开始下载FFmpeg...")
        
        try:
            # 创建临时目录
            temp_dir = os.path.join(self.project_dir, 'temp_ffmpeg')
            os.makedirs(temp_dir, exist_ok=True)
            
            # 下载文件
            zip_file = os.path.join(temp_dir, 'ffmpeg.zip')
            log_info(f"下载到: {zip_file}")
            urllib.request.urlretrieve(self.ffmpeg_url, zip_file)
            log_info("✅ FFmpeg下载完成")
            
            # 解压并查找ffmpeg.exe
            log_info("正在解压FFmpeg...")
            with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                for file_info in zip_ref.filelist:
                    if file_info.filename.endswith('ffmpeg.exe'):
                        # 提取ffmpeg.exe
                        with zip_ref.open(file_info) as source:
                            ffmpeg_path = os.path.join(temp_dir, 'ffmpeg.exe')
                            with open(ffmpeg_path, 'wb') as target:
                                shutil.copyfileobj(source, target)
                        log_info(f"✅ FFmpeg提取成功: {ffmpeg_path}")
                        return ffmpeg_path
            
            log_error("❌ 在下载的文件中未找到ffmpeg.exe")
            return None
            
        except Exception as e:
            log_error(f"❌ 下载FFmpeg失败: {e}")
            return None
    
    def build_executable(self, ffmpeg_path=None):
        """构建可执行文件"""
        log_info("开始构建可执行文件...")
        
        try:
            # 构建PyInstaller命令
            cmd = [
                'pyinstaller',
                '--onefile',  # 单文件模式
                '--windowed',  # 无控制台窗口
                '--name=VideoDeduplicationTool_v2.0',  # 使用英文名称避免编码问题
                '--icon=img/logo.ico',
                '--add-data=img;img',
                '--add-data=moviepy_config.py;.',
                '--add-data=ffmpeg_installer.py;.',  # 添加FFmpeg安装器
                '--hidden-import=cv2',
                '--hidden-import=numpy',
                '--hidden-import=moviepy',
                '--hidden-import=scenedetect',
                '--hidden-import=PyQt5',
                '--hidden-import=requests',
                '--hidden-import=PIL',
                '--hidden-import=whisper',
                '--hidden-import=openai',
                '--hidden-import=urllib.request',
                '--hidden-import=zipfile',
                '--collect-all=moviepy',
                '--collect-all=imageio',
                '--collect-all=imageio_ffmpeg',
                '--exclude-module=tkinter',  # 排除不需要的模块
                '--exclude-module=matplotlib',
                'enhanced_video_deduplication_gui.py'
            ]
            
            # 如果有FFmpeg，添加到打包中
            if ffmpeg_path and os.path.exists(ffmpeg_path):
                cmd.insert(-1, f'--add-binary={ffmpeg_path};.')
                log_info(f"✅ 将FFmpeg添加到打包中: {ffmpeg_path}")
            
            log_info(f"执行命令: {' '.join(cmd)}")

            # 执行构建，使用正确的编码处理
            try:
                result = subprocess.run(
                    cmd,
                    cwd=self.project_dir,
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    errors='ignore'  # 忽略编码错误
                )
            except UnicodeDecodeError:
                # 如果UTF-8失败，尝试使用系统默认编码
                result = subprocess.run(
                    cmd,
                    cwd=self.project_dir,
                    capture_output=True,
                    text=True,
                    encoding='gbk',
                    errors='ignore'
                )

            if result.returncode == 0:
                log_info("✅ 可执行文件构建成功")
                return True
            else:
                error_msg = result.stderr if result.stderr else "未知错误"
                log_error(f"❌ 构建失败: {error_msg}")
                # 也输出stdout以获取更多信息
                if result.stdout:
                    log_info(f"构建输出: {result.stdout}")
                return False
                
        except Exception as e:
            log_error(f"❌ 构建过程中发生错误: {e}")
            return False
    
    def post_build_setup(self, ffmpeg_path=None):
        """构建后设置"""
        log_info("执行构建后设置...")
        
        try:
            # 查找生成的exe文件
            exe_file = None
            for root, dirs, files in os.walk(self.dist_dir):
                for file in files:
                    if file.endswith('.exe'):
                        exe_file = os.path.join(root, file)
                        break
                if exe_file:
                    break
            
            if not exe_file:
                log_error("❌ 未找到生成的exe文件")
                return False
            
            exe_dir = os.path.dirname(exe_file)
            log_info(f"✅ 找到exe文件: {exe_file}")
            
            # 如果FFmpeg没有被打包进去，复制到exe同目录
            if ffmpeg_path and os.path.exists(ffmpeg_path):
                target_ffmpeg = os.path.join(exe_dir, 'ffmpeg.exe')
                if not os.path.exists(target_ffmpeg):
                    shutil.copy2(ffmpeg_path, target_ffmpeg)
                    log_info(f"✅ FFmpeg已复制到exe目录: {target_ffmpeg}")
            
            # 复制其他必要文件
            necessary_files = [
                'moviepy_config.py',
                'ffmpeg_installer.py',
                'activation_config.json'
            ]
            
            for file_name in necessary_files:
                src_file = os.path.join(self.project_dir, file_name)
                if os.path.exists(src_file):
                    dst_file = os.path.join(exe_dir, file_name)
                    shutil.copy2(src_file, dst_file)
                    log_info(f"✅ 已复制: {file_name}")
            
            # 复制img目录
            src_img_dir = os.path.join(self.project_dir, 'img')
            dst_img_dir = os.path.join(exe_dir, 'img')
            if os.path.exists(src_img_dir) and not os.path.exists(dst_img_dir):
                shutil.copytree(src_img_dir, dst_img_dir)
                log_info("✅ 已复制img目录")
            
            log_info("✅ 构建后设置完成")
            return True
            
        except Exception as e:
            log_error(f"❌ 构建后设置失败: {e}")
            return False
    
    def cleanup(self):
        """清理临时文件"""
        log_info("清理临时文件...")
        
        temp_dirs = [
            os.path.join(self.project_dir, 'temp_ffmpeg'),
            os.path.join(self.project_dir, 'build'),
            os.path.join(self.project_dir, '__pycache__')
        ]
        
        for temp_dir in temp_dirs:
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                    log_info(f"✅ 已清理: {temp_dir}")
                except Exception as e:
                    log_warning(f"⚠️ 清理失败 {temp_dir}: {e}")
    
    def build(self):
        """执行完整的构建流程"""
        log_info("=== 开始带FFmpeg的打包流程 ===")
        
        # 检查要求
        if not self.check_requirements():
            return False
        
        # 下载FFmpeg
        ffmpeg_path = self.download_ffmpeg()
        if ffmpeg_path:
            log_info(f"✅ FFmpeg准备就绪: {ffmpeg_path}")
        else:
            log_warning("⚠️ FFmpeg下载失败，将构建不包含FFmpeg的版本")
        
        # 构建可执行文件
        if not self.build_executable(ffmpeg_path):
            return False
        
        # 构建后设置
        if not self.post_build_setup(ffmpeg_path):
            return False
        
        # 清理临时文件
        self.cleanup()
        
        log_info("=== 构建完成 ===")
        log_info(f"输出目录: {self.dist_dir}")
        
        return True

def main():
    """主函数"""
    builder = BuildWithFFmpeg()
    
    if builder.build():
        print("\n🎉 构建成功！")
        print(f"📁 输出目录: {builder.dist_dir}")
        print("✨ 程序现在包含FFmpeg，应该可以正常进行视频合成了！")
    else:
        print("\n❌ 构建失败！")
        print("请检查错误信息并重试。")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
