#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windows编码修复版打包脚本
专门解决Windows系统中文编码问题
"""

import os
import sys
import shutil
import subprocess
import platform
import tempfile
import locale

# 强制设置UTF-8编码
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PYTHONUTF8'] = '1'

# 设置控制台编码
if platform.system() == "Windows":
    try:
        # 设置控制台代码页为UTF-8
        os.system('chcp 65001 >nul')
    except:
        pass

class WindowsBuildManager:
    """Windows专用打包管理器"""
    
    def __init__(self):
        self.app_name = "余下视频混剪工具"
        self.version = "1.0"
        self.main_script = "enhanced_video_deduplication_gui.py"
        self.build_dir = "build"
        self.dist_dir = "dist"
        
    def safe_run_command(self, cmd, timeout=600):
        """安全执行命令，处理编码问题"""
        print(f"执行命令: {' '.join(cmd)}")
        
        try:
            # 使用临时文件避免编码问题
            with tempfile.NamedTemporaryFile(mode='w+', encoding='utf-8', delete=False) as temp_stdout:
                with tempfile.NamedTemporaryFile(mode='w+', encoding='utf-8', delete=False) as temp_stderr:
                    
                    process = subprocess.Popen(
                        cmd,
                        stdout=temp_stdout,
                        stderr=temp_stderr,
                        cwd=os.getcwd(),
                        env=dict(os.environ, PYTHONIOENCODING='utf-8')
                    )
                    
                    # 等待进程完成
                    try:
                        process.wait(timeout=timeout)
                    except subprocess.TimeoutExpired:
                        process.kill()
                        print("❌ 命令执行超时")
                        return False, "命令执行超时"
                    
                    # 读取输出
                    temp_stdout.seek(0)
                    temp_stderr.seek(0)
                    
                    stdout_content = ""
                    stderr_content = ""
                    
                    try:
                        with open(temp_stdout.name, 'r', encoding='utf-8', errors='ignore') as f:
                            stdout_content = f.read()
                    except:
                        pass
                    
                    try:
                        with open(temp_stderr.name, 'r', encoding='utf-8', errors='ignore') as f:
                            stderr_content = f.read()
                    except:
                        pass
                    
                    # 清理临时文件
                    try:
                        os.unlink(temp_stdout.name)
                        os.unlink(temp_stderr.name)
                    except:
                        pass
                    
                    # 输出结果
                    if stdout_content:
                        print("输出:")
                        for line in stdout_content.split('\n'):
                            if line.strip():
                                print(f"  {line}")
                    
                    if stderr_content and process.returncode != 0:
                        print("错误:")
                        for line in stderr_content.split('\n'):
                            if line.strip():
                                print(f"  {line}")
                    
                    return process.returncode == 0, stderr_content
                    
        except Exception as e:
            print(f"❌ 命令执行异常: {e}")
            return False, str(e)
    
    def check_environment(self):
        """检查环境"""
        print("🔍 检查打包环境...")
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version < (3, 8):
            print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
            return False
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # 检查PyInstaller
        try:
            import PyInstaller
            print(f"✅ PyInstaller版本: {PyInstaller.__version__}")
        except ImportError:
            print("❌ 未安装PyInstaller")
            return False
        
        # 检查主程序文件
        if not os.path.exists(self.main_script):
            print(f"❌ 未找到主程序文件: {self.main_script}")
            return False
        print(f"✅ 找到主程序文件: {self.main_script}")
        
        return True
    
    def clean_build(self):
        """清理构建目录"""
        print("🧹 清理构建目录...")
        
        dirs_to_clean = [self.build_dir, self.dist_dir]
        files_to_clean = [f"{self.app_name}.spec"]
        
        for dir_path in dirs_to_clean:
            if os.path.exists(dir_path):
                try:
                    shutil.rmtree(dir_path)
                    print(f"  删除目录: {dir_path}")
                except Exception as e:
                    print(f"  警告: 无法删除目录 {dir_path}: {e}")
        
        for file_path in files_to_clean:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    print(f"  删除文件: {file_path}")
                except Exception as e:
                    print(f"  警告: 无法删除文件 {file_path}: {e}")
    
    def build_with_simple_command(self):
        """使用简化命令构建"""
        print("🔨 使用简化命令构建...")
        
        # 简化的打包命令，避免复杂的中文路径问题
        cmd = [
            "pyinstaller",
            "--onefile",
            "--windowed",
            "--clean",
            "--noconfirm",
            f"--name={self.app_name}",
            self.main_script
        ]
        
        # 添加图标（如果存在）
        if os.path.exists("img/logo.ico"):
            cmd.extend(["--icon=img/logo.ico"])
        
        # 添加关键的隐藏导入
        hidden_imports = [
            "cv2", "numpy", "moviepy.editor", "scenedetect",
            "PyQt5.QtCore", "PyQt5.QtGui", "PyQt5.QtWidgets", "PyQt5.QtNetwork",
            "performance_optimizer", "video_processor", "multi_rectangle_selector"
        ]
        
        for module in hidden_imports:
            cmd.extend([f"--hidden-import={module}"])
        
        # 执行构建
        success, error_msg = self.safe_run_command(cmd)
        
        if success:
            print("✅ 构建成功完成")
            return True
        else:
            print(f"❌ 构建失败: {error_msg}")
            return False
    
    def post_build_tasks(self):
        """构建后处理"""
        print("📦 执行构建后处理...")
        
        # 检查输出文件
        exe_name = f"{self.app_name}.exe"
        exe_path = os.path.join(self.dist_dir, exe_name)
        
        if not os.path.exists(exe_path):
            print(f"❌ 未找到可执行文件: {exe_path}")
            return False
        
        print(f"✅ 可执行文件已生成: {exe_path}")
        
        # 获取文件大小
        try:
            file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"📏 文件大小: {file_size:.1f} MB")
        except:
            print("📏 无法获取文件大小")
        
        # 复制必要文件
        files_to_copy = [
            'README.md',
            'config_example.ini'
        ]
        
        for file_name in files_to_copy:
            if os.path.exists(file_name):
                try:
                    dest_path = os.path.join(self.dist_dir, file_name)
                    shutil.copy2(file_name, dest_path)
                    print(f"  复制文件: {file_name}")
                except Exception as e:
                    print(f"  警告: 无法复制文件 {file_name}: {e}")
        
        # 创建启动脚本
        self.create_launcher_script()
        
        return True
    
    def create_launcher_script(self):
        """创建启动脚本"""
        print("📜 创建启动脚本...")
        
        # Windows批处理文件
        bat_content = f'''@echo off
chcp 65001 >nul
title {self.app_name} v{self.version}
echo {self.app_name} v{self.version}
echo 正在启动程序...
echo.

"{self.app_name}.exe"

if errorlevel 1 (
    echo.
    echo 程序运行出错
    echo 请检查系统要求和依赖
    pause
)
'''
        
        try:
            bat_path = os.path.join(self.dist_dir, "启动程序.bat")
            with open(bat_path, 'w', encoding='gbk', errors='ignore') as f:
                f.write(bat_content)
            print(f"  创建启动脚本: 启动程序.bat")
        except Exception as e:
            print(f"  警告: 无法创建启动脚本: {e}")
    
    def build(self):
        """执行完整构建流程"""
        print("🚀 Windows编码修复版打包流程")
        print("=" * 60)
        
        # 1. 检查环境
        if not self.check_environment():
            return False
        
        # 2. 清理构建目录
        self.clean_build()
        
        # 3. 构建可执行文件
        if not self.build_with_simple_command():
            return False
        
        # 4. 构建后处理
        if not self.post_build_tasks():
            return False
        
        print("\n" + "=" * 60)
        print("🎉 打包完成！")
        print(f"📁 输出目录: {self.dist_dir}")
        print(f"📦 可执行文件: {self.dist_dir}/{self.app_name}.exe")
        print("\n💡 使用说明:")
        print("1. 运行可执行文件测试功能")
        print("2. 检查所有功能是否正常")
        print("3. 在不同系统上测试兼容性")
        
        return True

def main():
    """主函数"""
    print("🔧 Windows编码修复版打包工具")
    print("专门解决Windows系统中文编码问题")
    print("=" * 60)
    
    builder = WindowsBuildManager()
    success = builder.build()
    
    if success:
        print("\n✅ 打包成功完成！")
    else:
        print("\n❌ 打包失败，请检查错误信息")
    
    input("\n按Enter键退出...")
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
