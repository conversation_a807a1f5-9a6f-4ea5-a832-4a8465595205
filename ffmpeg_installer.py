#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FFmpeg自动安装和配置工具
用于解决打包后程序中FFmpeg缺失的问题
"""

import os
import sys
import subprocess
import urllib.request
import zipfile
import shutil
import platform
from pathlib import Path

def log_info(msg):
    print(f"[INFO] {msg}")

def log_error(msg):
    print(f"[ERROR] {msg}")

def log_warning(msg):
    print(f"[WARNING] {msg}")

class FFmpegInstaller:
    def __init__(self):
        self.is_packaged = getattr(sys, 'frozen', False) or hasattr(sys, '_MEIPASS')
        self.exe_dir = os.path.dirname(sys.executable) if self.is_packaged else os.path.dirname(__file__)
        self.ffmpeg_url = self._get_ffmpeg_download_url()
        
    def _get_ffmpeg_download_url(self):
        """获取FFmpeg下载URL"""
        if platform.system() == "Windows":
            # 使用gyan.dev提供的FFmpeg构建版本（较小且稳定）
            return "https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip"
        else:
            return None  # Linux/macOS通常通过包管理器安装
    
    def check_ffmpeg_exists(self):
        """检查FFmpeg是否已存在"""
        # 检查exe同目录
        local_ffmpeg = os.path.join(self.exe_dir, 'ffmpeg.exe')
        if os.path.exists(local_ffmpeg):
            if self._test_ffmpeg(local_ffmpeg):
                log_info(f"找到本地FFmpeg: {local_ffmpeg}")
                return local_ffmpeg
        
        # 检查系统PATH
        try:
            result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                log_info("系统PATH中存在FFmpeg")
                return 'ffmpeg'
        except:
            pass
        
        # 检查常见安装路径
        common_paths = [
            r"C:\ffmpeg\bin\ffmpeg.exe",
            r"C:\Program Files\ffmpeg\bin\ffmpeg.exe",
            r"C:\Program Files (x86)\ffmpeg\bin\ffmpeg.exe",
        ]
        
        for path in common_paths:
            if os.path.exists(path) and self._test_ffmpeg(path):
                log_info(f"找到系统FFmpeg: {path}")
                return path
        
        return None
    
    def _test_ffmpeg(self, ffmpeg_path):
        """测试FFmpeg是否可用"""
        try:
            result = subprocess.run([ffmpeg_path, '-version'], capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except:
            return False
    
    def download_ffmpeg(self):
        """下载FFmpeg"""
        if platform.system() != "Windows":
            log_error("自动下载仅支持Windows系统")
            return False
        
        if not self.ffmpeg_url:
            log_error("无法获取FFmpeg下载URL")
            return False
        
        try:
            log_info("开始下载FFmpeg...")
            temp_zip = os.path.join(self.exe_dir, 'ffmpeg_temp.zip')
            
            # 下载文件
            urllib.request.urlretrieve(self.ffmpeg_url, temp_zip)
            log_info("FFmpeg下载完成")
            
            # 解压文件
            log_info("正在解压FFmpeg...")
            with zipfile.ZipFile(temp_zip, 'r') as zip_ref:
                # 查找ffmpeg.exe文件
                for file_info in zip_ref.filelist:
                    if file_info.filename.endswith('ffmpeg.exe'):
                        # 提取ffmpeg.exe到exe目录
                        with zip_ref.open(file_info) as source:
                            target_path = os.path.join(self.exe_dir, 'ffmpeg.exe')
                            with open(target_path, 'wb') as target:
                                shutil.copyfileobj(source, target)
                        log_info(f"FFmpeg已安装到: {target_path}")
                        break
                else:
                    log_error("在下载的文件中未找到ffmpeg.exe")
                    return False
            
            # 清理临时文件
            os.remove(temp_zip)
            
            # 测试安装
            ffmpeg_path = os.path.join(self.exe_dir, 'ffmpeg.exe')
            if self._test_ffmpeg(ffmpeg_path):
                log_info("FFmpeg安装成功！")
                return True
            else:
                log_error("FFmpeg安装失败，无法正常运行")
                return False
                
        except Exception as e:
            log_error(f"下载FFmpeg失败: {e}")
            return False
    
    def install_ffmpeg(self):
        """安装FFmpeg"""
        # 首先检查是否已存在
        existing_ffmpeg = self.check_ffmpeg_exists()
        if existing_ffmpeg:
            log_info("FFmpeg已存在，无需安装")
            return existing_ffmpeg
        
        # 如果不存在，尝试下载安装
        if platform.system() == "Windows":
            if self.download_ffmpeg():
                return os.path.join(self.exe_dir, 'ffmpeg.exe')
        
        # 提供手动安装指导
        self._show_manual_install_guide()
        return None
    
    def _show_manual_install_guide(self):
        """显示手动安装指导"""
        log_warning("自动安装失败，请手动安装FFmpeg：")
        
        if platform.system() == "Windows":
            log_info("\n=== Windows手动安装指导 ===")
            log_info("方法1（推荐）：")
            log_info(f"1. 下载FFmpeg: https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip")
            log_info(f"2. 解压后将ffmpeg.exe复制到程序目录: {self.exe_dir}")
            log_info("3. 重启程序")
            log_info("\n方法2：")
            log_info("1. 下载FFmpeg完整版")
            log_info("2. 解压到 C:\\ffmpeg")
            log_info("3. 将 C:\\ffmpeg\\bin 添加到系统PATH环境变量")
            log_info("4. 重启程序")
        else:
            log_info("\n=== Linux/macOS安装指导 ===")
            log_info("Ubuntu/Debian: sudo apt install ffmpeg")
            log_info("CentOS/RHEL: sudo yum install ffmpeg")
            log_info("macOS: brew install ffmpeg")

def main():
    """主函数"""
    print("=== FFmpeg自动安装工具 ===")
    
    installer = FFmpegInstaller()
    result = installer.install_ffmpeg()
    
    if result:
        print(f"\n✅ FFmpeg配置成功: {result}")
        print("程序现在应该可以正常进行视频合成了！")
    else:
        print("\n❌ FFmpeg配置失败")
        print("请按照上述指导手动安装FFmpeg")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
