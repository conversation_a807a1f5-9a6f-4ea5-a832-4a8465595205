#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面搜索ImageMagick安装位置
"""

import os
import sys
import subprocess
import glob
import winreg
from pathlib import Path

def search_registry_for_imagemagick():
    """在注册表中搜索ImageMagick"""
    print("🔍 在注册表中搜索ImageMagick...")
    
    registry_paths = []
    
    try:
        # 搜索HKEY_LOCAL_MACHINE
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE") as key:
            try:
                with winreg.OpenKey(key, "ImageMagick") as im_key:
                    # 枚举ImageMagick的子键
                    i = 0
                    while True:
                        try:
                            subkey_name = winreg.EnumKey(im_key, i)
                            with winreg.OpenKey(im_key, subkey_name) as subkey:
                                try:
                                    install_path, _ = winreg.QueryValueEx(subkey, "BinPath")
                                    registry_paths.append(install_path)
                                    print(f"  ✅ 注册表找到: {install_path}")
                                except FileNotFoundError:
                                    try:
                                        install_path, _ = winreg.QueryValueEx(subkey, "InstallPath")
                                        registry_paths.append(install_path)
                                        print(f"  ✅ 注册表找到: {install_path}")
                                    except FileNotFoundError:
                                        pass
                            i += 1
                        except OSError:
                            break
            except FileNotFoundError:
                pass
    except Exception as e:
        print(f"  ⚠️ 注册表搜索失败: {e}")
    
    return registry_paths

def search_all_drives():
    """搜索所有驱动器"""
    print("\n🔍 搜索所有驱动器...")
    
    drives = []
    for letter in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ':
        drive = f"{letter}:\\"
        if os.path.exists(drive):
            drives.append(drive)
    
    print(f"找到驱动器: {drives}")
    
    installations = []
    
    for drive in drives:
        print(f"\n搜索驱动器 {drive}...")
        
        # 常见路径模式
        search_patterns = [
            f"{drive}Program Files\\ImageMagick*",
            f"{drive}Program Files (x86)\\ImageMagick*",
            f"{drive}ImageMagick*",
            f"{drive}tools\\ImageMagick*",
            f"{drive}software\\ImageMagick*",
            f"{drive}apps\\ImageMagick*"
        ]
        
        for pattern in search_patterns:
            try:
                matches = glob.glob(pattern)
                for match in matches:
                    if os.path.isdir(match):
                        # 检查是否包含可执行文件
                        magick_exe = os.path.join(match, "magick.exe")
                        convert_exe = os.path.join(match, "convert.exe")
                        
                        if os.path.exists(magick_exe) or os.path.exists(convert_exe):
                            installations.append({
                                'path': match,
                                'magick_exe': magick_exe if os.path.exists(magick_exe) else None,
                                'convert_exe': convert_exe if os.path.exists(convert_exe) else None
                            })
                            print(f"  ✅ 找到安装: {match}")
            except Exception as e:
                print(f"  ⚠️ 搜索 {pattern} 失败: {e}")
    
    return installations

def search_where_command():
    """使用where命令搜索"""
    print("\n🔍 使用where命令搜索...")
    
    commands = ['magick', 'convert', 'identify']
    found_paths = []
    
    for cmd in commands:
        try:
            result = subprocess.run(['where', cmd], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                paths = result.stdout.strip().split('\n')
                for path in paths:
                    if path and os.path.exists(path):
                        dir_path = os.path.dirname(path)
                        found_paths.append(dir_path)
                        print(f"  ✅ {cmd} 找到于: {path}")
        except Exception as e:
            print(f"  ⚠️ where {cmd} 失败: {e}")
    
    return list(set(found_paths))  # 去重

def test_path_directly():
    """直接测试PATH中的命令"""
    print("\n🧪 测试PATH中的ImageMagick命令...")
    
    commands = ['magick', 'convert', 'identify']
    working_commands = []
    
    for cmd in commands:
        try:
            result = subprocess.run([cmd, '-version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                version_info = result.stdout.split('\n')[0]
                print(f"  ✅ {cmd} 工作正常: {version_info}")
                working_commands.append(cmd)
                
                # 尝试获取可执行文件路径
                try:
                    which_result = subprocess.run(['where', cmd], 
                                                capture_output=True, text=True, timeout=5)
                    if which_result.returncode == 0:
                        exe_path = which_result.stdout.strip().split('\n')[0]
                        print(f"    路径: {exe_path}")
                except:
                    pass
            else:
                print(f"  ❌ {cmd} 命令失败")
        except FileNotFoundError:
            print(f"  ❌ {cmd} 命令未找到")
        except Exception as e:
            print(f"  ❌ {cmd} 测试失败: {e}")
    
    return working_commands

def create_comprehensive_config(installations, registry_paths, where_paths):
    """创建综合配置"""
    print("\n⚙️ 创建综合配置...")
    
    # 收集所有可能的路径
    all_paths = []
    
    # 从安装目录
    for install in installations:
        if install['magick_exe']:
            all_paths.append(install['magick_exe'])
        elif install['convert_exe']:
            all_paths.append(install['convert_exe'])
    
    # 从注册表
    for reg_path in registry_paths:
        magick_exe = os.path.join(reg_path, "magick.exe")
        convert_exe = os.path.join(reg_path, "convert.exe")
        if os.path.exists(magick_exe):
            all_paths.append(magick_exe)
        elif os.path.exists(convert_exe):
            all_paths.append(convert_exe)
    
    # 从where命令
    for where_path in where_paths:
        magick_exe = os.path.join(where_path, "magick.exe")
        convert_exe = os.path.join(where_path, "convert.exe")
        if os.path.exists(magick_exe):
            all_paths.append(magick_exe)
        elif os.path.exists(convert_exe):
            all_paths.append(convert_exe)
    
    # 去重
    all_paths = list(set(all_paths))
    
    if not all_paths:
        print("  ❌ 未找到任何可用的ImageMagick可执行文件")
        return False
    
    print(f"  找到 {len(all_paths)} 个可能的可执行文件:")
    for i, path in enumerate(all_paths):
        print(f"    {i+1}. {path}")
    
    # 选择最佳路径（优先magick.exe）
    best_path = None
    for path in all_paths:
        if 'magick.exe' in path:
            best_path = path
            break
    
    if not best_path:
        best_path = all_paths[0]
    
    print(f"  选择: {best_path}")
    
    # 创建配置
    try:
        config_content = f'''# MoviePy ImageMagick配置
IMAGEMAGICK_BINARY = r"{best_path}"
'''
        
        with open('moviepy_config.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        print("  ✅ 已创建 moviepy_config.py")
        
        # 设置环境变量
        os.environ['IMAGEMAGICK_BINARY'] = best_path
        print("  ✅ 已设置环境变量")
        
        # 测试配置
        return test_config(best_path)
        
    except Exception as e:
        print(f"  ❌ 创建配置失败: {e}")
        return False

def test_config(binary_path):
    """测试配置"""
    print(f"\n🧪 测试配置: {binary_path}")
    
    try:
        # 重新导入moviepy
        import importlib
        if 'moviepy.config' in sys.modules:
            importlib.reload(sys.modules['moviepy.config'])
        
        import moviepy.config as config
        config.IMAGEMAGICK_BINARY = binary_path
        
        from moviepy.editor import TextClip
        
        # 创建测试TextClip
        txt_clip = TextClip("测试", fontsize=24, color='white', duration=1)
        print("  ✅ TextClip创建成功")
        
        # 清理
        txt_clip.close()
        return True
        
    except Exception as e:
        print(f"  ❌ 配置测试失败: {e}")
        return False

def main():
    """主搜索流程"""
    print("=" * 60)
    print("🔍 全面搜索ImageMagick安装")
    print("=" * 60)
    
    # 1. 测试PATH中的命令
    working_commands = test_path_directly()
    
    # 2. 使用where命令搜索
    where_paths = search_where_command()
    
    # 3. 搜索注册表
    registry_paths = search_registry_for_imagemagick()
    
    # 4. 搜索所有驱动器（如果前面的方法都失败）
    installations = []
    if not working_commands and not where_paths and not registry_paths:
        print("\n前面的方法都未找到，开始全盘搜索...")
        installations = search_all_drives()
    
    # 5. 汇总结果
    print("\n" + "="*40)
    print("📊 搜索结果汇总")
    print("="*40)
    
    print(f"工作的命令: {working_commands}")
    print(f"where命令找到的路径: {where_paths}")
    print(f"注册表路径: {registry_paths}")
    print(f"全盘搜索结果: {len(installations)} 个安装")
    
    # 6. 如果找到任何结果，尝试配置
    if working_commands or where_paths or registry_paths or installations:
        if create_comprehensive_config(installations, registry_paths, where_paths):
            print("\n🎉 ImageMagick配置成功！")
            print("现在可以正常使用智能混剪功能了")
            return True
        else:
            print("\n❌ 配置失败")
    else:
        print("\n❌ 未找到任何ImageMagick安装")
        print("\n💡 建议:")
        print("1. 确认ImageMagick已正确安装")
        print("2. 检查安装时是否勾选了开发库选项")
        print("3. 尝试重新安装ImageMagick")
    
    return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
