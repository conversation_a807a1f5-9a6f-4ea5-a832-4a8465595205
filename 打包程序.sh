#!/bin/bash

echo ""
echo "========================================"
echo "🎬 余下视频乱剪工具 v2.0 自动打包程序"
echo "========================================"
echo ""

echo "📋 打包选项:"
echo "1. 完整打包 (推荐)"
echo "2. 快速打包 (不清理缓存)"
echo "3. 仅生成配置文件"
echo "4. 手动打包命令"
echo "5. 退出"
echo ""

read -p "请选择打包方式 (1-5): " choice

case $choice in
    1)
        echo ""
        echo "🚀 开始完整打包..."
        python3 build.py
        ;;
    2)
        echo ""
        echo "⚡ 开始快速打包..."
        python3 build.py --no-clean
        ;;
    3)
        echo ""
        echo "📝 生成配置文件..."
        python3 build_spec.py
        echo ""
        echo "✅ 配置文件已生成"
        echo "💡 可以使用以下命令进行打包:"
        echo "pyinstaller 余下视频乱剪工具.spec"
        ;;
    4)
        echo ""
        echo "📋 手动打包命令参考:"
        echo ""
        echo "🔹 基础打包命令:"
        echo "pyinstaller --onefile --windowed --icon=img/logo.ico enhanced_video_deduplication_gui.py"
        echo ""
        echo "🔹 完整打包命令:"
        echo "pyinstaller --onefile --windowed --icon=img/logo.ico \\"
        echo "  --add-data \"img:img\" \\"
        echo "  --add-data \"config.ini:.\" \\"
        echo "  --add-data \"README.md:.\" \\"
        echo "  --hidden-import cv2 \\"
        echo "  --hidden-import numpy \\"
        echo "  --hidden-import moviepy.editor \\"
        echo "  --hidden-import scenedetect \\"
        echo "  --hidden-import PyQt5.QtNetwork \\"
        echo "  enhanced_video_deduplication_gui.py"
        echo ""
        echo "🔹 使用spec文件打包:"
        echo "python3 build_spec.py"
        echo "pyinstaller 余下视频乱剪工具.spec"
        echo ""
        ;;
    5)
        echo "退出程序"
        exit 0
        ;;
    *)
        echo ""
        echo "❌ 无效选择，请重新运行程序"
        exit 1
        ;;
esac

echo ""
echo "📁 输出目录: dist/"
echo "📦 可执行文件: dist/余下视频乱剪工具"
echo ""
echo "💡 打包完成后建议:"
echo "1. 测试可执行文件是否正常运行"
echo "2. 检查所有功能是否完整"
echo "3. 在不同系统上测试兼容性"
echo ""

read -p "按Enter键退出..."
