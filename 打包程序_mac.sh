#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo ""
echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}🎬 余下视频混剪工具 v2.0 Mac自动打包程序${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

# 检查操作系统
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo -e "${RED}❌ 此脚本仅适用于macOS系统${NC}"
    echo -e "${YELLOW}当前系统: $OSTYPE${NC}"
    exit 1
fi

# 检查Python3
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ 未找到python3命令${NC}"
    echo -e "${YELLOW}请安装Python 3.8或更高版本${NC}"
    exit 1
fi

# 检查PyInstaller
if ! python3 -c "import PyInstaller" 2>/dev/null; then
    echo -e "${RED}❌ 未安装PyInstaller${NC}"
    echo -e "${YELLOW}正在安装PyInstaller...${NC}"
    pip3 install pyinstaller
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ PyInstaller安装失败${NC}"
        exit 1
    fi
fi

# 显示菜单
echo -e "${BLUE}📋 打包选项:${NC}"
echo -e "${GREEN}1.${NC} 完整打包 (推荐)"
echo -e "${GREEN}2.${NC} 快速打包 (不清理缓存)"
echo -e "${GREEN}3.${NC} 仅生成spec配置文件"
echo -e "${GREEN}4.${NC} 手动打包命令参考"
echo -e "${GREEN}5.${NC} 安装依赖环境"
echo -e "${GREEN}6.${NC} 检查系统环境"
echo -e "${GREEN}7.${NC} 退出"
echo ""

read -p "请选择打包方式 (1-7): " choice

case $choice in
    1)
        echo ""
        echo -e "${CYAN}🚀 开始完整打包...${NC}"
        python3 build_mac.py
        ;;
    2)
        echo ""
        echo -e "${CYAN}⚡ 开始快速打包...${NC}"
        python3 build_mac.py --no-clean
        ;;
    3)
        echo ""
        echo -e "${CYAN}📝 生成Mac专用spec配置文件...${NC}"
        python3 -c "
from build_mac import MacBuildManager
builder = MacBuildManager()
if builder.check_environment():
    builder.generate_spec_file()
    print('✅ 配置文件已生成')
    print('💡 可以使用以下命令进行打包:')
    print('pyinstaller VideoMixingTool.spec')
else:
    print('❌ 环境检查失败')
"
        ;;
    4)
        echo ""
        echo -e "${BLUE}📋 Mac手动打包命令参考:${NC}"
        echo ""
        echo -e "${YELLOW}🔹 基础打包命令:${NC}"
        echo "pyinstaller --onefile --windowed --icon=img/logo.ico enhanced_video_deduplication_gui.py"
        echo ""
        echo -e "${YELLOW}🔹 创建App Bundle:${NC}"
        echo "pyinstaller --onefile --windowed --icon=img/logo.ico \\"
        echo "  --add-data \"img:img\" \\"
        echo "  --add-data \"config.ini:.\" \\"
        echo "  --add-data \"README.md:.\" \\"
        echo "  --hidden-import cv2 \\"
        echo "  --hidden-import numpy \\"
        echo "  --hidden-import moviepy.editor \\"
        echo "  --hidden-import scenedetect \\"
        echo "  --hidden-import PyQt5.QtNetwork \\"
        echo "  --osx-bundle-identifier com.videomixingtool.app \\"
        echo "  enhanced_video_deduplication_gui.py"
        echo ""
        echo -e "${YELLOW}🔹 使用spec文件打包:${NC}"
        echo "python3 build_mac.py"
        echo "pyinstaller VideoMixingTool.spec"
        echo ""
        echo -e "${YELLOW}🔹 创建DMG安装包:${NC}"
        echo "hdiutil create -volname \"余下视频混剪工具\" -srcfolder dist/VideoMixingTool.app -ov -format UDZO VideoMixingTool.dmg"
        echo ""
        echo -e "${YELLOW}🔹 代码签名 (可选):${NC}"
        echo "codesign --force --deep --sign \"Developer ID Application: Your Name\" dist/VideoMixingTool.app"
        echo ""
        ;;
    5)
        echo ""
        echo -e "${CYAN}🔧 安装依赖环境...${NC}"
        echo ""
        
        # 检查Homebrew
        if ! command -v brew &> /dev/null; then
            echo -e "${YELLOW}📦 安装Homebrew...${NC}"
            /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        else
            echo -e "${GREEN}✅ Homebrew已安装${NC}"
        fi
        
        # 安装必要工具
        echo -e "${YELLOW}📦 安装必要工具...${NC}"
        brew install ffmpeg imagemagick
        
        # 安装Python依赖
        echo -e "${YELLOW}📦 安装Python依赖...${NC}"
        pip3 install -r requirements.txt
        pip3 install pyinstaller
        
        echo -e "${GREEN}✅ 依赖环境安装完成${NC}"
        ;;
    6)
        echo ""
        echo -e "${CYAN}🔍 检查系统环境...${NC}"
        python3 -c "
from build_mac import MacBuildManager
builder = MacBuildManager()
builder.check_environment()
"
        ;;
    7)
        echo -e "${YELLOW}退出程序${NC}"
        exit 0
        ;;
    *)
        echo ""
        echo -e "${RED}❌ 无效选择，请重新运行程序${NC}"
        exit 1
        ;;
esac

echo ""
echo -e "${BLUE}📁 输出目录: ${CYAN}dist/${NC}"
echo -e "${BLUE}📱 应用程序: ${CYAN}VideoMixingTool.app${NC}"
echo -e "${BLUE}📦 安装包: ${CYAN}VideoMixingTool_v2.0_macOS.dmg${NC}"
echo ""
echo -e "${PURPLE}💡 打包完成后建议:${NC}"
echo -e "${YELLOW}1.${NC} 在Finder中双击.app文件测试应用程序"
echo -e "${YELLOW}2.${NC} 检查所有功能是否完整"
echo -e "${YELLOW}3.${NC} 在不同macOS版本上测试兼容性"
echo -e "${YELLOW}4.${NC} 考虑进行代码签名以避免安全警告"
echo -e "${YELLOW}5.${NC} 测试DMG安装包的安装过程"
echo ""

# 如果是打包操作，显示额外信息
if [[ $choice == "1" || $choice == "2" ]]; then
    echo -e "${CYAN}🔐 代码签名建议:${NC}"
    echo -e "${YELLOW}如需分发应用程序，建议进行代码签名:${NC}"
    echo "codesign --force --deep --sign \"Developer ID Application: Your Name\" dist/VideoMixingTool.app"
    echo ""
    echo -e "${CYAN}📋 系统要求:${NC}"
    echo -e "${YELLOW}• macOS 10.14 (Mojave) 或更高版本${NC}"
    echo -e "${YELLOW}• 至少 4GB RAM${NC}"
    echo -e "${YELLOW}• 至少 2GB 可用磁盘空间${NC}"
    echo ""
fi

read -p "按Enter键退出..."
