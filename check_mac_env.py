#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mac环境检查脚本
检查macOS打包环境是否完整
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 50)
    print(f"🔍 {title}")
    print("=" * 50)

def check_system():
    """检查系统信息"""
    print_header("系统信息检查")
    
    # 检查操作系统
    if platform.system() != "Darwin":
        print(f"❌ 当前系统: {platform.system()}")
        print("此脚本需要在macOS系统上运行")
        return False
    
    # 获取macOS版本
    mac_version = platform.mac_ver()[0]
    print(f"✅ 操作系统: macOS {mac_version}")
    
    # 检查系统版本是否满足要求
    version_parts = mac_version.split('.')
    major = int(version_parts[0])
    minor = int(version_parts[1]) if len(version_parts) > 1 else 0
    
    if major < 10 or (major == 10 and minor < 14):
        print(f"⚠️ 系统版本较低，建议升级到macOS 10.14或更高版本")
    else:
        print(f"✅ 系统版本满足要求")
    
    # 检查架构
    arch = platform.machine()
    print(f"✅ 系统架构: {arch}")
    
    return True

def check_python():
    """检查Python环境"""
    print_header("Python环境检查")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print(f"❌ Python版本过低，需要3.8或更高版本")
        return False
    
    # 检查pip
    try:
        import pip
        print(f"✅ pip已安装")
    except ImportError:
        print(f"❌ pip未安装")
        return False
    
    return True

def check_pyinstaller():
    """检查PyInstaller"""
    print_header("PyInstaller检查")
    
    try:
        import PyInstaller
        print(f"✅ PyInstaller版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print(f"❌ PyInstaller未安装")
        print("安装命令: pip3 install pyinstaller")
        return False

def check_dependencies():
    """检查项目依赖"""
    print_header("项目依赖检查")
    
    required_modules = [
        ('PyQt5', 'PyQt5'),
        ('cv2', 'opencv-python'),
        ('numpy', 'numpy'),
        ('moviepy', 'moviepy'),
        ('scenedetect', 'scenedetect'),
        ('PIL', 'Pillow'),
        ('imageio', 'imageio'),
        ('pandas', 'pandas'),
        ('tqdm', 'tqdm'),
        ('sklearn', 'scikit-learn'),
        ('librosa', 'librosa'),
        ('soundfile', 'soundfile'),
        ('scipy', 'scipy'),
        ('psutil', 'psutil'),
    ]
    
    missing_modules = []
    
    for module_name, package_name in required_modules:
        try:
            __import__(module_name)
            print(f"✅ {module_name}")
        except ImportError:
            print(f"❌ {module_name} (安装: pip3 install {package_name})")
            missing_modules.append(package_name)
    
    if missing_modules:
        print(f"\n缺少依赖包: {', '.join(missing_modules)}")
        print("安装命令: pip3 install -r requirements.txt")
        return False
    
    return True

def check_system_tools():
    """检查系统工具"""
    print_header("系统工具检查")
    
    tools = [
        ('ffmpeg', 'FFmpeg', 'brew install ffmpeg'),
        ('magick', 'ImageMagick', 'brew install imagemagick'),
        ('hdiutil', 'hdiutil (系统自带)', '系统自带工具'),
        ('codesign', 'codesign (系统自带)', '系统自带工具'),
    ]
    
    missing_tools = []
    
    for tool, name, install_cmd in tools:
        try:
            result = subprocess.run([tool, '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"✅ {name}")
            else:
                print(f"⚠️ {name} 可能未正确安装")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print(f"❌ {name} (安装: {install_cmd})")
            missing_tools.append((name, install_cmd))
    
    return len(missing_tools) == 0

def check_homebrew():
    """检查Homebrew"""
    print_header("Homebrew检查")
    
    try:
        result = subprocess.run(['brew', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print(f"✅ Homebrew已安装")
            return True
        else:
            print(f"❌ Homebrew未正确安装")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print(f"❌ Homebrew未安装")
        print("安装命令: /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
        return False

def check_project_files():
    """检查项目文件"""
    print_header("项目文件检查")
    
    required_files = [
        'enhanced_video_deduplication_gui.py',
        'requirements.txt',
        'config.ini',
    ]
    
    optional_files = [
        'img/logo.ico',
        'img/logo.png',
        'README.md',
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    for file_path in optional_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} (可选)")
        else:
            print(f"⚠️ {file_path} (可选，建议添加)")
    
    return len(missing_files) == 0

def check_disk_space():
    """检查磁盘空间"""
    print_header("磁盘空间检查")
    
    try:
        import shutil
        total, used, free = shutil.disk_usage('.')
        
        free_gb = free // (1024**3)
        total_gb = total // (1024**3)
        
        print(f"✅ 总空间: {total_gb} GB")
        print(f"✅ 可用空间: {free_gb} GB")
        
        if free_gb < 2:
            print(f"⚠️ 可用空间不足，建议至少2GB")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查磁盘空间失败: {e}")
        return False

def main():
    """主函数"""
    print("🍎 Mac打包环境检查工具")
    print("检查macOS应用程序打包所需的环境和依赖")
    
    checks = [
        ("系统环境", check_system),
        ("Python环境", check_python),
        ("PyInstaller", check_pyinstaller),
        ("Homebrew", check_homebrew),
        ("系统工具", check_system_tools),
        ("项目依赖", check_dependencies),
        ("项目文件", check_project_files),
        ("磁盘空间", check_disk_space),
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name}检查失败: {e}")
            results.append((check_name, False))
    
    # 显示总结
    print_header("检查总结")
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {check_name}")
        if result:
            passed += 1
    
    print(f"\n📊 检查结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 环境检查完全通过，可以开始打包!")
        print("\n🚀 开始打包:")
        print("./打包程序_mac.sh")
    else:
        print("⚠️ 部分检查未通过，请根据上述提示解决问题")
        print("\n📋 常用安装命令:")
        print("pip3 install -r requirements.txt")
        print("pip3 install pyinstaller")
        print("brew install ffmpeg imagemagick")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
