#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API密钥和敏感配置管理模块
管理服务器地址、端点和配置信息
"""

from typing import List, Dict, Any


class APISecrets:
    """API配置管理器 - 管理服务器和端点配置"""

    def __init__(self):
        """初始化API配置管理器"""
        pass
    
    def get_api_servers(self) -> List[str]:
        """
        获取API服务器列表

        Returns:
            服务器URL列表
        """
        # 服务器地址列表（明文存储）
        servers = [
            "http://api_script.pg-code-go.com",  # 主服务器
            "http://backup-api.pg-code-go.com",  # 备用服务器1
            "http://localhost:8080"              # 本地测试服务器
        ]

        return servers
    
    def get_api_endpoints(self) -> Dict[str, str]:
        """
        获取API端点配置

        Returns:
            端点配置字典
        """
        # API端点配置（明文存储）
        endpoints = {
            "activation": "/api/activation",    # 激活端点
            "verify": "/verify",                # 验证端点
            "list": "/list",                    # 列表端点
            "info": "/info",                    # 信息端点
            "generate": "/generate",            # 生成端点
            "update": "/update",                # 更新端点
            "delete": "/delete",                # 删除端点
            "disable": "/disable",              # 禁用端点
            "enable": "/enable",                # 启用端点
            "check": "/check",                  # 检查端点
            "machines": "/machines",            # 机器端点
            "statistics": "/statistics",        # 统计端点
            "cleanup": "/cleanup",              # 清理端点
            "health": "/health"                 # 健康检查端点
        }

        return endpoints
    
    def get_request_headers(self) -> Dict[str, str]:
        """
        获取请求头配置

        Returns:
            请求头字典
        """
        # 请求头配置（明文存储）
        headers = {
            "Content-Type": "application/json",           # 内容类型
            "User-Agent": "VideoDeduplicationTool/2.0",   # 用户代理
            "Accept": "application/json",                 # 接受类型
            "X-Client-Version": "2.0"                     # 客户端版本
        }

        return headers
    
    def get_security_config(self) -> Dict[str, Any]:
        """
        获取安全配置
        
        Returns:
            安全配置字典
        """
        return {
            "timestamp_tolerance": 300,  # 时间戳容差（秒）
            "enable_timestamp_check": True,
            "enable_ssl_verify": True,
            "max_retry_count": 3,
            "retry_delay": 1.0
        }
    
    def get_offline_config(self) -> Dict[str, Any]:
        """
        获取离线配置（已禁用离线模式）

        Returns:
            离线配置字典
        """
        return {
            "grace_period_days": 0,  # 不允许离线宽限期
            "enable_offline_mode": False,  # 禁用离线模式
            "offline_check_interval": 0  # 不进行离线检查
        }
    
    def get_logging_config(self) -> Dict[str, Any]:
        """
        获取日志配置
        
        Returns:
            日志配置字典
        """
        return {
            "enable_request_logging": False,  # 生产环境关闭请求日志
            "enable_response_logging": False, # 生产环境关闭响应日志
            "enable_error_logging": True,     # 保留错误日志
            "log_level": "ERROR"
        }
    
    def get_api_config(self) -> Dict[str, Any]:
        """
        获取完整的API配置
        
        Returns:
            完整配置字典
        """
        servers = self.get_api_servers()
        
        return {
            "api": {
                "base_url": servers[0] if servers else "http://localhost:8080",
                "backup_urls": servers[1:] if len(servers) > 1 else [],
                "timeout": 10,
                "retry_count": 3,
                "retry_delay": 1
            },
            "endpoints": self.get_api_endpoints(),
            "headers": self.get_request_headers(),
            "security": self.get_security_config(),
            "offline": self.get_offline_config(),
            "logging": self.get_logging_config()
        }
    
    def validate_server_url(self, url: str) -> bool:
        """
        验证服务器URL是否在允许列表中
        
        Args:
            url: 要验证的URL
            
        Returns:
            是否为有效的服务器URL
        """
        allowed_servers = self.get_api_servers()
        # 添加一些常见的本地测试地址
        allowed_servers.extend([
            "http://127.0.0.1:8080",
            "http://localhost:8080",
            "http://localhost:3000"
        ])
        
        return url.rstrip('/') in [server.rstrip('/') for server in allowed_servers]


# 创建全局密钥管理器实例
api_secrets = APISecrets()


# 便捷函数
def get_primary_server() -> str:
    """获取主服务器地址"""
    servers = api_secrets.get_api_servers()
    return servers[0] if servers else "http://localhost:8080"


def get_backup_servers() -> List[str]:
    """获取备用服务器列表"""
    servers = api_secrets.get_api_servers()
    return servers[1:] if len(servers) > 1 else []


def get_api_endpoint(name: str) -> str:
    """获取API端点"""
    endpoints = api_secrets.get_api_endpoints()
    return endpoints.get(name, "")


def get_full_api_url(endpoint_name: str, base_url: str = None) -> str:
    """获取完整的API URL"""
    if base_url is None:
        base_url = get_primary_server()
    
    activation_prefix = get_api_endpoint("activation")
    endpoint = get_api_endpoint(endpoint_name)
    
    if endpoint_name == "activation":
        return f"{base_url}{activation_prefix}"
    else:
        return f"{base_url}{activation_prefix}{endpoint}"


if __name__ == "__main__":
    # 测试API配置管理器
    print("🔍 测试API配置管理器...")

    print(f"主服务器: {get_primary_server()}")
    print(f"备用服务器: {get_backup_servers()}")
    print(f"验证端点: {get_full_api_url('verify')}")
    print(f"列表端点: {get_full_api_url('list')}")

    # 显示所有端点
    endpoints = api_secrets.get_api_endpoints()
    print("\n📋 所有API端点:")
    for name, path in endpoints.items():
        print(f"  {name}: {path}")

    # 显示请求头
    headers = api_secrets.get_request_headers()
    print("\n📋 请求头配置:")
    for key, value in headers.items():
        print(f"  {key}: {value}")

    print("\n✅ API配置管理器测试完成")
