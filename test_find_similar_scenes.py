#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 find_similar_scenes 方法的参数传递
"""

import sys
import os

def test_find_similar_scenes():
    """测试 find_similar_scenes 方法"""
    print("🔍 测试 find_similar_scenes 方法...")
    
    try:
        from performance_optimizer import OptimizedVideoProcessor
        
        # 创建处理器
        processor = OptimizedVideoProcessor(enable_gpu=True, num_threads=4)
        
        # 模拟场景数据
        main_scenes = [
            {'start_time': 0.0, 'end_time': 5.0},
            {'start_time': 5.0, 'end_time': 10.0}
        ]
        
        aux_scenes = [
            {'start_time': 0.0, 'end_time': 5.0},
            {'start_time': 10.0, 'end_time': 15.0},
            {'start_time': 20.0, 'end_time': 25.0}
        ]
        
        # 测试方法是否存在
        if hasattr(processor, 'find_similar_scenes'):
            print("✅ OptimizedVideoProcessor 有 find_similar_scenes 方法")
            
            # 测试方法签名（不实际执行，只检查参数）
            import inspect
            sig = inspect.signature(processor.find_similar_scenes)
            params = list(sig.parameters.keys())
            
            print(f"📋 方法参数: {params}")
            
            if 'feature_frames' in params:
                print("✅ find_similar_scenes 支持 feature_frames 参数")
            else:
                print("❌ find_similar_scenes 不支持 feature_frames 参数")
                
            if 'similarity_threshold' in params:
                print("✅ find_similar_scenes 支持 similarity_threshold 参数")
            else:
                print("❌ find_similar_scenes 不支持 similarity_threshold 参数")
                
        else:
            print("❌ OptimizedVideoProcessor 没有 find_similar_scenes 方法")
            
        # 测试 find_similar_scenes_optimized 方法
        if hasattr(processor, 'find_similar_scenes_optimized'):
            print("✅ OptimizedVideoProcessor 有 find_similar_scenes_optimized 方法")
            
            import inspect
            sig = inspect.signature(processor.find_similar_scenes_optimized)
            params = list(sig.parameters.keys())
            
            print(f"📋 优化方法参数: {params}")
            
            if 'feature_frames' in params:
                print("✅ find_similar_scenes_optimized 支持 feature_frames 参数")
            else:
                print("❌ find_similar_scenes_optimized 不支持 feature_frames 参数")
        else:
            print("❌ OptimizedVideoProcessor 没有 find_similar_scenes_optimized 方法")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_extract_features_parallel():
    """测试 extract_features_parallel 方法"""
    print("\n🔍 测试 extract_features_parallel 方法...")
    
    try:
        from performance_optimizer import PerformanceOptimizer
        
        optimizer = PerformanceOptimizer(max_workers=4)
        
        if hasattr(optimizer, 'extract_features_parallel'):
            print("✅ PerformanceOptimizer 有 extract_features_parallel 方法")
            
            import inspect
            sig = inspect.signature(optimizer.extract_features_parallel)
            params = list(sig.parameters.keys())
            
            print(f"📋 方法参数: {params}")
            
            if 'feature_frames' in params:
                print("✅ extract_features_parallel 支持 feature_frames 参数")
            else:
                print("❌ extract_features_parallel 不支持 feature_frames 参数")
        else:
            print("❌ PerformanceOptimizer 没有 extract_features_parallel 方法")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 开始测试 find_similar_scenes 方法")
    print("=" * 60)
    
    test_find_similar_scenes()
    test_extract_features_parallel()
    
    print("=" * 60)
    print("🎉 测试完成！")
