#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频匹配质量验证和优化建议系统
提供详细的匹配质量分析和自动优化建议
"""

import numpy as np
import os
import cv2
from typing import List, Dict, Tuple, Optional

# 导入日志系统
try:
    from logger_system import log_info, log_error, log_warning, log_debug
except ImportError:
    def log_info(msg): print(f"INFO: {msg}")
    def log_error(msg): print(f"ERROR: {msg}")
    def log_warning(msg): print(f"WARNING: {msg}")
    def log_debug(msg): print(f"DEBUG: {msg}")

class QualityValidator:
    """视频匹配质量验证器"""
    
    def __init__(self):
        self.quality_thresholds = {
            'excellent': 0.9,
            'good': 0.75,
            'acceptable': 0.6,
            'poor': 0.4
        }
        
        self.duration_tolerance = {
            'strict': 0.05,    # 0.05秒
            'normal': 0.1,     # 0.1秒
            'loose': 0.2       # 0.2秒
        }
    
    def comprehensive_quality_analysis(self, matches: List[Dict], main_scenes: List[Dict], 
                                     main_video_path: str, aux_video_path: str) -> Dict:
        """
        综合质量分析
        :param matches: 匹配结果列表
        :param main_scenes: 主视频场景列表
        :param main_video_path: 主视频路径
        :param aux_video_path: 辅助视频路径
        :return: 详细的质量分析报告
        """
        try:
            log_info("开始综合质量分析...")
            
            if not matches:
                return self._generate_no_match_report(main_scenes)
            
            # 基础统计分析
            basic_stats = self._calculate_basic_statistics(matches, main_scenes)
            
            # 时长精度分析
            duration_analysis = self._analyze_duration_accuracy(matches, main_scenes)
            
            # 相似度分布分析
            similarity_analysis = self._analyze_similarity_distribution(matches)
            
            # 覆盖率分析
            coverage_analysis = self._analyze_coverage(matches, main_scenes)
            
            # 连续性分析
            continuity_analysis = self._analyze_continuity(matches, main_scenes)
            
            # 质量一致性分析
            consistency_analysis = self._analyze_quality_consistency(matches)
            
            # 生成综合评分
            overall_score = self._calculate_overall_score(
                basic_stats, duration_analysis, similarity_analysis, 
                coverage_analysis, continuity_analysis, consistency_analysis
            )
            
            # 生成优化建议
            recommendations = self._generate_optimization_recommendations(
                basic_stats, duration_analysis, similarity_analysis,
                coverage_analysis, continuity_analysis, consistency_analysis
            )
            
            # 生成风险评估
            risk_assessment = self._assess_risks(matches, main_scenes)
            
            return {
                'overall_score': overall_score,
                'basic_statistics': basic_stats,
                'duration_analysis': duration_analysis,
                'similarity_analysis': similarity_analysis,
                'coverage_analysis': coverage_analysis,
                'continuity_analysis': continuity_analysis,
                'consistency_analysis': consistency_analysis,
                'recommendations': recommendations,
                'risk_assessment': risk_assessment,
                'quality_level': self._determine_quality_level(overall_score)
            }
            
        except Exception as e:
            log_error(f"综合质量分析失败: {e}")
            return self._generate_error_report(str(e))
    
    def _calculate_basic_statistics(self, matches: List[Dict], main_scenes: List[Dict]) -> Dict:
        """计算基础统计信息"""
        similarities = [m['similarity'] for m in matches]
        duration_errors = [m.get('duration_error', 0) for m in matches]
        
        return {
            'total_matches': len(matches),
            'total_scenes': len(main_scenes),
            'match_rate': len(matches) / len(main_scenes),
            'avg_similarity': np.mean(similarities),
            'min_similarity': np.min(similarities),
            'max_similarity': np.max(similarities),
            'std_similarity': np.std(similarities),
            'avg_duration_error': np.mean(duration_errors),
            'max_duration_error': np.max(duration_errors),
            'std_duration_error': np.std(duration_errors)
        }
    
    def _analyze_duration_accuracy(self, matches: List[Dict], main_scenes: List[Dict]) -> Dict:
        """分析时长精度"""
        duration_errors = [m.get('duration_error', 0) for m in matches]
        
        strict_count = sum(1 for e in duration_errors if e <= self.duration_tolerance['strict'])
        normal_count = sum(1 for e in duration_errors if e <= self.duration_tolerance['normal'])
        loose_count = sum(1 for e in duration_errors if e <= self.duration_tolerance['loose'])
        
        return {
            'avg_error': np.mean(duration_errors),
            'max_error': np.max(duration_errors),
            'std_error': np.std(duration_errors),
            'strict_accuracy': strict_count / len(matches),
            'normal_accuracy': normal_count / len(matches),
            'loose_accuracy': loose_count / len(matches),
            'error_distribution': {
                'excellent': strict_count,
                'good': normal_count - strict_count,
                'acceptable': loose_count - normal_count,
                'poor': len(matches) - loose_count
            }
        }
    
    def _analyze_similarity_distribution(self, matches: List[Dict]) -> Dict:
        """分析相似度分布"""
        similarities = [m['similarity'] for m in matches]
        
        excellent_count = sum(1 for s in similarities if s >= self.quality_thresholds['excellent'])
        good_count = sum(1 for s in similarities if s >= self.quality_thresholds['good'])
        acceptable_count = sum(1 for s in similarities if s >= self.quality_thresholds['acceptable'])
        poor_count = sum(1 for s in similarities if s >= self.quality_thresholds['poor'])
        
        return {
            'distribution': {
                'excellent': excellent_count,
                'good': good_count - excellent_count,
                'acceptable': acceptable_count - good_count,
                'poor': poor_count - acceptable_count,
                'very_poor': len(matches) - poor_count
            },
            'percentiles': {
                'p25': np.percentile(similarities, 25),
                'p50': np.percentile(similarities, 50),
                'p75': np.percentile(similarities, 75),
                'p90': np.percentile(similarities, 90)
            }
        }
    
    def _analyze_coverage(self, matches: List[Dict], main_scenes: List[Dict]) -> Dict:
        """分析覆盖率"""
        matched_indices = set(m['main_scene_index'] for m in matches)
        unmatched_indices = set(range(len(main_scenes))) - matched_indices
        
        # 分析未匹配场景的特征
        unmatched_durations = [
            main_scenes[i]['end_time'] - main_scenes[i]['start_time'] 
            for i in unmatched_indices
        ]
        
        return {
            'coverage_rate': len(matches) / len(main_scenes),
            'matched_count': len(matches),
            'unmatched_count': len(unmatched_indices),
            'unmatched_indices': list(unmatched_indices),
            'unmatched_avg_duration': np.mean(unmatched_durations) if unmatched_durations else 0,
            'coverage_gaps': self._identify_coverage_gaps(matched_indices, len(main_scenes))
        }
    
    def _analyze_continuity(self, matches: List[Dict], main_scenes: List[Dict]) -> Dict:
        """分析连续性"""
        matched_indices = sorted([m['main_scene_index'] for m in matches])
        
        # 计算连续段
        continuous_segments = []
        current_segment = []
        
        for i, idx in enumerate(matched_indices):
            if not current_segment or idx == matched_indices[i-1] + 1:
                current_segment.append(idx)
            else:
                if current_segment:
                    continuous_segments.append(current_segment)
                current_segment = [idx]
        
        if current_segment:
            continuous_segments.append(current_segment)
        
        # 计算连续性指标
        max_continuous = max(len(seg) for seg in continuous_segments) if continuous_segments else 0
        avg_continuous = np.mean([len(seg) for seg in continuous_segments]) if continuous_segments else 0
        
        return {
            'continuous_segments': continuous_segments,
            'segment_count': len(continuous_segments),
            'max_continuous_length': max_continuous,
            'avg_continuous_length': avg_continuous,
            'continuity_ratio': max_continuous / len(main_scenes) if main_scenes else 0
        }
    
    def _analyze_quality_consistency(self, matches: List[Dict]) -> Dict:
        """分析质量一致性"""
        similarities = [m['similarity'] for m in matches]
        duration_errors = [m.get('duration_error', 0) for m in matches]
        
        # 计算变异系数
        similarity_cv = np.std(similarities) / np.mean(similarities) if np.mean(similarities) > 0 else 0
        duration_cv = np.std(duration_errors) / np.mean(duration_errors) if np.mean(duration_errors) > 0 else 0
        
        return {
            'similarity_consistency': 1 - similarity_cv,  # 越接近1越一致
            'duration_consistency': 1 - duration_cv,
            'overall_consistency': (1 - similarity_cv + 1 - duration_cv) / 2,
            'quality_variance': np.var(similarities),
            'outlier_count': self._count_outliers(similarities)
        }
    
    def _calculate_overall_score(self, basic_stats: Dict, duration_analysis: Dict, 
                               similarity_analysis: Dict, coverage_analysis: Dict,
                               continuity_analysis: Dict, consistency_analysis: Dict) -> float:
        """计算综合评分"""
        # 各项指标权重
        weights = {
            'similarity': 0.3,
            'duration': 0.25,
            'coverage': 0.2,
            'continuity': 0.15,
            'consistency': 0.1
        }
        
        # 计算各项得分
        similarity_score = basic_stats['avg_similarity']
        duration_score = 1 - min(1, duration_analysis['avg_error'] / 0.5)  # 0.5秒为满分基准
        coverage_score = coverage_analysis['coverage_rate']
        continuity_score = continuity_analysis['continuity_ratio']
        consistency_score = consistency_analysis['overall_consistency']
        
        # 加权计算总分
        overall_score = (
            similarity_score * weights['similarity'] +
            duration_score * weights['duration'] +
            coverage_score * weights['coverage'] +
            continuity_score * weights['continuity'] +
            consistency_score * weights['consistency']
        )
        
        return max(0, min(1, overall_score))
    
    def _generate_optimization_recommendations(self, basic_stats: Dict, duration_analysis: Dict,
                                             similarity_analysis: Dict, coverage_analysis: Dict,
                                             continuity_analysis: Dict, consistency_analysis: Dict) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 相似度相关建议
        if basic_stats['avg_similarity'] < 0.7:
            recommendations.append("平均相似度较低，建议降低相似度阈值或使用更多特征类型")
        
        if basic_stats['std_similarity'] > 0.2:
            recommendations.append("相似度差异较大，建议检查视频内容一致性或调整特征权重")
        
        # 时长精度相关建议
        if duration_analysis['avg_error'] > 0.1:
            recommendations.append("时长误差较大，建议启用更精确的时长匹配算法")
        
        if duration_analysis['strict_accuracy'] < 0.8:
            recommendations.append("时长精度不足，建议使用智能时长匹配策略")
        
        # 覆盖率相关建议
        if coverage_analysis['coverage_rate'] < 0.7:
            recommendations.append("匹配覆盖率较低，建议降低相似度阈值或检查视频内容相关性")
        
        if len(coverage_analysis['coverage_gaps']) > 3:
            recommendations.append("存在多个覆盖空隙，建议使用连续性优化算法")
        
        # 连续性相关建议
        if continuity_analysis['continuity_ratio'] < 0.5:
            recommendations.append("连续性较差，建议优化场景检测参数或使用序列匹配算法")
        
        # 一致性相关建议
        if consistency_analysis['overall_consistency'] < 0.7:
            recommendations.append("质量一致性较差，建议使用自适应阈值或质量平衡算法")
        
        if not recommendations:
            recommendations.append("匹配质量良好，无需特别优化")
        
        return recommendations
    
    def _assess_risks(self, matches: List[Dict], main_scenes: List[Dict]) -> Dict:
        """评估风险"""
        risks = {
            'high': [],
            'medium': [],
            'low': []
        }
        
        # 检查各种风险
        similarities = [m['similarity'] for m in matches]
        duration_errors = [m.get('duration_error', 0) for m in matches]
        
        # 低相似度风险
        low_sim_count = sum(1 for s in similarities if s < 0.6)
        if low_sim_count > len(matches) * 0.3:
            risks['high'].append(f"有{low_sim_count}个低相似度匹配，可能影响视频质量")
        elif low_sim_count > 0:
            risks['medium'].append(f"有{low_sim_count}个低相似度匹配")
        
        # 时长误差风险
        large_error_count = sum(1 for e in duration_errors if e > 0.2)
        if large_error_count > 0:
            risks['high'].append(f"有{large_error_count}个大时长误差匹配，可能导致时长不准确")
        
        # 覆盖率风险
        coverage_rate = len(matches) / len(main_scenes)
        if coverage_rate < 0.5:
            risks['high'].append("匹配覆盖率过低，大量内容将无法替换")
        elif coverage_rate < 0.8:
            risks['medium'].append("匹配覆盖率中等，部分内容无法替换")
        
        return risks
    
    def _determine_quality_level(self, score: float) -> str:
        """确定质量等级"""
        if score >= 0.9:
            return "优秀"
        elif score >= 0.75:
            return "良好"
        elif score >= 0.6:
            return "可接受"
        elif score >= 0.4:
            return "较差"
        else:
            return "很差"
    
    def _generate_no_match_report(self, main_scenes: List[Dict]) -> Dict:
        """生成无匹配报告"""
        return {
            'overall_score': 0.0,
            'quality_level': "无匹配",
            'recommendations': [
                "未找到任何匹配，建议：",
                "1. 降低相似度阈值",
                "2. 检查视频内容相关性",
                "3. 尝试不同的特征类型组合",
                "4. 调整场景检测参数"
            ],
            'risk_assessment': {
                'high': ["无法生成任何替换内容"],
                'medium': [],
                'low': []
            }
        }
    
    def _generate_error_report(self, error_msg: str) -> Dict:
        """生成错误报告"""
        return {
            'overall_score': 0.0,
            'quality_level': "分析失败",
            'error': error_msg,
            'recommendations': ["质量分析失败，请检查输入数据"]
        }
    
    def _identify_coverage_gaps(self, matched_indices: set, total_scenes: int) -> List[Tuple[int, int]]:
        """识别覆盖空隙"""
        gaps = []
        sorted_indices = sorted(matched_indices)
        
        for i in range(len(sorted_indices) - 1):
            current = sorted_indices[i]
            next_idx = sorted_indices[i + 1]
            if next_idx - current > 1:
                gaps.append((current + 1, next_idx - 1))
        
        return gaps
    
    def _count_outliers(self, values: List[float]) -> int:
        """计算异常值数量"""
        if len(values) < 4:
            return 0
        
        q1 = np.percentile(values, 25)
        q3 = np.percentile(values, 75)
        iqr = q3 - q1
        
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        return sum(1 for v in values if v < lower_bound or v > upper_bound)

def print_quality_report(report: Dict):
    """打印质量报告"""
    print(f"\n📊 视频匹配质量报告")
    print(f"=" * 50)
    
    print(f"🏆 综合评分: {report['overall_score']:.3f} ({report['quality_level']})")
    
    if 'basic_statistics' in report:
        stats = report['basic_statistics']
        print(f"\n📈 基础统计:")
        print(f"   匹配数量: {stats['total_matches']}/{stats['total_scenes']}")
        print(f"   匹配率: {stats['match_rate']*100:.1f}%")
        print(f"   平均相似度: {stats['avg_similarity']:.3f}")
        print(f"   相似度范围: {stats['min_similarity']:.3f} - {stats['max_similarity']:.3f}")
        print(f"   平均时长误差: {stats['avg_duration_error']:.3f}秒")
    
    if 'recommendations' in report:
        print(f"\n💡 优化建议:")
        for i, rec in enumerate(report['recommendations'], 1):
            print(f"   {i}. {rec}")
    
    if 'risk_assessment' in report:
        risks = report['risk_assessment']
        if risks['high']:
            print(f"\n⚠️ 高风险:")
            for risk in risks['high']:
                print(f"   • {risk}")
        if risks['medium']:
            print(f"\n⚡ 中风险:")
            for risk in risks['medium']:
                print(f"   • {risk}")

# 使用示例
if __name__ == "__main__":
    # 创建质量验证器
    validator = QualityValidator()
    
    # 示例匹配数据
    sample_matches = [
        {'main_scene_index': 0, 'similarity': 0.85, 'duration_error': 0.02},
        {'main_scene_index': 1, 'similarity': 0.78, 'duration_error': 0.05},
        {'main_scene_index': 3, 'similarity': 0.92, 'duration_error': 0.01},
    ]
    
    sample_scenes = [
        {'start_time': 0, 'end_time': 5},
        {'start_time': 5, 'end_time': 10},
        {'start_time': 10, 'end_time': 15},
        {'start_time': 15, 'end_time': 20},
        {'start_time': 20, 'end_time': 25}
    ]
    
    # 进行质量分析
    report = validator.comprehensive_quality_analysis(
        sample_matches, sample_scenes, "main.mp4", "aux.mp4"
    )
    
    # 打印报告
    print_quality_report(report)
