# SpringBoot 激活系统配置文件

server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: activation-system
  
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************
    username: root
    password: your_password_here
    
    # 连接池配置
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: ActivationHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000

  # MyBatis配置
  mybatis:
    mapper-locations: classpath:mapper/*.xml
    type-aliases-package: com.example.activation.entity
    configuration:
      map-underscore-to-camel-case: true
      cache-enabled: true
      lazy-loading-enabled: true
      multiple-result-sets-enabled: true
      use-column-label: true
      use-generated-keys: true
      auto-mapping-behavior: partial

# 日志配置
logging:
  level:
    com.example.activation: DEBUG
    org.springframework.web: INFO
    org.mybatis: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/activation-system.log
    max-size: 10MB
    max-history: 30

# 激活系统自定义配置
activation:
  # 默认激活码有效期（天）
  default-expire-days: 365
  # 默认最大激活次数
  default-max-activations: 1
  # 是否启用激活日志
  enable-activation-log: true
  # 清理过期激活码的间隔（小时）
  cleanup-interval-hours: 24
  # 允许的客户端版本
  allowed-client-versions:
    - "2.0"
    - "2.1"

# 跨域配置
cors:
  allowed-origins: "*"
  allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
  allowed-headers: "*"
  allow-credentials: true

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
