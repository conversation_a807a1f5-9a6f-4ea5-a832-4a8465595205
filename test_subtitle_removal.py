#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试字幕移除功能的稳定性
"""

import sys
import os
import tempfile
import time

def test_subtitle_removal_methods():
    """测试字幕移除方法"""
    print("🔍 测试字幕移除方法...")
    
    try:
        from video_processor import VideoProcessor
        
        # 创建处理器
        processor = VideoProcessor(enable_gpu=False, num_threads=2)  # 使用较少线程避免问题
        
        print("✅ VideoProcessor 创建成功")
        
        # 测试方法是否存在
        methods_to_test = [
            'remove_subtitles',
            '_process_video_multithreaded_safe',
            '_process_video_multithreaded',
            '_remove_subtitle_from_single_frame'
        ]
        
        for method_name in methods_to_test:
            if hasattr(processor, method_name):
                print(f"✅ {method_name} 方法存在")
            else:
                print(f"❌ {method_name} 方法不存在")
        
        # 测试参数
        print(f"📋 处理器配置:")
        print(f"  - GPU启用: {processor.enable_gpu}")
        print(f"  - 线程数: {processor.num_threads}")
        print(f"  - GPU可用: {processor.gpu_available}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_optimized_processor_subtitle_removal():
    """测试OptimizedVideoProcessor的字幕移除功能"""
    print("\n🔍 测试OptimizedVideoProcessor字幕移除...")
    
    try:
        from performance_optimizer import OptimizedVideoProcessor
        
        # 创建处理器
        processor = OptimizedVideoProcessor(enable_gpu=False, num_threads=2)
        
        print("✅ OptimizedVideoProcessor 创建成功")
        
        # 测试基础处理器是否正确初始化
        if hasattr(processor, 'base_processor'):
            print("✅ 基础处理器已创建")
            
            # 测试基础处理器的字幕移除方法
            base_processor = processor.base_processor
            if hasattr(base_processor, 'remove_subtitles'):
                print("✅ 基础处理器有 remove_subtitles 方法")
            else:
                print("❌ 基础处理器没有 remove_subtitles 方法")
                
            # 测试配置
            print(f"📋 基础处理器配置:")
            print(f"  - GPU启用: {base_processor.enable_gpu}")
            print(f"  - 线程数: {base_processor.num_threads}")
            print(f"  - GPU可用: {base_processor.gpu_available}")
        else:
            print("❌ 基础处理器创建失败")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_thread_safety():
    """测试线程安全性"""
    print("\n🔍 测试线程安全性...")
    
    try:
        from video_processor import VideoProcessor
        import threading
        
        results = []
        
        def create_processor():
            try:
                processor = VideoProcessor(enable_gpu=False, num_threads=1)
                results.append(True)
            except Exception as e:
                print(f"线程创建处理器失败: {e}")
                results.append(False)
        
        # 创建多个线程同时创建处理器
        threads = []
        for i in range(3):
            thread = threading.Thread(target=create_processor)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        success_count = sum(results)
        print(f"📊 线程安全测试结果: {success_count}/{len(results)} 成功")
        
        if success_count == len(results):
            print("✅ 线程安全测试通过")
            return True
        else:
            print("❌ 线程安全测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 线程安全测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_memory_management():
    """测试内存管理"""
    print("\n🔍 测试内存管理...")
    
    try:
        import psutil
        import gc
        
        # 获取初始内存使用
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        print(f"📊 初始内存使用: {initial_memory:.1f} MB")
        
        # 创建和销毁多个处理器
        for i in range(5):
            from video_processor import VideoProcessor
            processor = VideoProcessor(enable_gpu=False, num_threads=1)
            
            # 模拟一些操作
            temp_dir = processor.temp_dir
            session_id = processor.session_id
            
            # 手动清理
            del processor
            gc.collect()
            
            current_memory = process.memory_info().rss / 1024 / 1024
            print(f"  第{i+1}次创建后内存: {current_memory:.1f} MB")
        
        # 最终内存检查
        final_memory = process.memory_info().rss / 1024 / 1024
        memory_increase = final_memory - initial_memory
        
        print(f"📊 最终内存使用: {final_memory:.1f} MB")
        print(f"📊 内存增长: {memory_increase:.1f} MB")
        
        if memory_increase < 100:  # 内存增长小于100MB认为正常
            print("✅ 内存管理测试通过")
            return True
        else:
            print("⚠️ 内存增长较大，可能存在内存泄漏")
            return False
            
    except ImportError:
        print("⚠️ psutil未安装，跳过内存测试")
        return True
    except Exception as e:
        print(f"❌ 内存管理测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试字幕移除功能稳定性")
    print("=" * 60)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(test_subtitle_removal_methods())
    test_results.append(test_optimized_processor_subtitle_removal())
    test_results.append(test_thread_safety())
    test_results.append(test_memory_management())
    
    print("=" * 60)
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"🎉 测试完成！通过: {passed}/{total}")
    
    if passed == total:
        print("✅ 所有测试通过，字幕移除功能稳定")
    else:
        print("⚠️ 部分测试失败，需要进一步优化")
    
    print("\n💡 建议:")
    print("1. 对于大视频文件，建议使用单线程处理")
    print("2. 确保有足够的内存和临时存储空间")
    print("3. 定期清理临时文件")
    print("4. 监控内存使用情况")
