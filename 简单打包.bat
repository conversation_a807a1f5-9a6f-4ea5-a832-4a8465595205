@echo off
chcp 65001 >nul
title 余下视频乱剪工具 - 简单打包程序

echo.
echo ========================================
echo 🎬 余下视频乱剪工具 v2.0 简单打包程序
echo ========================================
echo.

echo 🔧 设置环境变量...
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1

echo 🧹 清理旧的构建文件...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist "余下视频乱剪工具.spec" del "余下视频乱剪工具.spec"

echo.
echo 🚀 开始打包...
echo 使用简化命令避免编码问题
echo.

pyinstaller ^
  --onefile ^
  --windowed ^
  --clean ^
  --noconfirm ^
  --icon=img/logo.ico ^
  --name="余下视频乱剪工具" ^
  --hidden-import=cv2 ^
  --hidden-import=numpy ^
  --hidden-import=moviepy.editor ^
  --hidden-import=scenedetect ^
  --hidden-import=PyQt5.QtCore ^
  --hidden-import=PyQt5.QtGui ^
  --hidden-import=PyQt5.QtWidgets ^
  --hidden-import=PyQt5.QtNetwork ^
  --hidden-import=performance_optimizer ^
  --hidden-import=video_processor ^
  --hidden-import=multi_rectangle_selector ^
  enhanced_video_deduplication_gui.py

echo.
if exist "dist\余下视频乱剪工具.exe" (
    echo ✅ 打包成功完成！
    echo.
    echo 📁 输出目录: dist\
    echo 📦 可执行文件: dist\余下视频乱剪工具.exe
    echo.
    
    echo 📋 复制必要文件...
    if exist README.md copy README.md dist\ >nul
    if exist config_example.ini copy config_example.ini dist\ >nul
    
    echo 📜 创建启动脚本...
    echo @echo off > "dist\启动程序.bat"
    echo chcp 65001 ^>nul >> "dist\启动程序.bat"
    echo title 余下视频乱剪工具 v2.0 >> "dist\启动程序.bat"
    echo echo 🎬 余下视频乱剪工具 v2.0 >> "dist\启动程序.bat"
    echo echo 正在启动程序... >> "dist\启动程序.bat"
    echo echo. >> "dist\启动程序.bat"
    echo "余下视频乱剪工具.exe" >> "dist\启动程序.bat"
    echo if errorlevel 1 ^( >> "dist\启动程序.bat"
    echo     echo. >> "dist\启动程序.bat"
    echo     echo ❌ 程序运行出错 >> "dist\启动程序.bat"
    echo     echo 请检查系统要求和依赖 >> "dist\启动程序.bat"
    echo     pause >> "dist\启动程序.bat"
    echo ^) >> "dist\启动程序.bat"
    
    echo.
    echo 🎉 打包完成！
    echo.
    echo 💡 使用说明:
    echo 1. 进入 dist 目录
    echo 2. 双击 "余下视频乱剪工具.exe" 或 "启动程序.bat"
    echo 3. 测试所有功能是否正常
    echo.
    
    echo 📊 文件信息:
    for %%f in ("dist\余下视频乱剪工具.exe") do echo 文件大小: %%~zf 字节
    
) else (
    echo ❌ 打包失败！
    echo 请检查错误信息并重试
    echo.
    echo 💡 常见解决方案:
    echo 1. 确保已安装 PyInstaller: pip install pyinstaller
    echo 2. 确保已安装所有依赖: pip install -r requirements.txt
    echo 3. 检查文件路径是否包含特殊字符
    echo 4. 尝试以管理员身份运行
)

echo.
pause
