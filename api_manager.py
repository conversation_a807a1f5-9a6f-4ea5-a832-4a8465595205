#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一后端API管理模块
集中管理所有与后端服务器的API通信
"""

import requests
import json
import time
from typing import Dict, Any, Optional, List
from datetime import datetime
from api_config import api_config

# 导入日志系统
try:
    from logger_system import log_info, log_error, log_warning, log_debug, safe_print
except ImportError:
    # 备用函数
    def log_info(msg): print(msg)
    def log_error(msg): print(f"ERROR: {msg}")
    def log_warning(msg): print(f"WARNING: {msg}")
    def log_debug(msg): print(f"DEBUG: {msg}")
    def safe_print(*args, **kwargs): print(*args, **kwargs)


class APIManager:
    """统一API管理器"""
    
    def __init__(self, base_url: Optional[str] = None):
        """
        初始化API管理器

        Args:
            base_url: 后端服务器基础URL（可选，默认从配置文件读取）
        """
        self.base_url = (base_url or api_config.get_base_url()).rstrip('/')
        self.api_prefix = api_config.get_endpoint("activation")
        self.timeout = api_config.get_timeout()
        self.headers = api_config.get_headers()
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None,
                     timeout: Optional[int] = None, retry_backup: bool = True) -> Dict[str, Any]:
        """
        发送HTTP请求的通用方法（支持自动故障转移）

        Args:
            method: HTTP方法 (GET, POST, PUT, DELETE)
            endpoint: API端点
            data: 请求数据
            timeout: 超时时间
            retry_backup: 是否在主服务器失败时尝试备用服务器

        Returns:
            响应数据字典
        """
        # 首先尝试主服务器
        result = self._make_single_request(method, endpoint, data, timeout)

        # 如果主服务器失败且启用了备用服务器重试
        if not result.get('success') and retry_backup:
            error_type = result.get('error_type')
            if error_type in ['timeout', 'connection_error', 'request_error']:
                # 尝试备用服务器
                backup_urls = api_config.get_backup_urls()
                original_url = self.base_url

                for backup_url in backup_urls:
                    log_warning(f"⚠️ 主服务器失败，尝试备用服务器: {backup_url}")
                    self.base_url = backup_url.rstrip('/')

                    backup_result = self._make_single_request(method, endpoint, data, timeout)
                    if backup_result.get('success'):
                        log_info(f"✅ 备用服务器连接成功: {backup_url}")
                        return backup_result

                # 恢复原始URL
                self.base_url = original_url
                log_error(f"❌ 所有服务器都无法连接")

        return result

    def _make_single_request(self, method: str, endpoint: str, data: Optional[Dict] = None,
                           timeout: Optional[int] = None) -> Dict[str, Any]:
        """
        发送单个HTTP请求

        Args:
            method: HTTP方法
            endpoint: API端点
            data: 请求数据
            timeout: 超时时间

        Returns:
            响应数据字典
        """
        url = f"{self.base_url}{self.api_prefix}{endpoint}"
        request_timeout = timeout or self.timeout

        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=self.headers, timeout=request_timeout)
            elif method.upper() == 'POST':
                response = requests.post(url, json=data, headers=self.headers, timeout=request_timeout)
            elif method.upper() == 'PUT':
                response = requests.put(url, json=data, headers=self.headers, timeout=request_timeout)
            elif method.upper() == 'DELETE':
                response = requests.delete(url, headers=self.headers, timeout=request_timeout)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")

            # 解析响应
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    'success': False,
                    'message': f'HTTP错误: {response.status_code}',
                    'status_code': response.status_code,
                    'response_text': response.text
                }

        except requests.exceptions.Timeout:
            return {
                'success': False,
                'message': '请求超时，请检查网络连接',
                'error_type': 'timeout'
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'message': '无法连接到服务器',
                'error_type': 'connection_error'
            }
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'message': f'网络请求失败: {str(e)}',
                'error_type': 'request_error'
            }
        except json.JSONDecodeError:
            return {
                'success': False,
                'message': '服务器响应格式错误',
                'error_type': 'json_decode_error'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'未知错误: {str(e)}',
                'error_type': 'unknown_error'
            }
    
    # ==================== 激活码验证相关API ====================
    
    def verify_activation(self, activation_code: str, machine_code: str, 
                         machine_details: Optional[Dict] = None) -> Dict[str, Any]:
        """
        验证激活码
        
        Args:
            activation_code: 激活码
            machine_code: 机器码
            machine_details: 机器详细信息（可选）
            
        Returns:
            验证结果
        """
        data = {
            'activationCode': activation_code,
            'machineCode': machine_code,
            'timestamp': int(time.time()),
            'clientVersion': '2.0'
        }
        
        if machine_details:
            # 转换字段名称从Python风格到Java风格
            converted_details = {}
            field_mapping = {
                'cpu_id': 'cpuId',
                'motherboard_serial': 'motherboardSerial',
                'bios_serial': 'biosSerial',
                'disk_serial': 'diskSerial',
                'mac_address': 'macAddress',
                'windows_product_id': 'windowsProductId',
                'machine_guid': 'machineGuid',
                'computer_name': 'computerName',
                'system': 'operatingSystem',
                'processor': 'processor'
            }

            for python_key, java_key in field_mapping.items():
                if python_key in machine_details and machine_details[python_key]:
                    converted_details[java_key] = machine_details[python_key]

            data['machineDetails'] = converted_details
            
        return self._make_request('POST', '/verify', data)
    
    def check_machine_activation(self, machine_code: str) -> Dict[str, Any]:
        """
        检查机器激活状态
        
        Args:
            machine_code: 机器码
            
        Returns:
            激活状态信息
        """
        return self._make_request('GET', f'/check/{machine_code}')
    
    # ==================== 激活码管理相关API ====================
    
    def get_activation_code_list(self) -> Dict[str, Any]:
        """
        获取所有激活码列表
        
        Returns:
            激活码列表
        """
        return self._make_request('GET', '/list')
    
    def get_activation_code_info(self, activation_code: str) -> Dict[str, Any]:
        """
        获取激活码详细信息
        
        Args:
            activation_code: 激活码
            
        Returns:
            激活码详细信息
        """
        return self._make_request('GET', f'/info/{activation_code}')
    
    def generate_activation_code(self, user_id: str, expire_time: str, 
                               max_activations: int, remark: str = "") -> Dict[str, Any]:
        """
        生成新的激活码
        
        Args:
            user_id: 用户ID
            expire_time: 过期时间 (ISO格式)
            max_activations: 最大激活次数
            remark: 备注
            
        Returns:
            生成结果
        """
        data = {
            'userId': user_id,
            'expireTime': expire_time,
            'maxActivations': max_activations,
            'remark': remark
        }
        
        return self._make_request('POST', '/generate', data)
    
    def update_activation_code(self, activation_code: str, user_id: Optional[str] = None,
                             expire_time: Optional[str] = None, max_activations: Optional[int] = None,
                             remark: Optional[str] = None) -> Dict[str, Any]:
        """
        更新激活码信息
        
        Args:
            activation_code: 激活码
            user_id: 用户ID（可选）
            expire_time: 过期时间（可选）
            max_activations: 最大激活次数（可选）
            remark: 备注（可选）
            
        Returns:
            更新结果
        """
        data = {}
        if user_id is not None:
            data['userId'] = user_id
        if expire_time is not None:
            data['expireTime'] = expire_time
        if max_activations is not None:
            data['maxActivations'] = max_activations
        if remark is not None:
            data['remark'] = remark
            
        return self._make_request('PUT', f'/update/{activation_code}', data)
    
    def delete_activation_code(self, activation_code: str) -> Dict[str, Any]:
        """
        删除激活码
        
        Args:
            activation_code: 激活码
            
        Returns:
            删除结果
        """
        return self._make_request('DELETE', f'/delete/{activation_code}')
    
    def disable_activation_code(self, activation_code: str) -> Dict[str, Any]:
        """
        禁用激活码
        
        Args:
            activation_code: 激活码
            
        Returns:
            禁用结果
        """
        return self._make_request('POST', f'/disable/{activation_code}')
    
    def enable_activation_code(self, activation_code: str) -> Dict[str, Any]:
        """
        启用激活码
        
        Args:
            activation_code: 激活码
            
        Returns:
            启用结果
        """
        return self._make_request('POST', f'/enable/{activation_code}')
    
    # ==================== 机器管理相关API ====================
    
    def get_all_machine_info(self) -> Dict[str, Any]:
        """
        获取所有机器信息
        
        Returns:
            机器信息列表
        """
        return self._make_request('GET', '/machines')
    
    # ==================== 统计和系统相关API ====================
    
    def get_activation_statistics(self) -> Dict[str, Any]:
        """
        获取激活统计信息
        
        Returns:
            统计信息
        """
        return self._make_request('GET', '/statistics')
    
    def cleanup_expired_codes(self) -> Dict[str, Any]:
        """
        清理过期激活码
        
        Returns:
            清理结果
        """
        return self._make_request('POST', '/cleanup')
    
    def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            健康状态
        """
        return self._make_request('GET', '/health')
    
    # ==================== 工具方法 ====================
    
    def set_base_url(self, base_url: str):
        """
        设置基础URL

        Args:
            base_url: 新的基础URL
        """
        self.base_url = base_url.rstrip('/')
        # 同时更新内存配置
        api_config.update_base_url(self.base_url)

    def set_timeout(self, timeout: int):
        """
        设置请求超时时间

        Args:
            timeout: 超时时间（秒）
        """
        self.timeout = timeout
        # 同时更新内存配置
        api_config.set("api.timeout", timeout)
    
    def test_connection(self) -> bool:
        """
        测试与服务器的连接
        
        Returns:
            连接是否成功
        """
        result = self.health_check()
        return result.get('success', False)


# 创建全局API管理器实例
api_manager = APIManager()


# ==================== 便捷函数 ====================

def verify_activation_code(activation_code: str, machine_code: str) -> Dict[str, Any]:
    """便捷函数：验证激活码"""
    return api_manager.verify_activation(activation_code, machine_code)


def check_server_connection() -> bool:
    """便捷函数：检查服务器连接"""
    return api_manager.test_connection()


def get_activation_list() -> Dict[str, Any]:
    """便捷函数：获取激活码列表"""
    return api_manager.get_activation_code_list()


if __name__ == "__main__":
    # 测试API管理器
    log_info(f"🔍 测试API管理器...")
    
    # 测试连接
    if check_server_connection():
        log_info(f"✅ 服务器连接正常")
    else:
        log_error(f"❌ 服务器连接失败")
    
    # 测试获取激活码列表
    result = get_activation_list()
    if result.get('success'):
        log_info(f"✅ 获取激活码列表成功，共 {result.get('total', 0)} 条记录")
    else:
        log_error(f"❌ 获取激活码列表失败: {result.get('message')}")
