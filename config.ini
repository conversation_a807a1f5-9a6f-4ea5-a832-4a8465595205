# 智能视频反推工具配置文件
# 
# 基于示例文件创建的实际配置文件

[DEFAULT]
# 场景检测敏感度 (10-100)
scene_threshold = 30

# 相似度匹配精度 (0.50-0.95)
similarity_threshold = 0.80

# 特征提取帧数 (5-50)
feature_frames = 10

# 处理线程数 (1-16)
thread_count = 4

# 是否启用性能优化
enable_optimization = true

# 输出视频质量 (high/standard/fast)
output_quality = standard

# 替换策略 (0:相似度匹配, 1:均匀替换, 2:随机替换)
replacement_strategy = 0

# 替换率 (0.1-0.9)
replacement_rate = 0.5

[PATHS]
# 默认输入目录
default_input_dir = ./input

# 默认输出目录  
default_output_dir = ./output

# 临时文件目录
temp_dir = ./temp

[ADVANCED]
# 是否启用音频处理
enable_audio_processing = false

# 是否启用智能裁剪
enable_smart_crop = false

# 日志级别 (DEBUG/INFO/WARNING/ERROR)
log_level = INFO

[SUBTITLE_REMOVAL]
# 字幕检测敏感度 (0.1-1.0)
detection_sensitivity = 0.5

# 模糊强度 (5-35)
blur_strength = 15

# 是否自动检测字幕区域
auto_detect = true

# 默认字幕区域起始位置 (%)
default_start_position = 80

# 默认字幕区域结束位置 (%)
default_end_position = 100 