import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QPushButton, QLabel, QLineEdit, QFileDialog, 
                             QProgressBar, QTextEdit, QGroupBox, QSlider, QSpinBox,
                             QGridLayout, QFrame, QMessageBox, QTabWidget, QSplitter)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor, QPixmap
from video_processor import VideoProcessor

class VideoProcessingThread(QThread):
    """视频处理线程，用于在后台执行视频处理任务"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_processing = pyqtSignal(str)
    error_occurred = pyqtSignal(str)

    def __init__(self, main_video_path, aux_video_path, output_path, threshold, similarity_threshold):
        super().__init__()
        self.main_video_path = main_video_path
        self.aux_video_path = aux_video_path
        self.output_path = output_path
        self.threshold = threshold
        self.similarity_threshold = similarity_threshold
        self.processor = VideoProcessor()

    def run(self):
        try:
            # 步骤1: 切割主视频
            self.status_updated.emit("正在切割主视频...")
            self.progress_updated.emit(10)
            main_scenes = self.processor.split_video_into_scenes(self.main_video_path, self.threshold)
            
            # 步骤2: 切割辅助视频
            self.status_updated.emit("正在切割辅助视频...")
            self.progress_updated.emit(30)
            aux_scenes = self.processor.split_video_into_scenes(self.aux_video_path, self.threshold)
            
            # 步骤3: 查找相似视频段
            self.status_updated.emit("正在查找相似视频段...")
            self.progress_updated.emit(50)
            similar_matches = self.processor.find_similar_scenes(
                main_scenes, aux_scenes, self.main_video_path, self.aux_video_path, self.similarity_threshold
            )
            
            # 步骤4: 替换并合成视频
            self.status_updated.emit("正在替换并合成视频...")
            self.progress_updated.emit(70)

            # 创建进度回调函数
            def synthesis_progress_callback(current, total, message):
                """视频合成进度回调"""
                if total > 0:
                    # 将70-100%的进度范围分配给视频合成
                    progress = 70 + int((current / total) * 30)
                    self.progress_updated.emit(progress)
                self.status_updated.emit(message)

            self.processor.replace_and_concatenate_videos(
                self.main_video_path, self.aux_video_path, main_scenes, aux_scenes, similar_matches,
                self.output_path, progress_callback=synthesis_progress_callback
            )
            
            self.progress_updated.emit(100)
            self.status_updated.emit("处理完成！")
            self.finished_processing.emit(f"视频处理完成！\n主视频段数: {len(main_scenes)}\n辅助视频段数: {len(aux_scenes)}\n相似匹配数: {len(similar_matches)}")
            
        except Exception as e:
            self.error_occurred.emit(f"处理过程中发生错误: {str(e)}")

class VideoDeduplicationGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.processing_thread = None

    def init_ui(self):
        self.setWindowTitle("视频反推软件 - 智能去重工具")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置应用程序样式 - 优化字体清晰度
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
            }
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                margin-top: 12px;
                padding-top: 12px;
                background-color: rgba(255, 255, 255, 0.95);
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
            }
            QPushButton {
                background-color: #28a745;
                border: none;
                color: white;
                padding: 12px 24px;
                text-align: center;
                font-size: 15px;
                font-weight: bold;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
                margin: 4px 2px;
                border-radius: 8px;
                min-width: 100px;
                min-height: 18px;
            }
            QPushButton:hover {
                background-color: #20c997;
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
            QLineEdit {
                padding: 12px;
                border: 2px solid #ced4da;
                border-radius: 6px;
                font-size: 15px;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
                background-color: white;
                color: #2c3e50;
                min-height: 18px;
            }
            QLineEdit:focus {
                border-color: #007bff;
                box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
            }
            QProgressBar {
                border: 2px solid #ced4da;
                border-radius: 10px;
                text-align: center;
                font-weight: bold;
                font-size: 15px;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
                background-color: #f8f9fa;
                color: #2c3e50;
                min-height: 22px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #28a745, stop:1 #20c997);
                border-radius: 8px;
                margin: 2px;
            }
            QTextEdit {
                border: 2px solid #ced4da;
                border-radius: 6px;
                font-family: 'Microsoft YaHei UI', 'Consolas', 'Monaco', monospace;
                font-size: 13px;
                background-color: white;
                color: #2c3e50;
                padding: 8px;
                line-height: 1.4;
            }
            QSlider::groove:horizontal {
                border: 1px solid #ced4da;
                background: #f8f9fa;
                height: 12px;
                border-radius: 6px;
            }
            QSlider::sub-page:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #28a745, stop:1 #20c997);
                border: 1px solid #20c997;
                height: 12px;
                border-radius: 6px;
            }
            QSlider::add-page:horizontal {
                background: #f8f9fa;
                border: 1px solid #ced4da;
                height: 12px;
                border-radius: 6px;
            }
            QSlider::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #e9ecef);
                border: 2px solid #28a745;
                width: 20px;
                margin-top: -4px;
                margin-bottom: -4px;
                border-radius: 10px;
            }
            QSlider::handle:horizontal:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 2px solid #20c997;
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            }
            QLabel {
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
                color: #2c3e50;
                font-size: 14px;
            }
        """)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标题
        title_label = QLabel("🎬 视频反推软件 - 智能去重工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Microsoft YaHei UI", 22, QFont.Bold))
        title_label.setStyleSheet("""
            color: #2c3e50;
            margin: 20px;
            padding: 15px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
            color: white;
            border-radius: 12px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        """)
        main_layout.addWidget(title_label)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # 左侧面板
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # 文件选择区域
        file_group = QGroupBox("文件选择")
        file_layout = QGridLayout(file_group)
        
        # 主视频选择
        main_video_label = QLabel("🎥 主视频:")
        main_video_label.setStyleSheet("font-size: 15px; font-weight: bold; color: #2c3e50;")
        file_layout.addWidget(main_video_label, 0, 0)
        self.main_video_path = QLineEdit()
        self.main_video_path.setPlaceholderText("选择主视频文件...")
        file_layout.addWidget(self.main_video_path, 0, 1)
        self.main_video_btn = QPushButton("浏览")
        self.main_video_btn.clicked.connect(self.select_main_video)
        file_layout.addWidget(self.main_video_btn, 0, 2)

        # 辅助视频选择
        aux_video_label = QLabel("🎬 辅助视频:")
        aux_video_label.setStyleSheet("font-size: 15px; font-weight: bold; color: #2c3e50;")
        file_layout.addWidget(aux_video_label, 1, 0)
        self.aux_video_path = QLineEdit()
        self.aux_video_path.setPlaceholderText("选择辅助视频文件...")
        file_layout.addWidget(self.aux_video_path, 1, 1)
        self.aux_video_btn = QPushButton("浏览")
        self.aux_video_btn.clicked.connect(self.select_aux_video)
        file_layout.addWidget(self.aux_video_btn, 1, 2)

        # 输出文件选择
        output_label = QLabel("💾 输出文件:")
        output_label.setStyleSheet("font-size: 15px; font-weight: bold; color: #2c3e50;")
        file_layout.addWidget(output_label, 2, 0)
        self.output_path = QLineEdit()
        self.output_path.setPlaceholderText("选择输出文件路径...")
        file_layout.addWidget(self.output_path, 2, 1)
        self.output_btn = QPushButton("浏览")
        self.output_btn.clicked.connect(self.select_output_path)
        file_layout.addWidget(self.output_btn, 2, 2)
        
        left_layout.addWidget(file_group)

        # 参数设置区域
        param_group = QGroupBox("参数设置")
        param_layout = QGridLayout(param_group)
        
        # 场景检测阈值
        param_layout.addWidget(QLabel("场景检测阈值:"), 0, 0)
        self.threshold_slider = QSlider(Qt.Horizontal)
        self.threshold_slider.setRange(10, 100)
        self.threshold_slider.setValue(30)
        self.threshold_slider.valueChanged.connect(self.update_threshold_label)
        param_layout.addWidget(self.threshold_slider, 0, 1)
        self.threshold_label = QLabel("30.0")
        param_layout.addWidget(self.threshold_label, 0, 2)
        
        # 相似度阈值
        param_layout.addWidget(QLabel("相似度阈值:"), 1, 0)
        self.similarity_slider = QSlider(Qt.Horizontal)
        self.similarity_slider.setRange(50, 95)
        self.similarity_slider.setValue(80)
        self.similarity_slider.valueChanged.connect(self.update_similarity_label)
        param_layout.addWidget(self.similarity_slider, 1, 1)
        self.similarity_label = QLabel("0.80")
        param_layout.addWidget(self.similarity_label, 1, 2)
        
        left_layout.addWidget(param_group)

        # 控制按钮区域
        control_group = QGroupBox("操作控制")
        control_layout = QVBoxLayout(control_group)
        
        self.start_btn = QPushButton("开始处理")
        self.start_btn.setStyleSheet("QPushButton { font-size: 16px; padding: 15px; }")
        self.start_btn.clicked.connect(self.start_processing)
        control_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("停止处理")
        self.stop_btn.setStyleSheet("QPushButton { background-color: #f44336; } QPushButton:hover { background-color: #da190b; }")
        self.stop_btn.clicked.connect(self.stop_processing)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)
        
        left_layout.addWidget(control_group)
        
        # 添加弹性空间
        left_layout.addStretch()
        
        splitter.addWidget(left_panel)

        # 右侧面板
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # 进度显示区域
        progress_group = QGroupBox("处理进度")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)
        
        self.status_label = QLabel("就绪")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("font-size: 14px; color: #666; margin: 10px;")
        progress_layout.addWidget(self.status_label)
        
        right_layout.addWidget(progress_group)

        # 日志显示区域
        log_group = QGroupBox("处理日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(200)
        log_layout.addWidget(self.log_text)
        
        right_layout.addWidget(log_group)

        # 结果显示区域
        result_group = QGroupBox("处理结果")
        result_layout = QVBoxLayout(result_group)
        
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        result_layout.addWidget(self.result_text)
        
        right_layout.addWidget(result_group)
        
        splitter.addWidget(right_panel)
        
        # 设置分割器比例
        splitter.setSizes([400, 600])

        # 初始化日志
        self.log_message("应用程序启动完成")

    def update_threshold_label(self, value):
        self.threshold_label.setText(f"{value}.0")

    def update_similarity_label(self, value):
        self.similarity_label.setText(f"{value/100:.2f}")

    def select_main_video(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择主视频文件", "", "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv)"
        )
        if file_path:
            self.main_video_path.setText(file_path)
            self.log_message(f"已选择主视频: {os.path.basename(file_path)}")

    def select_aux_video(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择辅助视频文件", "", "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv)"
        )
        if file_path:
            self.aux_video_path.setText(file_path)
            self.log_message(f"已选择辅助视频: {os.path.basename(file_path)}")

    def select_output_path(self):
        file_path, _ = QFileDialog.getSaveFileName(
            self, "选择输出文件路径", "", "视频文件 (*.mp4)"
        )
        if file_path:
            if not file_path.endswith('.mp4'):
                file_path += '.mp4'
            self.output_path.setText(file_path)
            self.log_message(f"已设置输出路径: {os.path.basename(file_path)}")

    def start_processing(self):
        # 验证输入
        if not self.main_video_path.text():
            QMessageBox.warning(self, "警告", "请选择主视频文件！")
            return
        if not self.aux_video_path.text():
            QMessageBox.warning(self, "警告", "请选择辅助视频文件！")
            return
        if not self.output_path.text():
            QMessageBox.warning(self, "警告", "请设置输出文件路径！")
            return

        # 检查文件是否存在
        if not os.path.exists(self.main_video_path.text()):
            QMessageBox.warning(self, "警告", "主视频文件不存在！")
            return
        if not os.path.exists(self.aux_video_path.text()):
            QMessageBox.warning(self, "警告", "辅助视频文件不存在！")
            return

        # 禁用开始按钮，启用停止按钮
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        
        # 清空结果显示
        self.result_text.clear()
        self.progress_bar.setValue(0)
        
        # 获取参数
        threshold = float(self.threshold_slider.value())
        similarity_threshold = self.similarity_slider.value() / 100.0
        
        # 创建并启动处理线程
        self.processing_thread = VideoProcessingThread(
            self.main_video_path.text(),
            self.aux_video_path.text(),
            self.output_path.text(),
            threshold,
            similarity_threshold
        )
        
        # 连接信号
        self.processing_thread.progress_updated.connect(self.update_progress)
        self.processing_thread.status_updated.connect(self.update_status)
        self.processing_thread.finished_processing.connect(self.processing_finished)
        self.processing_thread.error_occurred.connect(self.processing_error)
        
        # 启动线程
        self.processing_thread.start()
        self.log_message("开始视频处理...")

    def stop_processing(self):
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.terminate()
            self.processing_thread.wait()
            self.log_message("处理已停止")
            self.reset_ui_state()

    def update_progress(self, value):
        self.progress_bar.setValue(value)

    def update_status(self, status):
        self.status_label.setText(status)
        self.log_message(status)

    def processing_finished(self, result):
        self.result_text.setText(result)
        self.log_message("处理完成！")
        self.reset_ui_state()
        QMessageBox.information(self, "完成", "视频处理完成！")

    def processing_error(self, error):
        self.result_text.setText(f"错误: {error}")
        self.log_message(f"错误: {error}")
        self.reset_ui_state()
        QMessageBox.critical(self, "错误", f"处理过程中发生错误:\n{error}")

    def reset_ui_state(self):
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.status_label.setText("就绪")

    def log_message(self, message):
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("视频反推软件")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("AI Video Tools")
    
    # 创建主窗口
    window = VideoDeduplicationGUI()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()

