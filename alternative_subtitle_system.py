#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
不依赖ImageMagick的字幕处理系统
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from moviepy.editor import VideoClip, CompositeVideoClip

class AlternativeSubtitleProcessor:
    """替代字幕处理器"""
    
    def __init__(self):
        self.default_font_size = 24
        self.default_color = (255, 255, 255)  # 白色
        self.default_stroke_color = (0, 0, 0)  # 黑色
        self.default_stroke_width = 2
    
    def create_text_image(self, text, width, height, style=None):
        """使用PIL创建文本图像"""
        try:
            # 创建透明背景图像
            img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)
            
            # 设置字体
            try:
                # 尝试使用系统字体
                font = ImageFont.truetype("arial.ttf", self.default_font_size)
            except:
                # 回退到默认字体
                font = ImageFont.load_default()
            
            # 获取文本尺寸
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            # 计算居中位置
            x = (width - text_width) // 2
            y = height - text_height - 50  # 距离底部50像素
            
            # 绘制描边
            if self.default_stroke_width > 0:
                for dx in range(-self.default_stroke_width, self.default_stroke_width + 1):
                    for dy in range(-self.default_stroke_width, self.default_stroke_width + 1):
                        if dx != 0 or dy != 0:
                            draw.text((x + dx, y + dy), text, font=font, 
                                    fill=self.default_stroke_color + (255,))
            
            # 绘制主文本
            draw.text((x, y), text, font=font, fill=self.default_color + (255,))
            
            # 转换为numpy数组
            return np.array(img)
            
        except Exception as e:
            print(f"创建文本图像失败: {e}")
            # 返回空的透明图像
            return np.zeros((height, width, 4), dtype=np.uint8)
    
    def create_subtitle_clip(self, text, start_time, duration, video_size):
        """创建字幕片段"""
        try:
            width, height = video_size
            
            def make_frame(t):
                # 创建文本图像
                text_img = self.create_text_image(text, width, height)
                # 转换为RGB格式
                return text_img[:, :, :3]
            
            # 创建视频片段
            clip = VideoClip(make_frame, duration=duration)
            clip = clip.set_start(start_time)
            
            return clip
            
        except Exception as e:
            print(f"创建字幕片段失败: {e}")
            return None
    
    def add_subtitles_to_video(self, video, subtitles, style=None):
        """为视频添加字幕"""
        try:
            subtitle_clips = []
            video_size = video.size
            
            for subtitle in subtitles:
                clip = self.create_subtitle_clip(
                    subtitle['text'],
                    subtitle['start'],
                    subtitle['duration'],
                    video_size
                )
                if clip:
                    subtitle_clips.append(clip)
            
            if subtitle_clips:
                # 合成视频
                final_video = CompositeVideoClip([video] + subtitle_clips)
                return final_video
            else:
                return video
                
        except Exception as e:
            print(f"添加字幕失败: {e}")
            return video

# 全局实例
alternative_subtitle_processor = AlternativeSubtitleProcessor()
