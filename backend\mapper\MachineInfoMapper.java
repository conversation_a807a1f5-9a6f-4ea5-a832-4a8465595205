package com.example.activation.mapper;

import com.example.activation.entity.MachineInfo;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 机器信息Mapper接口
 */
@Mapper
public interface MachineInfoMapper {
    
    /**
     * 根据机器码查询
     */
    @Select("SELECT * FROM machine_info WHERE machine_code = #{machineCode}")
    MachineInfo findByMachineCode(@Param("machineCode") String machineCode);
    
    /**
     * 根据ID查询
     */
    @Select("SELECT * FROM machine_info WHERE id = #{id}")
    MachineInfo findById(@Param("id") Long id);
    
    /**
     * 查询所有机器信息
     */
    @Select("SELECT * FROM machine_info ORDER BY create_time DESC")
    List<MachineInfo> findAll();
    
    /**
     * 根据激活状态查询
     */
    @Select("SELECT * FROM machine_info WHERE activation_status = #{status} ORDER BY create_time DESC")
    List<MachineInfo> findByActivationStatus(@Param("status") Integer status);
    
    /**
     * 根据计算机名称查询
     */
    @Select("SELECT * FROM machine_info WHERE computer_name = #{computerName}")
    List<MachineInfo> findByComputerName(@Param("computerName") String computerName);
    
    /**
     * 插入机器信息
     */
    @Insert("INSERT INTO machine_info (machine_code, computer_name, operating_system, processor, " +
            "cpu_id, motherboard_serial, bios_serial, disk_serial, mac_address, " +
            "windows_product_id, machine_guid, activation_status, create_time, update_time, " +
            "activation_count, remark) " +
            "VALUES (#{machineCode}, #{computerName}, #{operatingSystem}, #{processor}, " +
            "#{cpuId}, #{motherboardSerial}, #{biosSerial}, #{diskSerial}, #{macAddress}, " +
            "#{windowsProductId}, #{machineGuid}, #{activationStatus}, #{createTime}, #{updateTime}, " +
            "#{activationCount}, #{remark})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(MachineInfo machineInfo);
    
    /**
     * 更新机器信息
     */
    @Update("UPDATE machine_info SET " +
            "computer_name = #{computerName}, " +
            "operating_system = #{operatingSystem}, " +
            "processor = #{processor}, " +
            "cpu_id = #{cpuId}, " +
            "motherboard_serial = #{motherboardSerial}, " +
            "bios_serial = #{biosSerial}, " +
            "disk_serial = #{diskSerial}, " +
            "mac_address = #{macAddress}, " +
            "windows_product_id = #{windowsProductId}, " +
            "machine_guid = #{machineGuid}, " +
            "activation_status = #{activationStatus}, " +
            "first_activation_time = #{firstActivationTime}, " +
            "last_activation_time = #{lastActivationTime}, " +
            "update_time = #{updateTime}, " +
            "activation_count = #{activationCount}, " +
            "remark = #{remark} " +
            "WHERE id = #{id}")
    int update(MachineInfo machineInfo);
    
    /**
     * 更新激活状态
     */
    @Update("UPDATE machine_info SET " +
            "activation_status = #{status}, " +
            "last_activation_time = #{lastActivationTime}, " +
            "update_time = #{updateTime}, " +
            "activation_count = activation_count + 1 " +
            "WHERE machine_code = #{machineCode}")
    int updateActivationStatus(@Param("machineCode") String machineCode, 
                              @Param("status") Integer status,
                              @Param("lastActivationTime") LocalDateTime lastActivationTime,
                              @Param("updateTime") LocalDateTime updateTime);
    
    /**
     * 设置首次激活时间
     */
    @Update("UPDATE machine_info SET " +
            "first_activation_time = #{firstActivationTime}, " +
            "update_time = #{updateTime} " +
            "WHERE machine_code = #{machineCode} AND first_activation_time IS NULL")
    int setFirstActivationTime(@Param("machineCode") String machineCode,
                              @Param("firstActivationTime") LocalDateTime firstActivationTime,
                              @Param("updateTime") LocalDateTime updateTime);
    
    /**
     * 增加激活次数
     */
    @Update("UPDATE machine_info SET " +
            "activation_count = activation_count + 1, " +
            "last_activation_time = #{lastActivationTime}, " +
            "update_time = #{updateTime} " +
            "WHERE machine_code = #{machineCode}")
    int incrementActivationCount(@Param("machineCode") String machineCode,
                                @Param("lastActivationTime") LocalDateTime lastActivationTime,
                                @Param("updateTime") LocalDateTime updateTime);
    
    /**
     * 禁用机器
     */
    @Update("UPDATE machine_info SET " +
            "activation_status = 2, " +
            "update_time = #{updateTime} " +
            "WHERE id = #{id}")
    int disableMachine(@Param("id") Long id, @Param("updateTime") LocalDateTime updateTime);
    
    /**
     * 启用机器
     */
    @Update("UPDATE machine_info SET " +
            "activation_status = 0, " +
            "update_time = #{updateTime} " +
            "WHERE id = #{id}")
    int enableMachine(@Param("id") Long id, @Param("updateTime") LocalDateTime updateTime);
    
    /**
     * 根据ID删除
     */
    @Delete("DELETE FROM machine_info WHERE id = #{id}")
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据机器码删除
     */
    @Delete("DELETE FROM machine_info WHERE machine_code = #{machineCode}")
    int deleteByMachineCode(@Param("machineCode") String machineCode);
    
    /**
     * 统计机器信息数量
     */
    @Select("SELECT COUNT(*) FROM machine_info")
    int countAll();
    
    /**
     * 根据激活状态统计数量
     */
    @Select("SELECT COUNT(*) FROM machine_info WHERE activation_status = #{status}")
    int countByActivationStatus(@Param("status") Integer status);
    
    /**
     * 检查机器码是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM machine_info WHERE machine_code = #{machineCode}")
    boolean existsByMachineCode(@Param("machineCode") String machineCode);
    
    /**
     * 检查机器是否已激活
     */
    @Select("SELECT COUNT(*) > 0 FROM machine_info WHERE machine_code = #{machineCode} AND activation_status = 1")
    boolean isActivated(@Param("machineCode") String machineCode);
    
    /**
     * 分页查询机器信息
     */
    @Select("SELECT * FROM machine_info ORDER BY create_time DESC LIMIT #{offset}, #{limit}")
    List<MachineInfo> findWithPagination(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 根据时间范围查询激活记录
     */
    @Select("SELECT * FROM machine_info WHERE last_activation_time BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY last_activation_time DESC")
    List<MachineInfo> findByActivationTimeRange(@Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查询激活次数最多的机器
     */
    @Select("SELECT * FROM machine_info WHERE activation_count > 0 ORDER BY activation_count DESC LIMIT #{limit}")
    List<MachineInfo> findTopActivatedMachines(@Param("limit") int limit);
    
    /**
     * 更新机器详细信息
     */
    @Update("UPDATE machine_info SET " +
            "computer_name = #{computerName}, " +
            "operating_system = #{operatingSystem}, " +
            "processor = #{processor}, " +
            "cpu_id = #{cpuId}, " +
            "motherboard_serial = #{motherboardSerial}, " +
            "bios_serial = #{biosSerial}, " +
            "disk_serial = #{diskSerial}, " +
            "mac_address = #{macAddress}, " +
            "windows_product_id = #{windowsProductId}, " +
            "machine_guid = #{machineGuid}, " +
            "update_time = #{updateTime} " +
            "WHERE machine_code = #{machineCode}")
    int updateMachineDetails(MachineInfo machineInfo);
}
