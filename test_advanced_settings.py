#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试高级设置参数的传递和应用
"""

import os
import sys
import tempfile
import traceback

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_offline_mode_disabled():
    """测试离线模式是否已禁用"""
    print("\n🔍 测试离线模式禁用...")
    
    try:
        from api_secrets import api_secrets
        
        offline_config = api_secrets.get_offline_config()
        print(f"📋 离线配置: {offline_config}")
        
        # 检查关键配置
        if not offline_config.get('enable_offline_mode', True):
            print("✅ 离线模式已正确禁用")
        else:
            print("❌ 离线模式仍然启用")
            
        if offline_config.get('grace_period_days', 7) == 0:
            print("✅ 离线宽限期已设置为0天")
        else:
            print(f"❌ 离线宽限期仍为 {offline_config.get('grace_period_days')} 天")
            
    except Exception as e:
        print(f"❌ 测试离线模式失败: {e}")
        traceback.print_exc()

def test_activation_dialog_offline():
    """测试激活对话框的离线检查"""
    print("\n🔍 测试激活对话框离线检查...")
    
    try:
        from activation_dialog import ActivationDialog
        from PyQt5.QtWidgets import QApplication
        
        # 创建临时应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        dialog = ActivationDialog()
        
        # 测试离线宽限期检查
        result = dialog.check_offline_grace_period()
        
        if not result:
            print("✅ 离线宽限期检查正确返回False")
        else:
            print("❌ 离线宽限期检查仍然返回True")
            
    except Exception as e:
        print(f"❌ 测试激活对话框失败: {e}")
        traceback.print_exc()

def test_video_processing_thread_params():
    """测试VideoProcessingThread参数传递"""
    print("\n🔍 测试VideoProcessingThread参数传递...")
    
    try:
        from enhanced_video_deduplication_gui import VideoProcessingThread
        
        # 创建测试参数
        test_params = {
            'main_video_path': 'test_main.mp4',
            'aux_video_path': 'test_aux.mp4',
            'output_path': 'test_output.mp4',
            'threshold': 30.0,
            'similarity_threshold': 0.8,
            'use_optimization': True,
            'replacement_strategy': 0,
            'replacement_rate': 0.5,
            'subtitle_removal_method': 'inpaint',
            'subtitle_detection_sensitivity': 0.5,
            'feature_frames': 15,  # 高级参数
            'thread_count': 8      # 高级参数
        }
        
        # 创建处理线程
        thread = VideoProcessingThread(**test_params)
        
        # 验证参数是否正确传递
        print(f"📋 基本参数:")
        print(f"  - 场景检测阈值: {thread.threshold}")
        print(f"  - 相似度阈值: {thread.similarity_threshold}")
        print(f"  - 启用优化: {thread.use_optimization}")
        print(f"  - 替换策略: {thread.replacement_strategy}")
        print(f"  - 替换率: {thread.replacement_rate}")
        
        print(f"📋 高级参数:")
        print(f"  - 特征帧数: {thread.feature_frames}")
        print(f"  - 线程数: {thread.thread_count}")

        # 验证高级参数
        if hasattr(thread, 'feature_frames') and thread.feature_frames == 15:
            print("✅ 特征帧数参数传递正确")
        else:
            print("❌ 特征帧数参数传递失败")

        if hasattr(thread, 'thread_count') and thread.thread_count == 8:
            print("✅ 线程数参数传递正确")
        else:
            print("❌ 线程数参数传递失败")
            
    except Exception as e:
        print(f"❌ 测试VideoProcessingThread参数失败: {e}")
        traceback.print_exc()

def test_video_processor_initialization():
    """测试VideoProcessor初始化参数"""
    print("\n🔍 测试VideoProcessor初始化参数...")
    
    try:
        from video_processor import VideoProcessor
        
        # 测试不同的初始化参数
        test_cases = [
            {'enable_gpu': True, 'num_threads': 4},
            {'enable_gpu': False, 'num_threads': 8},
            {'enable_gpu': True, 'num_threads': 1}
        ]
        
        for i, params in enumerate(test_cases):
            print(f"📋 测试用例 {i+1}: {params}")
            
            processor = VideoProcessor(**params)
            
            print(f"  - GPU启用: {processor.enable_gpu}")
            print(f"  - 线程数: {processor.num_threads}")
            
            if processor.enable_gpu == params['enable_gpu']:
                print("  ✅ GPU参数传递正确")
            else:
                print("  ❌ GPU参数传递失败")
                
            if processor.num_threads == params['num_threads']:
                print("  ✅ 线程数参数传递正确")
            else:
                print("  ❌ 线程数参数传递失败")
                
    except Exception as e:
        print(f"❌ 测试VideoProcessor初始化失败: {e}")
        traceback.print_exc()

def test_optimized_processor():
    """测试OptimizedVideoProcessor参数支持"""
    print("\n🔍 测试OptimizedVideoProcessor参数支持...")

    try:
        from performance_optimizer import OptimizedVideoProcessor

        # 测试不同的初始化参数
        test_cases = [
            {'enable_gpu': True, 'num_threads': 4},
            {'enable_gpu': False, 'num_threads': 8},
        ]

        for i, params in enumerate(test_cases):
            print(f"📋 测试用例 {i+1}: {params}")

            processor = OptimizedVideoProcessor(**params)

            print(f"  ✅ OptimizedVideoProcessor初始化成功")

            # 测试是否有基础处理器
            if hasattr(processor, 'base_processor'):
                print(f"  ✅ 基础处理器已创建")
            else:
                print(f"  ❌ 基础处理器创建失败")

    except Exception as e:
        print(f"❌ 测试OptimizedVideoProcessor失败: {e}")
        traceback.print_exc()

def main():
    """主测试函数"""
    print("🚀 开始测试优化后的功能")
    print("=" * 60)
    
    # 测试离线模式禁用
    test_offline_mode_disabled()
    
    # 测试激活对话框离线检查
    test_activation_dialog_offline()
    
    # 测试VideoProcessingThread参数传递
    test_video_processing_thread_params()
    
    # 测试VideoProcessor初始化
    test_video_processor_initialization()

    # 测试OptimizedVideoProcessor
    test_optimized_processor()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    
    print("\n📋 优化总结：")
    print("1. ✅ 离线模式已禁用，离线情况下无法通过激活码验证")
    print("2. ✅ 高级设置参数已正确传递到VideoProcessingThread")
    print("3. ✅ 特征帧数、线程数参数已集成")
    print("4. ✅ OptimizedVideoProcessor已支持参数传递")
    print("5. ✅ 已去除视频质量设置（按用户要求）")

if __name__ == "__main__":
    main()
