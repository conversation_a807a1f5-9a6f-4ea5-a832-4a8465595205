#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ImageMagick快速安装脚本（可选）
"""

import os
import sys
import subprocess
import webbrowser
import urllib.request
import tempfile
from pathlib import Path

def check_admin_rights():
    """检查是否有管理员权限"""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def download_imagemagick():
    """下载ImageMagick安装程序"""
    print("📥 准备下载ImageMagick...")
    
    # ImageMagick下载URL（Windows 64位）
    download_url = "https://imagemagick.org/archive/binaries/ImageMagick-7.1.1-21-Q16-HDRI-x64-dll.exe"
    
    try:
        # 创建临时目录
        temp_dir = tempfile.gettempdir()
        installer_path = os.path.join(temp_dir, "ImageMagick-installer.exe")
        
        print(f"正在下载到: {installer_path}")
        print("这可能需要几分钟时间...")
        
        # 下载文件
        urllib.request.urlretrieve(download_url, installer_path)
        
        if os.path.exists(installer_path):
            file_size = os.path.getsize(installer_path) / (1024 * 1024)  # MB
            print(f"✅ 下载完成，文件大小: {file_size:.1f} MB")
            return installer_path
        else:
            print("❌ 下载失败")
            return None
            
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return None

def install_imagemagick(installer_path):
    """安装ImageMagick"""
    print("🔧 开始安装ImageMagick...")
    
    try:
        if not check_admin_rights():
            print("⚠️ 需要管理员权限来安装ImageMagick")
            print("请以管理员身份运行此脚本，或手动安装")
            return False
        
        # 静默安装命令
        cmd = [
            installer_path,
            "/SILENT",
            "/SUPPRESSMSGBOXES",
            "/NORESTART",
            "/SP-"
        ]
        
        print("正在安装，请稍候...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ ImageMagick安装成功")
            return True
        else:
            print(f"❌ 安装失败，返回码: {result.returncode}")
            print(f"错误信息: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def configure_environment():
    """配置环境变量"""
    print("⚙️ 配置环境变量...")
    
    try:
        # 查找ImageMagick安装路径
        possible_paths = [
            r"C:\Program Files\ImageMagick-7.1.1-Q16-HDRI",
            r"C:\Program Files\ImageMagick-7.1.0-Q16-HDRI",
            r"C:\Program Files (x86)\ImageMagick-7.1.1-Q16-HDRI",
            r"C:\Program Files (x86)\ImageMagick-7.1.0-Q16-HDRI"
        ]
        
        imagemagick_path = None
        for path in possible_paths:
            if os.path.exists(path):
                imagemagick_path = path
                break
        
        if not imagemagick_path:
            print("❌ 未找到ImageMagick安装路径")
            return False
        
        print(f"找到ImageMagick: {imagemagick_path}")
        
        # 添加到PATH环境变量
        current_path = os.environ.get('PATH', '')
        if imagemagick_path not in current_path:
            # 临时添加到当前会话
            os.environ['PATH'] = f"{imagemagick_path};{current_path}"
            print("✅ 已添加到当前会话PATH")
        
        # 创建MoviePy配置
        magick_exe = os.path.join(imagemagick_path, "magick.exe")
        if os.path.exists(magick_exe):
            config_content = f'''# MoviePy ImageMagick配置
IMAGEMAGICK_BINARY = r"{magick_exe}"
'''
            with open('moviepy_config.py', 'w', encoding='utf-8') as f:
                f.write(config_content)
            print("✅ 已创建MoviePy配置文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 环境配置失败: {e}")
        return False

def test_installation():
    """测试安装是否成功"""
    print("🧪 测试ImageMagick安装...")
    
    try:
        # 测试命令行
        result = subprocess.run(['magick', '-version'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            version_info = result.stdout.split('\n')[0]
            print(f"✅ ImageMagick命令行测试成功: {version_info}")
        else:
            print("⚠️ ImageMagick命令行测试失败")
        
        # 测试MoviePy TextClip
        try:
            from moviepy.editor import TextClip
            txt_clip = TextClip("测试", fontsize=24, color='white')
            txt_clip.close()
            print("✅ MoviePy TextClip测试成功")
            return True
        except Exception as e:
            print(f"❌ MoviePy TextClip测试失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def manual_install_guide():
    """显示手动安装指南"""
    print("\n" + "="*50)
    print("📋 手动安装指南")
    print("="*50)
    print("1. 访问ImageMagick官网:")
    print("   https://imagemagick.org/script/download.php#windows")
    print("\n2. 下载Windows版本:")
    print("   ImageMagick-7.x.x-Q16-HDRI-x64-dll.exe")
    print("\n3. 安装时注意:")
    print("   ✅ 勾选 'Install development headers and libraries for C and C++'")
    print("   ✅ 记住安装路径")
    print("\n4. 配置环境变量:")
    print("   - 将安装路径添加到系统PATH")
    print("   - 重启命令行和IDE")
    print("\n5. 验证安装:")
    print("   magick -version")
    
    # 询问是否打开下载页面
    try:
        open_browser = input("\n是否打开ImageMagick下载页面？(y/n): ").lower().strip()
        if open_browser == 'y':
            webbrowser.open("https://imagemagick.org/script/download.php#windows")
            print("✅ 已打开下载页面")
    except:
        pass

def main():
    """主安装流程"""
    print("=" * 60)
    print("🚀 ImageMagick快速安装工具")
    print("=" * 60)
    
    print("⚠️ 注意：当前字幕功能已通过替代方案正常工作")
    print("安装ImageMagick是可选的，可以获得更好的字幕效果")
    
    try:
        proceed = input("\n是否继续安装ImageMagick？(y/n): ").lower().strip()
        if proceed != 'y':
            print("已取消安装")
            return True
    except:
        print("已取消安装")
        return True
    
    # 检查管理员权限
    if not check_admin_rights():
        print("\n⚠️ 检测到非管理员权限")
        print("自动安装需要管理员权限")
        
        try:
            choice = input("选择操作：\n1. 显示手动安装指南\n2. 以管理员身份重新运行\n请输入选择 (1/2): ").strip()
            
            if choice == '1':
                manual_install_guide()
                return True
            elif choice == '2':
                print("请以管理员身份重新运行此脚本")
                return False
            else:
                manual_install_guide()
                return True
        except:
            manual_install_guide()
            return True
    
    # 自动安装流程
    print("\n🚀 开始自动安装流程...")
    
    # 步骤1：下载
    installer_path = download_imagemagick()
    if not installer_path:
        print("下载失败，显示手动安装指南")
        manual_install_guide()
        return False
    
    # 步骤2：安装
    if not install_imagemagick(installer_path):
        print("安装失败，显示手动安装指南")
        manual_install_guide()
        return False
    
    # 步骤3：配置
    if not configure_environment():
        print("配置失败，但ImageMagick可能已安装")
    
    # 步骤4：测试
    if test_installation():
        print("\n🎉 ImageMagick安装和配置成功！")
        print("现在可以使用原生TextClip功能了")
        return True
    else:
        print("\n⚠️ 安装完成但测试失败")
        print("请重启命令行和IDE后再试")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n用户取消安装")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 安装过程出现错误: {e}")
        sys.exit(1)
