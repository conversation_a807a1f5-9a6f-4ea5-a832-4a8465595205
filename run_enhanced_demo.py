#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强相似度匹配系统 - 快速演示脚本
一键运行所有改进功能的演示
"""

import os
import sys

def main():
    """主函数 - 运行增强相似度匹配演示"""
    
    print("🚀 增强相似度匹配系统")
    print("=" * 60)
    print("专门优化：主视频(已剪辑+字幕) vs 辅助视频(原始素材)")
    print("=" * 60)
    
    # 检查必要文件
    required_files = [
        "video_processor.py",
        "enhanced_similarity_matching_demo.py",
        "enhanced_matching_config.py", 
        "quality_validator.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n请确保所有文件都在当前目录中。")
        return False
    
    # 检查视频文件
    video_files = ["main_video.mp4", "aux_video.mp4"]
    missing_videos = []
    for video in video_files:
        if not os.path.exists(video):
            missing_videos.append(video)
    
    if missing_videos:
        print("📁 视频文件检查:")
        for video in missing_videos:
            print(f"   ❌ 缺少: {video}")
        
        print(f"\n💡 请准备以下视频文件:")
        print(f"   📹 main_video.mp4  - 主视频（已剪辑+字幕）")
        print(f"   📹 aux_video.mp4   - 辅助视频（原始素材，时长>主视频）")
        print(f"\n将视频文件放在当前目录后重新运行此脚本。")
        return False
    
    print("✅ 所有必要文件检查通过")
    print(f"📹 找到视频文件: {', '.join(video_files)}")
    
    # 运行演示
    print(f"\n🎬 开始运行增强相似度匹配演示...")
    print(f"=" * 60)
    
    try:
        # 导入并运行演示
        from enhanced_similarity_matching_demo import enhanced_similarity_matching_demo
        
        success = enhanced_similarity_matching_demo()
        
        if success:
            print(f"\n🎉 演示成功完成！")
            print(f"\n📋 主要改进总结:")
            print(f"  ✅ 字幕鲁棒特征 - 智能处理字幕干扰")
            print(f"  ✅ 智能时长匹配 - 多尺度精确搜索")
            print(f"  ✅ 运动特征分析 - 更好匹配动态内容")
            print(f"  ✅ 增强相似度计算 - 针对剪辑差异优化")
            print(f"  ✅ 自动配置系统 - 根据视频类型优化")
            print(f"  ✅ 质量验证机制 - 全面质量分析")
            
            print(f"\n📄 查看详细文档:")
            print(f"  📖 ENHANCED_SIMILARITY_MATCHING_GUIDE.md")
            print(f"  📊 SIMILARITY_MATCHING_IMPROVEMENTS_SUMMARY.md")
            
            # 检查输出文件
            output_file = "enhanced_output.mp4"
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file) / (1024 * 1024)
                print(f"\n🎥 输出视频: {output_file} ({file_size:.2f} MB)")
            
        else:
            print(f"\n❌ 演示失败")
            print(f"请检查错误信息并参考文档进行调试。")
            
    except ImportError as e:
        print(f"\n❌ 导入错误: {e}")
        print(f"请确保所有依赖库已正确安装:")
        print(f"  pip install opencv-python moviepy numpy scenedetect")
        
    except Exception as e:
        print(f"\n❌ 运行错误: {e}")
        print(f"请检查错误信息并参考文档进行调试。")
        
    return success

def show_help():
    """显示帮助信息"""
    print("🔧 增强相似度匹配系统 - 使用帮助")
    print("=" * 50)
    
    print(f"\n📋 系统要求:")
    print(f"  🐍 Python 3.7+")
    print(f"  📦 opencv-python, moviepy, numpy, scenedetect")
    print(f"  🎬 FFmpeg (添加到系统PATH)")
    
    print(f"\n📁 文件准备:")
    print(f"  📹 main_video.mp4  - 主视频（已剪辑+字幕）")
    print(f"  📹 aux_video.mp4   - 辅助视频（原始素材）")
    
    print(f"\n🚀 运行方式:")
    print(f"  python run_enhanced_demo.py")
    print(f"  python run_enhanced_demo.py --help")
    
    print(f"\n🎯 适用场景:")
    print(f"  ✅ 主视频已剪辑并添加字幕")
    print(f"  ✅ 辅助视频为原始素材（无剪辑、无字幕）")
    print(f"  ✅ 辅助视频时长大于主视频时长")
    print(f"  ✅ 需要从辅助视频复刻相同画面内容")
    
    print(f"\n📖 详细文档:")
    print(f"  📄 ENHANCED_SIMILARITY_MATCHING_GUIDE.md")
    print(f"  📊 SIMILARITY_MATCHING_IMPROVEMENTS_SUMMARY.md")

if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h', 'help']:
        show_help()
    else:
        try:
            success = main()
            sys.exit(0 if success else 1)
        except KeyboardInterrupt:
            print(f"\n⏹️ 用户中断操作")
            sys.exit(1)
        except Exception as e:
            print(f"\n💥 意外错误: {e}")
            sys.exit(1)
