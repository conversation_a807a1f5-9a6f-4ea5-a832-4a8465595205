2025-08-06 11:49:28 [INFO] ✅ 激活验证模块加载成功
2025-08-06 11:49:30 [INFO] ✅ 单实例应用初始化成功
2025-08-06 11:49:30 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-08-06 11:49:30 [INFO] 🔑 机器码: B609-6939-8E74-6EFF
2025-08-06 11:49:30 [INFO] 📁 配置文件路径: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\activation_config.json
2025-08-06 11:49:30 [INFO] 🔍 检查本地保存的激活码: 71C9-4CBE-4B7F-48BB
2025-08-06 11:49:30 [INFO] 🔍 与服务器验证激活码有效性
2025-08-06 11:49:31 [INFO] ✅ 服务器验证成功: 设备已激活
2025-08-06 11:49:31 [INFO] ✅ 激活码验证通过
2025-08-06 11:49:31 [INFO] ✅ 本地激活验证通过
2025-08-06 11:49:31 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-08-06 11:49:32 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-08-06 11:49:32 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-08-06 11:49:32 [INFO] 未检测到CUDA设备
2025-08-06 11:49:32 [INFO] VideoProcessor初始化:
2025-08-06 11:49:32 [INFO]   CPU线程数: 8
2025-08-06 11:49:32 [INFO]   GPU支持: 否
2025-08-06 11:49:32 [INFO]   GPU加速: 禁用
2025-08-06 11:49:32 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-08-06 11:49:32 [INFO] ✅ FFmpeg已配置: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-08-06 11:49:32 [INFO] 🔍 测试FFmpeg安装...
2025-08-06 11:49:32 [INFO] ✅ FFmpeg安装正确: ffmpeg version 7.0.2-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
2025-08-06 11:49:32 [INFO] ✅ 支持PCM音频编码
2025-08-06 11:49:32 [INFO] ✅ FFmpeg测试通过
2025-08-06 11:50:00 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-08-06 11:50:00 [INFO] 未检测到CUDA设备
2025-08-06 11:50:00 [INFO] VideoProcessor初始化:
2025-08-06 11:50:00 [INFO]   CPU线程数: 4
2025-08-06 11:50:00 [INFO]   GPU支持: 否
2025-08-06 11:50:00 [INFO]   GPU加速: 禁用
2025-08-06 11:50:00 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-08-06 11:50:00 [INFO] 使用PySceneDetect版本: 0.6.6
2025-08-06 11:50:00 [INFO] 成功导入ContentDetector(方法1)
2025-08-06 11:50:00 [INFO] 视频文件大小: 50.26 MB
2025-08-06 11:50:00 [INFO] 视频信息: 30.0 FPS, 7362 帧, 245.40 秒
2025-08-06 11:50:00 [INFO] 尝试使用PySceneDetect原生方法打开视频...
2025-08-06 11:50:00 [INFO] 开始执行场景检测...
2025-08-06 11:50:34 [INFO] 场景检测完成，耗时 33.47 秒
2025-08-06 11:50:34 [INFO] 检测到 7 个场景
2025-08-06 11:50:34 [INFO] 使用PySceneDetect版本: 0.6.6
2025-08-06 11:50:34 [INFO] 成功导入ContentDetector(方法1)
2025-08-06 11:50:34 [INFO] 视频文件大小: 19.39 MB
2025-08-06 11:50:34 [INFO] 视频信息: 24.0 FPS, 5890 帧, 245.42 秒
2025-08-06 11:50:34 [INFO] 尝试使用PySceneDetect原生方法打开视频...
2025-08-06 11:50:34 [INFO] 开始执行场景检测...
2025-08-06 11:50:57 [INFO] 场景检测完成，耗时 22.94 秒
2025-08-06 11:50:57 [INFO] 检测到 7 个场景
2025-08-06 11:50:57 [INFO] 优化相似度匹配: 主视频7场景, 辅助视频7场景
2025-08-06 11:50:57 [INFO] 正在并行提取主视频特征...
2025-08-06 11:50:57 [INFO] 增强精度模式: 视频段19.90秒，提取10帧
2025-08-06 11:50:57 [INFO] 增强精度模式: 视频段9.93秒，提取10帧
2025-08-06 11:50:57 [INFO] 增强精度模式: 视频段22.57秒，提取10帧
2025-08-06 11:50:57 [INFO] 增强精度模式: 视频段9.93秒，提取10帧
2025-08-06 11:51:02 [INFO] 增强精度模式: 视频段146.93秒，提取10帧
2025-08-06 11:51:02 [INFO] 增强精度模式: 视频段7.70秒，提取10帧
2025-08-06 11:51:02 [INFO] 增强精度模式: 视频段28.43秒，提取10帧
2025-08-06 11:51:07 [INFO] 正在并行提取辅助视频特征...
2025-08-06 11:51:07 [INFO] 增强精度模式: 视频段66.58秒，提取10帧
2025-08-06 11:51:07 [INFO] 增强精度模式: 视频段7.88秒，提取10帧
2025-08-06 11:51:07 [INFO] 增强精度模式: 视频段17.88秒，提取10帧
2025-08-06 11:51:07 [INFO] 增强精度模式: 视频段7.92秒，提取10帧
2025-08-06 11:51:13 [INFO] 增强精度模式: 视频段116.50秒，提取10帧
2025-08-06 11:51:13 [INFO] 增强精度模式: 视频段6.08秒，提取10帧
2025-08-06 11:51:13 [INFO] 增强精度模式: 视频段22.58秒，提取10帧
2025-08-06 11:51:17 [INFO] 正在并行计算相似度矩阵...
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:17 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:18 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:18 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:18 [DEBUG] 自适应特征权重: {'edge': 0.23529411764705885, 'color_hist': 0.35294117647058826, 'phash': 0.4117647058823529}
2025-08-06 11:51:18 [INFO] 正在查找最佳匹配...
2025-08-06 11:51:18 [INFO] 优化匹配完成: 找到 7 个匹配（限制: 7）
2025-08-06 11:51:18 [INFO] 使用临时路径处理视频: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\temp_output_a6e969eb_1754452278048_fd5b2c7b.mp4
2025-08-06 11:51:18 [INFO] 最终输出路径: D:/25125/Videos/video_test/ttt.mp4
2025-08-06 11:51:18 [INFO] 加载主视频: D:/25125/Videos/测试自媒体ab.mp4
2025-08-06 11:51:18 [INFO] 主视频加载成功，时长: 245.41秒
2025-08-06 11:51:18 [INFO] 开始按顺序处理 7 个主视频片段...
2025-08-06 11:51:18 [INFO] 其中 7 个片段将被替换
2025-08-06 11:51:18 [INFO] 片段 1 标记为替换 (时长: 19.90秒)
2025-08-06 11:51:18 [INFO] 片段 2 标记为替换 (时长: 9.93秒)
2025-08-06 11:51:18 [INFO] 片段 3 标记为替换 (时长: 22.57秒)
2025-08-06 11:51:18 [INFO] 片段 4 标记为替换 (时长: 9.93秒)
2025-08-06 11:51:18 [INFO] 片段 5 标记为替换 (时长: 146.93秒)
2025-08-06 11:51:18 [INFO] 片段 6 标记为替换 (时长: 7.70秒)
2025-08-06 11:51:18 [INFO] 片段 7 标记为替换 (时长: 28.43秒)
2025-08-06 11:51:18 [INFO] 处理 7 个替换片段...
2025-08-06 11:51:18 [INFO] 使用传统匹配格式（场景索引）
2025-08-06 11:51:18 [INFO] 加载辅助视频: D:/25125/Videos/www112.mp4
2025-08-06 14:23:58 [INFO] 📦 窗口已最小化到任务栏
2025-08-06 14:23:58 [INFO] 📦 窗口已最小化到任务栏
2025-08-06 14:24:01 [INFO] 📦 窗口已最小化到任务栏
2025-08-06 14:47:44 [INFO] 📦 窗口已最小化到任务栏
2025-08-06 14:51:44 [INFO] 📦 窗口已最小化到任务栏
2025-08-06 15:44:34 [INFO] 📦 窗口已最小化到任务栏
2025-08-06 16:33:47 [INFO] 🚪 用户选择完全退出程序
2025-08-06 16:33:47 [ERROR] ❌ 退出程序时发生错误: 'VideoProcessingThread' object has no attribute 'stop'
