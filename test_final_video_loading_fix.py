#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终视频加载修复测试
验证所有加载策略是否能解决问题
"""

import os
import sys
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_video_loading():
    """测试视频加载功能"""
    print("🔧 最终视频加载修复测试")
    print("=" * 80)
    
    # 测试视频路径
    test_video_path = "D:/25125/Videos/www333.mp4"
    
    if not os.path.exists(test_video_path):
        print(f"❌ 测试视频文件不存在: {test_video_path}")
        return False
    
    try:
        from video_processor import VideoProcessor
        processor = VideoProcessor()
        
        print(f"测试视频: {test_video_path}")
        file_size = os.path.getsize(test_video_path)
        print(f"文件大小: {file_size / (1024*1024):.2f} MB")
        
        # 测试1: simple_load_video
        print("\n1️⃣ 测试simple_load_video...")
        start_time = time.time()
        
        try:
            video_clip = processor.simple_load_video(test_video_path)
            load_time = time.time() - start_time
            
            if video_clip:
                print(f"✅ simple_load_video成功: {load_time:.2f}秒")
                print(f"   时长: {video_clip.duration:.2f}秒")
                video_clip.close()
                simple_success = True
            else:
                print(f"❌ simple_load_video失败: {load_time:.2f}秒")
                simple_success = False
                
        except Exception as e:
            load_time = time.time() - start_time
            print(f"❌ simple_load_video异常: {str(e)} ({load_time:.2f}秒)")
            simple_success = False
        
        # 测试2: load_video_with_fallback
        if not simple_success:
            print("\n2️⃣ 测试load_video_with_fallback...")
            start_time = time.time()
            
            try:
                video_clip = processor.load_video_with_fallback(test_video_path)
                load_time = time.time() - start_time
                
                if video_clip:
                    print(f"✅ load_video_with_fallback成功: {load_time:.2f}秒")
                    print(f"   时长: {video_clip.duration:.2f}秒")
                    video_clip.close()
                    fallback_success = True
                else:
                    print(f"❌ load_video_with_fallback失败: {load_time:.2f}秒")
                    fallback_success = False
                    
            except Exception as e:
                load_time = time.time() - start_time
                print(f"❌ load_video_with_fallback异常: {str(e)} ({load_time:.2f}秒)")
                fallback_success = False
        else:
            fallback_success = True  # 简单加载成功，不需要测试备用方案
        
        # 测试3: 模拟完整的replace_and_concatenate_videos流程
        print("\n3️⃣ 测试完整加载流程...")
        start_time = time.time()
        
        try:
            # 模拟主视频加载
            main_clip = processor.simple_load_video(test_video_path)
            if main_clip is None:
                main_clip = processor.load_video_with_fallback(test_video_path)
            
            if main_clip is None:
                raise Exception("无法加载主视频")
            
            # 模拟辅助视频加载
            aux_clip = processor.simple_load_video(test_video_path)  # 使用同一个文件模拟
            if aux_clip is None:
                aux_clip = processor.load_video_with_fallback(test_video_path)
            
            if aux_clip is None:
                raise Exception("无法加载辅助视频")
            
            load_time = time.time() - start_time
            print(f"✅ 完整流程成功: {load_time:.2f}秒")
            print(f"   主视频时长: {main_clip.duration:.2f}秒")
            print(f"   辅助视频时长: {aux_clip.duration:.2f}秒")
            
            # 清理资源
            main_clip.close()
            aux_clip.close()
            
            full_success = True
            
        except Exception as e:
            load_time = time.time() - start_time
            print(f"❌ 完整流程失败: {str(e)} ({load_time:.2f}秒)")
            full_success = False
        
        # 结果汇总
        print("\n" + "=" * 80)
        print("🎯 测试结果汇总")
        print("=" * 80)
        
        results = [
            ("simple_load_video", simple_success),
            ("load_video_with_fallback", fallback_success),
            ("完整加载流程", full_success)
        ]
        
        success_count = 0
        for test_name, success in results:
            status = "✅ 成功" if success else "❌ 失败"
            print(f"{test_name:25} : {status}")
            if success:
                success_count += 1
        
        print(f"\n成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
        
        if full_success:
            print("\n🎉 所有测试通过！视频加载问题已解决！")
            print("\n📋 修复总结:")
            print("1. ✅ 添加了simple_load_video方法（最快加载）")
            print("2. ✅ 保留了load_video_with_fallback备用方案")
            print("3. ✅ 优化了加载策略顺序")
            print("4. ✅ 改进了错误处理和日志记录")
            
            print("\n🚀 现在可以:")
            print("1. 重启应用程序")
            print("2. 重新尝试相似度策略合成")
            print("3. 视频应该能正常加载，不再超时")
            
            return True
        else:
            print("\n⚠️ 仍有问题，建议:")
            print("1. 检查系统内存是否充足")
            print("2. 尝试重启计算机")
            print("3. 检查是否有其他程序占用视频文件")
            return False
            
    except ImportError as e:
        print(f"❌ 导入错误: {str(e)}")
        print("请确保在正确的目录中运行此脚本")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    success = test_video_loading()
    
    if success:
        print("\n" + "=" * 80)
        print("🎊 恭喜！视频加载问题已完全解决！")
        print("现在可以正常使用相似度策略合成视频功能了。")
    else:
        print("\n" + "=" * 80)
        print("😞 抱歉，仍需要进一步诊断问题。")
        print("请联系技术支持并提供详细的错误日志。")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
