#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证增强相似度匹配功能
用于快速测试新功能是否正常工作
"""

import os
import sys
import time
import numpy as np
from video_processor import VideoProcessor

def quick_feature_test():
    """快速测试特征提取功能"""
    print("🧪 测试特征提取功能...")
    
    try:
        processor = VideoProcessor()
        
        # 创建测试数据（模拟视频帧）
        test_frame = np.random.randint(0, 255, (64, 64, 3), dtype=np.uint8)
        
        # 测试各种特征提取方法
        gray_frame = np.mean(test_frame, axis=2).astype(np.uint8)
        
        # 测试感知哈希
        phash = processor._extract_phash_feature(gray_frame, (8, 8))
        print(f"✅ 感知哈希特征: {len(phash)} 维")
        
        # 测试颜色直方图
        color_hist = processor._extract_color_histogram_feature(test_frame)
        print(f"✅ 颜色直方图特征: {len(color_hist)} 维")
        
        # 测试边缘特征
        edge_feat = processor._extract_edge_feature(gray_frame)
        print(f"✅ 边缘特征: {len(edge_feat)} 维")
        
        # 测试LBP特征
        lbp_feat = processor._extract_lbp_feature(gray_frame)
        print(f"✅ LBP纹理特征: {len(lbp_feat)} 维")
        
        # 测试纹理特征
        texture_feat = processor._extract_texture_feature(gray_frame)
        print(f"✅ 纹理特征: {len(texture_feat)} 维")
        
        return True
        
    except Exception as e:
        print(f"❌ 特征提取测试失败: {e}")
        return False

def quick_similarity_test():
    """快速测试相似度计算功能"""
    print("\n🔍 测试相似度计算功能...")
    
    try:
        processor = VideoProcessor()
        
        # 创建测试特征数据
        features1 = [
            {
                'phash': np.random.choice([True, False], 64),
                'color_hist': np.random.rand(48).astype(np.float32),
                'edge': np.random.rand(9).astype(np.float32),
                'lbp': np.random.rand(256).astype(np.float32)
            }
        ]
        
        # 创建相似的特征（添加少量噪声）
        features2 = [
            {
                'phash': features1[0]['phash'].copy(),
                'color_hist': features1[0]['color_hist'] + np.random.rand(48) * 0.1,
                'edge': features1[0]['edge'] + np.random.rand(9) * 0.1,
                'lbp': features1[0]['lbp'] + np.random.rand(256) * 0.1
            }
        ]
        
        # 修改部分特征使其不完全相同
        features2[0]['phash'][:10] = ~features2[0]['phash'][:10]
        
        # 测试多维度相似度计算
        similarity = processor.calculate_similarity(features1, features2)
        print(f"✅ 多维度相似度计算: {similarity:.3f}")
        
        # 测试不同权重的影响
        custom_weights = {
            'phash': 0.5,
            'color_hist': 0.2,
            'edge': 0.2,
            'lbp': 0.1
        }
        
        weighted_similarity = processor.calculate_similarity(
            features1, features2, custom_weights
        )
        print(f"✅ 加权相似度计算: {weighted_similarity:.3f}")
        
        # 测试完全不同的特征
        features3 = [
            {
                'phash': np.random.choice([True, False], 64),
                'color_hist': np.random.rand(48).astype(np.float32),
                'edge': np.random.rand(9).astype(np.float32),
                'lbp': np.random.rand(256).astype(np.float32)
            }
        ]
        
        dissimilarity = processor.calculate_similarity(features1, features3)
        print(f"✅ 不同特征相似度: {dissimilarity:.3f}")
        
        # 验证相似度范围
        if 0 <= similarity <= 1 and 0 <= dissimilarity <= 1:
            print("✅ 相似度范围验证通过")
            return True
        else:
            print("❌ 相似度范围验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 相似度计算测试失败: {e}")
        return False

def quick_quality_analysis_test():
    """快速测试质量分析功能"""
    print("\n📊 测试质量分析功能...")
    
    try:
        processor = VideoProcessor()
        
        # 创建模拟匹配结果
        mock_matches = [
            {
                'main_scene_index': 0,
                'similarity': 0.85,
                'duration_error': 0.02,
                'main_duration': 5.0
            },
            {
                'main_scene_index': 1,
                'similarity': 0.78,
                'duration_error': 0.05,
                'main_duration': 3.5
            },
            {
                'main_scene_index': 2,
                'similarity': 0.92,
                'duration_error': 0.01,
                'main_duration': 4.2
            }
        ]
        
        # 创建模拟主视频场景
        mock_main_scenes = [
            {'start_time': 0, 'end_time': 5},
            {'start_time': 5, 'end_time': 8.5},
            {'start_time': 8.5, 'end_time': 12.7},
            {'start_time': 12.7, 'end_time': 15}  # 这个没有匹配
        ]
        
        # 测试质量分析
        quality_analysis = processor.analyze_matching_quality(mock_matches, mock_main_scenes)
        
        print(f"✅ 质量分数: {quality_analysis['quality_score']:.3f}")
        print(f"✅ 平均相似度: {quality_analysis['avg_similarity']:.3f}")
        print(f"✅ 匹配率: {quality_analysis['match_rate']:.1%}")
        print(f"✅ 平均时长误差: {quality_analysis['avg_duration_error']:.3f}秒")
        print(f"✅ 优化建议数量: {len(quality_analysis['recommendations'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 质量分析测试失败: {e}")
        return False

def quick_threshold_test():
    """快速测试阈值调整功能"""
    print("\n🎯 测试智能阈值调整功能...")
    
    try:
        processor = VideoProcessor()
        
        # 由于需要实际视频文件，这里只测试函数是否存在和可调用
        if hasattr(processor, 'get_adaptive_similarity_threshold'):
            print("✅ 智能阈值调整函数存在")
            
            # 测试默认参数
            try:
                # 这会因为没有实际视频文件而失败，但我们可以捕获异常
                processor.get_adaptive_similarity_threshold([], [], "dummy.mp4", "dummy.mp4")
            except (FileNotFoundError, Exception):
                print("✅ 智能阈值调整函数可调用（需要实际视频文件）")
                
            return True
        else:
            print("❌ 智能阈值调整函数不存在")
            return False
            
    except Exception as e:
        print(f"❌ 阈值调整测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 增强相似度匹配功能快速验证")
    print("=" * 50)
    
    start_time = time.time()
    
    # 运行各项测试
    tests = [
        ("特征提取", quick_feature_test),
        ("相似度计算", quick_similarity_test),
        ("质量分析", quick_quality_analysis_test),
        ("智能阈值", quick_threshold_test)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✅ {test_name}测试通过")
        else:
            print(f"❌ {test_name}测试失败")
    
    end_time = time.time()
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    print(f"⏱️ 总耗时: {end_time - start_time:.2f}秒")
    
    if passed == total:
        print("🎉 所有功能验证通过！")
        print("\n📋 下一步:")
        print("1. 运行完整测试: python test_enhanced_similarity.py")
        print("2. 查看使用演示: python demo_enhanced_similarity.py")
        print("3. 阅读详细文档: ENHANCED_SIMILARITY_README.md")
        return True
    else:
        print("⚠️ 部分功能验证失败，请检查代码实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
