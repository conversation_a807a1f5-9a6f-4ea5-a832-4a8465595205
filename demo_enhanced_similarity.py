#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强相似度匹配功能演示脚本
展示如何使用优化后的多维度特征提取、精确时长匹配等功能
"""

import os
import sys
import time
from video_processor import VideoProcessor

def demo_basic_usage():
    """演示基本使用方法"""
    print("=" * 60)
    print("基本使用演示")
    print("=" * 60)
    
    # 初始化处理器
    processor = VideoProcessor(enable_gpu=True, num_threads=4)
    
    # 视频路径
    main_video = "main_video.mp4"
    aux_video = "aux_video.mp4"
    output_video = "output_basic.mp4"
    
    print("1. 基本相似度匹配（使用默认设置）:")
    print(f"""
    processor = VideoProcessor()
    main_scenes = processor.split_video_into_scenes("{main_video}")
    aux_scenes = processor.split_video_into_scenes("{aux_video}")
    
    # 使用默认的多维度特征匹配
    matches = processor.find_similar_scenes(
        main_scenes, aux_scenes, "{main_video}", "{aux_video}",
        similarity_threshold=0.8
    )
    
    processor.replace_and_concatenate_videos(
        "{main_video}", "{aux_video}",
        main_scenes, aux_scenes, matches, "{output_video}"
    )
    """)

def demo_advanced_features():
    """演示高级功能"""
    print("=" * 60)
    print("高级功能演示")
    print("=" * 60)
    
    print("2. 自定义特征类型和权重:")
    print("""
    # 自定义特征组合
    feature_types = ['phash', 'color_hist', 'edge', 'lbp']
    feature_weights = {
        'phash': 0.35,      # 感知哈希权重
        'color_hist': 0.25, # 颜色直方图权重
        'edge': 0.20,       # 边缘特征权重
        'lbp': 0.20         # 纹理特征权重
    }
    
    matches = processor.find_similar_scenes(
        main_scenes, aux_scenes, main_video, aux_video,
        similarity_threshold=0.8,
        feature_types=feature_types,
        feature_weights=feature_weights
    )
    """)
    
    print("\n3. 智能阈值调整:")
    print("""
    # 根据视频内容自动调整阈值
    adaptive_threshold = processor.get_adaptive_similarity_threshold(
        main_scenes, aux_scenes, main_video, aux_video,
        base_threshold=0.8, sample_size=5
    )
    
    matches = processor.find_similar_scenes(
        main_scenes, aux_scenes, main_video, aux_video,
        similarity_threshold=adaptive_threshold
    )
    """)
    
    print("\n4. 匹配质量分析:")
    print("""
    # 分析匹配质量并获得优化建议
    quality_analysis = processor.analyze_matching_quality(matches, main_scenes)
    
    print(f"质量分数: {quality_analysis['quality_score']:.3f}")
    print(f"平均相似度: {quality_analysis['avg_similarity']:.3f}")
    print(f"匹配率: {quality_analysis['match_rate']:.1%}")
    print(f"平均时长误差: {quality_analysis['avg_duration_error']:.3f}秒")
    
    for recommendation in quality_analysis['recommendations']:
        print(f"建议: {recommendation}")
    """)

def demo_complete_workflow():
    """演示完整的工作流程"""
    print("=" * 60)
    print("完整工作流程演示")
    print("=" * 60)
    
    processor = VideoProcessor(enable_gpu=True, num_threads=4)
    
    # 示例视频路径（实际使用时请替换为真实路径）
    main_video = "example_main.mp4"
    aux_video = "example_aux.mp4"
    output_video = "example_output.mp4"
    
    print("完整的优化工作流程:")
    print("""
def enhanced_video_processing(main_video, aux_video, output_video):
    # 1. 初始化处理器
    processor = VideoProcessor(enable_gpu=True, num_threads=4)
    
    # 2. 场景检测
    main_scenes = processor.split_video_into_scenes(main_video, threshold=30.0)
    aux_scenes = processor.split_video_into_scenes(aux_video, threshold=30.0)
    
    # 3. 智能阈值分析
    adaptive_threshold = processor.get_adaptive_similarity_threshold(
        main_scenes, aux_scenes, main_video, aux_video
    )
    
    # 4. 测试不同特征组合
    feature_combinations = [
        {
            'types': ['phash', 'color_hist'],
            'weights': {'phash': 0.6, 'color_hist': 0.4}
        },
        {
            'types': ['phash', 'color_hist', 'edge', 'lbp'],
            'weights': {'phash': 0.35, 'color_hist': 0.25, 'edge': 0.2, 'lbp': 0.2}
        }
    ]
    
    best_matches = None
    best_quality = 0.0
    
    for combo in feature_combinations:
        matches = processor.find_similar_scenes(
            main_scenes, aux_scenes, main_video, aux_video,
            similarity_threshold=adaptive_threshold,
            feature_types=combo['types'],
            feature_weights=combo['weights']
        )
        
        if matches:
            quality = processor.analyze_matching_quality(matches, main_scenes)
            if quality['quality_score'] > best_quality:
                best_quality = quality['quality_score']
                best_matches = matches
    
    # 5. 生成最终视频
    if best_matches:
        processor.replace_and_concatenate_videos(
            main_video, aux_video,
            main_scenes, aux_scenes,
            best_matches, output_video
        )
        
        # 6. 质量验证
        final_quality = processor.analyze_matching_quality(best_matches, main_scenes)
        print(f"最终质量分数: {final_quality['quality_score']:.3f}")
        
        return True
    else:
        print("未找到合适的匹配")
        return False
    """)

def demo_optimization_tips():
    """演示优化技巧"""
    print("=" * 60)
    print("优化技巧和最佳实践")
    print("=" * 60)
    
    print("🎯 特征选择建议:")
    print("""
    # 对于不同类型的视频内容，推荐不同的特征组合：
    
    # 1. 动作片/运动视频（运动较多）
    action_features = {
        'types': ['phash', 'edge', 'texture'],
        'weights': {'phash': 0.4, 'edge': 0.35, 'texture': 0.25}
    }
    
    # 2. 风景/静态视频（颜色重要）
    landscape_features = {
        'types': ['color_hist', 'phash', 'lbp'],
        'weights': {'color_hist': 0.4, 'phash': 0.35, 'lbp': 0.25}
    }
    
    # 3. 人物对话（结构重要）
    dialogue_features = {
        'types': ['phash', 'color_hist', 'edge'],
        'weights': {'phash': 0.45, 'color_hist': 0.3, 'edge': 0.25}
    }
    """)
    
    print("\n⚡ 性能优化建议:")
    print("""
    # 1. 根据视频长度调整处理策略
    if video_duration > 3600:  # 超过1小时
        # 使用快速模式和较少特征
        feature_types = ['phash', 'color_hist']
        fast_mode = True
    else:
        # 使用完整特征
        feature_types = ['phash', 'color_hist', 'edge', 'lbp']
        fast_mode = False
    
    # 2. 内存管理
    import gc
    gc.collect()  # 在处理大视频前强制垃圾回收
    
    # 3. 并行处理
    processor = VideoProcessor(enable_gpu=True, num_threads=cpu_count())
    """)
    
    print("\n🎨 质量优化建议:")
    print("""
    # 1. 阈值调整策略
    if avg_similarity < 0.6:
        # 内容差异较大，降低阈值
        threshold = max(0.5, adaptive_threshold - 0.1)
    elif avg_similarity > 0.9:
        # 内容非常相似，可以提高阈值
        threshold = min(0.95, adaptive_threshold + 0.05)
    
    # 2. 场景检测优化
    if video_has_many_cuts:
        scene_threshold = 20.0  # 更敏感的场景检测
    else:
        scene_threshold = 40.0  # 较粗粒度的场景检测
    
    # 3. 时长匹配精度
    # 系统会自动确保时长精确匹配，误差通常小于0.1秒
    """)

def main():
    """主函数"""
    print("🎬 增强相似度匹配功能演示")
    print("=" * 80)
    print("本演示展示了优化后的视频相似度匹配功能，包括：")
    print("• 多维度特征提取（感知哈希、颜色直方图、边缘特征、纹理特征）")
    print("• 精确时长匹配（确保输出视频与主视频时长完全一致）")
    print("• 智能阈值调整（根据视频内容自动优化匹配阈值）")
    print("• 匹配质量分析（提供详细的质量评估和优化建议）")
    print("• 重叠区域避免（防止辅助视频区域重复使用）")
    print("=" * 80)
    
    demo_basic_usage()
    print("\n")
    demo_advanced_features()
    print("\n")
    demo_complete_workflow()
    print("\n")
    demo_optimization_tips()
    
    print("\n" + "=" * 80)
    print("🚀 开始使用:")
    print("1. 运行测试脚本: python test_enhanced_similarity.py")
    print("2. 查看完整API文档和示例")
    print("3. 根据您的视频类型选择合适的特征组合")
    print("=" * 80)

if __name__ == "__main__":
    main()
