import cv2
import os
import sys
import numpy as np
import traceback
import random
import subprocess
import json
import re
import tempfile
import shutil
import platform
import time
import threading
import uuid
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor
from multiprocessing import cpu_count
import queue
import multiprocessing

# 导入日志系统
try:
    from logger_system import log_info, log_error, log_warning, log_debug, safe_print
except ImportError:
    # 备用函数
    def log_info(msg): print(msg)
    def log_error(msg): print(f"ERROR: {msg}")
    def log_warning(msg): print(f"WARNING: {msg}")
    def log_debug(msg): print(f"DEBUG: {msg}")
    def safe_print(*args, **kwargs): print(*args, **kwargs)

# 在导入MoviePy之前配置ImageMagick
def configure_imagemagick():
    """配置ImageMagick路径"""
    try:
        # 检查是否存在配置文件
        config_file = os.path.join(os.path.dirname(__file__), 'moviepy_config.py')
        if os.path.exists(config_file):
            # 读取配置文件
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'IMAGEMAGICK_BINARY' in content:
                    # 提取路径
                    match = re.search(r'IMAGEMAGICK_BINARY\s*=\s*r?"([^"]+)"', content)
                    if match:
                        binary_path = match.group(1)
                        if os.path.exists(binary_path):
                            os.environ['IMAGEMAGICK_BINARY'] = binary_path
                            log_info(f"✅ 已配置ImageMagick: {binary_path}")
                            return True

        # 尝试默认路径
        default_path = r"D:\environment\imagemagick\magick.exe"
        if os.path.exists(default_path):
            os.environ['IMAGEMAGICK_BINARY'] = default_path
            log_info(f"✅ 使用默认ImageMagick路径: {default_path}")
            return True

        log_warning(f"⚠️ 未找到ImageMagick配置，将使用备用方案")
        return False

    except Exception as e:
        log_warning(f"⚠️ ImageMagick配置失败: {e}")
        return False

# 配置ImageMagick
configure_imagemagick()

# 尝试导入必要的库，并提供有用的错误信息
try:
    from scenedetect import VideoManager
    from scenedetect import SceneManager
    from scenedetect.detectors import ContentDetector
except ImportError:
    log_info("Error: Could not import scenedetect. Please install it using: pip install scenedetect")
    log_info("If you already have it installed, make sure you're using the correct version.")

try:
    from moviepy.editor import VideoFileClip, concatenate_videoclips, vfx
except ImportError:
    log_info("Error: Could not import moviepy. Please install it using: pip install moviepy")
    log_info("If you already have it installed, make sure you're using the correct version.")

class VideoProcessor:
    def __init__(self, enable_gpu=True, num_threads=None):
        self.ffmpeg_path = self._find_ffmpeg_path()
        self.enable_gpu = enable_gpu
        self.num_threads = num_threads or min(cpu_count(), 8)  # 限制最大线程数
        self.gpu_available = self._check_gpu_support()
        self.performance_stats = {
            'frames_processed': 0,
            'processing_time': 0,
            'fps': 0
        }

        # 内存管理设置
        self.memory_limit_mb = 2048  # 2GB内存限制
        self.enable_memory_monitoring = True
        self.auto_cleanup = True

        # 输出质量设置
        self.output_quality = 'standard'  # 默认标准质量
        self.quality_settings = {
            'high': {
                'preset': 'slow',
                'crf': 18,
                'bitrate': None,
                'fps': None
            },
            'standard': {
                'preset': 'medium',
                'crf': 23,
                'bitrate': None,
                'fps': None
            },
            'fast': {
                'preset': 'ultrafast',
                'crf': 28,
                'bitrate': '2M',
                'fps': 24
            }
        }

        # 临时目录管理
        self.temp_dir = os.path.join(os.getcwd(), 'temp')
        self._ensure_temp_dir()

        # 生成唯一的会话ID用于临时文件命名
        self.session_id = str(uuid.uuid4())[:8]

        log_info(f"VideoProcessor初始化:")
        log_info(f"  CPU线程数: {self.num_threads}")
        log_info(f"  GPU支持: {'是' if self.gpu_available else '否'}")
        log_info(f"  GPU加速: {'启用' if self.enable_gpu and self.gpu_available else '禁用'}")
        log_info(f"  临时目录: {self.temp_dir}")

    def _ensure_temp_dir(self):
        """确保临时目录存在"""
        try:
            if not os.path.exists(self.temp_dir):
                os.makedirs(self.temp_dir, exist_ok=True)
                log_info(f"创建临时目录: {self.temp_dir}")
        except Exception as e:
            log_warning(f"创建临时目录失败: {e}")
            # 使用系统临时目录作为备选
            self.temp_dir = tempfile.gettempdir()
            log_info(f"使用系统临时目录: {self.temp_dir}")

    def get_temp_file_path(self, prefix="temp", suffix="", extension=""):
        """生成唯一的临时文件路径"""
        timestamp = int(time.time() * 1000)  # 毫秒时间戳
        random_id = str(uuid.uuid4())[:8]
        filename = f"{prefix}_{self.session_id}_{timestamp}_{random_id}{suffix}{extension}"
        return os.path.join(self.temp_dir, filename)

    def cleanup_temp_files(self, pattern=None):
        """清理临时文件"""
        try:
            if pattern is None:
                pattern = f"*{self.session_id}*"

            import glob
            temp_files = glob.glob(os.path.join(self.temp_dir, pattern))

            for temp_file in temp_files:
                try:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                        log_debug(f"清理临时文件: {temp_file}")
                except Exception as e:
                    log_warning(f"清理临时文件失败 {temp_file}: {e}")

        except Exception as e:
            log_warning(f"清理临时文件失败: {e}")

    def set_output_quality(self, quality):
        """
        设置输出视频质量

        Args:
            quality (str): 质量级别 ('high', 'standard', 'fast')
        """
        if quality in self.quality_settings:
            self.output_quality = quality
            log_info(f"输出质量设置为: {quality}")
            log_debug(f"质量参数: {self.quality_settings[quality]}")
        else:
            log_warning(f"不支持的质量级别: {quality}，使用默认质量 'standard'")
            self.output_quality = 'standard'

    def get_quality_params(self):
        """
        获取当前质量设置的FFmpeg参数

        Returns:
            dict: FFmpeg参数字典
        """
        return self.quality_settings.get(self.output_quality, self.quality_settings['standard'])

    def __del__(self):
        """析构函数，清理临时文件"""
        try:
            if hasattr(self, 'session_id'):
                self.cleanup_temp_files()
        except Exception as e:
            log_warning(f"析构函数清理失败: {e}")

    def safe_write_videofile(self, clip, output_path, **kwargs):
        """安全的视频写入方法，处理MoviePy的各种问题"""
        max_retries = 3  # 减少重试次数，避免无限重试
        retry_count = 0
        wait_time_base = 2  # 基础等待时间（秒）
        max_write_time = 300  # 最大写入时间（5分钟）

        # 首先验证clip是否有效
        if clip is None:
            log_error("视频片段为None，无法写入")
            return False

        try:
            # 验证clip的基本属性
            if not hasattr(clip, 'duration') or clip.duration is None or clip.duration <= 0:
                log_error(f"视频片段duration无效: {getattr(clip, 'duration', 'None')}")
                return False

            # 验证是否可以获取帧
            if hasattr(clip, 'get_frame'):
                try:
                    test_frame = clip.get_frame(0)
                    if test_frame is None:
                        log_error("视频片段无法获取帧")
                        return False
                except Exception as frame_error:
                    log_error(f"视频片段获取帧失败: {str(frame_error)}")
                    return False

            log_info(f"开始写入视频片段，时长: {clip.duration:.2f}秒")

        except Exception as validation_error:
            log_error(f"视频片段验证失败: {str(validation_error)}")
            return False

        while retry_count < max_retries:
            try:
                # 强制垃圾回收，释放内存
                import gc
                gc.collect()

                # 使用新的音频修复方法
                clip, has_audio = self._fix_audio_issues(clip)

                # 如果有音频，设置临时音频文件路径
                if has_audio and 'temp_audiofile' not in kwargs:
                    kwargs['temp_audiofile'] = self.get_temp_file_path("scene_audio", extension=".wav")

                # 设置默认参数，使用更保守的设置
                default_kwargs = {
                    'verbose': False,
                    'logger': None,
                    'remove_temp': True,
                    'threads': 1,  # 强制单线程，避免并发问题
                    'preset': 'ultrafast',  # 使用最快预设
                    'codec': 'libx264',  # 明确指定视频编解码器
                    'fps': 24,  # 设置默认帧率
                    'ffmpeg_params': ['-avoid_negative_ts', 'make_zero', '-max_muxing_queue_size', '9999']
                }

                # 如果clip没有fps属性，设置默认值
                if not hasattr(clip, 'fps') or clip.fps is None:
                    if 'fps' not in kwargs:
                        log_info("视频片段缺少fps属性，使用默认值24fps")
                        default_kwargs['fps'] = 24

                # 如果没有音频，明确设置audio=False
                if not has_audio:
                    default_kwargs['audio'] = False
                    default_kwargs['audio_codec'] = None
                else:
                    default_kwargs['audio_codec'] = 'aac'

                # 更新用户提供的参数
                default_kwargs.update(kwargs)

                # 写入视频文件（带超时机制）
                try:
                    import threading
                    import time

                    write_success = False
                    write_error = None

                    def write_video():
                        nonlocal write_success, write_error
                        try:
                            clip.write_videofile(output_path, **default_kwargs)
                            write_success = True
                        except Exception as e:
                            write_error = e

                    # 启动写入线程
                    write_thread = threading.Thread(target=write_video)
                    write_thread.daemon = True
                    write_thread.start()

                    # 等待写入完成或超时
                    write_thread.join(timeout=max_write_time)

                    if write_thread.is_alive():
                        # 写入超时，强制终止
                        log_warning(f"视频写入超时（{max_write_time}秒），强制终止...")
                        # 尝试清理可能存在的部分文件
                        if os.path.exists(output_path):
                            try:
                                os.remove(output_path)
                                log_info("已清理超时产生的部分文件")
                            except:
                                pass
                        raise Exception(f"视频写入超时（{max_write_time}秒），已终止写入进程")

                    if write_error:
                        raise write_error

                    if not write_success:
                        raise Exception("视频写入未成功完成")

                    # 验证输出文件
                    if os.path.exists(output_path) and os.path.getsize(output_path) > 1024:
                        file_size = os.path.getsize(output_path)
                        log_info(f"视频写入成功: {output_path}, 文件大小: {file_size} 字节")
                        return True
                    else:
                        file_size = os.path.getsize(output_path) if os.path.exists(output_path) else 0
                        raise Exception(f"输出文件无效或过小，文件大小: {file_size} 字节")

                except Exception as write_error:
                    # 如果写入失败，记录详细错误信息
                    log_error(f"视频写入过程失败: {str(write_error)}")
                    raise

            except Exception as e:
                retry_count += 1
                error_msg = str(e)

                if "'NoneType' object has no attribute 'stdout'" in error_msg:
                    # 计算指数退避等待时间
                    wait_time = wait_time_base * (2 ** (retry_count - 1))
                    log_warning(f"检测到subprocess None错误，重试 {retry_count}/{max_retries}")
                    # 尝试清理MoviePy的内部状态
                    self._cleanup_moviepy_state()
                    # 增加等待时间
                    time.sleep(wait_time)
                    continue
                elif "Broken pipe" in error_msg or "Connection reset" in error_msg:
                    wait_time = wait_time_base * (2 ** (retry_count - 1))
                    log_warning(f"检测到管道错误，重试 {retry_count}/{max_retries}")
                    self._cleanup_moviepy_state()
                    time.sleep(wait_time)
                    continue
                elif retry_count < max_retries:
                    log_warning(f"视频写入失败，重试 {retry_count}/{max_retries}: {error_msg}")
                    time.sleep(2)
                    continue
                else:
                    log_error(f"视频写入最终失败: {error_msg}")
                    # 尝试使用备用方法
                    return self._fallback_write_video(clip, output_path, **kwargs)

        # 如果所有重试都失败，尝试使用备用方法
        return self._fallback_write_video(clip, output_path, **kwargs)

    def _cleanup_moviepy_state(self):
        """清理MoviePy的内部状态"""
        try:
            import gc
            # 强制垃圾回收
            gc.collect()

            # 清理MoviePy的全局状态
            try:
                from moviepy.config import FFMPEG_BINARY
                # 重置FFmpeg二进制路径
                if hasattr(self, 'ffmpeg_path') and self.ffmpeg_path:
                    os.environ['FFMPEG_BINARY'] = self.ffmpeg_path
            except:
                pass

            # 清理可能存在的临时文件
            try:
                self.cleanup_temp_files("scene_audio*")
            except:
                pass

            # 等待一下让系统清理资源
            time.sleep(1.0)

        except Exception as e:
            log_warning(f"清理MoviePy状态失败: {e}")

    def _fallback_write_video(self, clip, output_path, **kwargs):
        """备用视频写入方法，使用FFmpeg直接处理"""
        try:
            log_info("尝试使用FFmpeg备用方法写入视频...")

            if not self.ffmpeg_path:
                # 尝试再次查找FFmpeg路径
                self.ffmpeg_path = self._find_ffmpeg_path()
                if not self.ffmpeg_path:
                    log_error("FFmpeg未找到，无法使用备用方法")
                    # 尝试使用最基本的MoviePy写入方法
                    return self._basic_moviepy_write(clip, output_path, **kwargs)

            # 创建临时输入文件
            temp_input = self.get_temp_file_path("fallback_input", extension=".mp4")
            temp_audio = None

            try:
                # 使用新的音频修复方法
                clip, has_audio = self._fix_audio_issues(clip)

                # 如果有有效音频，提取它
                if has_audio:
                    temp_audio = self.get_temp_file_path("fallback_audio", extension=".wav")
                    if not self._safe_audio_write(clip.audio, temp_audio):
                        log_warning("音频写入失败，将创建无声视频")
                        has_audio = False
                        clip = clip.without_audio()
                        if os.path.exists(temp_audio):
                            try:
                                os.remove(temp_audio)
                            except:
                                pass
                        temp_audio = None

                # 先用最简单的方式保存临时视频文件
                try:
                    # 确保clip有fps属性
                    if not hasattr(clip, 'fps') or clip.fps is None:
                        clip = clip.set_fps(24)  # 设置默认帧率

                    # 尝试使用更简单的设置保存视频
                    clip.write_videofile(
                        temp_input,
                        codec='libx264',
                        audio=False,  # 不包含音频
                        verbose=False,
                        logger=None,
                        threads=1,
                        preset='ultrafast',
                        fps=24,  # 明确指定帧率
                        ffmpeg_params=['-an']  # 强制无音频
                    )
                except Exception as video_error:
                    log_error(f"备用方法保存临时视频失败: {video_error}")
                    return False

                # 使用FFmpeg合并视频和音频
                if has_audio and temp_audio and os.path.exists(temp_audio) and os.path.exists(temp_input):
                    cmd = [
                        self.ffmpeg_path,
                        '-i', temp_input,
                        '-i', temp_audio,
                        '-c:v', 'copy',
                        '-c:a', 'aac',
                        '-b:a', '128k',  # 设置音频比特率
                        '-map', '0:v:0',
                        '-map', '1:a:0',
                        '-shortest',
                        '-avoid_negative_ts', 'make_zero',
                        '-y',
                        output_path
                    ]
                else:
                    # 只复制视频（无音频）
                    cmd = [
                        self.ffmpeg_path,
                        '-i', temp_input,
                        '-c:v', 'copy',
                        '-an',  # 明确指定无音频
                        '-avoid_negative_ts', 'make_zero',
                        '-y',
                        output_path
                    ]

                log_info(f"执行FFmpeg命令: {' '.join(cmd)}")
                
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=120  # 增加超时时间
                )

                if result.returncode == 0 and os.path.exists(output_path):
                    log_info(f"FFmpeg备用方法成功: {output_path}")
                    return True
                else:
                    log_error(f"FFmpeg备用方法失败: {result.stderr}")
                    return False

            finally:
                # 清理临时文件
                for temp_file in [temp_input, temp_audio]:
                    if temp_file and os.path.exists(temp_file):
                        try:
                            os.remove(temp_file)
                        except:
                            pass

        except Exception as e:
            log_error(f"备用写入方法失败: {e}")
            return False

    def _basic_moviepy_write(self, clip, output_path, **kwargs):
        """最基本的MoviePy写入方法，作为最后的备用方案"""
        try:
            log_info("尝试使用基本MoviePy方法写入视频...")

            # 确保clip有fps属性
            if not hasattr(clip, 'fps') or clip.fps is None:
                clip = clip.set_fps(24)  # 设置默认帧率

            # 使用最简单的参数
            basic_kwargs = {
                'codec': 'libx264',
                'audio_codec': 'aac',
                'verbose': False,
                'logger': None,
                'preset': 'ultrafast',
                'threads': 1,
                'fps': 24  # 明确指定帧率
            }

            # 如果clip没有音频，移除音频相关参数
            if not hasattr(clip, 'audio') or clip.audio is None:
                basic_kwargs.pop('audio_codec', None)
                basic_kwargs['audio'] = False

            clip.write_videofile(output_path, **basic_kwargs)

            # 验证输出文件
            if os.path.exists(output_path) and os.path.getsize(output_path) > 1024:
                file_size = os.path.getsize(output_path)
                log_info(f"基本MoviePy写入成功: {output_path}, 文件大小: {file_size} 字节")
                return True
            else:
                file_size = os.path.getsize(output_path) if os.path.exists(output_path) else 0
                log_error(f"基本MoviePy写入失败，文件大小: {file_size} 字节")
                return False

        except Exception as e:
            log_error(f"基本MoviePy写入方法失败: {e}")
            return False

    def _fix_audio_issues(self, clip):
        """修复音频相关问题"""
        try:
            if clip.audio is None:
                return clip, False

            # 检查音频是否有效
            try:
                # 尝试获取音频的基本属性
                audio_duration = clip.audio.duration
                if audio_duration is None or audio_duration <= 0:
                    log_warning("音频时长无效，移除音频")
                    return clip.without_audio(), False

                # 检查音频是否可以正常访问
                if hasattr(clip.audio, 'fps'):
                    audio_fps = clip.audio.fps
                    if audio_fps is None or audio_fps <= 0:
                        log_warning("音频帧率无效，移除音频")
                        return clip.without_audio(), False

                # 尝试获取一小段音频数据进行验证
                try:
                    test_audio = clip.audio.subclip(0, min(0.1, audio_duration))
                    if test_audio is None:
                        log_warning("无法获取音频数据，移除音频")
                        return clip.without_audio(), False
                except Exception as test_error:
                    log_warning(f"音频数据测试失败，移除音频: {test_error}")
                    return clip.without_audio(), False

                log_info("音频验证通过")
                return clip, True

            except Exception as audio_error:
                log_warning(f"音频验证失败，移除音频: {audio_error}")
                return clip.without_audio(), False

        except Exception as e:
            log_warning(f"音频修复过程失败，移除音频: {e}")
            try:
                return clip.without_audio(), False
            except:
                return clip, False

    def _safe_audio_write(self, audio_clip, output_path):
        """安全的音频写入方法"""
        try:
            # 使用最兼容的音频格式和参数
            audio_clip.write_audiofile(
                output_path,
                codec='pcm_s16le',  # PCM格式最兼容
                verbose=False,
                logger=None
            )
            return True
        except Exception as e:
            log_warning(f"PCM音频写入失败，尝试AAC: {e}")
            try:
                audio_clip.write_audiofile(
                    output_path,
                    codec='aac',
                    bitrate='128k',
                    verbose=False,
                    logger=None
                )
                return True
            except Exception as e2:
                log_warning(f"AAC音频写入失败，尝试默认格式: {e2}")
                try:
                    # 最后尝试使用默认参数
                    audio_clip.write_audiofile(
                        output_path,
                        verbose=False,
                        logger=None
                    )
                    return True
                except Exception as e3:
                    log_error(f"音频写入完全失败: {e3}")
                    return False

    def _check_gpu_support(self):
        """检查GPU支持"""
        try:
            # 检查OpenCV是否支持CUDA
            if cv2.cuda.getCudaEnabledDeviceCount() > 0:
                log_info(f"检测到 {cv2.cuda.getCudaEnabledDeviceCount()} 个CUDA设备")
                return True
            else:
                log_info("未检测到CUDA设备")
                return False
        except AttributeError:
            log_info("OpenCV未编译CUDA支持")
            return False
        except Exception as e:
            log_info(f"GPU检测失败: {str(e)}")
            return False

    def _find_ffmpeg_path(self):
        """查找FFmpeg可执行文件路径，支持打包后的环境"""
        # 检查是否为打包环境
        is_packaged = getattr(sys, 'frozen', False) or hasattr(sys, '_MEIPASS')

        # 如果是打包环境，优先检查exe同目录下的ffmpeg
        if is_packaged:
            exe_dir = os.path.dirname(sys.executable)
            packaged_ffmpeg = os.path.join(exe_dir, 'ffmpeg.exe')
            if os.path.exists(packaged_ffmpeg):
                try:
                    test_result = subprocess.run([packaged_ffmpeg, '-version'], capture_output=True, text=True, timeout=5)
                    if test_result.returncode == 0:
                        log_info(f"找到打包的FFmpeg: {packaged_ffmpeg}")
                        return packaged_ffmpeg
                except:
                    pass

            # 检查exe同目录下的ffmpeg子目录
            ffmpeg_subdir = os.path.join(exe_dir, 'ffmpeg', 'ffmpeg.exe')
            if os.path.exists(ffmpeg_subdir):
                try:
                    test_result = subprocess.run([ffmpeg_subdir, '-version'], capture_output=True, text=True, timeout=5)
                    if test_result.returncode == 0:
                        log_info(f"找到打包的FFmpeg(子目录): {ffmpeg_subdir}")
                        return ffmpeg_subdir
                except:
                    pass

        # 尝试通过which/where命令查找（最可靠的方法）
        try:
            if platform.system() == "Windows":
                # Windows使用where命令
                result = subprocess.run(['where', 'ffmpeg'], capture_output=True, text=True, shell=True)
                if result.returncode == 0 and result.stdout.strip():
                    ffmpeg_path = result.stdout.strip().split('\n')[0].strip()
                    log_info(f"通过where命令找到FFmpeg: {ffmpeg_path}")
                    # 验证路径是否有效
                    if os.path.exists(ffmpeg_path):
                        try:
                            test_result = subprocess.run([ffmpeg_path, '-version'], capture_output=True, text=True, timeout=5)
                            if test_result.returncode == 0:
                                return ffmpeg_path
                        except:
                            pass
            else:
                # Linux/macOS使用which命令
                result = subprocess.run(['which', 'ffmpeg'], capture_output=True, text=True)
                if result.returncode == 0 and result.stdout.strip():
                    ffmpeg_path = result.stdout.strip()
                    log_info(f"通过which命令找到FFmpeg: {ffmpeg_path}")
                    if os.path.exists(ffmpeg_path):
                        try:
                            test_result = subprocess.run([ffmpeg_path, '-version'], capture_output=True, text=True, timeout=5)
                            if test_result.returncode == 0:
                                return ffmpeg_path
                        except:
                            pass
        except Exception as e:
            log_info(f"通过系统命令查找FFmpeg失败: {str(e)}")

        # 直接尝试调用ffmpeg（如果在PATH中）
        try:
            result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                log_info("FFmpeg在PATH中可用")
                return 'ffmpeg'
        except Exception as e:
            log_info(f"直接调用ffmpeg失败: {str(e)}")

        # 在常见路径中查找FFmpeg
        common_paths = []

        if platform.system() == "Windows":
            # Windows常见路径
            common_paths = [
                r"C:\ffmpeg\bin\ffmpeg.exe",
                r"C:\Program Files\ffmpeg\bin\ffmpeg.exe",
                r"C:\Program Files (x86)\ffmpeg\bin\ffmpeg.exe",
                os.path.join(os.environ.get('USERPROFILE', ''), 'ffmpeg', 'bin', 'ffmpeg.exe'),
                os.path.join(os.path.dirname(sys.executable), 'Scripts', 'ffmpeg.exe'),
                os.path.join(os.path.dirname(sys.executable), 'ffmpeg.exe'),
            ]

            # 检查PATH环境变量中的所有路径
            path_env = os.environ.get('PATH', '')
            for path_dir in path_env.split(os.pathsep):
                if path_dir.strip():
                    ffmpeg_exe = os.path.join(path_dir.strip(), 'ffmpeg.exe')
                    if os.path.exists(ffmpeg_exe):
                        common_paths.append(ffmpeg_exe)
        else:
            common_paths = [
                '/usr/bin/ffmpeg',
                '/usr/local/bin/ffmpeg',
                '/opt/homebrew/bin/ffmpeg',  # macOS with Homebrew
                '/snap/bin/ffmpeg',  # Ubuntu snap
            ]

        # 检查常见路径
        for path in common_paths:
            if os.path.exists(path):
                try:
                    result = subprocess.run([path, '-version'], capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        log_info(f"在路径中找到FFmpeg: {path}")
                        return path
                except Exception as e:
                    log_info(f"测试FFmpeg路径 {path} 失败: {str(e)}")
                    continue

        log_info("警告: 未找到FFmpeg，某些功能可能无法正常工作")
        log_info("请确保FFmpeg已正确安装并添加到系统PATH中")
        log_info("您可以从 https://ffmpeg.org/download.html 下载FFmpeg")

        # 提供详细的安装指导
        if platform.system() == "Windows":
            log_info("\nWindows安装指导:")
            log_info("1. 下载FFmpeg Windows版本")
            log_info("2. 解压到 C:\\ffmpeg")
            log_info("3. 将 C:\\ffmpeg\\bin 添加到系统PATH环境变量")
            log_info("4. 重启命令行或程序")
            if is_packaged:
                log_info("5. 或者将ffmpeg.exe复制到程序同目录下")

        return None

    def test_ffmpeg_installation(self):
        """测试FFmpeg安装是否正确"""
        log_info(f"🔍 测试FFmpeg安装...")

        if not self.ffmpeg_path:
            log_error(f"❌ FFmpeg未找到")
            return False

        try:
            result = subprocess.run(
                [self.ffmpeg_path, '-version'],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                # 解析版本信息
                version_line = result.stdout.split('\n')[0]
                log_info(f"✅ FFmpeg安装正确: {version_line}")

                # 检查是否支持所需的编码器
                encoders_result = subprocess.run(
                    [self.ffmpeg_path, '-encoders'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )

                if 'pcm_s16le' in encoders_result.stdout:
                    log_info(f"✅ 支持PCM音频编码")
                else:
                    log_warning(f"⚠️ 可能不支持PCM音频编码")

                return True
            else:
                log_error(f"❌ FFmpeg测试失败: {result.stderr}")
                return False

        except Exception as e:
            log_error(f"❌ FFmpeg测试异常: {str(e)}")
            return False

    def split_video_into_scenes(self, video_path, threshold=30.0):
        """
        根据画面变化将视频切割成不同的视频段。
        :param video_path: 视频文件路径
        :param threshold: 场景检测阈值，值越小，检测到的场景越多
        :return: 视频段列表，每个元素包含开始时间和结束时间
        """
        try:
            # 检查是否安装了PySceneDetect库
            try:
                import scenedetect
                log_info(f"使用PySceneDetect版本: {scenedetect.__version__ if hasattr(scenedetect, '__version__') else '未知版本'}")
            except ImportError:
                log_info("未安装PySceneDetect库，将使用备用方法")
                # 如果没有安装PySceneDetect，直接使用OpenCV进行简单的场景分割
                import cv2
                cap = cv2.VideoCapture(video_path)
                if not cap.isOpened():
                    raise Exception(f"无法打开视频文件: {video_path}")
                
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                duration = frame_count / fps if fps > 0 else 0
                cap.release()
                
                if duration <= 0:
                    raise Exception(f"无效的视频时长: {duration}")
                    
                scenes = self._generate_fallback_scenes(duration)
                return scenes
            
            # 尝试导入所需模块，兼容不同版本的PySceneDetect
            try:
                # 先尝试导入通用模块
                try:
                    from scenedetect import open_video, SceneManager
                except ImportError:
                    log_info("无法导入open_video或SceneManager，尝试其他方法")
                    raise ImportError("缺少必要的模块")
                
                # 尝试导入不同版本的VideoManager
                try:
                    from scenedetect import VideoManager
                except ImportError:
                    try:
                        from scenedetect.video_manager import VideoManager
                    except ImportError:
                        log_info("无法导入VideoManager")
                        raise ImportError("缺少VideoManager模块")
                
                # 尝试导入不同版本的ContentDetector
                detector_imported = False
                content_detector = None
                
                # 尝试方法1: 直接从scenedetect导入
                try:
                    from scenedetect import ContentDetector
                    detector_imported = True
                    log_info("成功导入ContentDetector(方法1)")
                except ImportError:
                    pass
                
                # 尝试方法2: 从detectors子模块导入
                if not detector_imported:
                    try:
                        from scenedetect.detectors import ContentDetector
                        detector_imported = True
                        log_info("成功导入ContentDetector(方法2)")
                    except ImportError:
                        pass
                
                # 尝试方法3: 使用ThresholdDetector作为替代
                if not detector_imported:
                    try:
                        from scenedetect.scene_detector import ThresholdDetector as ContentDetector
                        detector_imported = True
                        log_info("使用ThresholdDetector替代ContentDetector")
                    except ImportError:
                        pass
                
                if not detector_imported:
                    log_info("无法导入任何场景检测器，将使用备用方法")
                    # 无法导入任何检测器，使用备用方法
                    import cv2
                    cap = cv2.VideoCapture(video_path)
                    if not cap.isOpened():
                        raise Exception(f"无法打开视频文件: {video_path}")
                    
                    fps = cap.get(cv2.CAP_PROP_FPS)
                    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                    duration = frame_count / fps if fps > 0 else 0
                    cap.release()
                    
                    scenes = self._generate_fallback_scenes(duration)
                    return scenes
                
            except Exception as import_error:
                log_info(f"导入PySceneDetect模块失败: {str(import_error)}")
                # 导入失败，使用备用方法
                import cv2
                cap = cv2.VideoCapture(video_path)
                if not cap.isOpened():
                    raise Exception(f"无法打开视频文件: {video_path}")
                
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                duration = frame_count / fps if fps > 0 else 0
                cap.release()
                
                scenes = self._generate_fallback_scenes(duration)
                return scenes
                
            import cv2
            import time
            
            # 检查视频文件是否存在
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"视频文件不存在: {video_path}")
            
            # 检查文件大小，过大的文件可能需要特殊处理
            file_size_mb = os.path.getsize(video_path) / (1024 * 1024)
            log_info(f"视频文件大小: {file_size_mb:.2f} MB")
            
            # 使用备用方法检查视频是否可以正常打开
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise Exception(f"视频文件无法用OpenCV打开: {video_path}")
                
            # 获取视频信息
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = frame_count / fps if fps > 0 else 0
            log_info(f"视频信息: {fps} FPS, {frame_count} 帧, {duration:.2f} 秒")
            
            # 检查视频是否过大
            is_large_video = file_size_mb > 1000  # 1GB
            
            # 释放OpenCV视频对象
            cap.release()
            
            # 尝试打开视频 - 主要方法
            try:
                log_info("尝试使用PySceneDetect原生方法打开视频...")
                video = open_video(video_path)
            except Exception as e:
                log_info(f"使用PySceneDetect原生方法打开视频失败: {str(e)}")
                log_info("尝试使用自定义方法...")
                
                # 如果原生方法失败，尝试自定义方法
                try:
                    # 使用VideoManager作为替代方案
                    video = VideoManager([video_path])
                    video.set_downscale_factor(2)  # 降低分辨率以提高性能
                    video.start()
                except Exception as e2:
                    log_info(f"使用VideoManager方法也失败: {str(e2)}")
                    # 如果所有方法都失败，尝试直接创建场景列表
                    if duration > 0:
                        log_info("使用备用方法直接生成场景...")
                        scenes = self._generate_fallback_scenes(duration)
                        return scenes
                    else:
                        raise Exception(f"无法打开视频文件 {video_path}: {str(e)}, {str(e2)}")
            
            # 创建场景管理器
            scene_manager = SceneManager()
            
            # 添加内容检测器，调整阈值和参数
            try:
                if is_large_video:
                    # 对于大型视频，使用更高的阈值减少场景数量
                    adjusted_threshold = threshold * 1.5
                    log_info(f"大型视频，调整阈值: {threshold} -> {adjusted_threshold}")
                    
                    try:
                        # 尝试使用新版本API
                        scene_manager.add_detector(ContentDetector(
                            threshold=adjusted_threshold,
                            min_scene_len=15  # 最小场景长度15帧
                        ))
                    except TypeError:
                        # 旧版本API可能不支持min_scene_len参数
                        log_info("使用旧版本API添加检测器")
                        scene_manager.add_detector(ContentDetector(threshold=adjusted_threshold))
                else:
                    # 对于普通视频，使用标准阈值
                    scene_manager.add_detector(ContentDetector(threshold=threshold))
            except Exception as detector_error:
                log_info(f"添加内容检测器失败: {str(detector_error)}，尝试使用默认参数")
                try:
                    # 尝试使用默认参数
                    scene_manager.add_detector(ContentDetector())
                except Exception as e:
                    log_info(f"添加默认检测器也失败: {str(e)}")
                    # 无法添加检测器，返回备用场景划分
                    if duration > 0:
                        log_info("使用备用方法直接生成场景...")
                        scenes = self._generate_fallback_scenes(duration)
                        return scenes
                    else:
                        raise Exception(f"无法添加场景检测器: {str(e)}")
            
            # 执行场景检测
            try:
                log_info("开始执行场景检测...")
                start_time = time.time()
                scene_manager.detect_scenes(video)
                end_time = time.time()
                log_info(f"场景检测完成，耗时 {end_time - start_time:.2f} 秒")
            except Exception as e:
                log_info(f"场景检测失败: {str(e)}")
                # 尝试备用方法
                if isinstance(video, VideoManager):
                    video.release()
                
                if duration > 0:
                    log_info("使用备用方法直接生成场景...")
                    scenes = self._generate_fallback_scenes(duration)
                    return scenes
                else:
                    raise Exception(f"场景检测失败: {str(e)}")
            
            # 获取场景列表
            scene_list = scene_manager.get_scene_list()
            if not scene_list:
                log_info("场景列表为空，使用备用方法")
                if duration > 0:
                    scenes = self._generate_fallback_scenes(duration)
                    return scenes
                else:
                    raise Exception("场景检测未生成任何场景")
            
            log_info(f"检测到 {len(scene_list)} 个场景")
            scenes = []
            for i, scene in enumerate(scene_list):
                start_time = scene[0].get_seconds()
                end_time = scene[1].get_seconds()
                
                # 验证时间有效性
                if start_time >= end_time or start_time < 0:
                    log_info(f"跳过无效场景 {i}: {start_time} -> {end_time}")
                    continue
                
                scenes.append({
                    'start_time': start_time,
                    'end_time': end_time
                })
            
            # 确保至少有一个场景
            if not scenes and duration > 0:
                log_info("没有有效场景，使用备用方法")
                scenes = self._generate_fallback_scenes(duration)
            
            # 释放资源
            if isinstance(video, VideoManager):
                video.release()
            
            return scenes
        except Exception as e:
            log_info(f"Error in split_video_into_scenes: {str(e)}")
            log_info(traceback.format_exc())
            raise
            
    def _generate_fallback_scenes(self, duration, num_scenes=10):
        """
        生成备用场景列表，在场景检测失败时使用
        :param duration: 视频时长（秒）
        :param num_scenes: 要生成的场景数量
        :return: 场景列表
        """
        log_info(f"生成备用场景列表: 视频时长 {duration:.2f} 秒, 生成 {num_scenes} 个场景")
        
        # 对于短视频，减少场景数量
        if duration < 60:  # 少于1分钟
            num_scenes = min(5, num_scenes)
        elif duration < 300:  # 少于5分钟
            num_scenes = min(8, num_scenes)
            
        # 确保至少有一个场景
        num_scenes = max(1, num_scenes)
        
        # 平均分割视频
        scene_duration = duration / num_scenes
        scenes = []
        
        for i in range(num_scenes):
            start_time = i * scene_duration
            end_time = (i + 1) * scene_duration
            
            # 最后一个场景确保覆盖到视频结尾
            if i == num_scenes - 1:
                end_time = duration
                
            scenes.append({
                'start_time': start_time,
                'end_time': end_time
            })
            
        log_info(f"生成了 {len(scenes)} 个备用场景")
        return scenes

    def extract_frame_features(self, video_path, start_time, end_time, num_frames=10, fast_mode=False, feature_types=['phash', 'color_hist', 'edge'], enhanced_accuracy=True, is_main_video=False):
        """
        从视频段中提取多维度关键帧特征（高精度版本）。
        支持多种特征类型：感知哈希、颜色直方图、边缘特征、纹理特征等
        :param video_path: 视频文件路径
        :param start_time: 视频段开始时间
        :param end_time: 视频段结束时间
        :param num_frames: 提取的帧数
        :param fast_mode: 快速模式，使用更少的帧和更小的特征
        :param feature_types: 特征类型列表，可选：'phash', 'color_hist', 'edge', 'texture', 'lbp', 'subtitle_robust'
        :param enhanced_accuracy: 是否启用增强精度模式
        :param is_main_video: 是否为主视频（已剪辑+字幕），用于启用字幕鲁棒特征
        :return: 多维度关键帧特征字典列表
        """
        try:
            # 检查视频文件是否存在
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"视频文件不存在: {video_path}")

            # 智能调整帧数（增强精度版本）
            duration = end_time - start_time
            if fast_mode:
                # 快速模式：使用更少的帧
                adjusted_num_frames = min(num_frames, 3)
                # 快速模式下只使用核心特征
                feature_types = ['phash', 'color_hist']
            elif enhanced_accuracy:
                # 增强精度模式：根据时长智能调整帧数
                if duration > 300:  # 超过5分钟
                    adjusted_num_frames = min(num_frames, 8)  # 增加帧数以提高精度
                elif duration > 60:  # 1-5分钟
                    adjusted_num_frames = min(num_frames, 12)  # 更多帧数
                elif duration > 10:  # 10秒-1分钟
                    adjusted_num_frames = min(num_frames, 15)  # 最多帧数
                elif duration < 2:  # 很短的片段
                    adjusted_num_frames = max(3, min(num_frames, 5))  # 至少3帧保证精度
                else:
                    adjusted_num_frames = num_frames
                log_info(f"增强精度模式: 视频段{duration:.2f}秒，提取{adjusted_num_frames}帧")
            else:
                # 标准模式
                if duration > 300:
                    adjusted_num_frames = min(num_frames, 5)
                    log_info(f"视频段较长 ({duration:.2f}秒)，减少提取帧数: {adjusted_num_frames}")
                elif duration < 2:
                    adjusted_num_frames = max(1, min(num_frames, 2))
                else:
                    adjusted_num_frames = num_frames

            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise Exception(f"无法打开视频文件: {video_path}")

            fps = cap.get(cv2.CAP_PROP_FPS)
            if fps <= 0:
                raise Exception(f"无效的视频帧率: {fps}")

            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            if total_frames <= 0:
                raise Exception(f"无法获取视频总帧数: {total_frames}")

            start_frame = int(start_time * fps)
            end_frame = int(end_time * fps)

            features = []
            if end_frame <= start_frame:
                cap.release()
                return features

            # 智能帧选择（增强精度版本）
            if enhanced_accuracy:
                # 增强模式：使用更智能的帧选择策略，传入视频路径用于基于内容的选择
                frame_indices = self._smart_frame_selection(
                    start_frame, end_frame, adjusted_num_frames, total_frames, video_path
                )
            else:
                # 标准模式：均匀分布
                frame_indices = np.linspace(start_frame, end_frame - 1, adjusted_num_frames, dtype=int)
                frame_indices = [idx for idx in frame_indices if idx < total_frames]

            # 根据模式设置参数（增强精度版本）
            if fast_mode:
                resize_size = (32, 32)
                hash_size = (8, 8)
                hist_bins = 32
            elif enhanced_accuracy:
                # 增强精度模式：使用更大的特征尺寸
                resize_size = (128, 128)  # 更大的图像尺寸
                hash_size = (32, 32)      # 更大的哈希尺寸
                hist_bins = 128           # 更多的直方图bins
            else:
                # 标准模式
                resize_size = (64, 64)
                hash_size = (16, 16)
                hist_bins = 64

            for i, frame_idx in enumerate(frame_indices):
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                ret, frame = cap.read()
                if ret:
                    # 预处理帧
                    frame_resized = cv2.resize(frame, resize_size, interpolation=cv2.INTER_AREA)
                    gray = cv2.cvtColor(frame_resized, cv2.COLOR_BGR2GRAY)

                    frame_features = {}

                    # 1. 感知哈希特征 (pHash)
                    if 'phash' in feature_types:
                        frame_features['phash'] = self._extract_phash_feature(gray, hash_size, fast_mode)

                    # 2. 颜色直方图特征
                    if 'color_hist' in feature_types:
                        frame_features['color_hist'] = self._extract_color_histogram_feature(frame_resized, hist_bins)

                    # 3. 边缘特征
                    if 'edge' in feature_types:
                        frame_features['edge'] = self._extract_edge_feature(gray)

                    # 4. 纹理特征（LBP - Local Binary Pattern）
                    if 'lbp' in feature_types:
                        frame_features['lbp'] = self._extract_lbp_feature(gray)

                    # 5. 梯度特征
                    if 'texture' in feature_types:
                        frame_features['texture'] = self._extract_texture_feature(gray)

                    # 6. 字幕鲁棒特征（新增）
                    if 'subtitle_robust' in feature_types:
                        frame_features['subtitle_robust'] = self._extract_subtitle_robust_feature(
                            frame_resized, gray, is_main_video
                        )

                    # 7. 运动特征（新增）
                    if 'motion' in feature_types and i > 0:
                        # 需要前一帧来计算运动特征
                        prev_frame_idx = frame_indices[i-1] if i > 0 else frame_idx - 1
                        if prev_frame_idx >= 0:
                            cap.set(cv2.CAP_PROP_POS_FRAMES, prev_frame_idx)
                            ret_prev, prev_frame = cap.read()
                            if ret_prev:
                                prev_gray = cv2.cvtColor(cv2.resize(prev_frame, resize_size), cv2.COLOR_BGR2GRAY)
                                frame_features['motion'] = self._extract_motion_feature(prev_gray, gray)
                            # 重新设置到当前帧
                            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)

                    features.append(frame_features)
                else:
                    log_info(f"Warning: Could not read frame {frame_idx} from {video_path}")

            cap.release()

            # 如果提取的特征太少，记录警告
            if len(features) < adjusted_num_frames / 2:
                log_info(f"Warning: 只提取到 {len(features)}/{adjusted_num_frames} 帧特征")

            return features
        except Exception as e:
            log_info(f"Error in extract_frame_features: {str(e)}")
            log_info(traceback.format_exc())
            if 'cap' in locals():
                cap.release()
            raise

    def _extract_phash_feature(self, gray_frame, hash_size, fast_mode=False):
        """提取感知哈希特征"""
        try:
            resized = cv2.resize(gray_frame, hash_size, interpolation=cv2.INTER_AREA)

            if fast_mode:
                # 快速模式：使用简单的平均哈希
                mean_val = np.mean(resized)
                phash = (resized > mean_val).flatten().astype(np.bool_)
            else:
                # 标准模式：使用DCT感知哈希
                dct = cv2.dct(np.float32(resized))
                dct_lowfreq = dct[:hash_size[0], :hash_size[1]]
                med = np.median(dct_lowfreq)
                phash = (dct_lowfreq > med).flatten().astype(np.bool_)

            return phash
        except Exception as e:
            log_warning(f"感知哈希特征提取失败: {e}")
            return np.zeros(hash_size[0] * hash_size[1], dtype=np.bool_)

    def _extract_color_histogram_feature(self, color_frame, bins=64):
        """提取颜色直方图特征"""
        try:
            # 转换到HSV颜色空间，对光照变化更鲁棒
            hsv = cv2.cvtColor(color_frame, cv2.COLOR_BGR2HSV)

            # 计算H、S、V通道的直方图
            h_hist = cv2.calcHist([hsv], [0], None, [bins//4], [0, 180])
            s_hist = cv2.calcHist([hsv], [1], None, [bins//4], [0, 256])
            v_hist = cv2.calcHist([hsv], [2], None, [bins//2], [0, 256])

            # 归一化
            h_hist = h_hist.flatten() / (h_hist.sum() + 1e-7)
            s_hist = s_hist.flatten() / (s_hist.sum() + 1e-7)
            v_hist = v_hist.flatten() / (v_hist.sum() + 1e-7)

            # 合并直方图
            color_hist = np.concatenate([h_hist, s_hist, v_hist])
            return color_hist.astype(np.float32)
        except Exception as e:
            log_warning(f"颜色直方图特征提取失败: {e}")
            return np.zeros(bins, dtype=np.float32)

    def _extract_edge_feature(self, gray_frame):
        """提取边缘特征"""
        try:
            # 使用Canny边缘检测
            edges = cv2.Canny(gray_frame, 50, 150)

            # 计算边缘密度和方向分布
            edge_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])

            # 计算梯度方向直方图
            grad_x = cv2.Sobel(gray_frame, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray_frame, cv2.CV_64F, 0, 1, ksize=3)

            # 计算梯度幅值和方向
            magnitude = np.sqrt(grad_x**2 + grad_y**2)
            angle = np.arctan2(grad_y, grad_x)

            # 方向直方图（8个方向）
            angle_hist, _ = np.histogram(angle[magnitude > np.mean(magnitude)], bins=8, range=(-np.pi, np.pi))
            angle_hist = angle_hist.astype(np.float32) / (angle_hist.sum() + 1e-7)

            # 合并特征
            edge_features = np.concatenate([[edge_density], angle_hist])
            return edge_features.astype(np.float32)
        except Exception as e:
            log_warning(f"边缘特征提取失败: {e}")
            return np.zeros(9, dtype=np.float32)

    def _extract_lbp_feature(self, gray_frame):
        """提取LBP（局部二值模式）纹理特征"""
        try:
            # 简化的LBP实现
            rows, cols = gray_frame.shape
            lbp = np.zeros_like(gray_frame)

            # 3x3邻域的LBP
            for i in range(1, rows-1):
                for j in range(1, cols-1):
                    center = gray_frame[i, j]
                    code = 0
                    code |= (gray_frame[i-1, j-1] >= center) << 7
                    code |= (gray_frame[i-1, j] >= center) << 6
                    code |= (gray_frame[i-1, j+1] >= center) << 5
                    code |= (gray_frame[i, j+1] >= center) << 4
                    code |= (gray_frame[i+1, j+1] >= center) << 3
                    code |= (gray_frame[i+1, j] >= center) << 2
                    code |= (gray_frame[i+1, j-1] >= center) << 1
                    code |= (gray_frame[i, j-1] >= center) << 0
                    lbp[i, j] = code

            # 计算LBP直方图
            lbp_hist, _ = np.histogram(lbp.flatten(), bins=256, range=(0, 256))
            lbp_hist = lbp_hist.astype(np.float32) / (lbp_hist.sum() + 1e-7)

            return lbp_hist
        except Exception as e:
            log_warning(f"LBP特征提取失败: {e}")
            return np.zeros(256, dtype=np.float32)

    def _extract_texture_feature(self, gray_frame):
        """提取纹理特征（基于灰度共生矩阵的简化版本）"""
        try:
            # 计算灰度级别的统计特征
            mean_val = np.mean(gray_frame)
            std_val = np.std(gray_frame)

            # 计算局部方差
            kernel = np.ones((3, 3), np.float32) / 9
            local_mean = cv2.filter2D(gray_frame.astype(np.float32), -1, kernel)
            local_var = cv2.filter2D((gray_frame.astype(np.float32) - local_mean)**2, -1, kernel)

            # 纹理统计特征
            texture_features = np.array([
                mean_val,
                std_val,
                np.mean(local_var),
                np.std(local_var),
                np.percentile(gray_frame, 25),
                np.percentile(gray_frame, 75),
                np.mean(np.abs(np.diff(gray_frame, axis=0))),  # 垂直变化
                np.mean(np.abs(np.diff(gray_frame, axis=1)))   # 水平变化
            ], dtype=np.float32)

            return texture_features
        except Exception as e:
            log_warning(f"纹理特征提取失败: {e}")
            return np.zeros(8, dtype=np.float32)

    def _smart_frame_selection(self, start_frame, end_frame, num_frames, total_frames, video_path=None):
        """
        智能帧选择策略：优先选择信息量丰富的关键帧
        """
        try:
            frame_range = end_frame - start_frame
            if frame_range <= num_frames:
                return list(range(start_frame, min(end_frame, total_frames)))

            # 如果提供了视频路径，使用基于内容的智能选择
            if video_path and os.path.exists(video_path):
                return self._content_based_frame_selection(video_path, start_frame, end_frame, num_frames, total_frames)

            # 改进的分层采样策略
            frame_indices = []

            # 1. 关键位置帧（确保时间分布的代表性）
            key_positions = [
                start_frame,  # 开始帧
                start_frame + frame_range // 4,  # 1/4位置
                start_frame + frame_range // 2,  # 中间位置
                start_frame + 3 * frame_range // 4,  # 3/4位置
                end_frame - 1  # 结束帧
            ]

            if num_frames <= 5:
                # 少量帧时，直接使用关键位置
                selected_indices = key_positions[:num_frames]
            else:
                # 更多帧时，在关键位置基础上添加均匀分布的帧
                frame_indices.extend(key_positions)
                remaining_frames = num_frames - len(key_positions)

                if remaining_frames > 0:
                    # 在关键位置之间的区间内均匀分布
                    for i in range(len(key_positions) - 1):
                        segment_start = key_positions[i] + 1
                        segment_end = key_positions[i + 1]
                        segment_frames = max(1, remaining_frames // (len(key_positions) - 1))

                        if segment_end > segment_start and segment_frames > 0:
                            segment_indices = np.linspace(segment_start, segment_end - 1,
                                                        min(segment_frames, segment_end - segment_start),
                                                        dtype=int)
                            frame_indices.extend(segment_indices)
                            remaining_frames -= len(segment_indices)

                            if remaining_frames <= 0:
                                break

                selected_indices = frame_indices

            # 去重、排序并限制数量
            selected_indices = sorted(list(set(selected_indices)))
            selected_indices = [max(start_frame, min(idx, end_frame - 1, total_frames - 1)) for idx in selected_indices]

            return selected_indices[:num_frames]

        except Exception as e:
            log_warning(f"智能帧选择失败，使用均匀分布: {e}")
            # 降级到均匀分布
            frame_indices = np.linspace(start_frame, end_frame - 1, num_frames, dtype=int)
            return [idx for idx in frame_indices if idx < total_frames]

    def _content_based_frame_selection(self, video_path, start_frame, end_frame, num_frames, total_frames):
        """
        基于内容的智能帧选择：选择信息量最丰富的帧
        """
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise Exception("无法打开视频文件")

            # 采样更多帧进行分析（但不要太多以免影响性能）
            sample_size = min(num_frames * 2, end_frame - start_frame, 20)
            sample_indices = np.linspace(start_frame, end_frame - 1, sample_size, dtype=int)

            frame_scores = []

            for idx in sample_indices:
                cap.set(cv2.CAP_PROP_POS_FRAMES, idx)
                ret, frame = cap.read()
                if ret:
                    # 计算帧的信息量得分
                    score = self._calculate_frame_information_score(frame)
                    frame_scores.append((idx, score))

            cap.release()

            if not frame_scores:
                raise Exception("无法获取帧信息")

            # 按得分排序，选择信息量最丰富的帧
            frame_scores.sort(key=lambda x: x[1], reverse=True)

            # 选择高分帧，但要保证时间分布的均匀性
            selected_indices = []
            min_distance = max(1, (end_frame - start_frame) // (num_frames + 1))  # 最小间距

            for idx, score in frame_scores:
                # 检查是否与已选择的帧过于接近
                too_close = False
                for used_idx in selected_indices:
                    if abs(idx - used_idx) < min_distance:
                        too_close = True
                        break

                if not too_close:
                    selected_indices.append(idx)
                    if len(selected_indices) >= num_frames:
                        break

            # 如果选择的帧不够，用均匀分布补充
            if len(selected_indices) < num_frames:
                remaining = num_frames - len(selected_indices)
                # 在未被选择的区域中均匀补充
                all_possible = set(range(start_frame, end_frame))
                used_set = set(selected_indices)
                available = sorted(list(all_possible - used_set))

                if available:
                    step = max(1, len(available) // remaining)
                    additional = available[::step][:remaining]
                    selected_indices.extend(additional)

            return sorted(selected_indices[:num_frames])

        except Exception as e:
            log_warning(f"基于内容的帧选择失败: {e}")
            # 降级到改进的均匀分布
            return self._smart_frame_selection(start_frame, end_frame, num_frames, total_frames, None)

    def _calculate_frame_information_score(self, frame):
        """
        计算帧的信息量得分
        """
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            # 1. 边缘密度（结构信息）
            edges = cv2.Canny(gray, 50, 150)
            edge_density = np.sum(edges > 0) / edges.size

            # 2. 纹理复杂度（局部方差）
            texture_score = np.var(gray)

            # 3. 颜色丰富度
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            color_hist = cv2.calcHist([hsv], [0, 1], None, [30, 30], [0, 180, 0, 256])
            color_hist_norm = color_hist / (color_hist.sum() + 1e-10)
            color_entropy = -np.sum(color_hist_norm * np.log(color_hist_norm + 1e-10))

            # 4. 梯度强度
            grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
            gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
            gradient_score = np.mean(gradient_magnitude)

            # 综合得分（归一化后加权）
            edge_weight = 0.3
            texture_weight = 0.25
            color_weight = 0.25
            gradient_weight = 0.2

            # 归一化各个分数到[0,1]范围
            edge_norm = min(1.0, edge_density * 10)  # 边缘密度通常很小
            texture_norm = min(1.0, texture_score / 5000)  # 纹理方差范围调整
            color_norm = min(1.0, color_entropy / 8)  # 颜色熵范围
            gradient_norm = min(1.0, gradient_score / 50)  # 梯度强度范围调整

            total_score = (edge_norm * edge_weight +
                          texture_norm * texture_weight +
                          color_norm * color_weight +
                          gradient_norm * gradient_weight)

            return total_score

        except Exception as e:
            log_warning(f"帧信息量计算失败: {e}")
            return 0.5  # 返回中等得分

    def _get_adaptive_feature_weights(self, features1, features2, enhanced_accuracy=True):
        """
        根据特征可用性和质量动态调整特征权重
        """
        try:
            # 检查第一帧的特征来确定可用的特征类型
            if not features1 or not features2:
                return self._get_default_feature_weights()

            sample_feat1 = features1[0] if isinstance(features1[0], dict) else {}
            sample_feat2 = features2[0] if isinstance(features2[0], dict) else {}

            available_features = set(sample_feat1.keys()) & set(sample_feat2.keys())

            if not available_features:
                return self._get_default_feature_weights()

            # 基础权重配置
            base_weights = {
                'phash': 0.30,           # 感知哈希：结构相似度，最重要
                'color_hist': 0.25,      # 颜色直方图：颜色分布
                'edge': 0.20,            # 边缘特征：轮廓信息
                'subtitle_robust': 0.15, # 字幕鲁棒特征：降低权重，避免过度依赖
                'lbp': 0.05,             # LBP纹理特征
                'texture': 0.03,         # 纹理特征
                'motion': 0.02           # 运动特征：辅助判断
            }

            # 根据增强精度模式调整权重
            if enhanced_accuracy:
                # 增强模式：提高结构和颜色特征权重，降低可能不稳定的特征权重
                enhanced_weights = {
                    'phash': 0.35,           # 提高感知哈希权重
                    'color_hist': 0.30,      # 提高颜色直方图权重
                    'edge': 0.20,            # 保持边缘特征权重
                    'subtitle_robust': 0.10, # 降低字幕鲁棒特征权重
                    'lbp': 0.03,             # 降低LBP权重
                    'texture': 0.01,         # 降低纹理权重
                    'motion': 0.01           # 降低运动特征权重
                }
                base_weights.update(enhanced_weights)

            # 只保留可用的特征，并重新归一化权重
            final_weights = {}
            total_weight = 0.0

            for feature_type in available_features:
                if feature_type in base_weights:
                    final_weights[feature_type] = base_weights[feature_type]
                    total_weight += base_weights[feature_type]

            # 归一化权重
            if total_weight > 0:
                for feature_type in final_weights:
                    final_weights[feature_type] /= total_weight

            # 如果没有匹配的特征，使用默认权重
            if not final_weights:
                return self._get_default_feature_weights()

            log_debug(f"自适应特征权重: {final_weights}")
            return final_weights

        except Exception as e:
            log_warning(f"自适应特征权重计算失败: {e}")
            return self._get_default_feature_weights()

    def _get_default_feature_weights(self):
        """
        获取默认特征权重
        """
        return {
            'phash': 0.35,
            'color_hist': 0.30,
            'edge': 0.20,
            'subtitle_robust': 0.10,
            'lbp': 0.03,
            'texture': 0.01,
            'motion': 0.01
        }

    def calculate_similarity(self, features1, features2, feature_weights=None, enhanced_accuracy=True):
        """
        计算两个视频段多维度特征的加权相似度（高精度版本）。
        :param features1: 视频段1的特征列表（字典列表）
        :param features2: 视频段2的特征列表（字典列表）
        :param feature_weights: 特征权重字典，如 {'phash': 0.4, 'color_hist': 0.3, 'edge': 0.2, 'lbp': 0.1}
        :param enhanced_accuracy: 是否启用增强精度模式
        :return: 相似度（0-1之间，1表示完全相同）
        """
        try:
            if not features1 or not features2:
                return 0.0

            min_len = min(len(features1), len(features2))
            if min_len == 0:
                return 0.0

            # 智能特征权重设置（根据特征可用性动态调整）
            if feature_weights is None:
                feature_weights = self._get_adaptive_feature_weights(features1, features2, enhanced_accuracy)

            frame_similarities = []

            for i in range(min_len):
                frame_feat1 = features1[i]
                frame_feat2 = features2[i]

                # 检查特征是否为字典格式（新格式）
                if isinstance(frame_feat1, dict) and isinstance(frame_feat2, dict):
                    # 多维度特征相似度计算
                    if enhanced_accuracy:
                        weighted_similarity = self._calculate_enhanced_multi_feature_similarity(
                            frame_feat1, frame_feat2, feature_weights
                        )
                    else:
                        weighted_similarity = self._calculate_multi_feature_similarity(
                            frame_feat1, frame_feat2, feature_weights
                        )
                else:
                    # 兼容旧格式（单一特征数组）
                    if hasattr(frame_feat1, '__len__') and hasattr(frame_feat2, '__len__'):
                        if len(frame_feat1) != len(frame_feat2):
                            log_warning(f"Feature length mismatch: {len(frame_feat1)} vs {len(frame_feat2)}")
                            continue

                        # 计算汉明距离（假设是布尔数组）
                        if frame_feat1.dtype == np.bool_ and frame_feat2.dtype == np.bool_:
                            diff = np.count_nonzero(frame_feat1 != frame_feat2)
                            weighted_similarity = 1 - (diff / len(frame_feat1))
                        else:
                            # 计算余弦相似度（假设是数值数组）
                            weighted_similarity = self._cosine_similarity(frame_feat1, frame_feat2)
                    else:
                        continue

                frame_similarities.append(weighted_similarity)

            if not frame_similarities:
                return 0.0

            # 增强精度模式的相似度计算
            if enhanced_accuracy:
                final_similarity = self._enhanced_similarity_aggregation(frame_similarities)
            else:
                # 标准模式：使用加权平均
                final_similarity = np.mean(frame_similarities)

            # 应用非线性变换增强区分度
            final_similarity = self._enhance_similarity_discrimination(final_similarity)

            return final_similarity

        except Exception as e:
            log_info(f"Error in calculate_similarity: {str(e)}")
            log_info(traceback.format_exc())
            raise

    def _calculate_multi_feature_similarity(self, feat1_dict, feat2_dict, weights):
        """计算多维度特征的加权相似度"""
        try:
            total_similarity = 0.0
            total_weight = 0.0

            # 遍历所有特征类型
            for feature_type, weight in weights.items():
                if feature_type in feat1_dict and feature_type in feat2_dict:
                    feat1 = feat1_dict[feature_type]
                    feat2 = feat2_dict[feature_type]

                    # 根据特征类型选择相似度计算方法
                    if feature_type == 'phash':
                        # 感知哈希使用汉明距离
                        similarity = self._hamming_similarity(feat1, feat2)
                    elif feature_type in ['color_hist', 'lbp']:
                        # 直方图特征使用直方图相关性
                        similarity = self._histogram_similarity(feat1, feat2)
                    elif feature_type in ['edge', 'texture', 'motion']:
                        # 数值特征使用余弦相似度
                        similarity = self._cosine_similarity(feat1, feat2)
                    elif feature_type == 'subtitle_robust':
                        # 字幕鲁棒特征使用专门的相似度计算
                        similarity = self._subtitle_robust_similarity(feat1, feat2)
                    else:
                        # 默认使用余弦相似度
                        similarity = self._cosine_similarity(feat1, feat2)

                    total_similarity += similarity * weight
                    total_weight += weight

            # 归一化
            if total_weight > 0:
                return total_similarity / total_weight
            else:
                return 0.0

        except Exception as e:
            log_warning(f"多维度特征相似度计算失败: {e}")
            return 0.0

    def _hamming_similarity(self, feat1, feat2):
        """计算汉明相似度（用于布尔特征）"""
        try:
            if len(feat1) != len(feat2):
                return 0.0
            diff = np.count_nonzero(feat1 != feat2)
            return 1 - (diff / len(feat1))
        except:
            return 0.0

    def _cosine_similarity(self, feat1, feat2):
        """计算余弦相似度（用于数值特征）"""
        try:
            feat1 = np.array(feat1, dtype=np.float32)
            feat2 = np.array(feat2, dtype=np.float32)

            if len(feat1) != len(feat2):
                return 0.0

            # 计算余弦相似度
            dot_product = np.dot(feat1, feat2)
            norm1 = np.linalg.norm(feat1)
            norm2 = np.linalg.norm(feat2)

            if norm1 == 0 or norm2 == 0:
                return 0.0

            similarity = dot_product / (norm1 * norm2)
            # 将相似度从[-1,1]映射到[0,1]
            return (similarity + 1) / 2
        except:
            return 0.0

    def _histogram_similarity(self, hist1, hist2):
        """计算直方图相似度（使用相关系数）"""
        try:
            hist1 = np.array(hist1, dtype=np.float32)
            hist2 = np.array(hist2, dtype=np.float32)

            if len(hist1) != len(hist2):
                return 0.0

            # 使用OpenCV的直方图比较方法（相关性）
            correlation = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)

            # 确保结果在[0,1]范围内
            return max(0.0, min(1.0, correlation))
        except:
            # 备用方法：使用余弦相似度
            return self._cosine_similarity(hist1, hist2)

    def _subtitle_robust_similarity(self, feat1, feat2):
        """
        字幕鲁棒特征的专门相似度计算方法
        :param feat1: 特征1
        :param feat2: 特征2
        :return: 相似度
        """
        try:
            feat1 = np.array(feat1, dtype=np.float32)
            feat2 = np.array(feat2, dtype=np.float32)

            if len(feat1) != len(feat2):
                return 0.0

            # 分段计算相似度，给不同部分不同权重
            # 假设特征结构：[上部颜色32+纹理8, 中部颜色32+纹理8, 下部颜色32+纹理8, 结构8]

            similarities = []
            weights = []

            # 上部区域（权重较高，通常没有字幕）
            upper_feat1 = feat1[:40]  # 32+8
            upper_feat2 = feat2[:40]
            upper_sim = self._cosine_similarity(upper_feat1, upper_feat2)
            similarities.append(upper_sim)
            weights.append(0.4)

            # 中部区域（权重最高，主要内容区域）
            middle_feat1 = feat1[40:80]  # 32+8
            middle_feat2 = feat2[40:80]
            middle_sim = self._cosine_similarity(middle_feat1, middle_feat2)
            similarities.append(middle_sim)
            weights.append(0.5)

            # 下部区域（权重较低，可能有字幕）
            lower_feat1 = feat1[80:120]  # 32+8
            lower_feat2 = feat2[80:120]
            lower_sim = self._cosine_similarity(lower_feat1, lower_feat2)
            similarities.append(lower_sim)
            weights.append(0.05)

            # 全局结构（权重较低但重要）
            if len(feat1) > 120:
                struct_feat1 = feat1[120:]
                struct_feat2 = feat2[120:]
                struct_sim = self._cosine_similarity(struct_feat1, struct_feat2)
                similarities.append(struct_sim)
                weights.append(0.05)

            # 加权平均
            weighted_sim = sum(s * w for s, w in zip(similarities, weights)) / sum(weights)

            return max(0.0, min(1.0, weighted_sim))

        except Exception as e:
            log_warning(f"字幕鲁棒相似度计算失败: {e}")
            return self._cosine_similarity(feat1, feat2)

    def _enhance_similarity_discrimination(self, similarity):
        """增强相似度的区分度"""
        try:
            # 使用S型函数增强区分度
            # 对于高相似度(>0.7)进一步提升，对于低相似度(<0.3)进一步降低
            if similarity > 0.7:
                # 高相似度区域：使用平方根函数提升
                enhanced = 0.7 + 0.3 * np.sqrt((similarity - 0.7) / 0.3)
            elif similarity < 0.3:
                # 低相似度区域：使用平方函数降低
                enhanced = 0.3 * (similarity / 0.3) ** 2
            else:
                # 中等相似度区域：保持线性
                enhanced = similarity

            return max(0.0, min(1.0, enhanced))
        except:
            return similarity

    def _calculate_enhanced_multi_feature_similarity(self, feat1_dict, feat2_dict, weights):
        """增强的多维度特征相似度计算"""
        try:
            total_similarity = 0.0
            total_weight = 0.0
            feature_similarities = {}

            # 遍历所有特征类型
            for feature_type, weight in weights.items():
                if feature_type in feat1_dict and feature_type in feat2_dict:
                    feat1 = feat1_dict[feature_type]
                    feat2 = feat2_dict[feature_type]

                    # 根据特征类型选择最优的相似度计算方法
                    if feature_type == 'phash':
                        # 感知哈希使用增强的汉明距离
                        similarity = self._enhanced_hamming_similarity(feat1, feat2)
                    elif feature_type == 'color_hist':
                        # 颜色直方图使用多种方法的融合
                        similarity = self._enhanced_histogram_similarity(feat1, feat2)
                    elif feature_type == 'edge':
                        # 边缘特征使用加权欧氏距离
                        similarity = self._enhanced_edge_similarity(feat1, feat2)
                    elif feature_type == 'lbp':
                        # LBP使用卡方距离
                        similarity = self._enhanced_lbp_similarity(feat1, feat2)
                    elif feature_type == 'texture':
                        # 纹理特征使用马氏距离
                        similarity = self._enhanced_texture_similarity(feat1, feat2)
                    elif feature_type == 'subtitle_robust':
                        # 字幕鲁棒特征使用专门的增强方法
                        similarity = self._enhanced_subtitle_robust_similarity(feat1, feat2)
                    elif feature_type == 'motion':
                        # 运动特征使用增强的余弦相似度
                        similarity = self._enhanced_motion_similarity(feat1, feat2)
                    else:
                        # 默认使用余弦相似度
                        similarity = self._cosine_similarity(feat1, feat2)

                    feature_similarities[feature_type] = similarity
                    total_similarity += similarity * weight
                    total_weight += weight

            # 增强的权重调整：根据特征一致性调整权重
            if len(feature_similarities) > 1:
                # 计算特征间的一致性
                similarities_list = list(feature_similarities.values())
                consistency = 1.0 - np.std(similarities_list)  # 一致性越高，标准差越小

                # 根据一致性调整最终相似度
                if total_weight > 0:
                    base_similarity = total_similarity / total_weight
                    # 一致性高的匹配给予奖励，一致性低的给予惩罚
                    enhanced_similarity = base_similarity * (0.8 + 0.4 * consistency)
                    return min(1.0, enhanced_similarity)

            # 归一化
            if total_weight > 0:
                return total_similarity / total_weight
            else:
                return 0.0

        except Exception as e:
            log_warning(f"增强多维度特征相似度计算失败: {e}")
            return 0.0

    def _enhanced_hamming_similarity(self, feat1, feat2):
        """增强的汉明相似度计算"""
        try:
            if len(feat1) != len(feat2):
                return 0.0

            # 基础汉明距离
            diff = np.count_nonzero(feat1 != feat2)
            base_similarity = 1 - (diff / len(feat1))

            # 增强：考虑连续匹配的权重
            consecutive_matches = 0
            max_consecutive = 0
            current_consecutive = 0

            for i in range(len(feat1)):
                if feat1[i] == feat2[i]:
                    current_consecutive += 1
                    max_consecutive = max(max_consecutive, current_consecutive)
                else:
                    current_consecutive = 0

            # 连续匹配奖励
            consecutive_bonus = min(0.1, max_consecutive / len(feat1) * 0.2)

            return min(1.0, base_similarity + consecutive_bonus)
        except:
            return 0.0

    def _enhanced_histogram_similarity(self, hist1, hist2):
        """增强的直方图相似度计算"""
        try:
            hist1 = np.array(hist1, dtype=np.float32)
            hist2 = np.array(hist2, dtype=np.float32)

            if len(hist1) != len(hist2):
                return 0.0

            # 多种相似度方法的融合
            # 1. 相关系数
            corr = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)

            # 2. 卡方距离（转换为相似度）
            chi_square = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CHISQR)
            chi_sim = 1.0 / (1.0 + chi_square)

            # 3. 巴氏距离（转换为相似度）
            bhattacharyya = cv2.compareHist(hist1, hist2, cv2.HISTCMP_BHATTACHARYYA)
            bhatt_sim = 1.0 - bhattacharyya

            # 4. 交集相似度
            intersection = cv2.compareHist(hist1, hist2, cv2.HISTCMP_INTERSECT)

            # 加权融合
            final_similarity = (
                corr * 0.4 +
                chi_sim * 0.3 +
                bhatt_sim * 0.2 +
                intersection * 0.1
            )

            return max(0.0, min(1.0, final_similarity))
        except:
            return self._cosine_similarity(hist1, hist2)

    def _enhanced_edge_similarity(self, edge1, edge2):
        """增强的边缘特征相似度"""
        try:
            edge1 = np.array(edge1, dtype=np.float32)
            edge2 = np.array(edge2, dtype=np.float32)

            if len(edge1) != len(edge2):
                return 0.0

            # 加权欧氏距离（边缘密度权重更高）
            weights = np.array([2.0] + [1.0] * (len(edge1) - 1))  # 第一个是边缘密度

            diff = edge1 - edge2
            weighted_distance = np.sqrt(np.sum(weights * diff ** 2))

            # 转换为相似度
            max_distance = np.sqrt(np.sum(weights))
            similarity = 1.0 - (weighted_distance / max_distance)

            return max(0.0, min(1.0, similarity))
        except:
            return self._cosine_similarity(edge1, edge2)

    def _enhanced_lbp_similarity(self, lbp1, lbp2):
        """增强的LBP特征相似度"""
        try:
            lbp1 = np.array(lbp1, dtype=np.float32)
            lbp2 = np.array(lbp2, dtype=np.float32)

            if len(lbp1) != len(lbp2):
                return 0.0

            # 使用卡方距离
            epsilon = 1e-10
            chi_square = np.sum((lbp1 - lbp2) ** 2 / (lbp1 + lbp2 + epsilon))

            # 转换为相似度
            similarity = 1.0 / (1.0 + chi_square)

            return max(0.0, min(1.0, similarity))
        except:
            return self._cosine_similarity(lbp1, lbp2)

    def _enhanced_texture_similarity(self, texture1, texture2):
        """增强的纹理特征相似度"""
        try:
            texture1 = np.array(texture1, dtype=np.float32)
            texture2 = np.array(texture2, dtype=np.float32)

            if len(texture1) != len(texture2):
                return 0.0

            # 使用标准化的欧氏距离
            diff = texture1 - texture2
            distance = np.linalg.norm(diff)

            # 标准化
            max_distance = np.linalg.norm(texture1) + np.linalg.norm(texture2)
            if max_distance > 0:
                normalized_distance = distance / max_distance
                similarity = 1.0 - normalized_distance
            else:
                similarity = 1.0

            return max(0.0, min(1.0, similarity))
        except:
            return self._cosine_similarity(texture1, texture2)

    def _enhanced_subtitle_robust_similarity(self, feat1, feat2):
        """
        增强的字幕鲁棒特征相似度计算
        """
        try:
            feat1 = np.array(feat1, dtype=np.float32)
            feat2 = np.array(feat2, dtype=np.float32)

            if len(feat1) != len(feat2):
                return 0.0

            # 使用更复杂的权重策略
            similarities = []
            weights = []

            # 上部区域（权重高，通常无字幕干扰）
            upper_feat1 = feat1[:40]
            upper_feat2 = feat2[:40]
            upper_sim = self._cosine_similarity(upper_feat1, upper_feat2)
            similarities.append(upper_sim)
            weights.append(0.35)

            # 中部区域（权重最高，核心内容）
            middle_feat1 = feat1[40:80]
            middle_feat2 = feat2[40:80]
            middle_sim = self._cosine_similarity(middle_feat1, middle_feat2)
            similarities.append(middle_sim)
            weights.append(0.45)

            # 下部区域（权重低，可能有字幕）
            lower_feat1 = feat1[80:120]
            lower_feat2 = feat2[80:120]
            lower_sim = self._cosine_similarity(lower_feat1, lower_feat2)
            similarities.append(lower_sim)
            weights.append(0.1)

            # 全局结构（权重中等，重要的结构信息）
            if len(feat1) > 120:
                struct_feat1 = feat1[120:]
                struct_feat2 = feat2[120:]
                struct_sim = self._cosine_similarity(struct_feat1, struct_feat2)
                similarities.append(struct_sim)
                weights.append(0.1)

            # 计算加权相似度
            weighted_sim = sum(s * w for s, w in zip(similarities, weights)) / sum(weights)

            # 应用一致性奖励
            consistency = 1.0 - np.std(similarities)
            enhanced_sim = weighted_sim * (0.9 + 0.2 * consistency)

            return max(0.0, min(1.0, enhanced_sim))

        except Exception as e:
            log_warning(f"增强字幕鲁棒相似度计算失败: {e}")
            return self._subtitle_robust_similarity(feat1, feat2)

    def _enhanced_motion_similarity(self, feat1, feat2):
        """
        增强的运动特征相似度计算
        """
        try:
            feat1 = np.array(feat1, dtype=np.float32)
            feat2 = np.array(feat2, dtype=np.float32)

            if len(feat1) != len(feat2):
                return 0.0

            # 运动特征结构：[统计特征5个, 方向直方图8个]
            # 分别计算统计特征和方向特征的相似度

            # 统计特征相似度（运动强度、变化等）
            stats_feat1 = feat1[:5]
            stats_feat2 = feat2[:5]
            stats_sim = self._cosine_similarity(stats_feat1, stats_feat2)

            # 方向特征相似度
            if len(feat1) > 5:
                dir_feat1 = feat1[5:]
                dir_feat2 = feat2[5:]
                dir_sim = self._histogram_similarity(dir_feat1, dir_feat2)
            else:
                dir_sim = 0.0

            # 加权组合（统计特征权重更高）
            enhanced_sim = 0.7 * stats_sim + 0.3 * dir_sim

            return max(0.0, min(1.0, enhanced_sim))

        except Exception as e:
            log_warning(f"增强运动相似度计算失败: {e}")
            return self._cosine_similarity(feat1, feat2)

    def _extract_subtitle_robust_feature(self, color_frame, gray_frame, is_main_video=False):
        """
        提取字幕鲁棒特征，专门处理字幕干扰问题
        :param color_frame: 彩色帧
        :param gray_frame: 灰度帧
        :param is_main_video: 是否为主视频（有字幕）
        :return: 字幕鲁棒特征向量
        """
        try:
            features = []

            # 1. 字幕区域检测和掩码
            subtitle_mask = self._detect_subtitle_region(gray_frame, is_main_video)

            # 2. 非字幕区域的特征提取
            masked_gray = gray_frame.copy()
            masked_color = color_frame.copy()

            if subtitle_mask is not None:
                # 用中值填充字幕区域
                median_val = np.median(gray_frame)
                masked_gray[subtitle_mask] = median_val

                # 彩色图像也进行相应处理
                for c in range(3):
                    channel_median = np.median(color_frame[:, :, c])
                    masked_color[subtitle_mask, c] = channel_median

            # 3. 提取核心内容区域特征
            # 分区域提取特征：上部、中部、下部
            h, w = gray_frame.shape
            regions = [
                (0, h//3),           # 上部
                (h//3, 2*h//3),      # 中部
                (2*h//3, h)          # 下部
            ]

            for start_h, end_h in regions:
                region_gray = masked_gray[start_h:end_h, :]
                region_color = masked_color[start_h:end_h, :, :]

                # 区域颜色直方图
                region_hist = self._extract_color_histogram_feature(region_color, bins=32)
                features.extend(region_hist)

                # 区域纹理特征
                region_texture = self._extract_texture_feature(region_gray)
                features.extend(region_texture)

            # 4. 全局结构特征（忽略字幕）
            # 使用Sobel算子提取主要结构
            sobel_x = cv2.Sobel(masked_gray, cv2.CV_64F, 1, 0, ksize=3)
            sobel_y = cv2.Sobel(masked_gray, cv2.CV_64F, 0, 1, ksize=3)

            # 结构方向分布
            magnitude = np.sqrt(sobel_x**2 + sobel_y**2)
            angle = np.arctan2(sobel_y, sobel_x)

            # 计算主要结构方向的直方图
            strong_edges = magnitude > np.percentile(magnitude, 75)
            if np.any(strong_edges):
                structure_hist, _ = np.histogram(
                    angle[strong_edges], bins=8, range=(-np.pi, np.pi)
                )
                structure_hist = structure_hist.astype(np.float32) / (structure_hist.sum() + 1e-7)
                features.extend(structure_hist)
            else:
                features.extend(np.zeros(8, dtype=np.float32))

            return np.array(features, dtype=np.float32)

        except Exception as e:
            log_warning(f"字幕鲁棒特征提取失败: {e}")
            # 返回默认特征
            return np.zeros(120, dtype=np.float32)  # 3*32 + 3*8 + 8 = 120

    def _detect_subtitle_region(self, gray_frame, is_main_video=False):
        """
        增强的字幕区域检测：多方法融合，提高检测准确性
        :param gray_frame: 灰度帧
        :param is_main_video: 是否为主视频
        :return: 字幕区域掩码
        """
        try:
            if not is_main_video:
                return None  # 辅助视频通常没有字幕

            h, w = gray_frame.shape

            # 多区域检测：不仅检测底部，也检测其他可能的字幕位置
            subtitle_regions = [
                (int(h*0.75), h),      # 底部25%区域（最常见）
                (int(h*0.05), int(h*0.25)),  # 顶部20%区域（有时字幕在上方）
                (int(h*0.4), int(h*0.6))     # 中间20%区域（有时字幕在中间）
            ]

            detected_masks = []

            for region_start, region_end in subtitle_regions:
                region = gray_frame[region_start:region_end, :]
                mask = self._detect_subtitle_in_region(region, region_start, gray_frame.shape)
                if mask is not None:
                    detected_masks.append(mask)

            # 合并所有检测到的字幕区域
            if detected_masks:
                final_mask = np.zeros_like(gray_frame, dtype=bool)
                for mask in detected_masks:
                    final_mask |= mask
                return final_mask

            return None

        except Exception as e:
            log_warning(f"字幕区域检测失败: {e}")
            return None

    def _detect_subtitle_in_region(self, region, region_start, frame_shape):
        """
        在指定区域内检测字幕
        """
        try:
            if region.size == 0:
                return None

            h_region, w_region = region.shape
            h_frame, w_frame = frame_shape

            # 方法1: 边缘密度检测
            edge_mask = self._detect_subtitle_by_edges(region, region_start, frame_shape)

            # 方法2: 对比度检测
            contrast_mask = self._detect_subtitle_by_contrast(region, region_start, frame_shape)

            # 方法3: 文本模式检测
            pattern_mask = self._detect_subtitle_by_pattern(region, region_start, frame_shape)

            # 融合多种检测结果
            masks = [mask for mask in [edge_mask, contrast_mask, pattern_mask] if mask is not None]

            if not masks:
                return None

            # 投票机制：至少两种方法检测到才认为是字幕
            if len(masks) >= 2:
                combined_mask = np.zeros(frame_shape, dtype=bool)
                for mask in masks:
                    combined_mask |= mask
                return combined_mask
            elif len(masks) == 1:
                # 只有一种方法检测到，需要更严格的验证
                mask = masks[0]
                if self._validate_subtitle_mask(mask, region):
                    return mask

            return None

        except Exception as e:
            log_warning(f"区域字幕检测失败: {e}")
            return None

    def _detect_subtitle_by_edges(self, region, region_start, frame_shape):
        """
        基于边缘密度的字幕检测
        """
        try:
            h_region, w_region = region.shape

            # 计算水平和垂直梯度
            grad_x = cv2.Sobel(region, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(region, cv2.CV_64F, 0, 1, ksize=3)

            # 计算梯度幅值
            gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)

            # 寻找水平边缘密集的行（字幕特征）
            horizontal_edges = np.sum(gradient_magnitude > np.percentile(gradient_magnitude, 85), axis=1)

            # 动态阈值：根据图像内容调整
            base_threshold = w_region * 0.15  # 降低基础阈值
            adaptive_threshold = max(base_threshold, np.mean(horizontal_edges) + 2 * np.std(horizontal_edges))

            subtitle_rows = horizontal_edges > adaptive_threshold

            if np.sum(subtitle_rows) < 2:  # 至少需要2行
                return None

            # 创建掩码
            mask = np.zeros(frame_shape, dtype=bool)

            # 找到连续的字幕行
            subtitle_regions = self._find_continuous_regions(subtitle_rows)

            for start, end in subtitle_regions:
                if end - start >= 2:  # 至少2行高
                    mask[region_start + start:region_start + end, :] = True

            return mask if np.any(mask) else None

        except Exception as e:
            log_warning(f"边缘检测字幕失败: {e}")
            return None

    def _detect_subtitle_by_contrast(self, region, region_start, frame_shape):
        """
        基于对比度的字幕检测
        """
        try:
            h_region, w_region = region.shape

            # 计算局部对比度
            kernel = np.ones((3, 3), np.float32) / 9
            mean_filtered = cv2.filter2D(region.astype(np.float32), -1, kernel)
            contrast = np.abs(region.astype(np.float32) - mean_filtered)

            # 寻找高对比度的水平条带
            row_contrast = np.mean(contrast, axis=1)
            contrast_threshold = np.percentile(row_contrast, 90)

            high_contrast_rows = row_contrast > contrast_threshold

            if np.sum(high_contrast_rows) < 2:
                return None

            # 创建掩码
            mask = np.zeros(frame_shape, dtype=bool)
            subtitle_regions = self._find_continuous_regions(high_contrast_rows)

            for start, end in subtitle_regions:
                if end - start >= 2:
                    mask[region_start + start:region_start + end, :] = True

            return mask if np.any(mask) else None

        except Exception as e:
            log_warning(f"对比度检测字幕失败: {e}")
            return None

    def _detect_subtitle_by_pattern(self, region, region_start, frame_shape):
        """
        基于文本模式的字幕检测
        """
        try:
            h_region, w_region = region.shape

            # 使用形态学操作检测文本模式
            # 水平结构元素，用于连接字符
            horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (w_region//20, 1))

            # 二值化
            _, binary = cv2.threshold(region, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # 形态学闭运算，连接字符
            closed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, horizontal_kernel)

            # 寻找水平连通区域
            contours, _ = cv2.findContours(closed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            text_rows = np.zeros(h_region, dtype=bool)

            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                # 字幕通常是水平的长条形
                if w > w_region * 0.1 and h < h_region * 0.3 and w/h > 3:
                    text_rows[y:y+h] = True

            if np.sum(text_rows) < 2:
                return None

            # 创建掩码
            mask = np.zeros(frame_shape, dtype=bool)
            subtitle_regions = self._find_continuous_regions(text_rows)

            for start, end in subtitle_regions:
                if end - start >= 2:
                    mask[region_start + start:region_start + end, :] = True

            return mask if np.any(mask) else None

        except Exception as e:
            log_warning(f"模式检测字幕失败: {e}")
            return None

    def _find_continuous_regions(self, boolean_array):
        """
        找到布尔数组中的连续True区域
        """
        regions = []
        in_region = False
        start = 0

        for i, value in enumerate(boolean_array):
            if value and not in_region:
                start = i
                in_region = True
            elif not value and in_region:
                regions.append((start, i))
                in_region = False

        if in_region:
            regions.append((start, len(boolean_array)))

        return regions

    def _validate_subtitle_mask(self, mask, region):
        """
        验证检测到的字幕掩码是否合理
        """
        try:
            # 检查掩码覆盖的区域大小
            mask_area = np.sum(mask)
            total_area = mask.size

            # 字幕通常不会覆盖太大的区域
            if mask_area / total_area > 0.3:
                return False

            # 字幕区域应该有一定的连续性
            # 这里可以添加更多的验证逻辑

            return True

        except Exception as e:
            log_warning(f"字幕掩码验证失败: {e}")
            return False

    def _extract_motion_feature(self, prev_gray, curr_gray):
        """
        提取运动特征，用于更好地匹配动态内容
        :param prev_gray: 前一帧灰度图
        :param curr_gray: 当前帧灰度图
        :return: 运动特征向量
        """
        try:
            # 1. 光流计算
            flow = cv2.calcOpticalFlowPyrLK(
                prev_gray, curr_gray,
                corners=cv2.goodFeaturesToTrack(prev_gray, maxCorners=100, qualityLevel=0.01, minDistance=10),
                nextPts=None
            )[0]

            if flow is not None and len(flow) > 0:
                # 计算运动向量的统计特征
                motion_vectors = flow.reshape(-1, 2)

                # 运动幅度
                motion_magnitude = np.linalg.norm(motion_vectors, axis=1)

                # 运动方向
                motion_angle = np.arctan2(motion_vectors[:, 1], motion_vectors[:, 0])

                # 统计特征
                features = [
                    np.mean(motion_magnitude),
                    np.std(motion_magnitude),
                    np.max(motion_magnitude),
                    np.percentile(motion_magnitude, 75),
                    np.percentile(motion_magnitude, 25)
                ]

                # 方向直方图
                angle_hist, _ = np.histogram(motion_angle, bins=8, range=(-np.pi, np.pi))
                angle_hist = angle_hist.astype(np.float32) / (angle_hist.sum() + 1e-7)
                features.extend(angle_hist)

                return np.array(features, dtype=np.float32)

            # 2. 备用方法：帧差分
            frame_diff = cv2.absdiff(curr_gray, prev_gray)

            # 运动区域检测
            motion_mask = frame_diff > np.percentile(frame_diff, 85)
            motion_ratio = np.sum(motion_mask) / motion_mask.size

            # 运动强度
            motion_intensity = np.mean(frame_diff[motion_mask]) if np.any(motion_mask) else 0

            # 运动分布
            motion_regions = []
            h, w = frame_diff.shape
            for i in range(3):
                for j in range(3):
                    region = motion_mask[i*h//3:(i+1)*h//3, j*w//3:(j+1)*w//3]
                    motion_regions.append(np.sum(region) / region.size)

            features = [motion_ratio, motion_intensity] + motion_regions
            # 补齐到与光流方法相同的维度
            features.extend([0] * (13 - len(features)))

            return np.array(features[:13], dtype=np.float32)

        except Exception as e:
            log_warning(f"运动特征提取失败: {e}")
            return np.zeros(13, dtype=np.float32)

    def _enhanced_similarity_aggregation(self, frame_similarities):
        """增强的相似度聚合方法：智能处理异常值和分布不均"""
        try:
            if not frame_similarities:
                return 0.0

            similarities = np.array(frame_similarities)
            n = len(similarities)

            if n == 1:
                return similarities[0]

            # 异常值检测和处理
            similarities_cleaned = self._remove_similarity_outliers(similarities)

            # 计算多种统计量
            mean_sim = np.mean(similarities_cleaned)
            median_sim = np.median(similarities_cleaned)
            std_sim = np.std(similarities_cleaned)

            # 计算分位数
            q25 = np.percentile(similarities_cleaned, 25)
            q75 = np.percentile(similarities_cleaned, 75)
            iqr = q75 - q25

            # 根据分布特征选择聚合策略
            if std_sim < 0.05:  # 非常一致的相似度
                # 直接使用算术平均
                final_similarity = mean_sim
                confidence = 0.95

            elif std_sim < 0.15:  # 较一致的相似度
                # 使用加权平均，给中位数更高权重
                final_similarity = 0.6 * mean_sim + 0.4 * median_sim
                confidence = 0.85

            elif iqr < 0.2:  # IQR较小，分布相对集中
                # 使用截断平均（去掉极值后的平均）
                trimmed_mean = self._calculate_trimmed_mean(similarities_cleaned, 0.1)
                final_similarity = 0.7 * trimmed_mean + 0.3 * median_sim
                confidence = 0.75

            else:  # 分布较分散
                # 使用更保守的策略
                # 计算加权中位数（给高相似度更高权重）
                weighted_median = self._calculate_weighted_median(similarities_cleaned)

                # 使用多种方法的保守组合
                conservative_mean = 0.3 * mean_sim + 0.4 * median_sim + 0.3 * weighted_median
                final_similarity = conservative_mean
                confidence = 0.6

            # 应用置信度调整
            final_similarity = final_similarity * confidence + (1 - confidence) * median_sim

            # 应用一致性奖励/惩罚
            consistency_factor = 1.0 - min(0.2, std_sim)  # 一致性越高，因子越接近1
            final_similarity = final_similarity * consistency_factor

            return max(0.0, min(1.0, final_similarity))

        except Exception as e:
            log_warning(f"增强相似度聚合失败: {e}")
            return np.mean(frame_similarities) if frame_similarities else 0.0

    def _remove_similarity_outliers(self, similarities, method='iqr'):
        """
        移除相似度数组中的异常值
        """
        try:
            if len(similarities) <= 3:
                return similarities  # 数据太少，不处理异常值

            if method == 'iqr':
                # 使用IQR方法检测异常值
                q25 = np.percentile(similarities, 25)
                q75 = np.percentile(similarities, 75)
                iqr = q75 - q25

                # 定义异常值边界
                lower_bound = q25 - 1.5 * iqr
                upper_bound = q75 + 1.5 * iqr

                # 过滤异常值
                mask = (similarities >= lower_bound) & (similarities <= upper_bound)
                cleaned = similarities[mask]

                # 如果过滤后数据太少，放宽条件
                if len(cleaned) < len(similarities) * 0.5:
                    lower_bound = q25 - 2.0 * iqr
                    upper_bound = q75 + 2.0 * iqr
                    mask = (similarities >= lower_bound) & (similarities <= upper_bound)
                    cleaned = similarities[mask]

                return cleaned if len(cleaned) > 0 else similarities

            elif method == 'zscore':
                # 使用Z-score方法
                mean_sim = np.mean(similarities)
                std_sim = np.std(similarities)

                if std_sim == 0:
                    return similarities

                z_scores = np.abs((similarities - mean_sim) / std_sim)
                mask = z_scores < 2.0  # 保留Z-score < 2的数据
                cleaned = similarities[mask]

                return cleaned if len(cleaned) > 0 else similarities

            return similarities

        except Exception as e:
            log_warning(f"异常值移除失败: {e}")
            return similarities

    def _calculate_trimmed_mean(self, similarities, trim_ratio=0.1):
        """
        计算截断平均值（去掉两端的极值）
        """
        try:
            if len(similarities) <= 2:
                return np.mean(similarities)

            # 排序
            sorted_sims = np.sort(similarities)
            n = len(sorted_sims)

            # 计算要截断的数量
            trim_count = int(n * trim_ratio)

            if trim_count == 0:
                return np.mean(sorted_sims)

            # 截断两端
            trimmed = sorted_sims[trim_count:-trim_count] if trim_count < n//2 else sorted_sims

            return np.mean(trimmed)

        except Exception as e:
            log_warning(f"截断平均计算失败: {e}")
            return np.mean(similarities)

    def _calculate_weighted_median(self, similarities):
        """
        计算加权中位数（给高相似度更高权重）
        """
        try:
            if len(similarities) <= 1:
                return np.median(similarities)

            # 创建权重：相似度越高，权重越大
            weights = similarities ** 2  # 平方权重
            weights = weights / np.sum(weights)  # 归一化

            # 排序
            sorted_indices = np.argsort(similarities)
            sorted_sims = similarities[sorted_indices]
            sorted_weights = weights[sorted_indices]

            # 计算累积权重
            cumulative_weights = np.cumsum(sorted_weights)

            # 找到中位数位置
            median_pos = 0.5
            median_idx = np.searchsorted(cumulative_weights, median_pos)

            if median_idx >= len(sorted_sims):
                median_idx = len(sorted_sims) - 1

            return sorted_sims[median_idx]

        except Exception as e:
            log_warning(f"加权中位数计算失败: {e}")
            return np.median(similarities)

    def _precise_duration_control(self, clip, target_duration, tolerance=0.01):
        """
        精确的时长控制方法
        :param clip: 视频片段
        :param target_duration: 目标时长
        :param tolerance: 允许的误差（秒）
        :return: 调整后的视频片段
        """
        try:
            if clip is None:
                return None

            actual_duration = clip.duration
            duration_error = abs(actual_duration - target_duration)

            if duration_error <= tolerance:
                # 时长已经足够精确
                return clip

            log_info(f"执行精确时长控制: {actual_duration:.3f}s → {target_duration:.3f}s (误差: {duration_error:.3f}s)")

            if actual_duration > target_duration:
                # 片段太长，需要裁剪
                # 使用中心裁剪，保持内容的代表性
                excess = actual_duration - target_duration
                start_trim = excess / 2
                end_trim = actual_duration - excess / 2

                adjusted_clip = clip.subclip(start_trim, end_trim)
                log_info(f"中心裁剪: {start_trim:.3f}s - {end_trim:.3f}s")

            else:
                # 片段太短，尝试不同的策略
                shortage = target_duration - actual_duration

                if shortage < 0.1:
                    # 差异很小，使用循环填充
                    try:
                        # 创建一个稍长的片段来填补差异
                        loop_clip = clip.loop(duration=target_duration)
                        adjusted_clip = loop_clip.subclip(0, target_duration)
                        log_info(f"循环填充: 补充 {shortage:.3f}s")
                    except Exception as loop_error:
                        log_warning(f"循环填充失败: {loop_error}，保持原片段")
                        adjusted_clip = clip
                else:
                    # 差异较大，保持原片段但记录警告
                    log_warning(f"时长差异过大({shortage:.3f}s)，保持原片段")
                    adjusted_clip = clip

            # 验证调整结果
            final_duration = adjusted_clip.duration
            final_error = abs(final_duration - target_duration)

            if final_error <= tolerance:
                log_info(f"时长控制成功: 最终误差 {final_error:.3f}s")
            else:
                log_warning(f"时长控制未达到目标精度: 最终误差 {final_error:.3f}s")

            return adjusted_clip

        except Exception as e:
            log_error(f"精确时长控制失败: {e}")
            return clip

    def _validate_total_duration(self, clips, expected_duration, tolerance=0.1):
        """
        验证总时长是否符合预期
        :param clips: 视频片段列表
        :param expected_duration: 预期总时长
        :param tolerance: 允许的误差（秒）
        :return: (是否通过验证, 实际总时长, 误差)
        """
        try:
            if not clips:
                return False, 0.0, expected_duration

            actual_total = sum(clip.duration for clip in clips if clip is not None)
            error = abs(actual_total - expected_duration)

            is_valid = error <= tolerance

            log_info(f"总时长验证: 预期={expected_duration:.3f}s, 实际={actual_total:.3f}s, 误差={error:.3f}s, 通过={is_valid}")

            return is_valid, actual_total, error

        except Exception as e:
            log_error(f"总时长验证失败: {e}")
            return False, 0.0, expected_duration

    def _duration_correction_strategy(self, clips, target_duration):
        """
        时长校正策略
        :param clips: 视频片段列表
        :param target_duration: 目标总时长
        :return: 校正后的视频片段列表
        """
        try:
            if not clips:
                return clips

            current_total = sum(clip.duration for clip in clips if clip is not None)
            error = current_total - target_duration

            if abs(error) <= 0.1:  # 误差很小，不需要校正
                return clips

            log_info(f"执行时长校正策略: 当前总时长={current_total:.3f}s, 目标={target_duration:.3f}s, 误差={error:.3f}s")

            corrected_clips = []

            if error > 0:
                # 总时长过长，需要缩短
                # 按比例缩短每个片段
                scale_factor = target_duration / current_total
                log_info(f"按比例缩短，缩放因子: {scale_factor:.4f}")

                for i, clip in enumerate(clips):
                    if clip is not None:
                        new_duration = clip.duration * scale_factor
                        corrected_clip = clip.subclip(0, new_duration)
                        corrected_clips.append(corrected_clip)
                        log_info(f"片段{i}: {clip.duration:.3f}s → {new_duration:.3f}s")
                    else:
                        corrected_clips.append(None)

            else:
                # 总时长过短，需要延长
                # 这种情况比较复杂，通常保持原样并记录警告
                shortage = abs(error)
                log_warning(f"总时长不足 {shortage:.3f}s，保持原片段")
                corrected_clips = clips

            # 验证校正结果
            corrected_total = sum(clip.duration for clip in corrected_clips if clip is not None)
            final_error = abs(corrected_total - target_duration)

            log_info(f"时长校正完成: 校正后总时长={corrected_total:.3f}s, 最终误差={final_error:.3f}s")

            return corrected_clips

        except Exception as e:
            log_error(f"时长校正策略失败: {e}")
            return clips

    def find_similar_scenes(self, main_video_scenes, aux_video_scenes, main_video_path, aux_video_path, similarity_threshold=0.8, feature_types=None, feature_weights=None):
        """
        优化的相似度匹配策略：精确时长匹配，多维度特征融合，智能阈值调整
        :param main_video_scenes: 主视频的视频段列表
        :param aux_video_scenes: 辅助视频的视频段列表
        :param main_video_path: 主视频文件路径
        :param aux_video_path: 辅助视频文件路径
        :param similarity_threshold: 相似度阈值，高于此阈值则认为相似
        :param feature_types: 特征类型列表，默认使用所有特征
        :param feature_weights: 特征权重字典，用于调整不同特征的重要性
        :return: 相似视频段的匹配列表，确保时长精确匹配
        """
        try:
            # 检查视频文件是否存在
            if not os.path.exists(main_video_path):
                raise FileNotFoundError(f"主视频文件不存在: {main_video_path}")
            if not os.path.exists(aux_video_path):
                raise FileNotFoundError(f"辅助视频文件不存在: {aux_video_path}")

            if not main_video_scenes or not aux_video_scenes:
                log_info("主视频或辅助视频场景为空")
                return []

            # 设置默认特征类型和权重（针对主视频+字幕 vs 辅助视频原始素材优化）
            if feature_types is None:
                feature_types = ['subtitle_robust', 'phash', 'color_hist', 'edge', 'motion']

            if feature_weights is None:
                feature_weights = {
                    'subtitle_robust': 0.35,  # 字幕鲁棒特征权重最高
                    'phash': 0.25,           # 感知哈希权重较高
                    'color_hist': 0.20,      # 颜色直方图权重中等
                    'edge': 0.15,            # 边缘特征权重中等
                    'motion': 0.05           # 运动特征权重较低
                }

            # 智能优化：根据视频大小决定处理策略
            main_count = len(main_video_scenes)
            aux_count = len(aux_video_scenes)

            log_info(f"开始优化相似度匹配: 主视频{main_count}个场景, 辅助视频{aux_count}个场景")
            log_info(f"使用特征类型: {feature_types}")
            log_info(f"特征权重: {feature_weights}")

            # 强制垃圾回收，确保有足够的内存
            import gc
            gc.collect()

            # 使用新的精确时长匹配策略
            log_info("使用精确时长匹配策略")
            return self._find_similar_scenes_precise_duration(
                main_video_scenes, aux_video_scenes,
                main_video_path, aux_video_path,
                similarity_threshold, feature_types, feature_weights
            )
        except Exception as e:
            log_info(f"Error in find_similar_scenes: {str(e)}")
            log_info(traceback.format_exc())
            raise

    def _find_similar_scenes_precise_duration(self, main_video_scenes, aux_video_scenes, main_video_path, aux_video_path, similarity_threshold, feature_types, feature_weights):
        """
        精确时长匹配策略：确保替换视频段与主视频段时长完全一致
        """
        try:
            import gc
            from moviepy.editor import VideoFileClip

            main_count = len(main_video_scenes)
            aux_count = len(aux_video_scenes)

            log_info(f"开始精确时长匹配: 主视频{main_count}个段, 辅助视频{aux_count}个段")
            log_info("策略: 精确时长匹配，智能阈值调整，避免重叠区域")

            # 获取辅助视频总时长
            aux_clip = VideoFileClip(aux_video_path)
            aux_total_duration = aux_clip.duration
            aux_clip.close()

            similar_matches = []
            processed_aux_regions = []  # 记录已使用的辅助视频区域
            adaptive_threshold = similarity_threshold  # 自适应阈值

            # 按顺序处理每个主视频段
            for main_idx, main_scene in enumerate(main_video_scenes):
                main_duration = main_scene['end_time'] - main_scene['start_time']
                main_start = main_scene['start_time']
                main_end = main_scene['end_time']

                log_info(f"处理主视频段 {main_idx+1}/{main_count} (时长: {main_duration:.3f}秒, {main_start:.2f}-{main_end:.2f})")

                # 提取主视频段特征（使用增强精度模式，标记为主视频以启用字幕鲁棒特征）
                main_features = self.extract_frame_features(
                    main_video_path, main_start, main_end,
                    num_frames=12, fast_mode=False, feature_types=feature_types,
                    enhanced_accuracy=True, is_main_video=True
                )

                if not main_features:
                    log_info(f"主视频段 {main_idx+1} 特征提取失败，跳过")
                    continue

                # 在辅助视频中寻找精确时长的最佳匹配（使用智能匹配策略）
                best_match = self._intelligent_duration_matching(
                    aux_video_path, main_features, main_duration,
                    adaptive_threshold, aux_total_duration, processed_aux_regions,
                    feature_types, feature_weights
                )

                if best_match:
                    # 验证时长精确性
                    matched_duration = best_match['end_time'] - best_match['start_time']
                    duration_error = abs(matched_duration - main_duration)

                    if duration_error > 0.1:  # 允许0.1秒的误差
                        log_warning(f"时长误差过大: {duration_error:.3f}秒，调整匹配区域")
                        # 调整匹配区域以确保精确时长
                        best_match['end_time'] = best_match['start_time'] + main_duration
                        if best_match['end_time'] > aux_total_duration:
                            best_match['start_time'] = aux_total_duration - main_duration
                            best_match['end_time'] = aux_total_duration

                    # 记录匹配结果
                    similar_matches.append({
                        'main_scene_index': main_idx,
                        'aux_scene_index': -1,  # 不使用场景索引，使用精确时间
                        'aux_start_time': best_match['start_time'],
                        'aux_end_time': best_match['end_time'],
                        'similarity': best_match['similarity'],
                        'main_duration': main_duration,
                        'main_start_time': main_start,
                        'main_end_time': main_end,
                        'duration_error': abs(best_match['end_time'] - best_match['start_time'] - main_duration),
                        'match_method': best_match.get('method', 'precise_duration')
                    })

                    # 记录已使用的辅助视频区域
                    processed_aux_regions.append({
                        'start_time': best_match['start_time'],
                        'end_time': best_match['end_time']
                    })

                    log_info(f"✓ 主视频段 {main_idx+1} 找到精确匹配: 相似度={best_match['similarity']:.3f}, "
                           f"辅助视频位置={best_match['start_time']:.3f}-{best_match['end_time']:.3f}秒, "
                           f"时长误差={similar_matches[-1]['duration_error']:.3f}秒")
                else:
                    log_info(f"✗ 主视频段 {main_idx+1} 未找到满足阈值的匹配")

                    # 自适应阈值调整：如果连续几个段都没找到匹配，降低阈值
                    if main_idx > 0 and len(similar_matches) < main_idx * 0.3:
                        adaptive_threshold = max(0.5, adaptive_threshold - 0.05)
                        log_info(f"自适应调整阈值: {similarity_threshold:.2f} -> {adaptive_threshold:.2f}")

                # 清理内存
                gc.collect()

            # 计算匹配统计信息
            if similar_matches:
                avg_similarity = sum(m['similarity'] for m in similar_matches) / len(similar_matches)
                max_similarity = max(m['similarity'] for m in similar_matches)
                min_similarity = min(m['similarity'] for m in similar_matches)
                avg_duration_error = sum(m['duration_error'] for m in similar_matches) / len(similar_matches)
                max_duration_error = max(m['duration_error'] for m in similar_matches)

                log_info(f"精确时长匹配完成: {len(similar_matches)}/{main_count} 个段找到匹配")
                log_info(f"匹配质量统计:")
                log_info(f"  - 相似度: 平均={avg_similarity:.3f}, 最高={max_similarity:.3f}, 最低={min_similarity:.3f}")
                log_info(f"  - 时长误差: 平均={avg_duration_error:.3f}秒, 最大={max_duration_error:.3f}秒")
                log_info(f"  - 匹配率: {len(similar_matches)}/{main_count} ({len(similar_matches)/main_count*100:.1f}%)")

                # 验证总时长一致性
                total_main_duration = sum(main_scene['end_time'] - main_scene['start_time'] for main_scene in main_video_scenes)
                total_matched_duration = sum(m['main_duration'] for m in similar_matches)
                total_aux_duration = sum(m['aux_end_time'] - m['aux_start_time'] for m in similar_matches)

                log_info(f"时长一致性验证:")
                log_info(f"  - 主视频总时长: {total_main_duration:.3f}秒")
                log_info(f"  - 匹配段总时长: {total_matched_duration:.3f}秒")
                log_info(f"  - 辅助视频段总时长: {total_aux_duration:.3f}秒")
                log_info(f"  - 时长一致性误差: {abs(total_matched_duration - total_aux_duration):.3f}秒")
            else:
                log_info("未找到任何匹配的视频段")

            return similar_matches

        except Exception as e:
            log_info(f"Error in _find_similar_scenes_precise_duration: {str(e)}")
            log_info(traceback.format_exc())
            raise

    def _find_precise_duration_match(self, aux_video_path, main_features, main_duration, similarity_threshold, aux_total_duration, processed_regions, feature_types, feature_weights):
        """
        在辅助视频中寻找精确时长的最佳匹配位置
        """
        try:
            import gc

            best_match = None
            best_similarity = 0.0

            log_info(f"搜索精确时长匹配: {main_duration:.3f}秒")

            # 方法1: 高精度滑动窗口搜索
            step_size = max(0.2, main_duration * 0.05)  # 更小的步长，提高精度
            search_positions = []

            # 生成搜索位置（避开已处理区域）
            current_pos = 0
            while current_pos + main_duration <= aux_total_duration:
                end_pos = current_pos + main_duration

                # 检查是否与已处理区域重叠
                if not self._is_region_overlapping(current_pos, end_pos, processed_regions):
                    search_positions.append(current_pos)

                current_pos += step_size

            # 智能限制搜索位置数量
            max_positions = min(100, len(search_positions))  # 增加搜索位置数量以提高精度
            if len(search_positions) > max_positions:
                # 均匀采样
                step = len(search_positions) / max_positions
                search_positions = [search_positions[int(i * step)] for i in range(max_positions)]

            log_info(f"精确搜索位置数量: {len(search_positions)}")

            # 分批处理以节省内存
            batch_size = 20
            total_batches = (len(search_positions) + batch_size - 1) // batch_size

            for batch_idx in range(total_batches):
                start_idx = batch_idx * batch_size
                end_idx = min((batch_idx + 1) * batch_size, len(search_positions))

                batch_positions = search_positions[start_idx:end_idx]

                for i, start_pos in enumerate(batch_positions):
                    end_pos = start_pos + main_duration

                    # 提取辅助视频段特征（使用增强精度模式，标记为辅助视频）
                    aux_features = self.extract_frame_features(
                        aux_video_path, start_pos, end_pos,
                        num_frames=12, fast_mode=False, feature_types=feature_types,
                        enhanced_accuracy=True, is_main_video=False
                    )

                    if aux_features:
                        # 使用增强的多维度相似度计算
                        similarity = self.calculate_similarity(
                            main_features, aux_features, feature_weights,
                            enhanced_accuracy=True
                        )

                        if similarity > best_similarity and similarity >= similarity_threshold:
                            best_similarity = similarity
                            best_match = {
                                'start_time': start_pos,
                                'end_time': end_pos,
                                'similarity': similarity,
                                'method': 'precise_sliding_window'
                            }

                # 每批清理内存
                gc.collect()

            # 方法2: 如果滑动窗口效果不好，尝试基于场景的精确匹配
            if best_similarity < similarity_threshold * 1.1:
                log_info("滑动窗口匹配质量不佳，尝试场景优化搜索")

                scene_match = self._find_scene_based_precise_match(
                    aux_video_path, main_features, main_duration,
                    similarity_threshold, processed_regions, feature_types, feature_weights
                )

                if scene_match and scene_match['similarity'] > best_similarity:
                    best_match = scene_match

            if best_match:
                log_info(f"找到精确匹配: 相似度={best_match['similarity']:.3f}, "
                       f"位置={best_match['start_time']:.3f}-{best_match['end_time']:.3f}秒, "
                       f"方法={best_match['method']}")
            else:
                log_info(f"未找到满足阈值 {similarity_threshold:.3f} 的精确匹配")

            return best_match

        except Exception as e:
            log_info(f"Error in _find_precise_duration_match: {str(e)}")
            return None

    def _find_scene_based_precise_match(self, aux_video_path, main_features, main_duration, similarity_threshold, processed_regions, feature_types, feature_weights):
        """
        基于场景的精确时长匹配
        """
        try:
            # 这里可以实现基于场景的优化搜索
            # 例如：在长场景中寻找最佳的精确时长片段
            # 暂时返回None，表示未实现
            return None
        except Exception as e:
            log_info(f"Error in _find_scene_based_precise_match: {str(e)}")
            return None

    def _intelligent_duration_matching(self, aux_video_path, main_features, main_duration, similarity_threshold, aux_total_duration, processed_regions, feature_types, feature_weights):
        """
        智能时长匹配策略：结合内容相似度和时长精确性的优化匹配
        """
        try:
            import gc

            best_match = None
            best_similarity = 0.0

            log_info(f"智能时长匹配: 目标时长 {main_duration:.3f}秒")

            # 策略1: 多尺度滑动窗口搜索
            # 使用不同的步长进行多轮搜索，从粗到细
            search_scales = [
                {'step_ratio': 0.1, 'name': '粗搜索'},    # 大步长快速定位
                {'step_ratio': 0.02, 'name': '精搜索'},   # 小步长精确定位
                {'step_ratio': 0.005, 'name': '微调搜索'} # 微步长最终优化
            ]

            candidate_matches = []

            for scale in search_scales:
                step_size = max(0.1, main_duration * scale['step_ratio'])
                log_info(f"执行{scale['name']}: 步长 {step_size:.3f}秒")

                # 生成搜索位置
                search_positions = []
                current_pos = 0

                while current_pos + main_duration <= aux_total_duration:
                    end_pos = current_pos + main_duration

                    # 检查是否与已处理区域重叠
                    if not self._is_region_overlapping(current_pos, end_pos, processed_regions):
                        search_positions.append(current_pos)

                    current_pos += step_size

                # 限制搜索位置数量
                max_positions = 50 if scale['step_ratio'] > 0.05 else 100
                if len(search_positions) > max_positions:
                    # 均匀采样
                    indices = np.linspace(0, len(search_positions)-1, max_positions, dtype=int)
                    search_positions = [search_positions[i] for i in indices]

                log_info(f"{scale['name']}位置数: {len(search_positions)}")

                # 搜索最佳匹配
                for start_pos in search_positions:
                    end_pos = start_pos + main_duration

                    # 提取特征并计算相似度
                    aux_features = self.extract_frame_features(
                        aux_video_path, start_pos, end_pos,
                        num_frames=8 if scale['step_ratio'] > 0.05 else 12,
                        fast_mode=scale['step_ratio'] > 0.05,
                        feature_types=feature_types,
                        enhanced_accuracy=True,
                        is_main_video=False
                    )

                    if aux_features:
                        similarity = self.calculate_similarity(
                            main_features, aux_features, feature_weights,
                            enhanced_accuracy=True
                        )

                        # 记录候选匹配
                        candidate_matches.append({
                            'start_time': start_pos,
                            'end_time': end_pos,
                            'similarity': similarity,
                            'scale': scale['name']
                        })

                        # 更新最佳匹配
                        if similarity > best_similarity and similarity >= similarity_threshold:
                            best_similarity = similarity
                            best_match = {
                                'start_time': start_pos,
                                'end_time': end_pos,
                                'similarity': similarity,
                                'method': f'intelligent_{scale["name"]}'
                            }

                # 内存清理
                gc.collect()

                # 如果粗搜索已经找到很好的匹配，可以提前结束
                if best_similarity > similarity_threshold * 1.2:
                    log_info(f"在{scale['name']}阶段找到高质量匹配，提前结束")
                    break

            # 策略2: 如果没有找到满意的匹配，尝试时长容忍匹配
            if best_similarity < similarity_threshold:
                log_info("尝试时长容忍匹配策略")

                # 允许±10%的时长差异
                tolerance_ratios = [0.95, 1.05, 0.9, 1.1]

                for ratio in tolerance_ratios:
                    search_duration = main_duration * ratio

                    # 快速搜索
                    step_size = max(0.5, search_duration * 0.1)
                    current_pos = 0

                    while current_pos + search_duration <= aux_total_duration:
                        end_pos = current_pos + search_duration

                        if not self._is_region_overlapping(current_pos, end_pos, processed_regions):
                            aux_features = self.extract_frame_features(
                                aux_video_path, current_pos, end_pos,
                                num_frames=6, fast_mode=True,
                                feature_types=feature_types,
                                enhanced_accuracy=False,
                                is_main_video=False
                            )

                            if aux_features:
                                similarity = self.calculate_similarity(
                                    main_features, aux_features, feature_weights,
                                    enhanced_accuracy=False
                                )

                                # 应用时长惩罚
                                duration_penalty = abs(ratio - 1.0) * 0.1
                                adjusted_similarity = similarity * (1.0 - duration_penalty)

                                if adjusted_similarity > best_similarity:
                                    best_similarity = adjusted_similarity
                                    best_match = {
                                        'start_time': current_pos,
                                        'end_time': current_pos + main_duration,  # 仍然使用原始时长
                                        'similarity': adjusted_similarity,
                                        'method': f'duration_tolerant_{ratio:.2f}'
                                    }

                        current_pos += step_size

                    # 如果找到可接受的匹配就停止
                    if best_similarity >= similarity_threshold * 0.8:
                        break

            # 策略3: 候选匹配优化
            if candidate_matches and best_match:
                # 分析候选匹配的分布，寻找可能的更好匹配
                candidate_matches.sort(key=lambda x: x['similarity'], reverse=True)
                top_candidates = candidate_matches[:5]

                log_info(f"分析前5个候选匹配:")
                for i, candidate in enumerate(top_candidates):
                    log_info(f"  候选{i+1}: 相似度={candidate['similarity']:.3f}, "
                           f"位置={candidate['start_time']:.2f}-{candidate['end_time']:.2f}, "
                           f"来源={candidate['scale']}")

            if best_match:
                log_info(f"智能时长匹配完成: 相似度={best_match['similarity']:.3f}, "
                       f"位置={best_match['start_time']:.3f}-{best_match['end_time']:.3f}秒, "
                       f"方法={best_match['method']}")
            else:
                log_info(f"智能时长匹配未找到满足阈值 {similarity_threshold:.3f} 的匹配")

            return best_match

        except Exception as e:
            log_error(f"智能时长匹配失败: {e}")
            return None

    def get_adaptive_similarity_threshold(self, main_video_scenes, aux_video_scenes, main_video_path, aux_video_path, base_threshold=0.8, sample_size=5):
        """
        增强的智能自适应阈值调整：多维度分析，更精确的阈值设定
        """
        try:
            log_info("开始增强智能阈值分析...")

            # 1. 视频内容复杂度分析
            content_complexity = self._analyze_video_complexity(main_video_path, aux_video_path)

            # 2. 多样本相似度分析
            similarity_stats = self._analyze_similarity_distribution(
                main_video_scenes, aux_video_scenes, main_video_path, aux_video_path, sample_size
            )

            if not similarity_stats:
                log_warning("相似度分析失败，使用基础阈值")
                return base_threshold

            # 3. 智能阈值计算
            adaptive_threshold = self._calculate_adaptive_threshold(
                similarity_stats, content_complexity, base_threshold
            )

            log_info(f"自适应阈值分析完成: {base_threshold:.3f} -> {adaptive_threshold:.3f}")
            return adaptive_threshold

        except Exception as e:
            log_warning(f"智能阈值调整失败: {e}")
            return base_threshold

    def _analyze_video_complexity(self, main_video_path, aux_video_path):
        """
        分析视频内容复杂度
        """
        try:
            complexity_scores = []

            for video_path in [main_video_path, aux_video_path]:
                if not os.path.exists(video_path):
                    continue

                cap = cv2.VideoCapture(video_path)
                if not cap.isOpened():
                    continue

                # 采样几帧分析复杂度
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                sample_frames = np.linspace(0, frame_count-1, min(10, frame_count), dtype=int)

                frame_complexities = []
                for frame_idx in sample_frames:
                    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                    ret, frame = cap.read()
                    if ret:
                        complexity = self._calculate_frame_complexity(frame)
                        frame_complexities.append(complexity)

                cap.release()

                if frame_complexities:
                    complexity_scores.append(np.mean(frame_complexities))

            if complexity_scores:
                avg_complexity = np.mean(complexity_scores)
                log_info(f"视频内容复杂度: {avg_complexity:.3f}")
                return avg_complexity

            return 0.5  # 默认中等复杂度

        except Exception as e:
            log_warning(f"视频复杂度分析失败: {e}")
            return 0.5

    def _calculate_frame_complexity(self, frame):
        """
        计算单帧的复杂度
        """
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            # 1. 边缘密度
            edges = cv2.Canny(gray, 50, 150)
            edge_density = np.sum(edges > 0) / edges.size

            # 2. 纹理复杂度
            texture_score = np.var(gray) / 10000  # 归一化

            # 3. 颜色丰富度
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            unique_colors = len(np.unique(hsv.reshape(-1, hsv.shape[-1]), axis=0))
            color_richness = min(1.0, unique_colors / 10000)

            # 综合复杂度
            complexity = 0.4 * edge_density + 0.3 * texture_score + 0.3 * color_richness
            return min(1.0, complexity)

        except Exception as e:
            log_warning(f"帧复杂度计算失败: {e}")
            return 0.5

    def _analyze_similarity_distribution(self, main_video_scenes, aux_video_scenes, main_video_path, aux_video_path, sample_size):
        """
        分析相似度分布特征
        """
        try:
            import random

            # 智能采样：选择不同时长和位置的场景
            sample_indices = self._smart_scene_sampling(main_video_scenes, sample_size)

            similarities = []
            high_quality_similarities = []  # 高质量匹配的相似度

            for main_idx in sample_indices:
                main_scene = main_video_scenes[main_idx]
                main_duration = main_scene['end_time'] - main_scene['start_time']

                # 提取主视频段特征（使用增强模式）
                main_features = self.extract_frame_features(
                    main_video_path, main_scene['start_time'], main_scene['end_time'],
                    num_frames=8, fast_mode=False, enhanced_accuracy=True, is_main_video=True
                )

                if not main_features:
                    continue

                # 在辅助视频中进行更智能的采样
                aux_positions = self._generate_smart_aux_positions(aux_video_path, main_duration, 15)

                scene_similarities = []
                for start_pos in aux_positions:
                    end_pos = start_pos + main_duration

                    aux_features = self.extract_frame_features(
                        aux_video_path, start_pos, end_pos,
                        num_frames=8, fast_mode=False, enhanced_accuracy=True, is_main_video=False
                    )

                    if aux_features:
                        similarity = self.calculate_similarity(
                            main_features, aux_features, enhanced_accuracy=True
                        )
                        similarities.append(similarity)
                        scene_similarities.append(similarity)

                # 记录该场景的最佳匹配
                if scene_similarities:
                    best_similarity = max(scene_similarities)
                    if best_similarity > 0.6:  # 只考虑相对较好的匹配
                        high_quality_similarities.append(best_similarity)

            if not similarities:
                return None

            # 计算统计特征
            stats = {
                'mean': np.mean(similarities),
                'median': np.median(similarities),
                'std': np.std(similarities),
                'max': np.max(similarities),
                'min': np.min(similarities),
                'q75': np.percentile(similarities, 75),
                'q25': np.percentile(similarities, 25),
                'high_quality_mean': np.mean(high_quality_similarities) if high_quality_similarities else 0,
                'high_quality_count': len(high_quality_similarities),
                'total_count': len(similarities)
            }

            log_info(f"相似度分布分析: 均值={stats['mean']:.3f}, 中位数={stats['median']:.3f}, "
                    f"标准差={stats['std']:.3f}, 最大值={stats['max']:.3f}")

            return stats

        except Exception as e:
            log_warning(f"相似度分布分析失败: {e}")
            return None

    def _smart_scene_sampling(self, scenes, sample_size):
        """
        智能场景采样：选择具有代表性的场景
        """
        try:
            if len(scenes) <= sample_size:
                return list(range(len(scenes)))

            # 按时长分组
            durations = [(i, scene['end_time'] - scene['start_time']) for i, scene in enumerate(scenes)]
            durations.sort(key=lambda x: x[1])

            # 分层采样：短、中、长时长各选一些
            short_scenes = [idx for idx, dur in durations if dur < 10]
            medium_scenes = [idx for idx, dur in durations if 10 <= dur < 60]
            long_scenes = [idx for idx, dur in durations if dur >= 60]

            selected = []
            remaining = sample_size

            # 优先选择中等时长的场景
            if medium_scenes and remaining > 0:
                count = min(remaining // 2, len(medium_scenes))
                selected.extend(random.sample(medium_scenes, count))
                remaining -= count

            # 选择短时长场景
            if short_scenes and remaining > 0:
                count = min(remaining // 2, len(short_scenes))
                selected.extend(random.sample(short_scenes, count))
                remaining -= count

            # 选择长时长场景
            if long_scenes and remaining > 0:
                count = min(remaining, len(long_scenes))
                selected.extend(random.sample(long_scenes, count))

            # 如果还不够，随机补充
            if len(selected) < sample_size:
                all_indices = set(range(len(scenes)))
                available = list(all_indices - set(selected))
                if available:
                    additional = min(sample_size - len(selected), len(available))
                    selected.extend(random.sample(available, additional))

            return selected[:sample_size]

        except Exception as e:
            log_warning(f"智能场景采样失败: {e}")
            return random.sample(range(len(scenes)), min(sample_size, len(scenes)))

    def _generate_smart_aux_positions(self, aux_video_path, target_duration, num_positions):
        """
        在辅助视频中生成智能采样位置
        """
        try:
            # 获取辅助视频时长
            try:
                from moviepy.editor import VideoFileClip
                aux_clip = VideoFileClip(aux_video_path)
                aux_duration = aux_clip.duration
                aux_clip.close()
            except Exception as e:
                log_warning(f"无法获取辅助视频时长: {e}")
                aux_duration = 3600  # 默认1小时

            if aux_duration <= target_duration:
                return [0]

            # 生成分布均匀的采样位置
            max_start = aux_duration - target_duration
            positions = []

            # 1. 均匀分布的位置
            uniform_positions = np.linspace(0, max_start, num_positions // 2)
            positions.extend(uniform_positions)

            # 2. 随机位置（增加多样性）
            remaining = num_positions - len(positions)
            if remaining > 0:
                random_positions = [random.uniform(0, max_start) for _ in range(remaining)]
                positions.extend(random_positions)

            return sorted(positions)

        except Exception as e:
            log_warning(f"智能辅助位置生成失败: {e}")
            return [0]

    def _calculate_adaptive_threshold(self, similarity_stats, content_complexity, base_threshold):
        """
        基于统计分析计算自适应阈值
        """
        try:
            mean_sim = similarity_stats['mean']
            max_sim = similarity_stats['max']
            std_sim = similarity_stats['std']
            high_quality_ratio = similarity_stats['high_quality_count'] / similarity_stats['total_count']

            # 基础调整因子
            adjustment_factor = 1.0

            # 1. 根据最大相似度调整
            if max_sim < base_threshold * 0.8:
                # 最大相似度很低，大幅降低阈值
                adjustment_factor *= 0.7
                log_info("检测到低相似度内容，大幅降低阈值")
            elif max_sim < base_threshold:
                # 最大相似度略低，适度降低阈值
                adjustment_factor *= 0.85
                log_info("检测到中低相似度内容，适度降低阈值")
            elif mean_sim > base_threshold * 1.1:
                # 平均相似度很高，可以提高阈值
                adjustment_factor *= 1.1
                log_info("检测到高相似度内容，适度提高阈值")

            # 2. 根据标准差调整（一致性）
            if std_sim < 0.1:
                # 相似度很一致，可以稍微提高阈值
                adjustment_factor *= 1.05
            elif std_sim > 0.3:
                # 相似度差异很大，降低阈值以包含更多匹配
                adjustment_factor *= 0.9

            # 3. 根据内容复杂度调整
            if content_complexity > 0.7:
                # 内容复杂，降低阈值
                adjustment_factor *= 0.95
            elif content_complexity < 0.3:
                # 内容简单，可以提高阈值
                adjustment_factor *= 1.05

            # 4. 根据高质量匹配比例调整
            if high_quality_ratio > 0.5:
                # 高质量匹配较多，可以提高阈值
                adjustment_factor *= 1.05
            elif high_quality_ratio < 0.2:
                # 高质量匹配很少，降低阈值
                adjustment_factor *= 0.9

            # 计算最终阈值
            adaptive_threshold = base_threshold * adjustment_factor

            # 限制阈值范围
            adaptive_threshold = max(0.4, min(0.95, adaptive_threshold))

            log_info(f"阈值调整因子: {adjustment_factor:.3f}, 内容复杂度: {content_complexity:.3f}, "
                    f"高质量比例: {high_quality_ratio:.3f}")

            return adaptive_threshold

        except Exception as e:
            log_warning(f"自适应阈值计算失败: {e}")
            return base_threshold

    def analyze_matching_quality(self, matches, main_video_scenes):
        """
        增强的匹配质量分析：多维度评估和智能建议
        """
        try:
            if not matches:
                return {
                    'quality_score': 0.0,
                    'match_rate': 0.0,
                    'avg_similarity': 0.0,
                    'recommendations': ['未找到任何匹配，建议降低相似度阈值或检查视频内容相关性'],
                    'detailed_analysis': {
                        'similarity_distribution': [],
                        'duration_accuracy': 0.0,
                        'temporal_consistency': 0.0
                    }
                }

            # 详细质量分析
            quality_analysis = self._perform_detailed_quality_analysis(matches, main_video_scenes)

            # 生成智能建议
            recommendations = self._generate_intelligent_recommendations(quality_analysis)

            return {
                'quality_score': quality_analysis['overall_score'],
                'match_rate': quality_analysis['match_rate'],
                'avg_similarity': quality_analysis['avg_similarity'],
                'recommendations': recommendations,
                'detailed_analysis': quality_analysis
            }

        except Exception as e:
            log_warning(f"匹配质量分析失败: {e}")
            return {
                'quality_score': 0.5,
                'match_rate': 0.0,
                'avg_similarity': 0.0,
                'recommendations': ['质量分析失败，建议检查匹配结果'],
                'detailed_analysis': {}
            }

    def _perform_detailed_quality_analysis(self, matches, main_video_scenes):
        """
        执行详细的质量分析
        """
        try:
            similarities = [m['similarity'] for m in matches]
            duration_errors = [m.get('duration_error', 0) for m in matches]

            # 基础统计
            avg_similarity = np.mean(similarities)
            std_similarity = np.std(similarities)
            min_similarity = np.min(similarities)
            max_similarity = np.max(similarities)

            match_rate = len(matches) / len(main_video_scenes)

            # 相似度分布分析
            similarity_distribution = {
                'excellent': sum(1 for s in similarities if s >= 0.9),
                'good': sum(1 for s in similarities if 0.8 <= s < 0.9),
                'fair': sum(1 for s in similarities if 0.7 <= s < 0.8),
                'poor': sum(1 for s in similarities if s < 0.7)
            }

            # 时长精度分析
            duration_accuracy = 1.0 - (np.mean(duration_errors) if duration_errors else 0)
            duration_consistency = 1.0 - (np.std(duration_errors) if len(duration_errors) > 1 else 0)

            # 时间一致性分析（检查匹配的时间顺序是否合理）
            temporal_consistency = self._analyze_temporal_consistency(matches)

            # 覆盖率分析
            coverage_analysis = self._analyze_coverage_quality(matches, main_video_scenes)

            # 计算综合质量分数
            similarity_score = avg_similarity
            consistency_score = 1.0 - min(0.5, std_similarity * 2)  # 一致性分数
            coverage_score = match_rate
            duration_score = duration_accuracy
            temporal_score = temporal_consistency

            overall_score = (
                similarity_score * 0.35 +
                consistency_score * 0.20 +
                coverage_score * 0.20 +
                duration_score * 0.15 +
                temporal_score * 0.10
            )

            return {
                'overall_score': overall_score,
                'match_rate': match_rate,
                'avg_similarity': avg_similarity,
                'std_similarity': std_similarity,
                'min_similarity': min_similarity,
                'max_similarity': max_similarity,
                'similarity_distribution': similarity_distribution,
                'duration_accuracy': duration_accuracy,
                'duration_consistency': duration_consistency,
                'temporal_consistency': temporal_consistency,
                'coverage_analysis': coverage_analysis,
                'component_scores': {
                    'similarity': similarity_score,
                    'consistency': consistency_score,
                    'coverage': coverage_score,
                    'duration': duration_score,
                    'temporal': temporal_score
                }
            }

        except Exception as e:
            log_warning(f"详细质量分析失败: {e}")
            return {'overall_score': 0.5}

    def _analyze_temporal_consistency(self, matches):
        """
        分析时间一致性：检查匹配的时间顺序是否合理
        """
        try:
            if len(matches) < 2:
                return 1.0  # 单个匹配无法分析时间一致性

            # 按主视频时间排序
            sorted_matches = sorted(matches, key=lambda m: m.get('main_start_time', 0))

            # 检查辅助视频的时间顺序
            aux_times = [m.get('start_time', 0) for m in sorted_matches]

            # 计算时间顺序的一致性
            consistent_pairs = 0
            total_pairs = len(aux_times) - 1

            for i in range(total_pairs):
                if aux_times[i+1] >= aux_times[i]:  # 时间递增是合理的
                    consistent_pairs += 1

            consistency = consistent_pairs / total_pairs if total_pairs > 0 else 1.0
            return consistency

        except Exception as e:
            log_warning(f"时间一致性分析失败: {e}")
            return 0.5

    def _analyze_coverage_quality(self, matches, main_video_scenes):
        """
        分析覆盖质量：检查匹配的分布是否均匀
        """
        try:
            total_scenes = len(main_video_scenes)
            matched_scenes = len(matches)

            if total_scenes == 0:
                return {'coverage_rate': 0.0, 'distribution_quality': 0.0}

            coverage_rate = matched_scenes / total_scenes

            # 分析匹配分布的均匀性
            if matched_scenes < 2:
                distribution_quality = 1.0 if matched_scenes == 1 else 0.0
            else:
                # 检查匹配在时间轴上的分布
                main_times = [m.get('main_start_time', 0) for m in matches]
                main_times.sort()

                # 计算间隔的标准差（越小越均匀）
                intervals = [main_times[i+1] - main_times[i] for i in range(len(main_times)-1)]
                if intervals:
                    interval_std = np.std(intervals)
                    mean_interval = np.mean(intervals)
                    distribution_quality = max(0.0, 1.0 - (interval_std / (mean_interval + 1e-6)))
                else:
                    distribution_quality = 1.0

            return {
                'coverage_rate': coverage_rate,
                'distribution_quality': distribution_quality
            }

        except Exception as e:
            log_warning(f"覆盖质量分析失败: {e}")
            return {'coverage_rate': 0.0, 'distribution_quality': 0.0}

    def _generate_intelligent_recommendations(self, quality_analysis):
        """
        基于质量分析生成智能建议
        """
        try:
            recommendations = []

            overall_score = quality_analysis.get('overall_score', 0)
            match_rate = quality_analysis.get('match_rate', 0)
            avg_similarity = quality_analysis.get('avg_similarity', 0)
            std_similarity = quality_analysis.get('std_similarity', 0)
            min_similarity = quality_analysis.get('min_similarity', 0)

            similarity_dist = quality_analysis.get('similarity_distribution', {})
            duration_accuracy = quality_analysis.get('duration_accuracy', 0)
            temporal_consistency = quality_analysis.get('temporal_consistency', 0)
            coverage_analysis = quality_analysis.get('coverage_analysis', {})

            # 1. 整体质量评估
            if overall_score >= 0.8:
                recommendations.append("✅ 匹配质量优秀，无需特别优化")
            elif overall_score >= 0.6:
                recommendations.append("⚠️ 匹配质量良好，可考虑微调参数以进一步提升")
            else:
                recommendations.append("❌ 匹配质量较差，需要重点优化")

            # 2. 匹配率分析
            if match_rate < 0.3:
                recommendations.append("🔍 匹配率过低(<30%)，建议：")
                recommendations.append("   - 降低相似度阈值(建议0.6-0.7)")
                recommendations.append("   - 增加特征类型(如color_hist, edge)")
                recommendations.append("   - 检查视频内容相关性")
            elif match_rate < 0.6:
                recommendations.append("📊 匹配率偏低(<60%)，建议适度降低阈值或优化特征权重")

            # 3. 相似度分析
            if avg_similarity < 0.6:
                recommendations.append("📉 平均相似度过低，建议：")
                recommendations.append("   - 检查主视频和辅助视频的内容相关性")
                recommendations.append("   - 尝试不同的特征组合")
                recommendations.append("   - 考虑预处理视频(如去噪、增强)")

            if std_similarity > 0.2:
                recommendations.append("📈 相似度差异较大，建议：")
                recommendations.append("   - 使用更稳定的特征类型")
                recommendations.append("   - 增加帧采样数量")
                recommendations.append("   - 启用增强精度模式")

            # 4. 相似度分布分析
            poor_matches = similarity_dist.get('poor', 0)
            total_matches = sum(similarity_dist.values())

            if total_matches > 0 and poor_matches / total_matches > 0.3:
                recommendations.append("⚠️ 低质量匹配过多(>30%)，建议：")
                recommendations.append("   - 提高相似度阈值")
                recommendations.append("   - 改进特征提取算法")
                recommendations.append("   - 使用更严格的验证机制")

            # 5. 时长精度分析
            if duration_accuracy < 0.8:
                recommendations.append("⏱️ 时长精度较低，建议：")
                recommendations.append("   - 检查视频帧率设置")
                recommendations.append("   - 优化时长匹配算法")
                recommendations.append("   - 验证场景分割的准确性")

            # 6. 时间一致性分析
            if temporal_consistency < 0.7:
                recommendations.append("🔄 时间一致性较差，建议：")
                recommendations.append("   - 检查场景检测算法")
                recommendations.append("   - 优化匹配排序逻辑")
                recommendations.append("   - 考虑添加时间约束")

            # 7. 覆盖质量分析
            coverage_rate = coverage_analysis.get('coverage_rate', 0)
            distribution_quality = coverage_analysis.get('distribution_quality', 0)

            if coverage_rate < 0.5:
                recommendations.append("📍 覆盖率较低，建议增加搜索范围或降低阈值")

            if distribution_quality < 0.6:
                recommendations.append("📐 匹配分布不均匀，建议优化采样策略")

            # 8. 特定优化建议
            if min_similarity < 0.5:
                recommendations.append("🎯 存在极低质量匹配，建议启用质量过滤")

            # 9. 参数调优建议
            if overall_score < 0.7:
                recommendations.append("🔧 参数调优建议：")
                if avg_similarity < 0.7:
                    recommendations.append("   - 尝试降低阈值到0.6-0.7")
                if std_similarity > 0.15:
                    recommendations.append("   - 启用增强精度模式")
                    recommendations.append("   - 增加特征权重中phash和color_hist的比例")
                if match_rate < 0.5:
                    recommendations.append("   - 扩大搜索窗口大小")
                    recommendations.append("   - 减少搜索步长")

            # 如果没有具体问题，给出优化建议
            if len(recommendations) <= 1:
                recommendations.append("💡 进一步优化建议：")
                recommendations.append("   - 尝试不同的特征权重组合")
                recommendations.append("   - 使用自适应阈值调整")
                recommendations.append("   - 启用多尺度搜索")

            return recommendations

        except Exception as e:
            log_warning(f"智能建议生成失败: {e}")
            return ["建议生成失败，请手动检查匹配结果"]

    def validate_and_optimize_matches(self, matches, main_video_scenes, main_video_path, aux_video_path):
        """
        验证和优化匹配结果
        """
        try:
            if not matches:
                return matches

            log_info("开始验证和优化匹配结果...")

            # 1. 质量分析
            quality_analysis = self.analyze_matching_quality(matches, main_video_scenes)
            log_info(f"匹配质量分数: {quality_analysis['quality_score']:.3f}")

            # 2. 过滤低质量匹配
            filtered_matches = self._filter_low_quality_matches(matches, quality_analysis)

            # 3. 优化匹配顺序
            optimized_matches = self._optimize_match_order(filtered_matches)

            # 4. 验证时间一致性
            validated_matches = self._validate_temporal_consistency(optimized_matches)

            log_info(f"匹配优化完成: {len(matches)} -> {len(validated_matches)}")

            return validated_matches

        except Exception as e:
            log_warning(f"匹配验证和优化失败: {e}")
            return matches

    def _filter_low_quality_matches(self, matches, quality_analysis):
        """
        过滤低质量匹配
        """
        try:
            if not matches:
                return matches

            avg_similarity = quality_analysis.get('avg_similarity', 0.7)
            std_similarity = quality_analysis.get('std_similarity', 0.1)

            # 动态阈值：基于平均值和标准差
            min_threshold = max(0.5, avg_similarity - 2 * std_similarity)

            filtered = []
            for match in matches:
                similarity = match.get('similarity', 0)
                if similarity >= min_threshold:
                    filtered.append(match)
                else:
                    log_debug(f"过滤低质量匹配: 相似度={similarity:.3f} < {min_threshold:.3f}")

            return filtered

        except Exception as e:
            log_warning(f"低质量匹配过滤失败: {e}")
            return matches

    def _optimize_match_order(self, matches):
        """
        优化匹配顺序
        """
        try:
            if len(matches) <= 1:
                return matches

            # 按主视频时间排序
            return sorted(matches, key=lambda m: m.get('main_start_time', 0))

        except Exception as e:
            log_warning(f"匹配顺序优化失败: {e}")
            return matches

    def _validate_temporal_consistency(self, matches):
        """
        验证和修复时间一致性问题
        """
        try:
            if len(matches) <= 1:
                return matches

            # 检查是否存在时间倒序的问题
            validated = []
            prev_aux_time = -1

            for match in matches:
                aux_time = match.get('start_time', 0)

                # 如果时间倒序，可能需要调整或跳过
                if aux_time >= prev_aux_time:
                    validated.append(match)
                    prev_aux_time = aux_time
                else:
                    # 时间倒序，记录警告但仍保留（可能是合理的）
                    log_debug(f"检测到时间倒序匹配: {aux_time} < {prev_aux_time}")
                    validated.append(match)

            return validated

        except Exception as e:
            log_warning(f"时间一致性验证失败: {e}")
            return matches

            return {
                'quality_score': quality_score,
                'avg_similarity': avg_similarity,
                'min_similarity': min_similarity,
                'max_duration_error': max_duration_error,
                'avg_duration_error': avg_duration_error,
                'match_rate': match_rate,
                'recommendations': recommendations
            }

        except Exception as e:
            log_warning(f"匹配质量分析失败: {e}")
            return {
                'quality_score': 0.5,
                'recommendations': ['质量分析失败，建议手动检查匹配结果']
            }
            similar_matches = []
            
            # 预先提取主视频的所有特征，因为主视频通常较短
            log_info("预先提取主视频特征...")
            main_features_dict = {}
            for i, main_scene in enumerate(main_video_scenes):
                main_features = self.extract_frame_features(main_video_path, main_scene['start_time'], main_scene['end_time'])
                if main_features:
                    main_features_dict[i] = main_features
                    
            log_info(f"成功提取 {len(main_features_dict)} 个主视频特征")
            
            # 对于长辅助视频，分批处理以节省内存
            batch_size = 50  # 每批处理的辅助视频段数量
            total_batches = (len(aux_video_scenes) + batch_size - 1) // batch_size
            
            for batch_idx in range(total_batches):
                start_idx = batch_idx * batch_size
                end_idx = min((batch_idx + 1) * batch_size, len(aux_video_scenes))
                
                log_info(f"处理辅助视频批次 {batch_idx + 1}/{total_batches}，场景 {start_idx} 到 {end_idx-1}")
                
                # 为当前批次创建辅助视频特征字典
                aux_features_dict = {}
                for j in range(start_idx, end_idx):
                    aux_scene = aux_video_scenes[j]
                    aux_features = self.extract_frame_features(aux_video_path, aux_scene['start_time'], aux_scene['end_time'])
                    if aux_features:
                        aux_features_dict[j] = aux_features
                
                # 计算当前批次的相似度
                for i, main_features in main_features_dict.items():
                    best_match_similarity = 0.0
                    best_match_aux_index = -1
                    
                    for j, aux_features in aux_features_dict.items():
                        similarity = self.calculate_similarity(main_features, aux_features)
                        if similarity > best_match_similarity:
                            best_match_similarity = similarity
                            best_match_aux_index = j
                    
                    if best_match_aux_index != -1 and best_match_similarity >= similarity_threshold:
                        # 检查是否已经有更好的匹配
                        existing_match = next((m for m in similar_matches if m['main_scene_index'] == i), None)
                        
                        if existing_match:
                            # 如果新匹配的相似度更高，则替换
                            if best_match_similarity > existing_match['similarity']:
                                existing_match['aux_scene_index'] = best_match_aux_index
                                existing_match['similarity'] = best_match_similarity
                        else:
                            # 添加新匹配
                            similar_matches.append({
                                'main_scene_index': i,
                                'aux_scene_index': best_match_aux_index,
                                'similarity': best_match_similarity
                            })
                
                # 清理当前批次的辅助视频特征，释放内存
                aux_features_dict.clear()
                gc.collect()
            
            # 按相似度从高到低排序
            similar_matches.sort(key=lambda x: x['similarity'], reverse=True)

            # 分析相似度分布，提供智能建议
            if similar_matches:
                max_similarity = similar_matches[0]['similarity']
                avg_similarity = sum(match['similarity'] for match in similar_matches) / len(similar_matches)
                log_info(f"相似度分析: 最高={max_similarity:.3f}, 平均={avg_similarity:.3f}, 阈值={similarity_threshold:.3f}")
            else:
                # 如果没有找到匹配，分析所有相似度来提供建议
                all_similarities = []
                # 使用已提取的特征进行分析
                main_indices = list(main_features_dict.keys())[:10]  # 只分析前10个场景以节省时间

                # 重新提取少量辅助视频特征用于分析
                aux_features_sample = {}
                for aux_idx in range(min(len(aux_video_scenes), 10)):
                    aux_scene = aux_video_scenes[aux_idx]
                    aux_features = self.extract_frame_features(aux_video_path, aux_scene['start_time'], aux_scene['end_time'])
                    if aux_features:
                        aux_features_sample[aux_idx] = aux_features

                for main_idx in main_indices:
                    for aux_idx, aux_features in aux_features_sample.items():
                        if main_idx in main_features_dict and aux_features:
                            similarity = self.calculate_similarity(main_features_dict[main_idx], aux_features)
                            all_similarities.append(similarity)

                if all_similarities:
                    max_found = max(all_similarities)
                    avg_found = sum(all_similarities) / len(all_similarities)
                    log_info(f"相似度分析: 最高可能={max_found:.3f}, 平均={avg_found:.3f}, 当前阈值={similarity_threshold:.3f}")

                    # 提供阈值建议
                    if max_found < similarity_threshold:
                        suggested_threshold = max(0.5, max_found * 0.9)
                        log_info(f"建议降低相似度阈值到 {suggested_threshold:.2f} 以获得更多匹配")

            log_info(f"找到 {len(similar_matches)} 对相似场景")
            return similar_matches

        except Exception as e:
            log_info(f"Error in find_similar_scenes: {str(e)}")
            log_info(traceback.format_exc())
            raise

    def _find_similar_scenes_segment_based(self, main_video_scenes, aux_video_scenes, main_video_path, aux_video_path, similarity_threshold):
        """
        优化的段对段匹配策略：
        1. 先将主视频切割成不同时长的视频段
        2. 从第一个视频段开始，逐个在辅助视频中寻找相似的视频画面
        3. 根据主视频段时长精确切割辅助视频进行替换
        4. 没有找到相似段的视频段保持不变
        """
        try:
            import gc

            main_count = len(main_video_scenes)
            aux_count = len(aux_video_scenes)

            log_info(f"开始优化的段对段匹配: 主视频{main_count}个段, 辅助视频{aux_count}个段")
            log_info("策略: 按顺序逐个匹配，确保时长一致，未匹配段保持原样")

            # 强制垃圾回收
            gc.collect()

            similar_matches = []
            processed_aux_regions = []  # 记录已使用的辅助视频区域，避免重复使用

            # 按顺序处理每个主视频段
            for main_idx, main_scene in enumerate(main_video_scenes):
                main_duration = main_scene['end_time'] - main_scene['start_time']

                log_info(f"处理主视频段 {main_idx+1}/{main_count} (时长: {main_duration:.2f}秒)")

                # 提取主视频段特征
                main_features = self.extract_frame_features(
                    main_video_path,
                    main_scene['start_time'],
                    main_scene['end_time'],
                    num_frames=8  # 使用更多帧提高匹配精度
                )

                if not main_features:
                    log_info(f"主视频段 {main_idx+1} 特征提取失败，跳过")
                    continue

                # 在辅助视频中寻找最佳匹配位置
                best_match = self._find_best_aux_position_sequential(
                    aux_video_path, main_features, main_duration,
                    similarity_threshold, aux_video_scenes, processed_aux_regions
                )

                if best_match:
                    # 记录匹配结果
                    similar_matches.append({
                        'main_scene_index': main_idx,
                        'aux_scene_index': -1,  # 不使用场景索引，使用精确时间
                        'aux_start_time': best_match['start_time'],
                        'aux_end_time': best_match['end_time'],
                        'similarity': best_match['similarity'],
                        'main_duration': main_duration,
                        'main_start_time': main_scene['start_time'],
                        'main_end_time': main_scene['end_time']
                    })

                    # 记录已使用的辅助视频区域
                    processed_aux_regions.append({
                        'start_time': best_match['start_time'],
                        'end_time': best_match['end_time']
                    })

                    log_info(f"✓ 主视频段 {main_idx+1} 找到匹配: 相似度={best_match['similarity']:.3f}, "
                           f"辅助视频位置={best_match['start_time']:.2f}-{best_match['end_time']:.2f}秒")
                else:
                    log_info(f"✗ 主视频段 {main_idx+1} 未找到满足阈值的匹配，将保持原样")

                # 清理内存
                gc.collect()

            log_info(f"段对段匹配完成: {len(similar_matches)}/{main_count} 个段找到匹配")

            # 输出详细匹配统计
            if similar_matches:
                avg_similarity = sum(m['similarity'] for m in similar_matches) / len(similar_matches)
                max_similarity = max(m['similarity'] for m in similar_matches)
                min_similarity = min(m['similarity'] for m in similar_matches)

                log_info(f"匹配质量统计:")
                log_info(f"  - 最高相似度: {max_similarity:.3f}")
                log_info(f"  - 最低相似度: {min_similarity:.3f}")
                log_info(f"  - 平均相似度: {avg_similarity:.3f}")
                log_info(f"  - 匹配率: {len(similar_matches)}/{main_count} ({len(similar_matches)/main_count*100:.1f}%)")

                # 验证时长一致性
                total_main_duration = sum(m['main_duration'] for m in similar_matches)
                total_aux_duration = sum(m['aux_end_time'] - m['aux_start_time'] for m in similar_matches)
                log_info(f"时长验证: 主视频段总时长={total_main_duration:.2f}秒, 辅助视频段总时长={total_aux_duration:.2f}秒")
            else:
                log_info("未找到任何匹配的视频段")

            return similar_matches

        except Exception as e:
            log_info(f"Error in _find_similar_scenes_segment_based: {str(e)}")
            log_info(traceback.format_exc())
            raise

    def _find_similar_scenes_ultra_optimized(self, main_video_scenes, aux_video_scenes, main_video_path, aux_video_path, similarity_threshold):
        """
        超大视频的高度优化相似度匹配策略
        - 智能采样：只处理关键场景
        - 快速特征：使用更简单的特征提取
        - 限制匹配：确保匹配数量不超过主视频场景数
        """
        try:
            import gc
            import random

            main_count = len(main_video_scenes)
            aux_count = len(aux_video_scenes)

            log_info(f"超大视频优化匹配: 主视频{main_count}场景, 辅助视频{aux_count}场景")

            # 智能采样：对于超大辅助视频，只处理部分场景
            max_aux_scenes = min(aux_count, main_count * 3)  # 最多处理主视频3倍的辅助场景

            if aux_count > max_aux_scenes:
                log_info(f"辅助视频场景过多，采样处理: {aux_count} -> {max_aux_scenes}")
                # 均匀采样
                step = aux_count / max_aux_scenes
                sampled_indices = [int(i * step) for i in range(max_aux_scenes)]
                sampled_aux_scenes = [aux_video_scenes[i] for i in sampled_indices]
                aux_scene_index_map = {i: sampled_indices[i] for i in range(len(sampled_indices))}
            else:
                sampled_aux_scenes = aux_video_scenes
                aux_scene_index_map = {i: i for i in range(len(aux_video_scenes))}

            # 快速特征提取：减少帧数
            log_info("快速提取主视频特征...")
            main_features_dict = {}
            for i, main_scene in enumerate(main_video_scenes):
                # 使用更少的帧数进行快速特征提取
                main_features = self.extract_frame_features(
                    main_video_path, main_scene['start_time'], main_scene['end_time'],
                    num_frames=3, fast_mode=True
                )
                if main_features:
                    main_features_dict[i] = main_features

            log_info(f"成功提取 {len(main_features_dict)} 个主视频特征")

            # 分批处理辅助视频，使用更小的批次
            batch_size = 20
            similar_matches = []
            total_batches = (len(sampled_aux_scenes) + batch_size - 1) // batch_size

            for batch_idx in range(total_batches):
                start_idx = batch_idx * batch_size
                end_idx = min((batch_idx + 1) * batch_size, len(sampled_aux_scenes))

                log_info(f"处理辅助视频批次 {batch_idx + 1}/{total_batches}")

                # 提取当前批次的辅助视频特征
                aux_features_dict = {}
                for j in range(start_idx, end_idx):
                    aux_scene = sampled_aux_scenes[j]
                    aux_features = self.extract_frame_features(
                        aux_video_path, aux_scene['start_time'], aux_scene['end_time'],
                        num_frames=3, fast_mode=True
                    )
                    if aux_features:
                        aux_features_dict[j] = aux_features

                # 快速相似度计算
                for i, main_features in main_features_dict.items():
                    for j, aux_features in aux_features_dict.items():
                        similarity = self.calculate_similarity(main_features, aux_features)
                        if similarity >= similarity_threshold:
                            original_aux_idx = aux_scene_index_map[j]
                            similar_matches.append({
                                'main_scene_index': i,
                                'aux_scene_index': original_aux_idx,
                                'similarity': similarity
                            })

                # 清理内存
                aux_features_dict.clear()
                gc.collect()

            # 去重并限制匹配数量
            similar_matches = self._deduplicate_and_limit_matches(similar_matches, main_count)

            log_info(f"超大视频优化匹配完成: 找到 {len(similar_matches)} 对相似场景")
            return similar_matches

        except Exception as e:
            log_info(f"Error in _find_similar_scenes_ultra_optimized: {str(e)}")
            raise

    def _find_similar_scenes_optimized_v2(self, main_video_scenes, aux_video_scenes, main_video_path, aux_video_path, similarity_threshold):
        """
        大视频的优化相似度匹配策略
        - 并行处理：使用多线程
        - 智能缓存：避免重复计算
        - 限制匹配：确保匹配数量合理
        """
        try:
            import gc
            from concurrent.futures import ThreadPoolExecutor, as_completed

            main_count = len(main_video_scenes)
            aux_count = len(aux_video_scenes)

            log_info(f"大视频优化匹配: 主视频{main_count}场景, 辅助视频{aux_count}场景")

            # 预先提取主视频特征（因为主视频通常较短）
            log_info("提取主视频特征...")
            main_features_dict = {}
            for i, main_scene in enumerate(main_video_scenes):
                main_features = self.extract_frame_features(
                    main_video_path, main_scene['start_time'], main_scene['end_time'],
                    num_frames=5, fast_mode=False
                )
                if main_features:
                    main_features_dict[i] = main_features

            log_info(f"成功提取 {len(main_features_dict)} 个主视频特征")

            # 分批处理辅助视频，使用适中的批次大小
            batch_size = 30
            similar_matches = []
            total_batches = (aux_count + batch_size - 1) // batch_size

            for batch_idx in range(total_batches):
                start_idx = batch_idx * batch_size
                end_idx = min((batch_idx + 1) * batch_size, aux_count)

                log_info(f"处理辅助视频批次 {batch_idx + 1}/{total_batches}")

                # 提取当前批次的辅助视频特征
                aux_features_dict = {}
                for j in range(start_idx, end_idx):
                    aux_scene = aux_video_scenes[j]
                    aux_features = self.extract_frame_features(
                        aux_video_path, aux_scene['start_time'], aux_scene['end_time'],
                        num_frames=5, fast_mode=False
                    )
                    if aux_features:
                        aux_features_dict[j] = aux_features

                # 计算相似度并找到最佳匹配
                for i, main_features in main_features_dict.items():
                    best_similarity = 0.0
                    best_aux_idx = -1

                    for j, aux_features in aux_features_dict.items():
                        similarity = self.calculate_similarity(main_features, aux_features)
                        if similarity > best_similarity and similarity >= similarity_threshold:
                            best_similarity = similarity
                            best_aux_idx = j

                    if best_aux_idx != -1:
                        # 检查是否已有更好的匹配
                        existing_match = next((m for m in similar_matches if m['main_scene_index'] == i), None)
                        if existing_match:
                            if best_similarity > existing_match['similarity']:
                                existing_match['aux_scene_index'] = best_aux_idx
                                existing_match['similarity'] = best_similarity
                        else:
                            similar_matches.append({
                                'main_scene_index': i,
                                'aux_scene_index': best_aux_idx,
                                'similarity': best_similarity
                            })

                # 清理内存
                aux_features_dict.clear()
                gc.collect()

            # 去重并限制匹配数量
            similar_matches = self._deduplicate_and_limit_matches(similar_matches, main_count)

            log_info(f"大视频优化匹配完成: 找到 {len(similar_matches)} 对相似场景")
            return similar_matches

        except Exception as e:
            log_info(f"Error in _find_similar_scenes_optimized_v2: {str(e)}")
            raise

    def _deduplicate_and_limit_matches(self, matches, max_matches):
        """
        去重并限制匹配数量，确保不超过主视频场景数
        """
        try:
            if not matches:
                return []

            # 按相似度排序
            matches.sort(key=lambda x: x['similarity'], reverse=True)

            # 去重：确保每个主场景和辅助场景只匹配一次
            used_main_indices = set()
            used_aux_indices = set()
            deduplicated_matches = []

            for match in matches:
                main_idx = match['main_scene_index']
                aux_idx = match['aux_scene_index']

                if main_idx not in used_main_indices and aux_idx not in used_aux_indices:
                    deduplicated_matches.append(match)
                    used_main_indices.add(main_idx)
                    used_aux_indices.add(aux_idx)

                    # 限制匹配数量不超过主视频场景数
                    if len(deduplicated_matches) >= max_matches:
                        break

            log_info(f"去重和限制后: {len(matches)} -> {len(deduplicated_matches)} 个匹配")
            return deduplicated_matches

        except Exception as e:
            log_info(f"Error in _deduplicate_and_limit_matches: {str(e)}")
            return matches[:max_matches]  # 降级处理

    def find_uniform_replacement_scenes(self, main_video_scenes, aux_video_scenes, replacement_rate=0.5):
        """
        均匀选择要替换的视频段
        :param main_video_scenes: 主视频的视频段列表
        :param aux_video_scenes: 辅助视频的视频段列表
        :param replacement_rate: 替换率（0.0-1.0），表示要替换的主视频段比例
        :return: 要替换的场景匹配列表
        """
        try:
            # 检查参数有效性
            if replacement_rate <= 0.0 or replacement_rate > 1.0:
                raise ValueError("替换率必须在0.0到1.0之间")
                
            if not main_video_scenes or not aux_video_scenes:
                return []
                
            # 计算要替换的场景数量
            num_main_scenes = len(main_video_scenes)
            num_aux_scenes = len(aux_video_scenes)
            num_to_replace = min(int(num_main_scenes * replacement_rate), num_aux_scenes)
            
            if num_to_replace <= 0:
                return []
                
            # 均匀选择主视频段
            main_indices = []
            if num_to_replace == 1:
                main_indices = [num_main_scenes // 2]  # 如果只替换一个，选择中间的场景
            else:
                step = num_main_scenes / num_to_replace
                for i in range(num_to_replace):
                    idx = int(i * step)
                    if idx < num_main_scenes:
                        main_indices.append(idx)
            
            # 均匀选择辅助视频段
            aux_indices = []
            if num_to_replace == 1:
                aux_indices = [num_aux_scenes // 2]  # 如果只替换一个，选择中间的场景
            else:
                step = num_aux_scenes / num_to_replace
                for i in range(num_to_replace):
                    idx = int(i * step)
                    if idx < num_aux_scenes:
                        aux_indices.append(idx)
            
            # 创建匹配列表
            matches = []
            for i in range(min(len(main_indices), len(aux_indices))):
                matches.append({
                    'main_scene_index': main_indices[i],
                    'aux_scene_index': aux_indices[i],
                    'similarity': 1.0  # 这里不是基于相似度的匹配，所以设为1.0
                })
                
            return matches
            
        except Exception as e:
            log_info(f"Error in find_uniform_replacement_scenes: {str(e)}")
            log_info(traceback.format_exc())
            raise
            
    def find_random_replacement_scenes(self, main_video_scenes, aux_video_scenes, replacement_rate=0.5, seed=None):
        """
        随机选择要替换的视频段
        :param main_video_scenes: 主视频的视频段列表
        :param aux_video_scenes: 辅助视频的视频段列表
        :param replacement_rate: 替换率（0.0-1.0），表示要替换的主视频段比例
        :param seed: 随机种子，用于重现结果
        :return: 要替换的场景匹配列表
        """
        try:
            # 设置随机种子
            if seed is not None:
                random.seed(seed)
                
            # 检查参数有效性
            if replacement_rate <= 0.0 or replacement_rate > 1.0:
                raise ValueError("替换率必须在0.0到1.0之间")
                
            if not main_video_scenes or not aux_video_scenes:
                return []
                
            # 计算要替换的场景数量
            num_main_scenes = len(main_video_scenes)
            num_aux_scenes = len(aux_video_scenes)
            num_to_replace = min(int(num_main_scenes * replacement_rate), num_aux_scenes)
            
            if num_to_replace <= 0:
                return []
                
            # 随机选择主视频段
            main_indices = random.sample(range(num_main_scenes), num_to_replace)
            
            # 随机选择辅助视频段
            aux_indices = random.sample(range(num_aux_scenes), num_to_replace)
            
            # 创建匹配列表
            matches = []
            for i in range(num_to_replace):
                matches.append({
                    'main_scene_index': main_indices[i],
                    'aux_scene_index': aux_indices[i],
                    'similarity': 1.0  # 这里不是基于相似度的匹配，所以设为1.0
                })
                
            return matches
            
        except Exception as e:
            log_info(f"Error in find_random_replacement_scenes: {str(e)}")
            log_info(traceback.format_exc())
            raise

    def replace_and_concatenate_videos(self, main_video_path, aux_video_path, main_video_scenes, aux_video_scenes, similar_matches, output_path, progress_callback=None):
        """
        根据相似度匹配结果替换视频段并合成新的视频。
        :param main_video_path: 主视频文件路径
        :param aux_video_path: 辅助视频文件路径
        :param main_video_scenes: 主视频的视频段列表
        :param aux_video_scenes: 辅助视频的视频段列表
        :param similar_matches: 相似视频段的匹配列表
        :param output_path: 输出视频文件路径
        :param progress_callback: 进度回调函数，接收(current, total, message)参数
        """
        try:
            # 检查视频文件是否存在
            if not os.path.exists(main_video_path):
                raise FileNotFoundError(f"主视频文件不存在: {main_video_path}")
            if not os.path.exists(aux_video_path):
                raise FileNotFoundError(f"辅助视频文件不存在: {aux_video_path}")
                
            # 检查输出路径是否合法
            try:
                output_dir = os.path.dirname(output_path)

                # 检查输出目录是否存在或可创建
                if output_dir:
                    if not os.path.exists(output_dir):
                        log_info(f"输出目录不存在，尝试创建: {output_dir}")
                        try:
                            os.makedirs(output_dir, exist_ok=True)
                        except Exception as dir_error:
                            log_info(f"无法创建输出目录，将使用临时目录: {str(dir_error)}")
                            # 如果无法创建指定目录，则使用临时目录
                            output_path = self.get_temp_file_path(prefix="output_video", extension=".mp4")
                            log_info(f"使用临时输出路径: {output_path}")

                    # 检查目录写权限
                    if not os.access(output_dir, os.W_OK):
                        log_info(f"输出目录无写入权限，将使用临时目录: {output_dir}")
                        output_path = self.get_temp_file_path(prefix="output_video", extension=".mp4")
                        log_info(f"使用临时输出路径: {output_path}")
            except Exception as path_error:
                log_info(f"输出路径检查失败: {str(path_error)}，将使用临时路径")
                output_path = self.get_temp_file_path(prefix="output_video", extension=".mp4")
                log_info(f"使用临时输出路径: {output_path}")
                
            # 先使用临时输出路径，成功后再复制到最终位置
            temp_output_path = self.get_temp_file_path(prefix="temp_output", extension=".mp4")
            final_output_path = output_path
            
            log_info(f"使用临时路径处理视频: {temp_output_path}")
            log_info(f"最终输出路径: {final_output_path}")

            # 初始化进度回调
            if progress_callback:
                progress_callback(5, 100, "初始化视频处理...")

            final_clips = []
            main_clip = None
            aux_clip = None

            # 强制垃圾回收，确保有足够的内存
            import gc
            gc.collect()

            try:
                # 安全地加载主视频文件
                log_info(f"加载主视频: {main_video_path}")
                main_clip = VideoFileClip(main_video_path)
                if main_clip is None:
                    raise Exception(f"无法加载主视频文件: {main_video_path}")
                log_info(f"主视频加载成功，时长: {main_clip.duration:.2f}秒")
                
                # 创建一个列表来存储最终的视频片段，并保持原始主视频的顺序
                # 初始化为None，表示该位置的片段尚未确定
                ordered_clips = [None] * len(main_video_scenes)

                # 创建匹配索引字典，便于快速查找
                match_dict = {match['main_scene_index']: match for match in similar_matches}

                log_info(f"开始按顺序处理 {len(main_video_scenes)} 个主视频片段...")
                log_info(f"其中 {len(similar_matches)} 个片段将被替换")

                if progress_callback:
                    progress_callback(10, 100, f"开始处理 {len(main_video_scenes)} 个视频片段...")

                # 按顺序处理每个主视频片段
                for i, main_scene in enumerate(main_video_scenes):
                    main_start = main_scene['start_time']
                    main_end = main_scene['end_time']
                    main_duration = main_end - main_start

                    # 更新进度
                    if progress_callback and i % 5 == 0:  # 每5个片段更新一次进度
                        progress = 10 + int((i / len(main_video_scenes)) * 30)
                        progress_callback(progress, 100, f"处理片段 {i+1}/{len(main_video_scenes)}...")

                    # 检查该片段是否需要被替换
                    if i in match_dict:
                        # 需要替换的片段 - 暂时跳过，稍后处理
                        log_info(f"片段 {i+1} 标记为替换 (时长: {main_duration:.2f}秒)")
                        continue
                    else:
                        # 保留原始主视频片段
                        try:
                            start_time = max(0, main_start)
                            end_time = min(main_clip.duration, main_end)

                            if end_time <= start_time:
                                log_info(f"警告: 主视频片段 {i+1} 时间无效 {start_time}-{end_time}，跳过")
                                continue

                            original_clip = main_clip.subclip(start_time, end_time)
                            if original_clip is None:
                                log_info(f"警告: 无法创建主视频片段 {i+1}，跳过")
                                continue

                            ordered_clips[i] = original_clip
                            log_info(f"✓ 保留主视频片段 {i+1}: {start_time:.2f}-{end_time:.2f}秒")

                        except Exception as clip_error:
                            log_info(f"警告: 创建主视频片段 {i+1} 失败: {clip_error}")
                            continue
                
                # 不要立即关闭主视频，因为subclip依赖于它
                # 将主视频对象保存起来，在最后合成完成后再关闭
                main_video_obj = main_clip

                # 处理替换片段
                log_info(f"处理 {len(similar_matches)} 个替换片段...")

                if progress_callback:
                    progress_callback(40, 100, f"加载辅助视频并处理 {len(similar_matches)} 个替换片段...")

                # 检测匹配格式
                has_precise_timing = any('aux_start_time' in match for match in similar_matches)

                if has_precise_timing:
                    log_info("使用优化的段对段匹配格式（精确时间和时长控制）")
                else:
                    log_info("使用传统匹配格式（场景索引）")

                # 一次性加载辅助视频，避免重复加载
                log_info(f"加载辅助视频: {aux_video_path}")
                aux_video_obj = VideoFileClip(aux_video_path)
                if aux_video_obj is None:
                    raise Exception(f"无法加载辅助视频文件: {aux_video_path}")
                log_info(f"辅助视频加载成功，时长: {aux_video_obj.duration:.2f}秒")

                # 按主视频段顺序处理替换
                replacement_count = 0
                for match_idx, match in enumerate(similar_matches):
                    # 更新进度
                    if progress_callback:
                        progress = 40 + int((match_idx / len(similar_matches)) * 30)
                        progress_callback(progress, 100, f"处理替换片段 {match_idx+1}/{len(similar_matches)}...")

                    main_idx = match['main_scene_index']
                    main_scene = main_video_scenes[main_idx]
                    main_duration = main_scene['end_time'] - main_scene['start_time']

                    try:
                        if has_precise_timing and 'aux_start_time' in match:
                            # 新格式：使用精确时间，确保时长一致
                            aux_start = max(0, match['aux_start_time'])
                            aux_end = match['aux_end_time']

                            # 确保辅助视频片段时长与主视频片段完全一致
                            aux_duration = aux_end - aux_start
                            if abs(aux_duration - main_duration) > 0.05:  # 允许0.05秒误差
                                log_info(f"调整辅助视频片段时长: {aux_duration:.2f}秒 → {main_duration:.2f}秒")
                                aux_end = aux_start + main_duration

                            # 验证调整后的时间范围
                            if aux_end > aux_video_obj.duration:
                                aux_end = aux_video_obj.duration
                                aux_start = max(0, aux_end - main_duration)
                                log_info(f"辅助视频时间超出范围，调整为: {aux_start:.2f}-{aux_end:.2f}秒")

                            if aux_end <= aux_start:
                                log_info(f"警告: 辅助视频片段 {main_idx+1} 时间无效，使用主视频片段")
                                # 降级使用主视频片段
                                main_start = main_scene['start_time']
                                main_end = main_scene['end_time']
                                start_time = max(0, main_start)
                                end_time = min(main_clip.duration, main_end)
                                if end_time > start_time:
                                    replaced_clip = main_clip.subclip(start_time, end_time)
                                    ordered_clips[main_idx] = replaced_clip
                                    log_info(f"✓ 降级使用主视频片段 {main_idx+1}: {start_time:.2f}-{end_time:.2f}秒")
                                continue

                            # 创建替换片段
                            replaced_clip = aux_video_obj.subclip(aux_start, aux_end)
                            if replaced_clip is None:
                                log_info(f"警告: 无法创建辅助视频片段 {main_idx+1}，使用主视频片段")
                                # 降级使用主视频片段
                                main_start = main_scene['start_time']
                                main_end = main_scene['end_time']
                                start_time = max(0, main_start)
                                end_time = min(main_clip.duration, main_end)
                                if end_time > start_time:
                                    replaced_clip = main_clip.subclip(start_time, end_time)
                                    ordered_clips[main_idx] = replaced_clip
                                    log_info(f"✓ 降级使用主视频片段 {main_idx+1}: {start_time:.2f}-{end_time:.2f}秒")
                                continue

                            # 精确时长控制和验证
                            actual_duration = replaced_clip.duration
                            duration_error = abs(actual_duration - main_duration)

                            if duration_error > 0.01:  # 更严格的时长控制（0.01秒）
                                log_info(f"精确调整替换片段时长: {actual_duration:.3f}秒 → {main_duration:.3f}秒 (误差: {duration_error:.3f}秒)")

                                if actual_duration > main_duration:
                                    # 如果太长，精确裁剪
                                    replaced_clip = replaced_clip.subclip(0, main_duration)
                                elif actual_duration < main_duration:
                                    # 如果太短，尝试微调或重新提取
                                    shortage = main_duration - actual_duration
                                    if shortage < 0.1:  # 如果差异很小，尝试扩展
                                        try:
                                            # 尝试稍微扩展结束时间
                                            extended_end = min(aux_end + shortage, aux_video_obj.duration)
                                            if extended_end > aux_end:
                                                extended_clip = aux_video_obj.subclip(aux_start, extended_end)
                                                if abs(extended_clip.duration - main_duration) < duration_error:
                                                    replaced_clip = extended_clip
                                                    log_info(f"通过扩展修正时长: {extended_clip.duration:.3f}秒")
                                        except Exception as extend_error:
                                            log_warning(f"时长扩展失败: {extend_error}")

                                # 最终验证
                                final_duration = replaced_clip.duration
                                final_error = abs(final_duration - main_duration)
                                if final_error > 0.05:
                                    log_warning(f"时长调整后仍有误差: {final_error:.3f}秒")
                                else:
                                    log_info(f"时长调整成功: 最终误差 {final_error:.3f}秒")

                            ordered_clips[main_idx] = replaced_clip
                            replacement_count += 1
                            log_info(f"✓ 替换片段 {main_idx+1}: 辅助视频 {aux_start:.2f}-{aux_end:.2f}秒 "
                                   f"(相似度: {match['similarity']:.3f}, 时长: {main_duration:.2f}秒)")

                        else:
                            # 传统格式：使用场景索引（保持向后兼容）
                            aux_idx = match['aux_scene_index']
                            if aux_idx >= len(aux_video_scenes):
                                log_info(f"警告: 辅助视频场景索引 {aux_idx} 超出范围，跳过")
                                continue

                            aux_scene = aux_video_scenes[aux_idx]
                            start_time = max(0, aux_scene['start_time'])
                            end_time = min(aux_video_obj.duration, aux_scene['end_time'])

                            # 验证时间范围
                            if end_time <= start_time:
                                log_info(f"警告: 辅助视频片段时间无效 {start_time}-{end_time}，跳过")
                                continue

                            # 创建替换片段
                            replaced_clip = aux_video_obj.subclip(start_time, end_time)
                            if replaced_clip is None:
                                log_info(f"警告: 无法创建辅助视频片段 {start_time}-{end_time}，跳过")
                                continue

                            ordered_clips[main_idx] = replaced_clip
                            replacement_count += 1
                            log_info(f"✓ 替换片段 {main_idx+1}: 辅助视频场景 {aux_idx} "
                                   f"({start_time:.2f}-{end_time:.2f}秒)")

                    except Exception as replace_error:
                        log_info(f"替换片段 {main_idx+1} 失败: {replace_error}，使用主视频片段")
                        # 降级使用主视频片段
                        try:
                            main_start = main_scene['start_time']
                            main_end = main_scene['end_time']
                            start_time = max(0, main_start)
                            end_time = min(main_clip.duration, main_end)
                            if end_time > start_time:
                                fallback_clip = main_clip.subclip(start_time, end_time)
                                ordered_clips[main_idx] = fallback_clip
                                log_info(f"✓ 降级使用主视频片段 {main_idx+1}: {start_time:.2f}-{end_time:.2f}秒")
                        except Exception as fallback_error:
                            log_info(f"警告: 主视频片段 {main_idx+1} 也创建失败: {fallback_error}")

                log_info(f"替换处理完成: 成功替换 {replacement_count}/{len(similar_matches)} 个片段")

                # 过滤掉None值（理论上不应该有，但为了健壮性）
                final_clips = [clip for clip in ordered_clips if clip is not None]

                # 计算预期总时长
                expected_total_duration = sum(scene['end_time'] - scene['start_time'] for scene in main_video_scenes)

                # 精确时长验证和校正
                log_info("执行精确时长验证和校正...")
                is_valid, actual_total, duration_error = self._validate_total_duration(
                    final_clips, expected_total_duration, tolerance=0.05
                )

                if not is_valid:
                    log_warning(f"总时长不符合预期，执行校正策略")
                    final_clips = self._duration_correction_strategy(final_clips, expected_total_duration)

                    # 重新验证
                    is_valid, actual_total, duration_error = self._validate_total_duration(
                        final_clips, expected_total_duration, tolerance=0.1
                    )

                    if is_valid:
                        log_info("时长校正成功")
                    else:
                        log_warning(f"时长校正后仍有误差: {duration_error:.3f}s")

                # 详细分析片段情况
                total_main_scenes = len(main_video_scenes)
                total_matches = len(similar_matches)
                successful_clips = len(final_clips)

                log_info(f"片段分析: 主视频场景数={total_main_scenes}, 匹配数={total_matches}, 成功片段数={successful_clips}")

                if not final_clips:
                    # 提供详细的错误信息
                    error_msg = f"无法生成任何有效的视频片段。\n"
                    error_msg += f"主视频场景数: {total_main_scenes}\n"
                    error_msg += f"辅助视频匹配数: {total_matches}\n"

                    if total_matches == 0:
                        error_msg += "建议: 当前相似度阈值可能过高，请尝试降低相似度阈值或使用其他替换策略。"
                    else:
                        error_msg += "建议: 辅助视频可能存在格式问题或时间范围无效，请检查辅助视频文件。"

                    raise Exception(error_msg)

                if final_clips:
                    log_info(f"开始合成 {len(final_clips)} 个视频片段...")

                    # 验证所有片段都有效，增强验证逻辑
                    valid_clips = []
                    for i, clip in enumerate(final_clips):
                        try:
                            if clip is None:
                                log_info(f"警告: 片段 {i} 为None，跳过")
                                continue

                            # 检查基本属性
                            if not hasattr(clip, 'duration'):
                                log_info(f"警告: 片段 {i} 缺少duration属性，跳过")
                                continue

                            duration = clip.duration
                            if duration is None or duration <= 0:
                                log_info(f"警告: 片段 {i} duration无效 ({duration})，跳过")
                                continue

                            # 检查是否可以获取帧（这是关键验证）
                            try:
                                test_frame = clip.get_frame(0)
                                if test_frame is None:
                                    log_info(f"警告: 片段 {i} 无法获取帧，跳过")
                                    continue
                            except Exception as frame_error:
                                log_info(f"警告: 片段 {i} 获取帧失败: {str(frame_error)}，跳过")
                                continue

                            # 如果所有验证都通过，添加到有效片段列表
                            valid_clips.append(clip)
                            log_info(f"片段 {i} 验证通过，时长: {duration:.2f}秒")

                        except Exception as validation_error:
                            log_info(f"警告: 片段 {i} 验证过程出错: {str(validation_error)}，跳过")
                            continue

                    if not valid_clips:
                        # 提供更详细的错误信息
                        error_msg = f"所有视频片段都无效，无法合成。\n"
                        error_msg += f"原始片段数: {len(final_clips)}\n"
                        error_msg += f"有效片段数: 0\n"
                        error_msg += "建议: 请检查视频文件格式是否正确，或尝试使用其他替换策略。"
                        raise Exception(error_msg)

                    log_info(f"有效片段数量: {len(valid_clips)}")

                    if progress_callback:
                        progress_callback(70, 100, f"开始合成视频，共 {len(valid_clips)} 个有效片段...")

                    # 使用新方法尝试处理视频合成和写入
                    success = False
                    concat_error = None
                    
                    try:
                        # 方法1：使用内存友好的合并方法
                        log_info("尝试方法1：使用内存优化方法合成视频...")
                        
                        try:
                            final_video = self._memory_efficient_concatenate(valid_clips)

                            if final_video is None:
                                raise Exception("视频合成失败：_memory_efficient_concatenate返回None")

                            # 检查视频长度是否合理
                            total_duration = sum(clip.duration for clip in valid_clips)
                            if abs(final_video.duration - total_duration) > 3.0:  # 允许3秒的误差
                                log_info(f"警告：合成视频长度与预期不符 (预期: {total_duration:.2f}s, 实际: {final_video.duration:.2f}s)")
                                # 但仍然继续，这可能是由于转场或其他处理导致的

                            log_info(f"视频合成成功，总时长: {final_video.duration:.2f}秒")

                            if progress_callback:
                                progress_callback(80, 100, "正在写入视频文件...")

                            # 写入临时视频文件
                            write_success = self.safe_write_videofile(final_video, temp_output_path)

                            # 检查临时输出文件是否成功创建
                            if write_success and os.path.exists(temp_output_path) and os.path.getsize(temp_output_path) > 1024:
                                log_info(f"临时文件写入成功: {temp_output_path}, 大小: {os.path.getsize(temp_output_path)} 字节")
                                success = True
                            else:
                                file_size = os.path.getsize(temp_output_path) if os.path.exists(temp_output_path) else 0
                                raise Exception(f"临时输出文件无效或过小: {temp_output_path}, 大小: {file_size} 字节, 写入状态: {write_success}")

                        except Exception as method1_error:
                            concat_error = f"方法1失败: {str(method1_error)}"
                            log_info(concat_error)
                            # 继续尝试方法2
                        
                        # 如果方法1失败，尝试方法2
                        if not success:
                            log_info("尝试方法2：使用FFmpeg直接连接...")
                            
                            # 分别保存每个片段为临时文件
                            temp_segment_files = []
                            temp_file_list = self.get_temp_file_path("segments", extension=".txt")

                            try:
                                for i, clip in enumerate(valid_clips):
                                    # 验证片段是否有效
                                    if clip is None:
                                        log_info(f"跳过None片段 {i}")
                                        continue

                                    try:
                                        # 验证clip的基本属性
                                        if not hasattr(clip, 'duration') or clip.duration is None or clip.duration <= 0:
                                            log_info(f"跳过无效duration的片段 {i}: {getattr(clip, 'duration', 'None')}")
                                            continue

                                        # 验证是否可以获取帧
                                        if hasattr(clip, 'get_frame'):
                                            test_frame = clip.get_frame(0)
                                            if test_frame is None:
                                                log_info(f"跳过无法获取帧的片段 {i}")
                                                continue

                                    except Exception as validation_error:
                                        log_info(f"跳过验证失败的片段 {i}: {str(validation_error)}")
                                        continue

                                    temp_file = self.get_temp_file_path(f"segment_{i}", extension=".mp4")
                                    log_info(f"保存片段 {i} 到 {temp_file}，时长: {clip.duration:.2f}秒")

                                    try:
                                        # 使用最简单的设置保存片段
                                        clip.write_videofile(
                                            temp_file,
                                            codec='libx264',
                                            audio_codec='aac',
                                            verbose=False,
                                            ffmpeg_params=['-shortest'],
                                            preset='ultrafast'
                                        )

                                        if os.path.exists(temp_file) and os.path.getsize(temp_file) > 1024:
                                            temp_segment_files.append(temp_file)
                                            log_info(f"片段 {i} 保存成功，文件大小: {os.path.getsize(temp_file)} 字节")
                                        else:
                                            file_size = os.path.getsize(temp_file) if os.path.exists(temp_file) else 0
                                            log_info(f"警告：片段 {i} 保存失败或文件过小，文件大小: {file_size} 字节")

                                    except Exception as save_error:
                                        log_info(f"保存片段 {i} 失败: {str(save_error)}")
                                        continue

                                # 检查是否有有效的片段文件
                                if not temp_segment_files:
                                    raise Exception("没有有效的片段文件可以合并")

                                log_info(f"准备合并 {len(temp_segment_files)} 个片段文件")

                                # 创建文件列表
                                with open(temp_file_list, 'w') as f:
                                    for temp_file in temp_segment_files:
                                        f.write(f"file '{temp_file}'\n")

                                # 使用FFmpeg的concat过滤器合并
                                ffmpeg_path = self._find_ffmpeg_path()
                                if not ffmpeg_path:
                                    raise Exception("找不到FFmpeg")
                                
                                cmd = [
                                    ffmpeg_path,
                                    '-f', 'concat',
                                    '-safe', '0',
                                    '-i', temp_file_list,
                                    '-c', 'copy',
                                    '-y',
                                    temp_output_path
                                ]
                                
                                log_info(f"执行FFmpeg命令: {' '.join(cmd)}")
                                result = subprocess.run(cmd, capture_output=True, text=True)
                                
                                if result.returncode == 0 and os.path.exists(temp_output_path):
                                    log_info(f"FFmpeg合并成功: {temp_output_path}")
                                    success = True
                                else:
                                    raise Exception(f"FFmpeg合并失败: {result.stderr}")

                            except Exception as method2_error:
                                if concat_error:
                                    concat_error = f"{concat_error}; 方法2失败: {str(method2_error)}"
                                else:
                                    concat_error = f"方法2失败: {str(method2_error)}"
                                log_info(concat_error)
                            
                            finally:
                                # 清理临时文件
                                for temp_file in temp_segment_files:
                                    try:
                                        if os.path.exists(temp_file):
                                            os.remove(temp_file)
                                    except:
                                        pass
                                
                                try:
                                    if os.path.exists(temp_file_list):
                                        os.remove(temp_file_list)
                                except:
                                    pass
                        
                        # 检查是否成功合成视频
                        if not success:
                            raise Exception(f"视频合成失败: {concat_error if concat_error else '未知原因'}")
                        
                        # 将临时文件复制到最终位置
                        try:
                            if progress_callback:
                                progress_callback(90, 100, "正在保存最终视频文件...")

                            log_info(f"将临时文件复制到最终位置: {temp_output_path} -> {final_output_path}")
                            import shutil

                            # 确保目标目录存在
                            final_output_dir = os.path.dirname(final_output_path)
                            if final_output_dir and not os.path.exists(final_output_dir):
                                os.makedirs(final_output_dir, exist_ok=True)

                            # 复制文件
                            shutil.copyfile(temp_output_path, final_output_path)

                            # 验证最终输出文件
                            if os.path.exists(final_output_path) and os.path.getsize(final_output_path) > 1024:
                                log_info(f"最终输出文件创建成功: {final_output_path}")
                                if progress_callback:
                                    progress_callback(95, 100, "视频文件保存成功！")
                            else:
                                raise Exception(f"最终输出文件创建失败: {final_output_path}")

                        except Exception as copy_error:
                            log_info(f"复制到最终位置失败: {str(copy_error)}")
                            log_info(f"使用临时文件作为最终结果: {temp_output_path}")
                            # 如果复制失败，返回临时文件路径
                            return temp_output_path

                        if progress_callback:
                            progress_callback(100, 100, "视频合成完成！")

                        return final_output_path
                        
                    except Exception as e:
                        # 清理临时文件
                        try:
                            if os.path.exists(temp_output_path):
                                os.remove(temp_output_path)
                        except:
                            pass
                        
                        raise Exception(f"视频合成过程失败: {str(e)}")

                else:
                    raise Exception("没有可合成的视频片段")
            
            except Exception as e:
                log_info(f"处理过程中发生错误: {str(e)}")
                raise
                
            finally:
                # 确保关闭视频文件
                try:
                    if 'main_video_obj' in locals() and main_video_obj:
                        main_video_obj.close()
                        log_info("主视频对象已关闭")
                except:
                    pass

                try:
                    if 'aux_video_obj' in locals() and aux_video_obj:
                        aux_video_obj.close()
                        log_info("辅助视频对象已关闭")
                except:
                    pass

                # 清理片段对象（注意：不要关闭subclip，因为它们依赖于父对象）
                try:
                    if 'final_clips' in locals():
                        # 只清理引用，不调用close()，因为subclip依赖于父视频对象
                        final_clips.clear()
                        log_info("片段引用已清理")
                except:
                    pass

                # 强制垃圾回收
                gc.collect()
                log_info("内存清理完成")
            
        except Exception as e:
            log_info(f"Error in replace_and_concatenate_videos: {str(e)}")
            log_info(traceback.format_exc())
            raise

    def remove_subtitles(self, video_path, output_path, subtitle_region=None, detection_sensitivity=0.5, removal_method='inpaint', use_acceleration=True):
        """
        移除视频中的字幕区域
        :param video_path: 输入视频路径
        :param output_path: 输出视频路径
        :param subtitle_region: 字幕区域，格式为 (y_start, y_end) 表示字幕区域的垂直范围，如果为None则自动检测
        :param detection_sensitivity: 字幕检测敏感度 (0.1-1.0)，值越高检测越敏感
        :param removal_method: 移除方法 ('inpaint', 'background_fill', 'blur', 'black_fill')
        :param use_acceleration: 是否使用加速处理
        :return: 处理后的视频路径
        """
        try:
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"视频文件不存在: {video_path}")
                
            # 创建输出目录（如果不存在）
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
                
            # 打开视频文件
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise Exception(f"无法打开视频文件: {video_path}")
                
            # 获取视频属性
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            # 如果没有指定字幕区域，则自动检测
            if subtitle_region is None:
                subtitle_region = self._detect_subtitle_region(cap, height, detection_sensitivity)
                log_info(f"自动检测到字幕区域: {subtitle_region}")
                
            # 重新打开视频以进行处理
            cap.release()
            
            # 使用MoviePy处理视频
            clip = VideoFileClip(video_path)
            
            # 选择处理方式 - 优先使用稳定的单线程处理
            if use_acceleration and self.num_threads > 1 and clip.duration < 300:  # 只对5分钟以下的视频使用多线程
                log_info(f"尝试使用多线程加速处理 ({self.num_threads} 线程)")
                try:
                    processed_clip = self._process_video_multithreaded_safe(
                        clip, subtitle_region, removal_method
                    )
                except Exception as e:
                    log_warning(f"多线程处理失败，回退到单线程: {str(e)}")
                    # 定义字幕移除函数
                    def remove_subtitle_from_frame(frame):
                        return self._remove_subtitle_from_single_frame(frame, subtitle_region, removal_method)
                    # 应用字幕移除函数到每一帧
                    processed_clip = clip.fl_image(remove_subtitle_from_frame)
            else:
                log_info("使用单线程处理")
                # 定义字幕移除函数
                def remove_subtitle_from_frame(frame):
                    return self._remove_subtitle_from_single_frame(frame, subtitle_region, removal_method)

                # 应用字幕移除函数到每一帧
                processed_clip = clip.fl_image(remove_subtitle_from_frame)
            
            # 保存处理后的视频，使用更稳定的参数
            try:
                processed_clip.write_videofile(
                    output_path,
                    codec="libx264",
                    audio_codec="aac",
                    temp_audiofile='temp-audio.m4a',
                    remove_temp=True,
                    verbose=False,
                    logger=None
                )
            except Exception as e:
                log_warning(f"视频保存失败，尝试备用方法: {str(e)}")
                # 备用保存方法
                processed_clip.write_videofile(
                    output_path,
                    codec="libx264",
                    audio_codec="aac",
                    verbose=False,
                    logger=None
                )

            # 清理资源
            try:
                clip.close()
            except:
                pass
            try:
                processed_clip.close()
            except:
                pass

            return output_path
            
        except Exception as e:
            log_info(f"Error in remove_subtitles: {str(e)}")
            log_info(traceback.format_exc())
            if 'clip' in locals():
                try:
                    clip.close()
                except:
                    pass
            if 'processed_clip' in locals():
                try:
                    processed_clip.close()
                except:
                    pass
            raise

    def remove_subtitles_multi_regions(self, video_path, output_path, subtitle_regions, removal_method='inpaint'):
        """
        移除视频中的多个矩形字幕区域
        :param video_path: 输入视频路径
        :param output_path: 输出视频路径
        :param subtitle_regions: 多个矩形区域列表 [(x1, y1, x2, y2), ...]
        :param removal_method: 移除方法 ('inpaint', 'background_fill', 'blur', 'black_fill')
        :return: 处理后的视频路径
        """
        try:
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"视频文件不存在: {video_path}")

            if not subtitle_regions or len(subtitle_regions) == 0:
                raise ValueError("未提供有效的字幕区域")

            # 创建输出目录（如果不存在）
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 使用MoviePy处理视频
            clip = VideoFileClip(video_path)

            # 定义多矩形字幕移除函数
            def remove_subtitles_from_frame(frame):
                return self._remove_subtitles_from_frame_multi_regions(frame, subtitle_regions, removal_method)

            # 应用字幕移除函数到每一帧
            processed_clip = clip.fl_image(remove_subtitles_from_frame)

            # 保存处理后的视频
            processed_clip.write_videofile(output_path, codec="libx264", audio_codec="aac", verbose=False, logger=None)

            # 清理资源
            clip.close()
            processed_clip.close()

            return output_path

        except Exception as e:
            log_info(f"Error in remove_subtitles_multi_regions: {str(e)}")
            log_info(traceback.format_exc())
            if 'clip' in locals():
                try:
                    clip.close()
                except:
                    pass
            if 'processed_clip' in locals():
                try:
                    processed_clip.close()
                except:
                    pass
            raise

    def _remove_subtitles_from_frame_multi_regions(self, frame, subtitle_regions, removal_method):
        """
        从单帧中移除多个矩形区域的字幕
        :param frame: 输入帧
        :param subtitle_regions: 多个矩形区域列表 [(x1, y1, x2, y2), ...]
        :param removal_method: 移除方法
        :return: 处理后的帧
        """
        try:
            processed_frame = frame.copy()

            for region in subtitle_regions:
                x1, y1, x2, y2 = region

                # 确保坐标是整数类型
                x1 = int(x1)
                y1 = int(y1)
                x2 = int(x2)
                y2 = int(y2)

                # 确保坐标在有效范围内
                height, width = frame.shape[:2]
                x1 = max(0, min(x1, width - 1))
                y1 = max(0, min(y1, height - 1))
                x2 = max(x1 + 1, min(x2, width))
                y2 = max(y1 + 1, min(y2, height))

                # 提取区域
                region_frame = processed_frame[y1:y2, x1:x2]

                if region_frame.size == 0:
                    continue

                # 根据移除方法处理区域
                if removal_method == 'inpaint':
                    # 使用与单矩形模式相同的高级智能填充算法
                    processed_region = self._inpaint_single_region(processed_frame, x1, y1, x2, y2)
                elif removal_method == 'background_fill':
                    # 使用周围像素的平均值填充
                    if y1 > 0:
                        top_pixels = processed_frame[int(y1-1):int(y1), int(x1):int(x2)]
                        if top_pixels.size > 0:
                            fill_color = np.mean(top_pixels, axis=(0, 1))
                            processed_region = np.full_like(region_frame, fill_color)
                        else:
                            processed_region = np.zeros_like(region_frame)
                    else:
                        processed_region = np.zeros_like(region_frame)
                elif removal_method == 'blur':
                    # 超强多层次模糊处理
                    processed_region = region_frame.copy()

                    # 第一层：超大核模糊
                    processed_region = cv2.GaussianBlur(processed_region, (99, 99), 0)
                    # 第二层：大核模糊
                    processed_region = cv2.GaussianBlur(processed_region, (71, 71), 0)
                    # 第三层：中核模糊
                    processed_region = cv2.GaussianBlur(processed_region, (51, 51), 0)
                    # 第四层：小核模糊（平滑边缘）
                    processed_region = cv2.GaussianBlur(processed_region, (31, 31), 0)

                    # 添加运动模糊效果
                    kernel_motion = np.ones((15, 15), np.float32) / 225
                    processed_region = cv2.filter2D(processed_region, -1, kernel_motion)
                else:  # black_fill
                    # 黑色填充
                    processed_region = np.zeros_like(region_frame)

                # 将处理后的区域放回原帧
                processed_frame[y1:y2, x1:x2] = processed_region

            return processed_frame

        except Exception as e:
            log_info(f"Error in _remove_subtitles_from_frame_multi_regions: {str(e)}")
            return frame

    def _inpaint_single_region(self, frame, x1, y1, x2, y2):
        """
        对单个矩形区域进行高级智能填充处理
        使用与单矩形模式相同的多层次策略
        :param frame: 输入帧
        :param x1, y1, x2, y2: 矩形区域坐标
        :return: 处理后的区域
        """
        try:
            # 输入验证
            if frame is None or frame.size == 0:
                log_info("错误：输入帧为空")
                return np.zeros((y2-y1, x2-x1, 3), dtype=np.uint8)

            height, width = frame.shape[:2]

            # 确保坐标在有效范围内
            x1 = max(0, min(x1, width - 1))
            y1 = max(0, min(y1, height - 1))
            x2 = max(x1 + 1, min(x2, width))
            y2 = max(y1 + 1, min(y2, height))

            # 扩展区域以获得更好的上下文信息（关键改进）
            expand_size = min(10, x1, y1, width - x2, height - y2)
            region_x1 = max(0, x1 - expand_size)
            region_y1 = max(0, y1 - expand_size)
            region_x2 = min(width, x2 + expand_size)
            region_y2 = min(height, y2 + expand_size)

            # 提取扩展的区域
            extended_region = frame[region_y1:region_y2, region_x1:region_x2].copy()

            if extended_region.size == 0:
                log_info("错误：提取的扩展区域为空")
                return frame[y1:y2, x1:x2].copy()

            # 创建精确的局部掩码
            local_mask = np.zeros(extended_region.shape[:2], dtype=np.uint8)
            mask_x1 = x1 - region_x1
            mask_y1 = y1 - region_y1
            mask_x2 = x2 - region_x1
            mask_y2 = y2 - region_y1
            local_mask[mask_y1:mask_y2, mask_x1:mask_x2] = 255

            # 验证掩码
            if np.sum(local_mask) == 0:
                log_info("错误：掩码为空")
                return frame[y1:y2, x1:x2].copy()

            # 多层次智能填充策略
            inpainted_region = None

            # 策略1：尝试高质量双算法组合
            try:
                # 根据区域大小动态调整inpaint半径
                mask_height = mask_y2 - mask_y1
                mask_width = mask_x2 - mask_x1
                region_size = min(mask_width, mask_height)
                inpaint_radius = min(15, max(3, region_size // 8))

                # TELEA算法（快速，适合纹理）
                result_telea = cv2.inpaint(extended_region, local_mask, inpaint_radius, cv2.INPAINT_TELEA)

                # NS算法（高质量，适合平滑区域）
                result_ns = cv2.inpaint(extended_region, local_mask, inpaint_radius, cv2.INPAINT_NS)

                # 智能权重组合（根据区域特征调整权重）
                # 计算区域的纹理复杂度
                gray_region = cv2.cvtColor(extended_region, cv2.COLOR_BGR2GRAY)
                texture_complexity = np.std(gray_region)

                if texture_complexity > 30:  # 高纹理区域，偏向TELEA
                    telea_weight = 0.7
                    ns_weight = 0.3
                else:  # 平滑区域，偏向NS
                    telea_weight = 0.4
                    ns_weight = 0.6

                inpainted_region = cv2.addWeighted(result_telea, telea_weight, result_ns, ns_weight, 0)
                log_info(f"双算法组合成功，纹理复杂度: {texture_complexity:.2f}")
            except Exception as e:
                log_info(f"双算法组合失败: {e}")

            # 策略2：如果双算法失败，尝试单一最佳算法
            if inpainted_region is None:
                try:
                    # 根据区域大小动态调整inpaint半径
                    inpaint_radius = min(10, max(3, (mask_y2 - mask_y1) // 6))
                    inpainted_region = cv2.inpaint(extended_region, local_mask, inpaint_radius, cv2.INPAINT_TELEA)
                    log_info("单一TELEA算法成功")
                except Exception as e:
                    log_info(f"TELEA算法失败: {e}")

            # 策略3：如果还是失败，尝试基础算法
            if inpainted_region is None:
                try:
                    inpainted_region = cv2.inpaint(extended_region, local_mask, 3, cv2.INPAINT_NS)
                    log_info("NS算法成功")
                except Exception as e:
                    log_info(f"NS算法失败: {e}")

            # 策略4：如果所有inpaint方法都失败，使用增强背景填充
            if inpainted_region is None:
                log_info("所有inpaint方法失败，使用增强背景填充")
                inpainted_region = self._enhanced_background_fill_region(extended_region, local_mask)

            # 后处理优化
            try:
                # 软边缘融合
                blend_mask = local_mask.astype(np.float32) / 255.0
                blend_mask = cv2.GaussianBlur(blend_mask, (5, 5), 2.0)
                blend_mask_3ch = np.stack([blend_mask] * 3, axis=2)

                # 软融合
                inpainted_region = (inpainted_region * blend_mask_3ch +
                                  extended_region * (1 - blend_mask_3ch)).astype(np.uint8)

                # GPU加速后处理（如果可用）
                if self.enable_gpu and self.gpu_available:
                    try:
                        # 使用GPU进行降噪处理
                        inpainted_region = cv2.fastNlMeansDenoisingColored(inpainted_region, None, 3, 3, 7, 21)
                    except Exception as e:
                        log_info(f"GPU后处理失败: {e}")

            except Exception as e:
                log_info(f"后处理失败: {e}")

            # 提取目标区域
            result_region = inpainted_region[mask_y1:mask_y2, mask_x1:mask_x2]
            return result_region

        except Exception as e:
            log_info(f"Error in _inpaint_single_region: {str(e)}")
            # 返回原始区域作为回退
            return frame[y1:y2, x1:x2].copy()

    def _enhanced_background_fill_region(self, extended_region, local_mask):
        """
        增强的背景填充策略，用于inpaint失败时的回退
        :param extended_region: 扩展的区域
        :param local_mask: 局部掩码
        :return: 填充后的区域
        """
        try:
            # 分析掩码区域周围的像素
            height, width = extended_region.shape[:2]

            # 创建膨胀掩码来获取边界像素
            kernel = np.ones((3, 3), np.uint8)
            dilated_mask = cv2.dilate(local_mask, kernel, iterations=1)
            border_mask = dilated_mask - local_mask

            # 获取边界像素
            border_pixels = extended_region[border_mask > 0]

            if len(border_pixels) > 0:
                # 使用边界像素的统计信息
                fill_color = np.mean(border_pixels, axis=0)

                # 添加一些随机性以避免过于均匀
                noise = np.random.normal(0, 5, fill_color.shape)
                fill_color = np.clip(fill_color + noise, 0, 255)

                # 创建填充区域
                result = extended_region.copy()
                result[local_mask > 0] = fill_color

                # 使用渐变填充使效果更自然
                mask_float = local_mask.astype(np.float32) / 255.0
                mask_blur = cv2.GaussianBlur(mask_float, (15, 15), 5.0)
                mask_3ch = np.stack([mask_blur] * 3, axis=2)

                # 渐变融合
                fill_region = np.full_like(extended_region, fill_color)
                result = (fill_region * mask_3ch + extended_region * (1 - mask_3ch)).astype(np.uint8)

                return result
            else:
                # 如果没有边界像素，使用整个区域的平均色
                fill_color = np.mean(extended_region, axis=(0, 1))
                result = extended_region.copy()
                result[local_mask > 0] = fill_color
                return result

        except Exception as e:
            log_info(f"增强背景填充失败: {e}")
            # 最后的回退：黑色填充
            result = extended_region.copy()
            result[local_mask > 0] = 0
            return result

    def _detect_subtitle_region(self, cap, height, sensitivity=0.5):
        """
        自动检测视频中的字幕区域
        :param cap: 视频捕获对象
        :param height: 视频高度
        :param sensitivity: 检测敏感度
        :return: 字幕区域 (y_start, y_end)
        """
        # 采样帧数
        sample_frames = 20
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        sample_interval = max(1, frame_count // sample_frames)
        
        # 累积边缘检测结果
        edge_accumulator = None
        
        # 采样帧并检测边缘
        for i in range(0, frame_count, sample_interval):
            cap.set(cv2.CAP_PROP_POS_FRAMES, i)
            ret, frame = cap.read()
            if not ret:
                continue
                
            # 转换为灰度图
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 应用高斯模糊减少噪声
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            # 边缘检测
            edges = cv2.Canny(blurred, 50, 150)
            
            # 累积边缘
            if edge_accumulator is None:
                edge_accumulator = np.zeros_like(edges, dtype=np.float32)
            edge_accumulator += edges
        
        if edge_accumulator is None:
            # 如果无法检测，返回默认值（底部20%区域）
            return (int(height * 0.8), height)
            
        # 归一化累积边缘
        edge_accumulator = edge_accumulator / edge_accumulator.max()
        
        # 计算每一行的边缘密度
        row_density = np.mean(edge_accumulator, axis=1)
        
        # 应用阈值
        threshold = np.max(row_density) * sensitivity
        subtitle_rows = np.where(row_density > threshold)[0]
        
        if len(subtitle_rows) == 0:
            # 如果未检测到字幕，返回默认值（底部20%区域）
            return (int(height * 0.8), height)
        
        # 找到字幕区域的起始和结束位置
        # 通常字幕在底部，所以我们关注下半部分
        lower_half_rows = subtitle_rows[subtitle_rows > height/2]
        
        if len(lower_half_rows) == 0:
            # 如果下半部分没有检测到，使用所有检测到的行
            y_start = max(0, np.min(subtitle_rows) - 10)
            y_end = min(height, np.max(subtitle_rows) + 10)
        else:
            # 使用下半部分检测到的行
            y_start = max(0, np.min(lower_half_rows) - 10)
            y_end = min(height, np.max(lower_half_rows) + 10)
        
        # 确保区域至少有一定高度
        min_height = int(height * 0.05)  # 至少5%的视频高度
        if y_end - y_start < min_height:
            y_end = min(height, y_start + min_height)
        
        return (int(y_start), int(y_end))
    
    def remove_subtitles_from_scenes(self, video_path, scenes, output_path, subtitle_region=None, detection_sensitivity=0.5, removal_method='inpaint'):
        """
        从视频场景中移除字幕
        :param video_path: 视频文件路径
        :param scenes: 视频场景列表
        :param output_path: 输出视频路径
        :param subtitle_region: 字幕区域，如果为None则自动检测
        :param detection_sensitivity: 字幕检测敏感度
        :param removal_method: 移除方法 ('inpaint', 'background_fill', 'blur', 'black_fill')
        :return: 处理后的视频路径
        """
        try:
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"视频文件不存在: {video_path}")
            
            # 打开视频文件
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise Exception(f"无法打开视频文件: {video_path}")
                
            # 获取视频属性
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # 如果没有指定字幕区域，则自动检测
            if subtitle_region is None:
                subtitle_region = self._detect_subtitle_region(cap, height, detection_sensitivity)
                log_info(f"自动检测到字幕区域: {subtitle_region}")
            
            cap.release()
            
            # 使用MoviePy处理视频
            clip = VideoFileClip(video_path)
            
            # 定义字幕移除函数
            def remove_subtitle_from_frame(frame):
                return self._remove_subtitle_from_single_frame(frame, subtitle_region, removal_method)
            
            # 处理每个场景
            processed_clips = []
            for scene in scenes:
                scene_clip = clip.subclip(scene['start_time'], scene['end_time'])
                processed_scene = scene_clip.fl_image(remove_subtitle_from_frame)
                processed_clips.append(processed_scene)
            
            # 合并处理后的场景
            if processed_clips:
                final_clip = concatenate_videoclips(processed_clips)
                final_clip.write_videofile(output_path, codec="libx264", audio_codec="aac")
                final_clip.close()
            
            # 清理资源
            clip.close()
            for processed_clip in processed_clips:
                processed_clip.close()
            
            return output_path
            
        except Exception as e:
            log_info(f"Error in remove_subtitles_from_scenes: {str(e)}")
            log_info(traceback.format_exc())
            if 'clip' in locals():
                try:
                    clip.close()
                except:
                    pass
            if 'processed_clips' in locals():
                for pc in processed_clips:
                    try:
                        pc.close()
                    except:
                        pass
            if 'final_clip' in locals():
                try:
                    final_clip.close()
                except:
                    pass
            raise

    def extract_subtitles_to_string(self, video_path, method='auto', language='zh'):
        """
        从视频中提取字幕并转换为字符串
        :param video_path: 视频文件路径
        :param method: 提取方法 ('auto', 'embedded', 'speech_recognition')
        :param language: 语言代码 ('zh', 'en', 'auto')
        :return: 提取的字幕文本字符串
        """
        try:
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"视频文件不存在: {video_path}")

            subtitle_text = ""

            if method == 'auto' or method == 'embedded':
                # 首先尝试提取内嵌字幕
                subtitle_text = self._extract_embedded_subtitles(video_path)
                if subtitle_text and method == 'embedded':
                    return subtitle_text

            if (method == 'auto' and not subtitle_text) or method == 'speech_recognition':
                # 如果没有内嵌字幕或指定使用语音识别，则使用语音识别
                subtitle_text = self._extract_subtitles_by_speech_recognition(video_path, language)

            return subtitle_text if subtitle_text else "未能提取到字幕内容"

        except Exception as e:
            log_info(f"Error in extract_subtitles_to_string: {str(e)}")
            log_info(traceback.format_exc())
            raise

    def _extract_embedded_subtitles(self, video_path):
        """
        提取视频中的内嵌字幕
        :param video_path: 视频文件路径
        :return: 字幕文本字符串
        """
        try:
            if not self.ffmpeg_path:
                return "FFmpeg未找到，无法提取内嵌字幕。请安装FFmpeg或使用语音识别功能。"

            # 使用ffmpeg提取字幕
            with tempfile.TemporaryDirectory() as temp_dir:
                subtitle_file = os.path.join(temp_dir, "subtitles.srt")

                # 尝试提取字幕流
                cmd = [
                    self.ffmpeg_path, '-i', video_path,
                    '-map', '0:s:0',  # 选择第一个字幕流
                    '-c:s', 'srt',    # 转换为SRT格式
                    subtitle_file,
                    '-y'  # 覆盖输出文件
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')

                if os.path.exists(subtitle_file):
                    with open(subtitle_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 解析SRT格式，提取纯文本
                    return self._parse_srt_to_text(content)
                else:
                    # 如果没有字幕文件生成，检查错误信息
                    if "No subtitle stream found" in result.stderr or "does not contain any stream" in result.stderr:
                        return "视频中没有内嵌字幕"
                    return ""

        except Exception as e:
            log_info(f"提取内嵌字幕失败: {str(e)}")
            return f"提取内嵌字幕失败: {str(e)}"

    def _parse_srt_to_text(self, srt_content):
        """
        解析SRT字幕文件内容，提取纯文本
        :param srt_content: SRT文件内容
        :return: 纯文本字符串
        """
        try:
            # 移除时间戳和序号，只保留文本内容
            lines = srt_content.split('\n')
            text_lines = []

            for line in lines:
                line = line.strip()
                # 跳过空行、序号行和时间戳行
                if (line and
                    not line.isdigit() and
                    not re.match(r'\d{2}:\d{2}:\d{2},\d{3} --> \d{2}:\d{2}:\d{2},\d{3}', line)):
                    text_lines.append(line)

            return '\n'.join(text_lines)

        except Exception as e:
            log_info(f"解析SRT内容失败: {str(e)}")
            return srt_content

    def _extract_subtitles_by_speech_recognition(self, video_path, language='zh'):
        """
        使用语音识别提取字幕
        :param video_path: 视频文件路径
        :param language: 语言代码
        :return: 识别的文本字符串
        """
        try:
            # 首先提取音频
            with tempfile.TemporaryDirectory() as temp_dir:
                audio_file = os.path.join(temp_dir, "audio.wav")

                # 优先使用FFmpeg提取音频（更可靠），如果没有FFmpeg则使用MoviePy
                if self.ffmpeg_path:
                    try:
                        log_info(f"使用FFmpeg提取音频: {self.ffmpeg_path}")

                        # 确保路径使用正确的格式
                        video_path_norm = os.path.normpath(video_path)
                        audio_file_norm = os.path.normpath(audio_file)

                        cmd = [
                            self.ffmpeg_path,
                            '-i', video_path_norm,
                            '-vn',  # 不包含视频
                            '-acodec', 'pcm_s16le',  # 使用PCM编码
                            '-ar', '16000',  # 采样率16kHz
                            '-ac', '1',  # 单声道
                            '-f', 'wav',  # 强制WAV格式
                            audio_file_norm,
                            '-y'  # 覆盖输出文件
                        ]

                        log_info(f"FFmpeg命令: {' '.join(cmd)}")

                        result = subprocess.run(
                            cmd,
                            capture_output=True,
                            text=True,
                            timeout=60,  # 60秒超时
                            cwd=os.path.dirname(video_path)  # 设置工作目录
                        )

                        if result.returncode != 0:
                            log_info(f"FFmpeg音频提取失败 (返回码: {result.returncode})")
                            log_info(f"错误输出: {result.stderr}")
                            raise Exception(f"FFmpeg音频提取失败: {result.stderr}")

                        # 验证输出文件
                        if os.path.exists(audio_file):
                            file_size = os.path.getsize(audio_file)
                            log_info(f"FFmpeg音频提取成功，文件大小: {file_size} bytes")
                            if file_size == 0:
                                raise Exception("FFmpeg生成的音频文件为空")
                        else:
                            raise Exception("FFmpeg未生成音频文件")

                    except Exception as e:
                        log_info(f"FFmpeg提取音频失败，尝试使用MoviePy: {str(e)}")
                        # 如果FFmpeg失败，回退到MoviePy
                        self._extract_audio_with_moviepy(video_path, audio_file)
                else:
                    # 如果没有FFmpeg，使用MoviePy
                    log_info("FFmpeg不可用，使用MoviePy提取音频")
                    self._extract_audio_with_moviepy(video_path, audio_file)

                if not os.path.exists(audio_file):
                    return "音频提取失败"

                # 尝试使用Whisper进行语音识别（如果可用）
                try:
                    import whisper
                    log_info("使用Whisper进行语音识别...")

                    # 检查音频文件是否存在
                    if not os.path.exists(audio_file):
                        raise Exception(f"音频文件不存在: {audio_file}")

                    # 检查音频文件大小
                    file_size = os.path.getsize(audio_file)
                    if file_size == 0:
                        raise Exception("音频文件为空")

                    log_info(f"音频文件路径: {audio_file}")
                    log_info(f"音频文件大小: {file_size} bytes")

                    # 加载Whisper模型
                    model = whisper.load_model("base")

                    # 使用自定义音频加载方法绕过FFmpeg依赖
                    audio_data = self._load_audio_without_ffmpeg(audio_file)

                    # 设置Whisper参数
                    transcribe_options = {
                        "language": language if language != 'auto' else None,
                        "fp16": False,  # 禁用FP16以提高兼容性
                        "verbose": False
                    }

                    # 执行转录（直接传递音频数据而不是文件路径）
                    result = model.transcribe(audio_data, **transcribe_options)

                    if result and "text" in result:
                        transcribed_text = result["text"].strip()
                        if transcribed_text:
                            return transcribed_text
                        else:
                            return "Whisper未识别到任何语音内容"
                    else:
                        return "Whisper转录结果为空"

                except ImportError:
                    log_info("Whisper未安装，尝试使用其他语音识别方法...")
                except Exception as e:
                    log_info(f"Whisper识别失败: {str(e)}")
                    import traceback
                    log_info(f"详细错误信息: {traceback.format_exc()}")

                # 如果Whisper不可用，尝试使用speech_recognition库
                try:
                    import speech_recognition as sr
                    log_info("使用SpeechRecognition进行语音识别...")

                    r = sr.Recognizer()
                    with sr.AudioFile(audio_file) as source:
                        audio_data = r.record(source)

                    # 尝试使用Google语音识别
                    text = r.recognize_google(audio_data, language=language)
                    return text
                except ImportError:
                    return "语音识别库未安装，请安装whisper或speech_recognition"
                except Exception as e:
                    return f"语音识别失败: {str(e)}"

        except Exception as e:
            log_info(f"语音识别提取字幕失败: {str(e)}")
            return f"语音识别失败: {str(e)}"

    def _extract_audio_with_moviepy(self, video_path, audio_file):
        """使用MoviePy提取音频"""
        try:
            log_info(f"使用MoviePy从 {video_path} 提取音频到 {audio_file}")

            video_clip = VideoFileClip(video_path)
            if video_clip.audio is None:
                video_clip.close()
                raise Exception("视频中没有音频轨道")

            audio_clip = video_clip.audio

            # 设置音频参数以确保兼容性
            audio_clip.write_audiofile(
                audio_file,
                verbose=False,
                logger=None,
                codec='pcm_s16le',  # 使用PCM编码
                ffmpeg_params=['-ar', '16000', '-ac', '1']  # 16kHz采样率，单声道
            )

            video_clip.close()
            audio_clip.close()

            # 验证生成的音频文件
            if os.path.exists(audio_file):
                file_size = os.path.getsize(audio_file)
                log_info(f"音频文件生成成功，大小: {file_size} bytes")
                if file_size == 0:
                    raise Exception("生成的音频文件为空")
            else:
                raise Exception("音频文件生成失败")

        except Exception as e:
            raise Exception(f"MoviePy音频提取失败: {str(e)}")

    def _load_audio_without_ffmpeg(self, audio_file):
        """
        不依赖FFmpeg加载音频数据，供Whisper使用
        :param audio_file: 音频文件路径
        :return: 音频数据数组
        """
        try:
            import librosa
            log_info("使用librosa加载音频...")

            # 使用librosa加载音频，Whisper期望16kHz采样率
            audio_data, sr = librosa.load(audio_file, sr=16000, mono=True)
            log_info(f"音频加载成功，采样率: {sr}, 长度: {len(audio_data)} samples")
            return audio_data

        except ImportError:
            log_info("librosa未安装，尝试使用scipy...")
            try:
                from scipy.io import wavfile
                import numpy as np

                # 使用scipy读取WAV文件
                sample_rate, audio_data = wavfile.read(audio_file)

                # 转换为float32并归一化
                if audio_data.dtype == np.int16:
                    audio_data = audio_data.astype(np.float32) / 32768.0
                elif audio_data.dtype == np.int32:
                    audio_data = audio_data.astype(np.float32) / 2147483648.0

                # 如果是立体声，转换为单声道
                if len(audio_data.shape) > 1:
                    audio_data = np.mean(audio_data, axis=1)

                # 重采样到16kHz（如果需要）
                if sample_rate != 16000:
                    log_info(f"重采样从 {sample_rate}Hz 到 16000Hz...")
                    # 简单的重采样
                    ratio = 16000 / sample_rate
                    new_length = int(len(audio_data) * ratio)
                    audio_data = np.interp(
                        np.linspace(0, len(audio_data), new_length),
                        np.arange(len(audio_data)),
                        audio_data
                    )

                log_info(f"音频加载成功，采样率: 16000Hz, 长度: {len(audio_data)} samples")
                return audio_data.astype(np.float32)

            except ImportError:
                log_info("scipy未安装，尝试使用soundfile...")
                try:
                    import soundfile as sf
                    import numpy as np

                    # 使用soundfile读取音频
                    audio_data, sample_rate = sf.read(audio_file)

                    # 如果是立体声，转换为单声道
                    if len(audio_data.shape) > 1:
                        audio_data = np.mean(audio_data, axis=1)

                    # 重采样到16kHz（如果需要）
                    if sample_rate != 16000:
                        log_info(f"重采样从 {sample_rate}Hz 到 16000Hz...")
                        ratio = 16000 / sample_rate
                        new_length = int(len(audio_data) * ratio)
                        audio_data = np.interp(
                            np.linspace(0, len(audio_data), new_length),
                            np.arange(len(audio_data)),
                            audio_data
                        )

                    log_info(f"音频加载成功，采样率: 16000Hz, 长度: {len(audio_data)} samples")
                    return audio_data.astype(np.float32)

                except ImportError:
                    # 如果所有库都不可用，回退到原始方法（会失败但提供清晰的错误信息）
                    raise Exception(
                        "无法加载音频文件。请安装以下任一音频处理库：\n"
                        "pip install librosa\n"
                        "pip install scipy\n"
                        "pip install soundfile\n"
                        "或者安装FFmpeg并将其添加到PATH中"
                    )

        except Exception as e:
            log_info(f"音频加载失败: {str(e)}")
            raise

    def _process_video_multithreaded_safe(self, video_clip, subtitle_region, removal_method):
        """
        安全的多线程视频处理方法
        :param video_clip: 视频剪辑对象
        :param subtitle_region: 字幕区域
        :param removal_method: 移除方法
        :return: 处理后的视频剪辑
        """
        try:
            log_info("开始安全多线程视频处理...")
            start_time = time.time()

            # 限制线程数，避免过度并发
            safe_thread_count = min(self.num_threads, 4)  # 最多4个线程

            # 分批处理，避免内存过载
            batch_size = 50  # 每批处理50帧
            frame_count = int(video_clip.fps * video_clip.duration)

            log_info(f"视频总帧数: {frame_count}, 使用 {safe_thread_count} 线程, 批大小: {batch_size}")

            # 定义帧处理函数
            def process_single_frame(frame):
                try:
                    return self._remove_subtitle_from_single_frame(frame, subtitle_region, removal_method)
                except Exception as e:
                    log_debug(f"处理单帧失败: {str(e)}")
                    return frame  # 返回原始帧

            # 使用MoviePy的内置多线程支持，但限制线程数
            processed_clip = video_clip.fl_image(
                process_single_frame,
                apply_to=['mask']
            )

            total_time = time.time() - start_time
            log_info(f"安全多线程处理完成: {total_time:.1f}秒")

            return processed_clip

        except Exception as e:
            log_warning(f"安全多线程处理失败: {str(e)}")
            raise e

    def _process_video_multithreaded(self, video_clip, subtitle_region, removal_method):
        """
        使用多线程处理视频帧（保留原方法作为备用）
        :param video_clip: 视频剪辑对象
        :param subtitle_region: 字幕区域
        :param removal_method: 移除方法
        :return: 处理后的视频剪辑
        """
        try:
            from moviepy.editor import ImageSequenceClip
            import tempfile

            log_info("开始多线程视频处理...")
            start_time = time.time()

            # 提取所有帧
            frames = []
            frame_count = int(video_clip.fps * video_clip.duration)

            log_info(f"提取 {frame_count} 帧...")
            for i, frame in enumerate(video_clip.iter_frames()):
                frames.append((i, frame))
                if i % 100 == 0:
                    log_info(f"提取进度: {i}/{frame_count} ({i/frame_count*100:.1f}%)")

            log_info(f"开始多线程处理 {len(frames)} 帧 (使用 {self.num_threads} 线程)...")

            # 使用线程池处理帧
            processed_frames = [None] * len(frames)

            def process_frame_batch(frame_data):
                """处理单个帧"""
                index, frame = frame_data
                try:
                    processed_frame = self._remove_subtitle_from_single_frame(
                        frame, subtitle_region, removal_method
                    )
                    return index, processed_frame
                except Exception as e:
                    log_debug(f"处理帧 {index} 失败: {str(e)}")
                    return index, frame  # 返回原始帧

            # 使用线程池处理
            with ThreadPoolExecutor(max_workers=self.num_threads) as executor:
                # 提交所有任务
                futures = [executor.submit(process_frame_batch, frame_data) for frame_data in frames]

                # 收集结果
                completed = 0
                for future in futures:
                    try:
                        index, processed_frame = future.result(timeout=60)
                        processed_frames[index] = processed_frame
                        completed += 1

                        if completed % 100 == 0:
                            elapsed = time.time() - start_time
                            fps = completed / elapsed if elapsed > 0 else 0
                            log_info(f"处理进度: {completed}/{len(frames)} ({completed/len(frames)*100:.1f}%) - {fps:.1f} FPS")

                    except Exception as e:
                        log_debug(f"处理帧失败: {str(e)}")

            # 过滤掉None值
            valid_frames = [f for f in processed_frames if f is not None]

            if not valid_frames:
                raise Exception("没有成功处理的帧")

            log_info("重建视频...")

            # 创建新的视频剪辑
            processed_clip = ImageSequenceClip(valid_frames, fps=video_clip.fps)

            # 复制音频
            if video_clip.audio is not None:
                processed_clip = processed_clip.set_audio(video_clip.audio)

            total_time = time.time() - start_time
            avg_fps = len(frames) / total_time if total_time > 0 else 0
            log_info(f"多线程处理完成: {total_time:.1f}秒, 平均 {avg_fps:.1f} FPS")

            return processed_clip

        except Exception as e:
            log_warning(f"多线程处理失败，回退到单线程: {str(e)}")
            # 回退到单线程处理
            def remove_subtitle_from_frame(frame):
                return self._remove_subtitle_from_single_frame(frame, subtitle_region, removal_method)

            return video_clip.fl_image(remove_subtitle_from_frame)

    def _remove_subtitle_from_single_frame(self, frame, subtitle_region, removal_method='inpaint'):
        """
        从单个帧中移除字幕
        :param frame: 输入帧
        :param subtitle_region: 字幕区域 (y_start, y_end)
        :param removal_method: 移除方法
        :return: 处理后的帧
        """
        try:
            y_start, y_end = subtitle_region

            # 尝试使用GPU加速
            if self.enable_gpu and self.gpu_available:
                return self._remove_subtitle_gpu(frame, y_start, y_end, removal_method)
            else:
                return self._remove_subtitle_cpu(frame, y_start, y_end, removal_method)

        except Exception as e:
            log_info(f"字幕移除失败: {str(e)}")
            return frame

    def _remove_subtitle_gpu(self, frame, y_start, y_end, removal_method):
        """
        使用GPU加速移除字幕
        """
        try:
            # 上传到GPU
            gpu_frame = cv2.cuda_GpuMat()
            gpu_frame.upload(frame)

            if removal_method == 'blur':
                # GPU超强多层次模糊
                gpu_subtitle_area = gpu_frame.rowRange(y_start, y_end)

                # 第一层：超大核模糊
                gpu_blurred = cv2.cuda.GaussianBlur(gpu_subtitle_area, (99, 99), 0)
                # 第二层：大核模糊
                gpu_blurred = cv2.cuda.GaussianBlur(gpu_blurred, (71, 71), 0)
                # 第三层：中核模糊
                gpu_blurred = cv2.cuda.GaussianBlur(gpu_blurred, (51, 51), 0)
                # 第四层：小核模糊
                gpu_blurred = cv2.cuda.GaussianBlur(gpu_blurred, (31, 31), 0)

                # 替换字幕区域
                gpu_result = gpu_frame.clone()
                gpu_blurred.copyTo(gpu_result.rowRange(y_start, y_end))

                # 下载回CPU
                result = gpu_result.download()
                return result

            elif removal_method == 'black_fill':
                # GPU黑色填充
                gpu_result = gpu_frame.clone()
                cv2.cuda.rectangle(gpu_result, (0, y_start), (frame.shape[1], y_end), (0, 0, 0), -1)

                result = gpu_result.download()
                return result

            else:
                # 对于复杂操作，回退到CPU
                return self._remove_subtitle_cpu(frame, y_start, y_end, removal_method)

        except Exception as e:
            log_info(f"GPU处理失败，回退到CPU: {str(e)}")
            return self._remove_subtitle_cpu(frame, y_start, y_end, removal_method)

    def _remove_subtitle_cpu(self, frame, y_start, y_end, removal_method):
        """
        使用CPU移除字幕
        """
        new_frame = frame.copy()

        if removal_method == 'inpaint':
            # 使用OpenCV的内容感知填充
            return self._inpaint_subtitle_region(new_frame, y_start, y_end)
        elif removal_method == 'background_fill':
            # 使用背景色填充
            return self._background_fill_subtitle_region(new_frame, y_start, y_end)
        elif removal_method == 'black_fill':
            # 使用黑色填充
            new_frame[y_start:y_end, :] = 0
            return new_frame
        elif removal_method == 'blur':
            # 使用超强模糊效果 - 多层次模糊处理
            subtitle_area = new_frame[y_start:y_end, :]

            # 第一层：超大核模糊
            blurred_area = cv2.GaussianBlur(subtitle_area, (99, 99), 0)
            # 第二层：大核模糊
            blurred_area = cv2.GaussianBlur(blurred_area, (71, 71), 0)
            # 第三层：中核模糊
            blurred_area = cv2.GaussianBlur(blurred_area, (51, 51), 0)
            # 第四层：小核模糊（平滑边缘）
            blurred_area = cv2.GaussianBlur(blurred_area, (31, 31), 0)

            # 可选：添加运动模糊效果以进一步模糊文字
            kernel_motion = np.ones((15, 15), np.float32) / 225
            blurred_area = cv2.filter2D(blurred_area, -1, kernel_motion)

            new_frame[y_start:y_end, :] = blurred_area
            return new_frame
        else:
            # 默认使用内容感知填充
            return self._inpaint_subtitle_region(new_frame, y_start, y_end)

    def _inpaint_subtitle_region(self, frame, y_start, y_end):
        """
        使用改进的内容感知填充移除字幕区域
        """
        try:
            # 输入验证
            if frame is None or frame.size == 0:
                log_info("错误：输入帧为空")
                return self._background_fill_subtitle_region(frame, y_start, y_end)

            height, width = frame.shape[:2]
            if y_start >= height or y_end <= 0 or y_start >= y_end:
                log_info(f"错误：无效的字幕区域坐标 y_start={y_start}, y_end={y_end}, height={height}")
                return frame

            # 确保坐标在有效范围内
            y_start = max(0, min(y_start, height - 1))
            y_end = max(y_start + 1, min(y_end, height))

            # 扩展区域以获得更好的上下文信息
            expand_size = min(10, y_start, height - y_end)
            region_y_start = max(0, y_start - expand_size)
            region_y_end = min(height, y_end + expand_size)

            # 提取扩展的字幕区域
            subtitle_region = frame[region_y_start:region_y_end, :].copy()

            if subtitle_region.size == 0:
                log_info("错误：提取的字幕区域为空")
                return frame

            # 创建精确的局部掩码
            local_mask = np.zeros(subtitle_region.shape[:2], dtype=np.uint8)
            mask_y_start = y_start - region_y_start
            mask_y_end = y_end - region_y_start
            local_mask[mask_y_start:mask_y_end, :] = 255

            # 验证掩码
            if np.sum(local_mask) == 0:
                log_info("错误：掩码为空")
                return frame

            # 多层次智能填充策略
            inpainted_region = None

            # 策略1：尝试高质量双算法组合
            try:
                # 根据区域大小动态调整inpaint半径
                mask_height = mask_y_end - mask_y_start
                mask_width = local_mask.shape[1]
                region_size = min(mask_width, mask_height)
                inpaint_radius = min(15, max(3, region_size // 8))

                # TELEA算法（快速，适合纹理）
                result_telea = cv2.inpaint(subtitle_region, local_mask, inpaint_radius, cv2.INPAINT_TELEA)

                # NS算法（高质量，适合平滑区域）
                result_ns = cv2.inpaint(subtitle_region, local_mask, inpaint_radius, cv2.INPAINT_NS)

                # 智能权重组合（根据区域特征调整权重）
                # 计算区域的纹理复杂度
                gray_region = cv2.cvtColor(subtitle_region, cv2.COLOR_BGR2GRAY)
                texture_complexity = np.std(gray_region)

                if texture_complexity > 30:  # 高纹理区域，偏向TELEA
                    telea_weight = 0.7
                    ns_weight = 0.3
                else:  # 平滑区域，偏向NS
                    telea_weight = 0.4
                    ns_weight = 0.6

                inpainted_region = cv2.addWeighted(result_telea, telea_weight, result_ns, ns_weight, 0)
            except Exception as e:
                log_info(f"双算法组合失败: {e}")

            # 策略2：如果双算法失败，尝试单一最佳算法
            if inpainted_region is None:
                try:
                    # 根据区域大小动态调整inpaint半径
                    inpaint_radius = min(10, max(3, (mask_y_end - mask_y_start) // 6))
                    inpainted_region = cv2.inpaint(subtitle_region, local_mask, inpaint_radius, cv2.INPAINT_TELEA)
                    log_info("单一TELEA算法成功")
                except Exception as e:
                    log_info(f"TELEA算法失败: {e}")

            # 策略3：如果还是失败，尝试基础算法
            if inpainted_region is None:
                try:
                    inpainted_region = cv2.inpaint(subtitle_region, local_mask, 3, cv2.INPAINT_NS)
                    log_info("NS算法成功")
                except Exception as e:
                    log_info(f"NS算法失败: {e}")

            # 如果所有inpaint方法都失败，使用智能背景填充
            if inpainted_region is None:
                log_info("所有inpaint方法失败，使用智能背景填充")
                return self._enhanced_background_fill(frame, y_start, y_end)

            # 后处理优化
            try:
                # GPU加速后处理（如果可用）
                if self.enable_gpu and self.gpu_available:
                    try:
                        gpu_region = cv2.cuda_GpuMat()
                        gpu_region.upload(inpainted_region)
                        # 使用GPU降噪改善效果
                        gpu_result = cv2.cuda.fastNlMeansDenoisingColored(gpu_region, None, 1.5, 1.5, 7, 21)
                        inpainted_region = gpu_result.download()
                    except Exception as e:
                        log_info(f"GPU后处理失败，继续使用CPU结果: {e}")

                # 边缘融合处理
                # 创建渐变掩码以实现更自然的边缘融合
                blend_mask = local_mask.astype(np.float32) / 255.0

                # 对掩码进行高斯模糊以创建软边缘
                blend_mask = cv2.GaussianBlur(blend_mask, (5, 5), 2.0)

                # 三通道掩码
                blend_mask_3ch = np.stack([blend_mask] * 3, axis=2)

                # 软融合
                inpainted_region = (inpainted_region * blend_mask_3ch +
                                  subtitle_region * (1 - blend_mask_3ch)).astype(np.uint8)

            except Exception as e:
                log_info(f"后处理失败，使用原始inpaint结果: {e}")

            # 将处理后的区域放回原图
            result = frame.copy()
            result[region_y_start:region_y_end, :] = inpainted_region

            return result

        except Exception as e:
            log_info(f"智能填充完全失败: {str(e)}")
            import traceback
            log_info(traceback.format_exc())
            return self._enhanced_background_fill(frame, y_start, y_end)

    def _enhanced_background_fill(self, frame, y_start, y_end):
        """
        增强的智能背景填充方法
        """
        try:
            height, width = frame.shape[:2]

            # 确保坐标有效
            y_start = max(0, min(y_start, height - 1))
            y_end = max(y_start + 1, min(y_end, height))

            result = frame.copy()

            # 策略1：渐变填充（从上下边缘向中心渐变）
            try:
                # 获取上边缘像素
                if y_start > 0:
                    top_edge = frame[max(0, y_start-3):y_start, :]
                    top_color = np.mean(top_edge, axis=(0, 1)) if top_edge.size > 0 else [0, 0, 0]
                else:
                    top_color = [0, 0, 0]

                # 获取下边缘像素
                if y_end < height:
                    bottom_edge = frame[y_end:min(height, y_end+3), :]
                    bottom_color = np.mean(bottom_edge, axis=(0, 1)) if bottom_edge.size > 0 else [0, 0, 0]
                else:
                    bottom_color = [0, 0, 0]

                # 创建渐变填充
                subtitle_height = y_end - y_start
                for i in range(subtitle_height):
                    # 计算渐变权重
                    top_weight = 1.0 - (i / subtitle_height)
                    bottom_weight = i / subtitle_height
                    
                    # 混合颜色
                    color = top_color * top_weight + bottom_color * bottom_weight
                    
                    # 应用到结果
                    result[y_start + i, :] = color
                    
                return result
                
            except Exception as e:
                log_info(f"渐变填充失败: {str(e)}")
                
            # 策略2：如果渐变填充失败，使用简单的背景色填充
            try:
                # 获取上下文颜色
                context_color = self._get_context_color(frame, y_start, y_end)
                
                # 填充区域
                result[y_start:y_end, :] = context_color
                return result
                
            except Exception as e:
                log_info(f"背景色填充失败: {str(e)}")
                
            # 策略3：最后的备选方案 - 黑色填充
            result[y_start:y_end, :] = [0, 0, 0]
            return result
            
        except Exception as e:
            log_info(f"增强背景填充失败: {str(e)}")
            # 最后的保底方案 - 返回原始帧
            return frame
            
    def _get_context_color(self, frame, y_start, y_end):
        """获取上下文颜色"""
        height, width = frame.shape[:2]
        
        # 收集周围区域的颜色
        colors = []
        
        # 上方区域
        if y_start > 5:
            top_region = frame[y_start-5:y_start, :]
            colors.append(np.mean(top_region, axis=(0, 1)))
            
        # 下方区域
        if y_end < height - 5:
            bottom_region = frame[y_end:y_end+5, :]
            colors.append(np.mean(bottom_region, axis=(0, 1)))
            
        # 如果没有收集到颜色，使用默认黑色
        if not colors:
            return [0, 0, 0]
            
        # 返回平均颜色
        return np.mean(colors, axis=0)

    def separate_video_audio(self, video_path, output_video_path=None, output_audio_path=None):
        """
        分离视频和音频
        :param video_path: 输入视频文件路径
        :param output_video_path: 输出视频文件路径（无音频），如果为None则自动生成
        :param output_audio_path: 输出音频文件路径，如果为None则自动生成
        :return: (video_path, audio_path) 输出文件路径元组
        """
        try:
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"视频文件不存在: {video_path}")

            # 生成输出文件路径
            base_name = os.path.splitext(os.path.basename(video_path))[0]
            base_dir = os.path.dirname(video_path)

            if output_video_path is None:
                output_video_path = os.path.join(base_dir, f"{base_name}_video_only.mp4")

            if output_audio_path is None:
                output_audio_path = os.path.join(base_dir, f"{base_name}_audio_only.wav")

            # 创建输出目录（如果不存在）
            for path in [output_video_path, output_audio_path]:
                output_dir = os.path.dirname(path)
                if output_dir and not os.path.exists(output_dir):
                    os.makedirs(output_dir)

            # 使用MoviePy分离视频和音频
            video_clip = VideoFileClip(video_path)

            # 提取无音频的视频
            video_only = video_clip.without_audio()
            video_only.write_videofile(output_video_path, codec="libx264", verbose=False, logger=None)
            video_only.close()

            # 提取音频
            if video_clip.audio is not None:
                audio_clip = video_clip.audio
                audio_clip.write_audiofile(output_audio_path, verbose=False, logger=None)
                audio_clip.close()
            else:
                log_info("警告: 视频中没有音频轨道")
                output_audio_path = None

            video_clip.close()

            return (output_video_path, output_audio_path)

        except Exception as e:
            log_info(f"Error in separate_video_audio: {str(e)}")
            log_info(traceback.format_exc())
            raise

    def split_video_by_scenes(self, video_path, output_dir, threshold=30.0, remove_audio=False, progress_callback=None):
        """
        根据画面变化将视频分割成不同时长的视频段并保存为独立文件
        :param video_path: 输入视频文件路径
        :param output_dir: 输出目录路径
        :param threshold: 场景检测阈值，值越小，检测到的场景越多
        :param remove_audio: 是否去除音频
        :param progress_callback: 进度回调函数，接收(current, total, message)参数
        :return: 分割后的视频文件路径列表
        """
        try:
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"视频文件不存在: {video_path}")

            # 创建输出目录
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            if progress_callback:
                progress_callback(10, 100, "正在检测视频场景...")

            # 使用现有的场景检测方法
            scenes = self.split_video_into_scenes(video_path, threshold)

            if not scenes:
                raise Exception("未检测到任何场景")

            if progress_callback:
                progress_callback(30, 100, f"检测到 {len(scenes)} 个场景，开始分割...")

            # 获取原视频文件名（不含扩展名）
            base_name = os.path.splitext(os.path.basename(video_path))[0]

            # 加载原视频
            video_clip = VideoFileClip(video_path)

            output_files = []
            total_scenes = len(scenes)

            failed_scenes = 0
            consecutive_failures = 0
            max_consecutive_failures = 5  # 最大连续失败次数
            max_failed_scenes = max(5, total_scenes // 3)  # 允许最多1/3场景失败，但至少允许5个失败

            for i, scene in enumerate(scenes):
                try:
                    # 检查失败场景数量
                    if failed_scenes > max_failed_scenes:
                        log_warning(f"失败场景过多({failed_scenes}/{total_scenes})，停止处理")
                        break
                    
                    # 检查连续失败次数
                    if consecutive_failures >= max_consecutive_failures:
                        log_warning(f"连续失败场景过多({consecutive_failures})，尝试跳过当前场景继续处理")
                        consecutive_failures = 0
                        continue

                    # 生成输出文件名
                    scene_filename = f"{base_name}_scene_{i+1:03d}.mp4"
                    scene_output_path = os.path.join(output_dir, scene_filename)

                    # 提取场景片段
                    start_time = scene['start_time']
                    end_time = scene['end_time']
                    duration = end_time - start_time

                    # 跳过过短的片段（小于0.5秒）
                    if duration < 0.5:
                        log_info(f"跳过过短的场景 {i+1}: {duration:.2f}秒")
                        continue

                    # 强制垃圾回收，释放内存
                    import gc
                    gc.collect()

                    scene_clip = video_clip.subclip(start_time, end_time)

                    # 根据选项处理音频
                    if remove_audio:
                        scene_clip = scene_clip.without_audio()

                    # 保存场景视频，使用安全写入方法
                    write_kwargs = {
                        'codec': "libx264",
                        'audio_codec': "aac" if not remove_audio else None,
                        'threads': 1,  # 强制单线程
                        'preset': 'ultrafast'  # 使用最快预设
                    }

                    if not remove_audio:
                        write_kwargs['temp_audiofile'] = self.get_temp_file_path("scene_audio", extension=".m4a")

                    success = self.safe_write_videofile(scene_clip, scene_output_path, **write_kwargs)

                    if success:
                        output_files.append(scene_output_path)
                        consecutive_failures = 0  # 重置连续失败计数
                        log_info(f"✅ 场景 {i+1} 处理成功")
                    else:
                        failed_scenes += 1
                        consecutive_failures += 1
                        log_error(f"❌ 场景 {i+1} 写入失败，跳过 (连续失败: {consecutive_failures}, 总失败: {failed_scenes}/{total_scenes})")

                    # 立即关闭clip释放资源
                    try:
                        scene_clip.close()
                    except:
                        pass

                    # 更新进度
                    progress = 30 + int((i + 1) / total_scenes * 60)
                    if progress_callback:
                        success_rate = len(output_files) / (i + 1) * 100 if i > 0 else 100
                        progress_callback(progress, 100, f"已处理 {i+1}/{total_scenes} 个场景 (成功率: {success_rate:.1f}%)")

                except Exception as e:
                    failed_scenes += 1
                    consecutive_failures += 1
                    log_error(f"分割场景 {i+1} 失败: {str(e)} (连续失败: {consecutive_failures}, 总失败: {failed_scenes}/{total_scenes})")
                    
                    # 尝试清理资源
                    try:
                        self._cleanup_moviepy_state()
                        gc.collect()
                    except:
                        pass
                    
                    # 如果连续失败过多，暂停一下再继续
                    if consecutive_failures % 3 == 0:
                        log_warning(f"检测到多次连续失败，暂停5秒后继续...")
                        time.sleep(5)
                    
                    continue

            # 清理资源
            video_clip.close()

            if progress_callback:
                progress_callback(100, 100, f"分割完成！共生成 {len(output_files)} 个视频文件")

            return output_files

        except Exception as e:
            log_info(f"Error in split_video_by_scenes: {str(e)}")
            log_info(traceback.format_exc())
            raise

    def smart_video_mixing(self, video_files, output_path, video_duration=30,
                          num_outputs=1, transition_type='fade', background_music=None,
                          remove_original_audio=False, use_acceleration=True, progress_callback=None,
                          mixing_intensity='medium', scene_detection=False, output_folder=None,
                          scene_detection_sensitivity=30.0, random_seeds=None):
        """
        智能视频混剪功能 - 从多个视频中随机剪切片段并合成新视频
        :param video_files: 视频文件列表
        :param output_path: 输出视频路径（如果生成多个视频，会自动添加序号）
        :param video_duration: 生成视频的时长（秒）
        :param num_outputs: 生成视频的个数
        :param transition_type: 转场类型 ('fade', 'slide', 'zoom', 'none')
        :param background_music: 背景音乐文件路径（可选）
        :param remove_original_audio: 是否去除原视频音频
        :param use_acceleration: 是否使用加速处理
        :param progress_callback: 进度回调函数，接收(current, total, message)参数
        :param mixing_intensity: 混剪程度 ('low', 'medium', 'high', 'extreme')
        :param scene_detection: 是否启用基于画面变化的智能混剪
        :param output_folder: 输出文件夹路径（如果指定，会覆盖output_path的目录部分）
        :param scene_detection_sensitivity: 画面变化检测灵敏度 (0-100，值越低检测越灵敏)
        :param random_seeds: 随机种子列表，用于确保每次生成的混剪视频都是唯一的
        :return: 生成的视频路径列表
        """
        try:
            if not video_files:
                raise ValueError("视频文件列表不能为空")

            # 处理输出路径
            if output_folder:
                # 如果指定了输出文件夹，重新构建输出路径
                if not os.path.exists(output_folder):
                    os.makedirs(output_folder, exist_ok=True)

                # 从原始output_path提取文件名
                original_filename = os.path.basename(output_path)
                if not original_filename:
                    original_filename = "mixed_video.mp4"

                output_path = os.path.join(output_folder, original_filename)

            log_info(f"开始智能混剪 {len(video_files)} 个视频...")
            log_info(f"混剪程度: {mixing_intensity}")
            log_info(f"画面变化检测: {'启用' if scene_detection else '禁用'}")

            if progress_callback:
                progress_callback(5, 100, f"开始智能混剪 {len(video_files)} 个视频...")

            # 检查文件是否存在
            for video_file in video_files:
                if not os.path.exists(video_file):
                    raise FileNotFoundError(f"视频文件不存在: {video_file}")

            # 检查背景音乐文件
            if background_music and not os.path.exists(background_music):
                raise FileNotFoundError(f"背景音乐文件不存在: {background_music}")

            # 生成多个混剪视频
            output_paths = []
            for i in range(num_outputs):
                if progress_callback:
                    progress_callback(10 + (i * 80 // num_outputs), 100, f"生成第 {i+1}/{num_outputs} 个混剪视频...")

                # 为每个输出视频生成唯一的文件名
                if num_outputs == 1:
                    current_output_path = output_path
                else:
                    base_name, ext = os.path.splitext(output_path)
                    current_output_path = f"{base_name}_{i+1:02d}{ext}"

                # 生成单个混剪视频
                # 获取当前视频的随机种子
                current_seed = None
                if random_seeds and i < len(random_seeds):
                    current_seed = random_seeds[i]
                
                result_path = self._create_single_mixed_video(
                    video_files, current_output_path, video_duration,
                    transition_type, background_music, remove_original_audio,
                    use_acceleration, progress_callback, i, num_outputs,
                    mixing_intensity, scene_detection, scene_detection_sensitivity, 
                    current_seed
                )

                if result_path:
                    output_paths.append(result_path)
                    log_info(f"✅ 第 {i+1} 个混剪视频生成完成: {result_path}")
                else:
                    log_error(f"❌ 第 {i+1} 个混剪视频生成失败")

            if progress_callback:
                progress_callback(100, 100, f"智能混剪完成，共生成 {len(output_paths)} 个视频")

            log_info(f"智能混剪完成，共生成 {len(output_paths)} 个视频")
            return output_paths

        except Exception as e:
            log_info(f"Error in smart_video_mixing: {str(e)}")
            log_info(traceback.format_exc())
            raise

    def _create_single_mixed_video(self, video_files, output_path, video_duration,
                                  transition_type, background_music, remove_original_audio,
                                  use_acceleration, progress_callback, video_index, total_videos,
                                  mixing_intensity='medium', scene_detection=False, 
                                  scene_detection_sensitivity=30.0, random_seed=None):
        """
        创建单个混剪视频
        :param video_files: 视频文件列表
        :param output_path: 输出路径
        :param video_duration: 视频时长（秒）
        :param transition_type: 转场类型
        :param background_music: 背景音乐文件
        :param remove_original_audio: 是否去除原视频音频
        :param use_acceleration: 是否使用加速
        :param progress_callback: 进度回调
        :param video_index: 当前视频索引
        :param total_videos: 总视频数
        :param mixing_intensity: 混剪程度
        :param scene_detection: 是否启用画面变化检测
        :param scene_detection_sensitivity: 画面变化检测灵敏度 (0-100，值越低检测越灵敏)
        :param random_seed: 随机种子，用于确保每次生成的混剪视频都是唯一的
        :return: 输出视频路径
        """
        try:
            from moviepy.editor import VideoFileClip, concatenate_videoclips, AudioFileClip
            import random

            # 设置随机种子，确保可重现性或唯一性
            if random_seed is not None:
                random.seed(random_seed)
                log_info(f"使用随机种子: {random_seed}")
            
            log_info(f"开始生成第 {video_index + 1} 个混剪视频...")

            # 随机选择视频片段
            video_clips = []
            current_duration = 0
            target_duration = video_duration

            # 根据混剪程度计算片段数量和时长
            num_clips, min_clip_duration, max_clip_duration = self._calculate_mixing_parameters(
                video_duration, mixing_intensity, len(video_files)
            )
            avg_clip_duration = target_duration / num_clips

            log_info(f"目标时长: {target_duration}秒，计划片段数: {num_clips}")
            log_info(f"混剪程度: {mixing_intensity} (片段时长: {min_clip_duration:.1f}-{max_clip_duration:.1f}秒)")

            # 随机打乱视频文件顺序
            shuffled_videos = video_files.copy()
            random.shuffle(shuffled_videos)

            for i in range(num_clips):
                if current_duration >= target_duration:
                    break

                # 循环使用视频文件
                video_file = shuffled_videos[i % len(shuffled_videos)]

                # 计算这个片段的时长
                remaining_duration = target_duration - current_duration
                if i == num_clips - 1:  # 最后一个片段
                    clip_duration = remaining_duration
                else:
                    # 根据混剪程度调整片段时长
                    clip_duration = random.uniform(min_clip_duration,
                                                 min(max_clip_duration, remaining_duration * 0.8))

                # 从视频中选择片段（支持场景检测）
                if scene_detection:
                    clip = self._extract_scene_based_clip(video_file, clip_duration, scene_detection_sensitivity)
                else:
                    clip = self._extract_random_clip(video_file, clip_duration)
                if clip:
                    video_clips.append(clip)
                    current_duration += clip.duration
                    log_info(f"添加片段 {i+1}: {os.path.basename(video_file)} ({clip.duration:.1f}秒)")

            if not video_clips:
                raise ValueError("无法生成有效的视频片段")

            log_info(f"共生成 {len(video_clips)} 个片段，总时长: {current_duration:.1f}秒")

            # 验证所有视频片段的有效性
            valid_clips = []
            for i, clip in enumerate(video_clips):
                if self._validate_video_clip(clip, f"片段{i+1}"):
                    valid_clips.append(clip)
                else:
                    log_info(f"跳过无效片段 {i+1}")
                    try:
                        clip.close()
                    except:
                        pass

            if not valid_clips:
                raise ValueError("没有有效的视频片段可以合并")

            video_clips = valid_clips
            log_info(f"验证后有效片段数: {len(video_clips)}")

            # 添加转场效果
            if transition_type != 'none':
                video_clips = self._add_transitions_for_mixing(video_clips, transition_type)

            # 合并视频片段
            final_video = concatenate_videoclips(video_clips, method="compose")

            # 检查合并后的视频音频状态
            has_valid_audio = False
            if final_video.audio is not None:
                try:
                    # 测试音频是否可用
                    _ = final_video.audio.duration
                    has_valid_audio = True
                except (AttributeError, Exception) as audio_error:
                    log_info(f"警告: 合并视频的音频有问题，移除音频: {audio_error}")
                    final_video = final_video.without_audio()

            # 处理音频
            if remove_original_audio:
                # 去除原视频音频
                final_video = final_video.without_audio()
                has_valid_audio = False

            # 添加背景音乐
            if background_music:
                try:
                    final_video = self._add_background_music(final_video, background_music)
                    has_valid_audio = True
                except Exception as bg_error:
                    log_info(f"警告: 添加背景音乐失败: {bg_error}")

            # 输出视频
            log_info(f"开始渲染视频: {output_path}")

            # 根据音频状态决定音频编码器
            audio_codec = None
            if has_valid_audio and final_video.audio is not None:
                try:
                    # 再次验证音频可用性
                    _ = final_video.audio.duration
                    audio_codec = 'aac'
                except:
                    log_info("警告: 最终音频验证失败，将不包含音频")
                    final_video = final_video.without_audio()

            # 使用临时目录
            temp_audio_file = None
            if audio_codec:
                temp_audio_file = self.get_temp_file_path("audio", extension=".m4a")

            final_video.write_videofile(
                output_path,
                codec='libx264',
                audio_codec=audio_codec,
                temp_audiofile=temp_audio_file,
                remove_temp=True,
                verbose=False,
                logger=None
            )

            # 清理资源
            # 注意：由于clip可能依赖于原始video对象，需要小心清理
            try:
                final_video.close()
            except:
                pass

            # 清理video_clips（这些可能是subclip，需要特殊处理）
            for clip in video_clips:
                try:
                    # 如果是subclip，它可能依赖于父video对象
                    # 我们只关闭那些独立的clip
                    if hasattr(clip, 'close'):
                        clip.close()
                except:
                    pass

            return output_path

        except Exception as e:
            log_info(f"生成混剪视频失败: {str(e)}")
            log_info(traceback.format_exc())
            return None

    def _extract_random_clip(self, video_file, duration):
        """
        从视频文件中随机提取指定时长的片段
        :param video_file: 视频文件路径
        :param duration: 片段时长
        :return: 视频片段对象
        """
        try:
            from moviepy.editor import VideoFileClip
            import random

            # 加载视频
            video = VideoFileClip(video_file)

            # 验证视频是否正确加载
            if video.reader is None:
                log_info(f"警告: 视频文件 {video_file} 读取器为空，尝试重新加载")
                video.close()
                # 尝试重新加载
                video = VideoFileClip(video_file)
                if video.reader is None:
                    log_info(f"错误: 无法加载视频文件 {video_file}")
                    video.close()
                    return None

            video_duration = video.duration

            if video_duration <= duration:
                # 如果视频时长不够，返回整个视频
                # 检查并处理音频问题
                if video.audio is not None:
                    try:
                        # 测试音频是否可用
                        _ = video.audio.duration
                    except (AttributeError, Exception) as audio_error:
                        log_info(f"警告: 视频 {video_file} 的音频有问题，移除音频: {audio_error}")
                        video = video.without_audio()
                # 不要关闭video对象，直接返回
                return video

            # 随机选择开始时间
            max_start_time = video_duration - duration
            start_time = random.uniform(0, max_start_time)

            # 提取片段
            clip = video.subclip(start_time, start_time + duration)

            # 检查并处理音频问题
            if clip.audio is not None:
                try:
                    # 测试音频是否可用
                    _ = clip.audio.duration
                    # 测试音频读取器
                    if hasattr(clip.audio, 'reader') and clip.audio.reader is None:
                        log_info(f"警告: 音频读取器为空，移除音频")
                        clip = clip.without_audio()
                except (AttributeError, Exception) as audio_error:
                    log_info(f"警告: 视频片段 {video_file} 的音频有问题，移除音频: {audio_error}")
                    clip = clip.without_audio()

            # 重要：不要关闭原视频对象，因为clip依赖于它
            # video.close()  # 注释掉这行

            return clip

        except Exception as e:
            log_info(f"提取视频片段失败 {video_file}: {str(e)}")
            return None

    def _calculate_mixing_parameters(self, video_duration, mixing_intensity, num_videos):
        """
        根据混剪程度计算片段参数
        :param video_duration: 目标视频时长
        :param mixing_intensity: 混剪程度 ('low', 'medium', 'high', 'extreme')
        :param num_videos: 视频文件数量
        :return: (片段数量, 最小片段时长, 最大片段时长)
        """
        # 混剪程度配置
        intensity_config = {
            'low': {
                'clips_per_minute': 2,      # 每分钟2个片段
                'min_duration': 8.0,        # 最小8秒
                'max_duration': 30.0        # 最大30秒
            },
            'medium': {
                'clips_per_minute': 4,      # 每分钟4个片段
                'min_duration': 5.0,        # 最小5秒
                'max_duration': 20.0        # 最大20秒
            },
            'high': {
                'clips_per_minute': 6,      # 每分钟6个片段
                'min_duration': 3.0,        # 最小3秒
                'max_duration': 12.0        # 最大12秒
            },
            'extreme': {
                'clips_per_minute': 10,     # 每分钟10个片段
                'min_duration': 1.5,        # 最小1.5秒
                'max_duration': 6.0         # 最大6秒
            }
        }

        config = intensity_config.get(mixing_intensity, intensity_config['medium'])

        # 计算片段数量
        base_clips = int(video_duration / 60 * config['clips_per_minute'])
        num_clips = max(2, min(base_clips, num_videos * 3))  # 至少2个，最多每个视频3个片段

        return num_clips, config['min_duration'], config['max_duration']

    def _extract_scene_based_clip(self, video_file, duration, threshold=30.0):
        """
        基于场景变化提取视频片段
        :param video_file: 视频文件路径
        :param duration: 片段时长
        :param threshold: 场景变化检测阈值（0-100，值越低检测越灵敏）
        :return: 视频片段对象
        """
        try:
            from moviepy.editor import VideoFileClip
            import random

            # 加载视频
            video = VideoFileClip(video_file)

            # 验证视频是否正确加载
            if video.reader is None:
                log_info(f"警告: 视频文件 {video_file} 读取器为空，使用随机提取")
                return self._extract_random_clip(video_file, duration)

            video_duration = video.duration

            if video_duration <= duration:
                # 如果视频时长不够，返回整个视频
                if video.audio is not None:
                    try:
                        _ = video.audio.duration
                    except (AttributeError, Exception) as audio_error:
                        log_info(f"警告: 视频 {video_file} 的音频有问题，移除音频: {audio_error}")
                        video = video.without_audio()
                return video

            # 检测场景变化点，使用传入的阈值
            scene_changes = self._detect_scene_changes(video, video_file, threshold)

            if not scene_changes:
                # 如果没有检测到场景变化，使用随机提取
                log_info(f"未检测到场景变化，使用随机提取: {video_file}")
                return self._extract_random_clip(video_file, duration)

            # 选择一个场景变化点作为起始点
            start_time = random.choice(scene_changes)

            # 确保不会超出视频范围
            if start_time + duration > video_duration:
                start_time = max(0, video_duration - duration)

            # 提取片段
            clip = video.subclip(start_time, start_time + duration)

            # 检查并处理音频问题
            if clip.audio is not None:
                try:
                    _ = clip.audio.duration
                    if hasattr(clip.audio, 'reader') and clip.audio.reader is None:
                        log_info(f"警告: 音频读取器为空，移除音频")
                        clip = clip.without_audio()
                except (AttributeError, Exception) as audio_error:
                    log_info(f"警告: 视频片段 {video_file} 的音频有问题，移除音频: {audio_error}")
                    clip = clip.without_audio()

            log_info(f"基于场景变化提取片段: {os.path.basename(video_file)} (起始: {start_time:.1f}s, 阈值: {threshold})")
            return clip

        except Exception as e:
            log_info(f"场景检测失败，使用随机提取: {video_file}: {str(e)}")
            return self._extract_random_clip(video_file, duration)

    def _validate_video_clip(self, clip, clip_name="视频片段"):
        """
        验证视频片段是否有效
        :param clip: 视频片段对象
        :param clip_name: 片段名称（用于日志）
        :return: 是否有效
        """
        try:
            if clip is None:
                log_info(f"{clip_name}: 片段为空")
                return False

            # 检查视频读取器
            if not hasattr(clip, 'reader') or clip.reader is None:
                log_info(f"{clip_name}: 视频读取器为空")
                return False

            # 检查基本属性
            try:
                _ = clip.duration
                _ = clip.size
                _ = clip.fps
            except Exception as e:
                log_info(f"{clip_name}: 基本属性访问失败: {e}")
                return False

            # 尝试获取第一帧
            try:
                _ = clip.get_frame(0)
            except Exception as e:
                log_info(f"{clip_name}: 无法获取视频帧: {e}")
                return False

            # 检查音频（如果存在）
            if clip.audio is not None:
                try:
                    _ = clip.audio.duration
                    # 检查音频读取器
                    if hasattr(clip.audio, 'reader') and clip.audio.reader is None:
                        log_info(f"{clip_name}: 音频读取器为空，移除音频")
                        clip = clip.without_audio()
                except Exception as e:
                    log_info(f"{clip_name}: 音频有问题，移除音频: {e}")
                    clip = clip.without_audio()

            return True

        except Exception as e:
            log_info(f"{clip_name}: 验证失败: {e}")
            return False

    def _detect_scene_changes(self, video, video_file, threshold=30.0, max_scenes=10):
        """
        检测视频中的场景变化点
        :param video: VideoFileClip对象
        :param video_file: 视频文件路径（用于日志）
        :param threshold: 场景变化阈值
        :param max_scenes: 最大场景数量
        :return: 场景变化时间点列表
        """
        try:
            import numpy as np

            video_duration = video.duration
            sample_interval = max(1.0, video_duration / 30)  # 最多采样30个点

            scene_changes = []
            prev_frame = None

            # 采样视频帧进行场景检测
            sample_times = np.arange(0, video_duration, sample_interval)

            for i, t in enumerate(sample_times):
                if i >= max_scenes:
                    break

                try:
                    # 获取当前帧
                    frame = video.get_frame(t)

                    if prev_frame is not None:
                        # 计算帧差异
                        diff = np.mean(np.abs(frame.astype(float) - prev_frame.astype(float)))

                        # 如果差异超过阈值，认为是场景变化
                        if diff > threshold:
                            scene_changes.append(t)
                            log_info(f"检测到场景变化: {t:.1f}s (差异: {diff:.1f})")

                    prev_frame = frame

                except Exception as frame_error:
                    # 如果获取帧失败，跳过这个时间点
                    continue

            # 如果没有检测到场景变化，添加一些默认点
            if not scene_changes:
                # 在视频的1/4、1/2、3/4处添加默认场景点
                default_points = [video_duration * 0.25, video_duration * 0.5, video_duration * 0.75]
                scene_changes = [t for t in default_points if t < video_duration - 5]  # 确保有足够时间

            log_info(f"场景检测完成: {os.path.basename(video_file)} - 发现 {len(scene_changes)} 个场景变化点")
            return scene_changes

        except Exception as e:
            log_info(f"场景检测失败: {video_file}: {str(e)}")
            # 返回一些默认的时间点
            video_duration = video.duration
            return [video_duration * 0.2, video_duration * 0.5, video_duration * 0.8]

    def _add_background_music(self, video, music_file):
        """
        为视频添加背景音乐
        :param video: 视频对象
        :param music_file: 音乐文件路径
        :return: 带背景音乐的视频对象
        """
        try:
            from moviepy.editor import AudioFileClip, CompositeAudioClip

            # 检查音乐文件是否存在
            if not os.path.exists(music_file):
                log_info(f"背景音乐文件不存在: {music_file}")
                return video

            # 加载背景音乐
            music = AudioFileClip(music_file)

            # 验证音乐是否有效
            try:
                _ = music.duration
            except (AttributeError, Exception) as music_error:
                log_info(f"背景音乐文件无效: {music_error}")
                music.close()
                return video

            # 如果音乐比视频长，截取音乐
            if music.duration > video.duration:
                music = music.subclip(0, video.duration)
            # 如果音乐比视频短，循环播放音乐
            elif music.duration < video.duration:
                from moviepy.editor import concatenate_audioclips
                # 计算需要循环的次数
                loops = int(video.duration / music.duration) + 1
                music_clips = [music] * loops
                music = concatenate_audioclips(music_clips)
                music = music.subclip(0, video.duration)

            # 降低背景音乐音量（避免盖过原声）
            music = music.volumex(0.3)

            # 合成音频
            video_has_valid_audio = False
            if video.audio is not None:
                try:
                    # 验证视频音频是否有效
                    _ = video.audio.duration
                    video_has_valid_audio = True
                except (AttributeError, Exception):
                    log_info("警告: 视频原音频无效，仅使用背景音乐")

            if video_has_valid_audio:
                # 如果视频有原声，混合原声和背景音乐
                final_audio = CompositeAudioClip([video.audio, music])
            else:
                # 如果视频没有原声，直接使用背景音乐
                final_audio = music

            # 设置新的音频
            video_with_music = video.set_audio(final_audio)

            # 清理资源
            music.close()

            return video_with_music

        except Exception as e:
            log_info(f"添加背景音乐失败: {str(e)}")
            import traceback
            log_info(traceback.format_exc())
            return video

    def merge_videos_with_subtitles(self, video_files, subtitle_files, output_path,
                                   transition_type='fade', subtitle_style=None, single_subtitle_mode=False, use_acceleration=True, progress_callback=None):
        """
        保持向后兼容性的方法 - 重定向到新的智能混剪功能
        """
        log_warning(f"⚠️ 注意: merge_videos_with_subtitles 方法已被弃用，请使用 smart_video_mixing 方法")

        # 使用默认参数调用新方法
        return self.smart_video_mixing(
            video_files=video_files,
            output_path=output_path,
            video_duration=60,  # 默认60秒
            num_outputs=1,
            transition_type=transition_type,
            background_music=None,
            remove_original_audio=False,
            use_acceleration=use_acceleration,
            progress_callback=progress_callback
        )

    def _print_performance_stats(self):
        """打印性能统计信息"""
        stats = self.performance_stats
        log_info(f"\n📊 性能统计:")
        log_info(f"  处理帧数: {stats['frames_processed']}")
        log_info(f"  处理时间: {stats['processing_time']:.1f}秒")
        log_info(f"  平均FPS: {stats['fps']:.1f}")
        log_info(f"  加速模式: {'GPU' if self.enable_gpu and self.gpu_available else 'CPU多线程'}")
        log_info(f"  线程数: {self.num_threads}")

    def set_performance_mode(self, mode='auto'):
        """设置性能模式"""
        try:
            if mode == 'auto':
                # 自动检测最佳模式
                if self._check_gpu_support():
                    log_info("自动选择 GPU 加速模式")
                    self.enable_gpu = True
                    self.num_threads = min(8, multiprocessing.cpu_count())
                    self.low_memory_mode = False
                else:
                    log_info("自动选择 CPU 多线程模式")
                    self.enable_gpu = False
                    self.num_threads = min(8, multiprocessing.cpu_count())
                    self.low_memory_mode = False
            elif mode == 'gpu':
                log_info("设置为 GPU 加速模式")
                self.enable_gpu = True
                self.num_threads = min(6, multiprocessing.cpu_count())
                self.low_memory_mode = False
            elif mode == 'cpu':
                log_info("设置为 CPU 多线程模式")
                self.enable_gpu = False
                self.num_threads = min(8, multiprocessing.cpu_count())
                self.low_memory_mode = False
            elif mode == 'single_thread':
                log_info("设置为单线程模式")
                self.enable_gpu = False
                self.num_threads = 1
                self.low_memory_mode = True
            elif mode == 'low_memory':
                log_info("设置为低内存模式")
                self.enable_gpu = False
                self.num_threads = 2  # 降低线程数以减少内存使用
                self.low_memory_mode = True
            else:
                log_info(f"未知性能模式: {mode}，使用自动模式")
                self.set_performance_mode('auto')
                return

            # 打印性能设置
            log_info(f"性能模式: {mode}, GPU: {self.enable_gpu}, 线程数: {self.num_threads}, 低内存模式: {self.low_memory_mode}")
            
            # 在低内存模式下设置视频处理参数
            if hasattr(self, 'low_memory_mode') and self.low_memory_mode:
                self.video_resize_factor = 0.7  # 视频处理时降低为原分辨率的70%
                self.max_concurrent_clips = 2    # 限制并行处理的视频片段数量
                log_info("启用低内存模式: 降低视频分辨率和限制并行处理")
            else:
                self.video_resize_factor = 1.0
                self.max_concurrent_clips = 5
                
        except Exception as e:
            log_info(f"设置性能模式失败: {str(e)}")
            # 默认设置
            self.enable_gpu = False
            self.num_threads = 2
            self.low_memory_mode = True
            self.video_resize_factor = 0.7
            self.max_concurrent_clips = 2

    def get_acceleration_info(self):
        """获取加速信息"""
        return {
            'gpu_available': self.gpu_available,
            'gpu_enabled': self.enable_gpu and self.gpu_available,
            'cpu_threads': self.num_threads,
            'cpu_count': cpu_count(),
            'opencv_cuda': hasattr(cv2, 'cuda') and cv2.cuda.getCudaEnabledDeviceCount() > 0 if hasattr(cv2, 'cuda') else False
        }

    def set_memory_limit(self, limit_mb):
        """
        设置内存使用限制
        :param limit_mb: 内存限制（MB）
        """
        self.memory_limit_mb = limit_mb
        log_info(f"内存限制设置为: {limit_mb} MB")

    def get_memory_usage(self):
        """获取当前内存使用情况"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            return {
                'rss_mb': memory_info.rss / 1024 / 1024,
                'vms_mb': memory_info.vms / 1024 / 1024,
                'percent': process.memory_percent()
            }
        except ImportError:
            return {'rss_mb': 0, 'vms_mb': 0, 'percent': 0}
        except Exception as e:
            log_info(f"获取内存使用失败: {str(e)}")
            return {'rss_mb': 0, 'vms_mb': 0, 'percent': 0}

    def check_memory_available(self, required_mb=100):
        """
        检查是否有足够的可用内存
        :param required_mb: 需要的内存量（MB）
        :return: 是否有足够内存
        """
        try:
            import psutil

            # 获取系统内存信息
            memory = psutil.virtual_memory()
            available_mb = memory.available / 1024 / 1024

            # 获取当前进程内存使用
            current_usage = self.get_memory_usage()

            # 检查是否超过限制
            if current_usage['rss_mb'] + required_mb > self.memory_limit_mb:
                log_warning(f"⚠️ 内存使用将超过限制: {current_usage['rss_mb']:.1f} + {required_mb} > {self.memory_limit_mb} MB")
                return False

            # 检查系统可用内存
            if available_mb < required_mb:
                log_warning(f"⚠️ 系统可用内存不足: {available_mb:.1f} < {required_mb} MB")
                return False

            return True

        except ImportError:
            log_warning(f"⚠️ psutil未安装，无法检查内存")
            return True  # 假设有足够内存
        except Exception as e:
            log_info(f"内存检查失败: {str(e)}")
            return True

    def force_memory_cleanup(self):
        """强制内存清理"""
        try:
            import gc

            # 强制垃圾回收
            collected = gc.collect()

            # 获取清理后的内存使用
            memory_usage = self.get_memory_usage()

            log_info(f"内存清理完成: 回收 {collected} 个对象, 当前使用 {memory_usage['rss_mb']:.1f} MB")

            return memory_usage

        except Exception as e:
            log_info(f"内存清理失败: {str(e)}")
            return None

    def _process_single_subtitle_mode(self, video_files, subtitle_file):
        """
        处理单字幕文件模式：一个字幕文件控制多个视频
        :param video_files: 视频文件列表
        :param subtitle_file: 单个字幕文件
        :return: (video_clips, merged_subtitles)
        """
        try:
            # 解析字幕文件
            subtitle_segments = self._parse_subtitle_file(subtitle_file)
            log_info(f"解析字幕文件: {len(subtitle_segments)} 个片段")

            # 智能分配字幕片段到不同视频
            video_assignments = self._assign_subtitles_to_videos(subtitle_segments, video_files)

            # 根据分配结果剪切视频片段
            video_clips = []
            for assignment in video_assignments:
                clips = self._cut_video_by_subtitles_with_assignment(
                    assignment['video_file'],
                    assignment['segments'],
                    assignment['video_index']
                )
                video_clips.extend(clips)

            # 重新构建字幕序列（保持原始时间轴）
            merged_subtitles = self._rebuild_subtitle_sequence(subtitle_segments)

            return video_clips, merged_subtitles

        except Exception as e:
            log_info(f"单字幕模式处理失败: {str(e)}")
            raise

    def _process_multiple_subtitle_mode(self, video_files, subtitle_files):
        """
        处理多字幕文件模式：每个视频对应一个字幕文件
        :param video_files: 视频文件列表
        :param subtitle_files: 字幕文件列表
        :return: (video_clips, merged_subtitles)
        """
        try:
            # 解析所有字幕文件
            subtitle_segments = []
            for i, subtitle_file in enumerate(subtitle_files):
                try:
                    if i >= len(video_files):
                        log_info(f"字幕文件 {i} 没有对应的视频文件，跳过")
                        continue

                    segments = self._parse_subtitle_file(subtitle_file)
                    if not segments:
                        log_info(f"字幕文件 {subtitle_file} 解析结果为空，跳过")
                        continue

                    subtitle_segments.append({
                        'video_index': i,
                        'video_file': video_files[i],
                        'segments': segments
                    })
                except Exception as parse_error:
                    log_info(f"解析字幕文件 {subtitle_file} 失败: {str(parse_error)}")
                    continue

            if not subtitle_segments:
                raise ValueError("没有有效的字幕段可以处理")

            # 根据字幕时长剪切视频片段
            video_clips = []
            for subtitle_data in subtitle_segments:
                try:
                    clips = self._cut_video_by_subtitles(
                        subtitle_data['video_file'],
                        subtitle_data['segments']
                    )
                    if clips:  # 确保返回的片段不为空
                        # 过滤掉None的片段
                        valid_clips = [clip for clip in clips if clip is not None and 
                                      (not isinstance(clip, dict) or 
                                       (isinstance(clip, dict) and 'clip' in clip and clip['clip'] is not None))]
                        video_clips.extend(valid_clips)
                    else:
                        log_info(f"视频 {subtitle_data['video_file']} 没有生成有效片段")
                except Exception as cut_error:
                    log_info(f"剪切视频 {subtitle_data['video_file']} 失败: {str(cut_error)}")
                    continue

            if not video_clips:
                raise ValueError("没有生成有效的视频片段")

            # 合并字幕序列
            merged_subtitles = self._merge_subtitle_sequences(subtitle_segments)

            log_info(f"多字幕模式处理完成: {len(video_clips)} 个视频片段, {len(merged_subtitles)} 个字幕段")
            return video_clips, merged_subtitles

        except Exception as e:
            log_info(f"多字幕模式处理失败: {str(e)}")
            raise

    def _assign_subtitles_to_videos(self, subtitle_segments, video_files):
        """
        智能分配字幕片段到不同的视频文件
        :param subtitle_segments: 字幕片段列表
        :param video_files: 视频文件列表
        :return: 分配结果列表
        """
        try:
            assignments = []
            num_videos = len(video_files)

            # 策略1: 循环分配（简单均匀分配）
            for i, video_file in enumerate(video_files):
                # 为每个视频分配对应的字幕片段
                assigned_segments = []
                for j, segment in enumerate(subtitle_segments):
                    if j % num_videos == i:
                        assigned_segments.append(segment)

                if assigned_segments:
                    assignments.append({
                        'video_index': i,
                        'video_file': video_file,
                        'segments': assigned_segments
                    })

            # 策略2: 基于内容长度的智能分配（可选）
            # 这里可以添加更复杂的分配逻辑，比如：
            # - 根据字幕内容长度分配
            # - 根据视频时长分配
            # - 根据关键词匹配分配

            log_info(f"字幕分配完成: {len(assignments)} 个视频获得字幕片段")
            for i, assignment in enumerate(assignments):
                log_info(f"  视频 {i+1}: {len(assignment['segments'])} 个片段")

            return assignments

        except Exception as e:
            log_info(f"字幕分配失败: {str(e)}")
            raise

    def _cut_video_by_subtitles_with_assignment(self, video_file, subtitle_segments, video_index):
        """
        根据分配的字幕片段剪切视频（带视频索引）
        :param video_file: 视频文件路径
        :param subtitle_segments: 分配给该视频的字幕片段
        :param video_index: 视频索引
        :return: 视频片段列表
        """
        try:
            from moviepy.editor import VideoFileClip

            video_clip = VideoFileClip(video_file)
            video_clips = []

            for i, segment in enumerate(subtitle_segments):
                try:
                    # 计算在原视频中的相对时间
                    segment_duration = segment['duration']

                    # 在视频中随机选择一个时间点开始剪切（增加多样性）
                    max_start_time = max(0, video_clip.duration - segment_duration - 0.5)
                    if max_start_time > 0:
                        import random
                        start_time = random.uniform(0, max_start_time)
                    else:
                        start_time = 0

                    end_time = min(start_time + segment_duration, video_clip.duration)

                    # 确保片段长度合理和时间有效性
                    if end_time - start_time < 0.5:
                        continue

                    # 确保时间范围在视频范围内
                    start_time = max(0, start_time)
                    end_time = min(end_time, video_clip.duration)

                    # 再次检查时间有效性
                    if start_time >= end_time or start_time >= video_clip.duration:
                        log_info(f"跳过无效时间片段: start={start_time:.2f}, end={end_time:.2f}, duration={video_clip.duration:.2f}")
                        continue

                    # 剪切视频片段，使用安全剪切方法
                    try:
                        clip = self._safe_subclip(video_clip, start_time, end_time)

                        # 验证剪切后的片段
                        if clip.duration <= 0:
                            log_info(f"跳过零长度片段: {clip.duration}")
                            clip.close()
                            continue

                    except Exception as clip_error:
                        log_info(f"剪切片段失败 {i}: {str(clip_error)}")
                        continue

                    video_clips.append({
                        'clip': clip,
                        'original_video': video_file,
                        'video_index': video_index,
                        'subtitle': segment,
                        'start_time': start_time,
                        'end_time': end_time,
                        'duration': end_time - start_time,
                        'original_subtitle_time': segment['start']  # 保存原始字幕时间
                    })

                except Exception as e:
                    log_info(f"剪切视频片段失败 {i}: {str(e)}")
                    continue

            log_info(f"从 {video_file} 剪切了 {len(video_clips)} 个片段")
            return video_clips

        except Exception as e:
            log_info(f"剪切视频失败 {video_file}: {str(e)}")
            raise

    def _rebuild_subtitle_sequence(self, subtitle_segments):
        """
        重建字幕序列，确保字幕时间与视频片段正确同步
        :param subtitle_segments: 原始字幕片段
        :return: 重建的字幕序列
        """
        try:
            merged_subtitles = []
            current_time = 0

            for segment in subtitle_segments:
                # 使用实际的视频片段时长，而不是原始字幕时长
                duration = segment.get('duration', 0)

                # 确保时长有效
                if duration <= 0:
                    log_info(f"跳过无效时长的字幕片段: {duration}")
                    continue

                merged_subtitles.append({
                    'start': current_time,
                    'end': current_time + duration,
                    'duration': duration,
                    'text': segment['text'],
                    'original_start': segment.get('start', 0)  # 保存原始时间
                })

                current_time += duration

            log_info(f"重建字幕序列: {len(merged_subtitles)} 个片段，总时长: {current_time:.2f}秒")
            return merged_subtitles

        except Exception as e:
            log_info(f"重建字幕序列失败: {str(e)}")
            return []

    def _parse_subtitle_file(self, subtitle_file):
        """
        解析字幕文件，支持SRT、VTT等格式
        :param subtitle_file: 字幕文件路径
        :return: 字幕片段列表
        """
        try:
            segments = []

            with open(subtitle_file, 'r', encoding='utf-8') as f:
                content = f.read()

            if subtitle_file.lower().endswith('.srt'):
                segments = self._parse_srt_content(content)
            elif subtitle_file.lower().endswith('.vtt'):
                segments = self._parse_vtt_content(content)
            else:
                # 尝试自动检测格式
                if '-->' in content:
                    if 'WEBVTT' in content:
                        segments = self._parse_vtt_content(content)
                    else:
                        segments = self._parse_srt_content(content)
                else:
                    raise ValueError(f"不支持的字幕格式: {subtitle_file}")

            log_info(f"解析字幕文件 {subtitle_file}: {len(segments)} 个片段")
            return segments

        except Exception as e:
            log_info(f"解析字幕文件失败 {subtitle_file}: {str(e)}")
            raise

    def _parse_srt_content(self, content):
        """解析SRT格式字幕内容"""
        segments = []
        blocks = content.strip().split('\n\n')

        for block in blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                try:
                    # 解析时间戳
                    time_line = lines[1]
                    start_time, end_time = time_line.split(' --> ')

                    start_seconds = self._time_to_seconds(start_time.strip())
                    end_seconds = self._time_to_seconds(end_time.strip())

                    # 合并字幕文本
                    text = '\n'.join(lines[2:])

                    segments.append({
                        'start': start_seconds,
                        'end': end_seconds,
                        'duration': end_seconds - start_seconds,
                        'text': text.strip()
                    })
                except Exception as e:
                    log_info(f"解析SRT片段失败: {str(e)}")
                    continue

        return segments

    def _parse_vtt_content(self, content):
        """解析VTT格式字幕内容"""
        segments = []
        lines = content.split('\n')

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # 跳过WEBVTT头和空行
            if line.startswith('WEBVTT') or not line:
                i += 1
                continue

            # 查找时间戳行
            if '-->' in line:
                try:
                    start_time, end_time = line.split(' --> ')
                    start_seconds = self._time_to_seconds(start_time.strip())
                    end_seconds = self._time_to_seconds(end_time.strip())

                    # 收集字幕文本
                    i += 1
                    text_lines = []
                    while i < len(lines) and lines[i].strip():
                        text_lines.append(lines[i].strip())
                        i += 1

                    if text_lines:
                        segments.append({
                            'start': start_seconds,
                            'end': end_seconds,
                            'duration': end_seconds - start_seconds,
                            'text': '\n'.join(text_lines)
                        })
                except Exception as e:
                    log_info(f"解析VTT片段失败: {str(e)}")

            i += 1

        return segments

    def _time_to_seconds(self, time_str):
        """将时间字符串转换为秒数"""
        # 支持格式: HH:MM:SS,mmm 或 HH:MM:SS.mmm
        time_str = time_str.replace(',', '.')

        parts = time_str.split(':')
        if len(parts) == 3:
            hours = int(parts[0])
            minutes = int(parts[1])
            seconds_parts = parts[2].split('.')
            seconds = int(seconds_parts[0])
            milliseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0

            total_seconds = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000
            return total_seconds
        else:
            raise ValueError(f"无效的时间格式: {time_str}")

    def _cut_video_by_subtitles(self, video_file, subtitle_segments):
        """
        根据字幕片段剪切视频
        :param video_file: 视频文件路径
        :param subtitle_segments: 字幕片段列表
        :return: 视频片段列表
        """
        try:
            from moviepy.editor import VideoFileClip

            video_clip = VideoFileClip(video_file)
            video_clips = []

            for i, segment in enumerate(subtitle_segments):
                try:
                    # 计算剪切时间，添加一些缓冲
                    start_time = max(0, segment['start'] - 0.1)  # 提前0.1秒
                    end_time = min(video_clip.duration, segment['end'] + 0.1)  # 延后0.1秒

                    # 确保片段长度合理
                    if end_time - start_time < 0.5:  # 最短0.5秒
                        continue

                    # 再次确保时间范围有效性
                    if start_time >= end_time or start_time >= video_clip.duration:
                        log_info(f"跳过无效时间片段: start={start_time:.2f}, end={end_time:.2f}, duration={video_clip.duration:.2f}")
                        continue

                    # 剪切视频片段，使用安全剪切方法
                    try:
                        clip = self._safe_subclip(video_clip, start_time, end_time)

                        # 验证剪切后的片段
                        if clip.duration <= 0:
                            log_info(f"跳过零长度片段: {clip.duration}")
                            clip.close()
                            continue

                    except Exception as clip_error:
                        log_info(f"剪切片段失败 {i}: {str(clip_error)}")
                        continue

                    video_clips.append({
                        'clip': clip,
                        'original_video': video_file,
                        'subtitle': segment,
                        'start_time': start_time,
                        'end_time': end_time,
                        'duration': end_time - start_time
                    })

                except Exception as e:
                    log_info(f"剪切视频片段失败 {i}: {str(e)}")
                    continue

            log_info(f"从 {video_file} 剪切了 {len(video_clips)} 个片段")
            return video_clips

        except Exception as e:
            log_info(f"剪切视频失败 {video_file}: {str(e)}")
            raise

    def _optimize_video_sequence(self, video_clips):
        """
        智能优化视频片段序列
        :param video_clips: 视频片段列表
        :return: 优化后的片段列表
        """
        try:
            # 按字幕开始时间排序
            sorted_clips = sorted(video_clips, key=lambda x: x['subtitle']['start'])

            # 移除过短的片段
            min_duration = 1.0  # 最短1秒
            filtered_clips = [clip for clip in sorted_clips if clip['duration'] >= min_duration]

            # 限制单个片段的最大长度
            max_duration = 10.0  # 最长10秒
            optimized_clips = []

            for clip in filtered_clips:
                if clip['duration'] > max_duration:
                    # 分割长片段
                    split_clips = self._split_long_clip(clip, max_duration)
                    optimized_clips.extend(split_clips)
                else:
                    optimized_clips.append(clip)

            # 确保视频流畅性
            smoothed_clips = self._smooth_video_transitions(optimized_clips)

            log_info(f"视频序列优化: {len(video_clips)} -> {len(smoothed_clips)} 个片段")
            return smoothed_clips

        except Exception as e:
            log_info(f"视频序列优化失败: {str(e)}")
            return video_clips

    def _split_long_clip(self, clip, max_duration):
        """分割过长的视频片段"""
        try:
            split_clips = []
            total_duration = clip['duration']
            num_splits = int(np.ceil(total_duration / max_duration))

            for i in range(num_splits):
                start_offset = i * max_duration
                end_offset = min((i + 1) * max_duration, total_duration)

                if end_offset - start_offset < 1.0:  # 跳过过短的片段
                    continue

                split_clip = clip['clip'].subclip(start_offset, end_offset)

                split_clips.append({
                    'clip': split_clip,
                    'original_video': clip['original_video'],
                    'subtitle': {
                        'start': clip['subtitle']['start'] + start_offset,
                        'end': clip['subtitle']['start'] + end_offset,
                        'duration': end_offset - start_offset,
                        'text': clip['subtitle']['text']
                    },
                    'start_time': clip['start_time'] + start_offset,
                    'end_time': clip['start_time'] + end_offset,
                    'duration': end_offset - start_offset
                })

            return split_clips

        except Exception as e:
            log_info(f"分割长片段失败: {str(e)}")
            return [clip]

    def _smooth_video_transitions(self, clips):
        """平滑视频转换，避免突兀的跳跃"""
        if len(clips) <= 1:
            return clips

        smoothed_clips = []

        for i, clip in enumerate(clips):
            # 检查是否需要调整片段长度以适应转场
            if i > 0:
                prev_clip = clips[i-1]
                # 如果来自不同视频源，可能需要调整
                if clip['original_video'] != prev_clip['original_video']:
                    # 可以在这里添加更复杂的平滑逻辑
                    pass

            smoothed_clips.append(clip)

        return smoothed_clips

    def _add_transitions_for_mixing(self, video_clips, transition_type='fade'):
        """
        为智能混剪添加转场效果（处理VideoFileClip对象列表）
        :param video_clips: VideoFileClip对象列表
        :param transition_type: 转场类型
        :return: 添加转场效果的片段列表
        """
        try:
            if len(video_clips) <= 1 or transition_type == 'none':
                return video_clips

            transition_duration = 0.5  # 转场持续时间
            clips_with_transitions = []

            for i, clip in enumerate(video_clips):
                current_clip = clip  # 直接使用VideoFileClip对象
                
                # 检查当前片段是否为None
                if current_clip is None:
                    log_info(f"跳过无效片段 {i}：片段为None")
                    continue

                if i == 0:
                    # 第一个片段：添加淡入效果
                    if transition_type == 'fade':
                        try:
                            current_clip = current_clip.fadein(transition_duration)
                        except Exception as e:
                            log_info(f"添加淡入效果失败: {str(e)}")
                    clips_with_transitions.append(current_clip)

                elif i == len(video_clips) - 1:
                    # 最后一个片段：添加淡出效果
                    if transition_type == 'fade':
                        try:
                            current_clip = current_clip.fadeout(transition_duration)
                        except Exception as e:
                            log_info(f"添加淡出效果失败: {str(e)}")
                    clips_with_transitions.append(current_clip)

                else:
                    # 中间片段：根据转场类型处理
                    try:
                        if transition_type == 'fade':
                            current_clip = current_clip.fadein(transition_duration).fadeout(transition_duration)
                        # 其他转场类型可以在这里添加

                        # 检查处理后的片段是否为None
                        if current_clip is not None:
                            clips_with_transitions.append(current_clip)
                        else:
                            log_info(f"转场处理后片段 {i} 为None，跳过")
                    except Exception as e:
                        log_info(f"处理片段 {i} 转场效果失败: {str(e)}，使用原始片段")
                        if clip is not None:
                            clips_with_transitions.append(clip)

            log_info(f"添加转场效果: {transition_type}，有效片段数: {len(clips_with_transitions)}")
            return clips_with_transitions

        except Exception as e:
            log_info(f"添加转场效果失败: {str(e)}")
            # 如果转场失败，返回原始片段（过滤None值）
            return [clip for clip in video_clips if clip is not None]

    def _add_transitions(self, video_clips, transition_type='fade'):
        """
        为视频片段添加转场效果
        :param video_clips: 视频片段列表
        :param transition_type: 转场类型
        :return: 添加转场效果的片段列表
        """
        try:
            if len(video_clips) <= 1 or transition_type == 'none':
                return video_clips

            from moviepy.editor import CompositeVideoClip, concatenate_videoclips

            transition_duration = 0.5  # 转场持续时间
            clips_with_transitions = []

            for i, clip in enumerate(video_clips):
                current_clip = clip['clip']
                
                # 检查当前片段是否为None
                if current_clip is None:
                    log_info(f"跳过无效片段 {i}：片段为None")
                    continue

                if i == 0:
                    # 第一个片段：添加淡入效果
                    if transition_type == 'fade':
                        try:
                            current_clip = current_clip.fadein(transition_duration)
                        except Exception as e:
                            log_info(f"添加淡入效果失败: {str(e)}")
                    clips_with_transitions.append(current_clip)

                elif i == len(video_clips) - 1:
                    # 最后一个片段：添加淡出效果
                    if transition_type == 'fade':
                        try:
                            current_clip = current_clip.fadeout(transition_duration)
                        except Exception as e:
                            log_info(f"添加淡出效果失败: {str(e)}")
                    clips_with_transitions.append(current_clip)

                else:
                    # 中间片段：根据转场类型处理
                    try:
                        if transition_type == 'fade':
                            current_clip = current_clip.fadein(transition_duration).fadeout(transition_duration)
                        elif transition_type == 'slide':
                            current_clip = self._add_slide_transition(current_clip, i)
                        elif transition_type == 'zoom':
                            current_clip = self._add_zoom_transition(current_clip, i)

                        # 检查处理后的片段是否为None
                        if current_clip is not None:
                            clips_with_transitions.append(current_clip)
                        else:
                            log_info(f"转场处理后片段 {i} 为None，使用原始片段")
                            clips_with_transitions.append(clip['clip'])
                    except Exception as e:
                        log_info(f"处理片段 {i} 转场效果失败: {str(e)}，使用原始片段")
                        clips_with_transitions.append(clip['clip'])

            log_info(f"添加转场效果: {transition_type}，有效片段数: {len(clips_with_transitions)}")
            return clips_with_transitions

        except Exception as e:
            log_info(f"添加转场效果失败: {str(e)}")
            # 如果转场失败，返回原始片段
            valid_clips = []
            for clip in video_clips:
                if isinstance(clip, dict) and 'clip' in clip and clip['clip'] is not None:
                    valid_clips.append(clip['clip'])
                elif not isinstance(clip, dict) and clip is not None:
                    valid_clips.append(clip)
            return valid_clips

    def _add_slide_transition(self, clip, index):
        """添加滑动转场效果"""
        try:
            # 检查clip是否为None
            if clip is None:
                log_info("无法添加滑动转场：视频片段为None")
                return None
                
            from moviepy.editor import CompositeVideoClip

            # 简单的滑动效果：从右侧滑入
            w, h = clip.size
            slide_duration = 0.3

            def slide_effect(get_frame, t):
                # 检查get_frame是否为None
                if get_frame is None:
                    log_info("滑动转场中get_frame为None")
                    return None
                    
                if t < slide_duration:
                    # 滑入阶段
                    offset = int(w * (1 - t / slide_duration))
                    frame = get_frame(t)
                    # 这里可以添加更复杂的滑动逻辑
                    return frame
                else:
                    return get_frame(t)

            return clip.fl(slide_effect)

        except Exception as e:
            log_info(f"滑动转场失败: {str(e)}")
            return clip

    def _add_zoom_transition(self, clip, index):
        """添加缩放转场效果"""
        try:
            # 检查clip是否为None
            if clip is None:
                log_info("无法添加缩放转场：视频片段为None")
                return None
                
            zoom_duration = 0.4

            def zoom_effect(get_frame, t):
                # 检查get_frame是否为None
                if get_frame is None:
                    log_info("缩放转场中get_frame为None")
                    return None
                    
                if t < zoom_duration:
                    # 缩放进入
                    scale = 0.8 + 0.2 * (t / zoom_duration)
                    frame = get_frame(t)
                    # 这里可以添加缩放逻辑
                    return frame
                else:
                    return get_frame(t)

            return clip.fl(zoom_effect)

        except Exception as e:
            log_info(f"缩放转场失败: {str(e)}")
            return clip

    def _merge_subtitle_sequences(self, subtitle_segments):
        """
        合并多个视频的字幕序列，确保时间轴正确同步
        :param subtitle_segments: 字幕片段数据列表
        :return: 合并后的字幕序列
        """
        try:
            merged_subtitles = []
            current_time = 0

            for subtitle_data in subtitle_segments:
                for segment in subtitle_data['segments']:
                    # 重新计算时间戳，确保时长有效
                    duration = segment.get('duration', 0)

                    if duration <= 0:
                        log_info(f"跳过无效时长的字幕片段: {duration}")
                        continue

                    merged_subtitles.append({
                        'start': current_time,
                        'end': current_time + duration,
                        'duration': duration,
                        'text': segment['text'],
                        'source_video': subtitle_data.get('video_file', 'unknown')
                    })

                    current_time += duration

            log_info(f"合并字幕序列: {len(merged_subtitles)} 个片段，总时长: {current_time:.2f}秒")
            return merged_subtitles

        except Exception as e:
            log_info(f"合并字幕序列失败: {str(e)}")
            return []

    def _render_final_video(self, video_clips, subtitles, output_path, subtitle_style=None, progress_callback=None):
        """
        渲染最终视频，包括字幕（内存优化版本）
        :param video_clips: 视频片段列表
        :param subtitles: 字幕列表
        :param output_path: 输出路径
        :param subtitle_style: 字幕样式
        :param progress_callback: 进度回调函数
        :return: 输出路径
        """
        try:
            if not video_clips:
                raise ValueError("没有视频片段可以渲染")

            # 检查内存使用情况
            self._check_memory_usage("开始渲染前")
            
            # 根据低内存模式调整视频片段
            low_memory_mode = getattr(self, 'low_memory_mode', False)
            resize_factor = getattr(self, 'video_resize_factor', 1.0)
            
            if low_memory_mode and resize_factor < 1.0:
                log_info(f"低内存模式: 将视频缩放至原始分辨率的 {resize_factor*100:.0f}%")
                resized_clips = []
                for clip_item in video_clips:
                    # 提取实际的视频剪辑对象
                    if isinstance(clip_item, dict) and 'clip' in clip_item:
                        actual_clip = clip_item['clip']
                        # 检查是否已经被缩放（避免重复缩放）
                        if not hasattr(actual_clip, '_was_resized'):
                            resized_clip = actual_clip.resize(resize_factor)
                            resized_clip._was_resized = True
                            # 更新字典中的clip
                            updated_clip_item = clip_item.copy()
                            updated_clip_item['clip'] = resized_clip
                            resized_clips.append(updated_clip_item)
                        else:
                            resized_clips.append(clip_item)
                    else:
                        # 如果已经是clip对象
                        if not hasattr(clip_item, '_was_resized'):
                            resized_clip = clip_item.resize(resize_factor)
                            resized_clip._was_resized = True
                            resized_clips.append(resized_clip)
                        else:
                            resized_clips.append(clip_item)
                video_clips = resized_clips

            # 使用内存友好的合并方法
            log_info("使用内存优化的视频合并...")
            if progress_callback:
                progress_callback(60, 100, "合并视频片段...")
            final_video = self._memory_efficient_concatenate(video_clips)

            self._check_memory_usage("视频合并后")

            # 添加字幕
            if subtitles and subtitle_style is not None:
                log_info("添加字幕...")
                if progress_callback:
                    progress_callback(75, 100, "添加字幕...")
                if low_memory_mode:
                    # 低内存模式下使用优化的字幕添加方法
                    video_with_subtitles = self._add_subtitles_to_video_optimized(
                        final_video, subtitles, subtitle_style
                    )
                else:
                    # 标准模式
                    video_with_subtitles = self._add_subtitles_to_video(
                        final_video, subtitles, subtitle_style
                    )
            else:
                video_with_subtitles = final_video

            self._check_memory_usage("字幕添加后")

            # 优化输出设置
            output_preset = 'ultrafast' if low_memory_mode else 'medium'
            threads = min(2, self.num_threads) if low_memory_mode else min(4, self.num_threads)
            
            # 输出视频
            log_info(f"输出视频到: {output_path} (预设: {output_preset}, 线程: {threads})")
            if progress_callback:
                progress_callback(85, 100, f"输出视频到: {output_path}")

            try:
                # 首先尝试标准输出，使用临时目录
                temp_audio_file = self.get_temp_file_path("audio", extension=".m4a")
                video_with_subtitles.write_videofile(
                    output_path,
                    codec='libx264',
                    audio_codec='aac',
                    temp_audiofile=temp_audio_file,
                    remove_temp=True,
                    verbose=False,
                    logger=None,
                    threads=threads,
                    preset=output_preset,
                    ffmpeg_params=['-crf', '28' if low_memory_mode else '23']  # 压缩率控制
                )

                # 验证输出文件
                if not os.path.exists(output_path):
                    raise Exception(f"输出文件未生成: {output_path}")

                file_size = os.path.getsize(output_path)
                if file_size < 1024:  # 小于1KB可能是损坏的文件
                    raise Exception(f"输出文件可能损坏，文件大小: {file_size} bytes")

                log_info(f"✅ 视频输出成功，文件大小: {file_size / (1024*1024):.2f} MB")

            except Exception as e:
                log_error(f"❌ 视频输出失败: {e}")
                error_msg = str(e).lower()

                # 检查是否是音频相关错误
                if 'audio' in error_msg or 'index' in error_msg and 'bounds' in error_msg:
                    log_info("🔄 检测到音频问题，尝试无音频输出...")
                    try:
                        # 移除音频后重试
                        video_no_audio = video_with_subtitles.without_audio()
                        video_no_audio.write_videofile(
                            output_path,
                            codec='libx264',
                            preset='ultrafast',
                            threads=1,
                            verbose=False,
                            logger=None
                        )
                        log_info(f"✅ 无音频模式输出成功")
                        video_no_audio.close()

                    except Exception as audio_error:
                        log_error(f"❌ 无音频模式也失败: {audio_error}")
                        # 继续尝试兼容模式
                        pass

                # 尝试使用更兼容的设置重新输出
                log_info("🔄 尝试使用兼容模式重新输出...")
                try:
                    backup_output = output_path.replace('.mp4', '_backup.mp4')
                    video_with_subtitles.write_videofile(
                        backup_output,
                        codec='libx264',
                        audio_codec='aac',
                        preset='ultrafast',
                        threads=1,
                        verbose=False,
                        logger=None
                    )

                    # 如果备份成功，替换原文件
                    if os.path.exists(backup_output) and os.path.getsize(backup_output) > 1024:
                        if os.path.exists(output_path):
                            os.remove(output_path)
                        os.rename(backup_output, output_path)
                        log_info(f"✅ 兼容模式输出成功")
                    else:
                        raise Exception("兼容模式输出也失败")

                except Exception as e2:
                    log_error(f"❌ 兼容模式输出也失败: {e2}")
                    raise Exception(f"视频输出完全失败: 原始错误={e}, 兼容模式错误={e2}")

            if progress_callback:
                progress_callback(95, 100, "视频输出完成，清理资源...")

            # 清理资源
            self._cleanup_video_resources(final_video, video_with_subtitles, video_clips)

            self._check_memory_usage("渲染完成后")

            return output_path

        except MemoryError as me:
            log_info(f"渲染视频时内存不足: {str(me)}")
            log_info("尝试紧急低内存渲染方法...")
            
            try:
                # 紧急低内存模式 - 使用更极端的设置
                self.force_memory_cleanup()  # 强制清理内存
                
                # 创建极小分辨率的视频
                emergency_clips = []
                for clip_item in video_clips:
                    # 提取实际的视频剪辑对象
                    if isinstance(clip_item, dict) and 'clip' in clip_item:
                        actual_clip = clip_item['clip']
                    else:
                        actual_clip = clip_item
                    emergency_clip = actual_clip.resize(0.5)  # 降低到50%分辨率
                    emergency_clips.append(emergency_clip)
                
                # 使用链式合并方法
                from moviepy.editor import concatenate_videoclips
                emergency_video = concatenate_videoclips(emergency_clips, method="chain")
                
                # 直接输出视频，不添加字幕，使用临时目录
                temp_audio_file = self.get_temp_file_path("emergency_audio", extension=".m4a")
                emergency_video.write_videofile(
                    output_path,
                    codec='libx264',
                    audio_codec='aac',
                    temp_audiofile=temp_audio_file,
                    remove_temp=True,
                    verbose=False,
                    logger=None,
                    threads=1,
                    preset='ultrafast',
                    ffmpeg_params=['-crf', '30']  # 高压缩率
                )
                
                return output_path
            except Exception as e2:
                log_info(f"紧急渲染失败: {str(e2)}")
                raise me  # 重新抛出原始内存错误
                
        except Exception as e:
            log_info(f"渲染最终视频失败: {str(e)}")
            # 尝试清理资源
            try:
                self._cleanup_video_resources(None, None, video_clips)
            except:
                pass
            raise

    def _safe_subclip(self, video_clip, start_time, end_time):
        """
        安全的视频剪切方法，避免音频索引越界
        """
        try:
            # 确保时间范围有效
            start_time = max(0, start_time)
            end_time = min(end_time, video_clip.duration)

            if start_time >= end_time:
                raise ValueError(f"无效的时间范围: start={start_time}, end={end_time}")

            # 首先尝试直接剪切（包含音频）
            try:
                clipped_video = video_clip.subclip(start_time, end_time)
                # 验证剪切结果
                if clipped_video.duration > 0:
                    return clipped_video
            except Exception as direct_error:
                log_info(f"直接剪切失败: {str(direct_error)}")

            # 如果直接剪切失败，尝试分别处理视频和音频
            try:
                # 先剪切视频部分（不包含音频）
                video_only = video_clip.without_audio()
                clipped_video = video_only.subclip(start_time, end_time)

                # 如果原视频有音频，尝试单独处理音频
                if video_clip.audio is not None:
                    try:
                        # 尝试剪切音频
                        clipped_audio = video_clip.audio.subclip(start_time, end_time)
                        clipped_video = clipped_video.set_audio(clipped_audio)
                    except Exception as audio_error:
                        log_info(f"音频剪切失败，保持无音频: {str(audio_error)}")
                        # 如果音频剪切失败，保持无音频状态

                return clipped_video

            except Exception as separate_error:
                log_info(f"分离处理也失败: {str(separate_error)}")
                raise

        except Exception as e:
            log_info(f"安全剪切完全失败: {str(e)}")
            raise

    def _memory_efficient_concatenate(self, video_clips):
        """
        内存友好的视频合并方法，针对大型视频进行了优化
        """
        try:
            from moviepy.editor import concatenate_videoclips
            import gc

            # 提取实际的视频剪辑对象（如果video_clips是字典列表）
            actual_clips = []
            for clip_item in video_clips:
                # 检查是否为None
                if clip_item is None:
                    log_info("跳过None片段")
                    continue
                    
                if isinstance(clip_item, dict) and 'clip' in clip_item:
                    # 如果是字典，提取其中的clip对象
                    if clip_item['clip'] is not None:
                        actual_clips.append(clip_item['clip'])
                    else:
                        log_info("跳过字典中clip为None的片段")
                else:
                    # 如果已经是clip对象，直接使用
                    actual_clips.append(clip_item)
            
            # 确保有有效的片段
            if not actual_clips:
                raise ValueError("没有有效的视频片段可以合并")
            
            # 检查总时长和片段数量，进行更严格的验证
            valid_clips = []
            for i, clip in enumerate(actual_clips):
                if clip is None:
                    log_info(f"跳过None片段 {i}")
                    continue

                if not hasattr(clip, 'duration'):
                    log_info(f"跳过无duration属性的片段 {i}")
                    continue

                try:
                    # 验证duration是否有效
                    duration = clip.duration
                    if duration is None or duration <= 0:
                        log_info(f"跳过无效duration的片段 {i}: {duration}")
                        continue

                    # 验证是否可以获取帧
                    if hasattr(clip, 'get_frame'):
                        try:
                            # 尝试获取第一帧来验证clip是否有效
                            test_frame = clip.get_frame(0)
                            if test_frame is None:
                                log_info(f"跳过无法获取帧的片段 {i}")
                                continue
                        except Exception as frame_error:
                            log_info(f"跳过获取帧失败的片段 {i}: {str(frame_error)}")
                            continue

                    valid_clips.append(clip)
                    log_info(f"片段 {i} 验证通过，时长: {duration:.2f}秒")

                except Exception as validation_error:
                    log_info(f"跳过验证失败的片段 {i}: {str(validation_error)}")
                    continue

            if not valid_clips:
                raise ValueError("所有片段都无效，无法合并")

            total_duration = sum(clip.duration for clip in valid_clips)
            log_info(f"合并 {len(valid_clips)} 个有效片段，总时长: {total_duration:.2f}秒")

            # 使用有效片段替换actual_clips
            actual_clips = valid_clips
            
            # 如果片段数量太多或总时长太长，尝试降低分辨率
            if len(actual_clips) > 20 or total_duration > 1800:  # 超过20个片段或30分钟
                log_info("视频片段数量或总时长较大，使用降低分辨率的合并策略")
                return self._concatenate_with_reduced_resolution(actual_clips)

            # 如果片段数量较少，使用标准方法
            if len(actual_clips) <= 3:
                log_info(f"使用标准合并方法 ({len(actual_clips)} 个片段)")
                try:
                    return concatenate_videoclips(actual_clips, method="compose")
                except Exception as e:
                    log_info(f"标准合并失败: {str(e)}，尝试使用chain方法")
                    return concatenate_videoclips(actual_clips, method="chain")

            # 对于大量片段，分批合并
            log_info(f"使用分批合并方法 ({len(actual_clips)} 个片段)")
            batch_size = 2  # 每批处理2个片段
            merged_clips = []
            
            # 强制垃圾回收
            gc.collect()

            # 分批处理
            for i in range(0, len(actual_clips), batch_size):
                batch = actual_clips[i:i+batch_size]
                if len(batch) == 1:
                    merged_clips.append(batch[0])
                else:
                    try:
                        log_info(f"合并批次 {i//batch_size + 1}/{(len(actual_clips)-1)//batch_size + 1}")
                        merged = concatenate_videoclips(batch, method="compose")
                        merged_clips.append(merged)
                        
                        # 每次批次合并后清理内存
                        gc.collect()
                    except Exception as batch_error:
                        log_info(f"批次合并失败，尝试使用chain方法: {str(batch_error)}")
                        try:
                            merged = concatenate_videoclips(batch, method="chain")
                            merged_clips.append(merged)
                        except:
                            # 如果还是失败，单独添加每个片段
                            for clip in batch:
                                merged_clips.append(clip)

            # 最终合并所有批次
            if len(merged_clips) == 1:
                return merged_clips[0]
            else:
                log_info(f"最终合并 {len(merged_clips)} 个批次")
                # 递归合并以减少同时处理的片段数
                if len(merged_clips) > 3:
                    mid = len(merged_clips) // 2
                    log_info(f"递归合并: 分为 {mid} 和 {len(merged_clips) - mid} 个片段")
                    first_half = self._memory_efficient_concatenate(merged_clips[:mid])
                    
                    # 清理内存
                    gc.collect()
                    
                    second_half = self._memory_efficient_concatenate(merged_clips[mid:])
                    
                    # 清理内存
                    gc.collect()
                    
                    return concatenate_videoclips([first_half, second_half], method="compose")
                else:
                    return concatenate_videoclips(merged_clips, method="compose")

        except Exception as e:
            log_info(f"内存友好合并失败: {str(e)}")
            try:
                # 尝试使用chain方法（最低内存消耗）
                log_info("尝试使用chain方法合并...")
                from moviepy.editor import concatenate_videoclips
                return concatenate_videoclips(actual_clips, method="chain")
            except Exception as e2:
                log_info(f"chain方法合并失败: {str(e2)}")
                raise Exception(f"视频合并失败: {str(e)}, {str(e2)}")
                
    def _concatenate_with_reduced_resolution(self, video_clips):
        """
        使用降低分辨率的方式合并视频，适用于大型视频
        """
        try:
            from moviepy.editor import concatenate_videoclips
            import gc
            
            log_info("降低视频分辨率以节省内存...")
            
            # 过滤None值
            valid_clips = [clip for clip in video_clips if clip is not None and hasattr(clip, 'duration')]
            if not valid_clips:
                raise ValueError("没有有效的视频片段可以合并")
            
            # 降低分辨率的比例，根据片段数量和总时长动态调整
            total_duration = sum(clip.duration for clip in valid_clips)
            if total_duration > 3600:  # 超过1小时
                scale_factor = 0.5
            elif total_duration > 1800:  # 超过30分钟
                scale_factor = 0.6
            else:
                scale_factor = 0.7
                
            log_info(f"使用缩放因子: {scale_factor}，处理 {len(valid_clips)} 个有效片段")
            
            # 降低分辨率
            resized_clips = []
            for i, clip in enumerate(valid_clips):
                try:
                    # 检查clip是否有效
                    if clip is None or not hasattr(clip, 'resize'):
                        log_info(f"跳过无效片段 {i}")
                        continue
                        
                    # 降低分辨率
                    resized_clip = clip.resize(scale_factor)
                    resized_clips.append(resized_clip)
                    
                    # 每处理5个片段强制垃圾回收
                    if (i + 1) % 5 == 0:
                        gc.collect()
                        
                except Exception as clip_error:
                    log_info(f"处理片段 {i} 失败: {str(clip_error)}，尝试使用原始片段")
                    if clip is not None:
                        resized_clips.append(clip)
            
            # 确保有有效的片段
            if not resized_clips:
                raise ValueError("处理后没有有效的视频片段可以合并")
            
            # 强制垃圾回收
            gc.collect()
            
            # 分批合并降低分辨率后的片段
            batch_size = 3
            merged_clips = []
            
            for i in range(0, len(resized_clips), batch_size):
                batch = resized_clips[i:i+batch_size]
                if len(batch) == 1:
                    merged_clips.append(batch[0])
                else:
                    try:
                        log_info(f"合并降低分辨率的批次 {i//batch_size + 1}/{(len(resized_clips)-1)//batch_size + 1}")
                        merged = concatenate_videoclips(batch, method="compose")
                        merged_clips.append(merged)
                    except Exception as batch_error:
                        log_info(f"批次合并失败，尝试使用chain方法: {str(batch_error)}")
                        merged = concatenate_videoclips(batch, method="chain")
                        merged_clips.append(merged)
                
                # 每次批次合并后清理内存
                gc.collect()
            
            # 最终合并
            if len(merged_clips) == 1:
                return merged_clips[0]
            else:
                log_info(f"最终合并 {len(merged_clips)} 个降低分辨率的批次")
                return concatenate_videoclips(merged_clips, method="chain")
                
        except Exception as e:
            log_info(f"降低分辨率合并失败: {str(e)}")
            # 最后的尝试 - 使用不太内存密集的方法
            log_info("尝试使用chain方法合并原始片段...")
            from moviepy.editor import concatenate_videoclips
            return concatenate_videoclips(video_clips, method="chain")

    def _check_memory_usage(self, stage=""):
        """检查内存使用情况"""
        try:
            import psutil
            import gc

            # 强制垃圾回收
            gc.collect()

            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024

            log_info(f"内存使用 ({stage}): {memory_mb:.1f} MB")

            # 如果内存使用超过阈值，发出警告
            if memory_mb > 2048:  # 2GB
                log_warning(f"⚠️ 内存使用较高: {memory_mb:.1f} MB")

        except ImportError:
            pass  # psutil未安装，跳过内存检查
        except Exception as e:
            log_info(f"内存检查失败: {str(e)}")

    def _cleanup_video_resources(self, final_video, video_with_subtitles, video_clips):
        """清理视频资源"""
        try:
            import gc

            # 清理主要视频对象
            if final_video:
                try:
                    final_video.close()
                except:
                    pass

            if video_with_subtitles and video_with_subtitles != final_video:
                try:
                    video_with_subtitles.close()
                except:
                    pass

            # 清理所有片段
            for clip_item in video_clips:
                try:
                    # 提取实际的视频剪辑对象
                    if isinstance(clip_item, dict) and 'clip' in clip_item:
                        actual_clip = clip_item['clip']
                        if hasattr(actual_clip, 'close'):
                            actual_clip.close()
                    elif hasattr(clip_item, 'close'):
                        clip_item.close()
                except:
                    pass

            # 强制垃圾回收
            gc.collect()

            log_info("视频资源清理完成")

        except Exception as e:
            log_info(f"资源清理失败: {str(e)}")

    def _add_subtitles_to_video_optimized(self, video, subtitles, style):
        """
        优化的字幕添加方法（支持ImageMagick替代方案）
        """
        try:
            # 首先尝试使用MoviePy的TextClip
            return self._add_subtitles_with_textclip(video, subtitles, style, optimized=True)
        except Exception as e:
            error_msg = str(e).lower()
            if 'imagemagick' in error_msg or 'textclip' in error_msg:
                log_warning(f"⚠️ ImageMagick不可用，使用替代方案: {e}")
                return self._add_subtitles_with_alternative(video, subtitles, style)
            else:
                log_info(f"字幕添加失败: {e}")
                raise

    def _add_subtitles_with_textclip(self, video, subtitles, style, optimized=False):
        """
        使用MoviePy TextClip添加字幕
        """
        try:
            from moviepy.editor import TextClip, CompositeVideoClip
            import gc

            # 默认字幕样式
            default_style = {
                'fontsize': 24,
                'color': 'white',
                'font': 'Arial-Bold',
                'stroke_color': 'black',
                'stroke_width': 2
            }

            if style:
                default_style.update(style)

            # 限制同时处理的字幕数量
            max_concurrent_subtitles = 10 if optimized else len(subtitles)
            subtitle_clips = []

            log_info(f"使用TextClip处理 {len(subtitles)} 个字幕...")

            for i, subtitle in enumerate(subtitles):
                try:
                    # 创建字幕文本片段
                    txt_clip = TextClip(
                        subtitle['text'],
                        fontsize=default_style['fontsize'],
                        color=default_style['color'],
                        font=default_style['font'],
                        stroke_color=default_style.get('stroke_color'),
                        stroke_width=default_style.get('stroke_width', 0)
                    ).set_start(subtitle['start']).set_duration(subtitle['duration'])

                    # 设置字幕位置（底部居中，距离底部50像素）
                    txt_clip = txt_clip.set_position(('center', video.h - 50))

                    subtitle_clips.append(txt_clip)

                    # 定期清理内存（仅在优化模式下）
                    if optimized and (i + 1) % max_concurrent_subtitles == 0:
                        gc.collect()

                    if (i + 1) % 50 == 0:
                        log_info(f"字幕处理进度: {i + 1}/{len(subtitles)}")

                except Exception as e:
                    log_info(f"创建字幕片段失败 {i}: {str(e)}")
                    continue

            if subtitle_clips:
                log_info("合成视频和字幕...")
                # 分批合成以减少内存使用
                if optimized and len(subtitle_clips) > max_concurrent_subtitles:
                    # 分批处理字幕
                    all_clips = [video]
                    for i in range(0, len(subtitle_clips), max_concurrent_subtitles):
                        batch = subtitle_clips[i:i + max_concurrent_subtitles]
                        all_clips.extend(batch)
                else:
                    all_clips = [video] + subtitle_clips

                final_video = CompositeVideoClip(all_clips)

                # 清理字幕片段
                for clip in subtitle_clips:
                    try:
                        clip.close()
                    except:
                        pass

                return final_video
            else:
                return video

        except Exception as e:
            log_info(f"TextClip字幕添加失败: {str(e)}")
            raise

    def _add_subtitles_with_alternative(self, video, subtitles, style):
        """
        使用替代方案添加字幕（不依赖ImageMagick）
        """
        try:
            from moviepy.editor import VideoClip, CompositeVideoClip
            from PIL import Image, ImageDraw, ImageFont
            import numpy as np

            log_info(f"使用替代方案处理 {len(subtitles)} 个字幕...")

            # 默认字幕样式
            default_style = {
                'fontsize': 24,
                'color': (255, 255, 255),  # 白色
                'stroke_color': (0, 0, 0),  # 黑色
                'stroke_width': 2
            }

            if style:
                # 转换颜色格式
                if 'color' in style and isinstance(style['color'], str):
                    if style['color'] == 'white':
                        style['color'] = (255, 255, 255)
                    elif style['color'] == 'black':
                        style['color'] = (0, 0, 0)
                default_style.update(style)

            video_size = video.size
            subtitle_clips = []

            for i, subtitle in enumerate(subtitles):
                try:
                    # 创建字幕片段
                    clip = self._create_subtitle_clip_with_pil(
                        subtitle['text'],
                        subtitle['start'],
                        subtitle['duration'],
                        video_size,
                        default_style
                    )

                    if clip:
                        subtitle_clips.append(clip)

                    if (i + 1) % 20 == 0:
                        log_info(f"替代字幕处理进度: {i + 1}/{len(subtitles)}")

                except Exception as e:
                    log_info(f"创建替代字幕片段失败 {i}: {str(e)}")
                    continue

            if subtitle_clips:
                log_info("合成视频和替代字幕...")
                final_video = CompositeVideoClip([video] + subtitle_clips)

                # 清理字幕片段
                for clip in subtitle_clips:
                    try:
                        clip.close()
                    except:
                        pass

                return final_video
            else:
                return video

        except Exception as e:
            log_info(f"替代字幕方案失败: {str(e)}")
            log_warning(f"⚠️ 字幕功能不可用，返回原视频")
            return video

    def _create_subtitle_clip_with_pil(self, text, start_time, duration, video_size, style):
        """
        使用PIL创建字幕片段
        """
        try:
            from moviepy.editor import VideoClip
            from PIL import Image, ImageDraw, ImageFont
            import numpy as np

            width, height = video_size
            fontsize = style.get('fontsize', 24)
            color = style.get('color', (255, 255, 255))
            stroke_color = style.get('stroke_color', (0, 0, 0))
            stroke_width = style.get('stroke_width', 2)

            def make_frame(t):
                # 创建透明背景图像
                img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
                draw = ImageDraw.Draw(img)

                # 尝试加载字体
                try:
                    # 尝试使用系统字体
                    font_paths = [
                        "C:/Windows/Fonts/arial.ttf",
                        "C:/Windows/Fonts/simhei.ttf",  # 黑体
                        "C:/Windows/Fonts/simsun.ttc",  # 宋体
                        "/System/Library/Fonts/Arial.ttf",  # macOS
                        "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"  # Linux
                    ]

                    font = None
                    for font_path in font_paths:
                        try:
                            if os.path.exists(font_path):
                                font = ImageFont.truetype(font_path, fontsize)
                                break
                        except:
                            continue

                    if font is None:
                        font = ImageFont.load_default()

                except Exception:
                    font = ImageFont.load_default()

                # 获取文本尺寸
                try:
                    bbox = draw.textbbox((0, 0), text, font=font)
                    text_width = bbox[2] - bbox[0]
                    text_height = bbox[3] - bbox[1]
                except:
                    # 回退方法
                    text_width = len(text) * fontsize // 2
                    text_height = fontsize

                # 计算位置（底部居中）
                x = (width - text_width) // 2
                y = height - text_height - 50  # 距离底部50像素

                # 绘制描边
                if stroke_width > 0:
                    for dx in range(-stroke_width, stroke_width + 1):
                        for dy in range(-stroke_width, stroke_width + 1):
                            if dx != 0 or dy != 0:
                                draw.text((x + dx, y + dy), text, font=font,
                                        fill=stroke_color + (255,))

                # 绘制主文本
                draw.text((x, y), text, font=font, fill=color + (255,))

                # 转换为numpy数组并返回RGB格式
                img_array = np.array(img)
                return img_array[:, :, :3]  # 只返回RGB通道

            # 创建视频片段
            clip = VideoClip(make_frame, duration=duration)
            clip = clip.set_start(start_time)

            return clip

        except Exception as e:
            log_info(f"使用PIL创建字幕片段失败: {e}")
            return None

    def _add_subtitles_to_video(self, video, subtitles, style):
        """
        为视频添加字幕（支持ImageMagick替代方案）
        :param video: 视频对象
        :param subtitles: 字幕列表
        :param style: 字幕样式
        :return: 带字幕的视频
        """
        try:
            # 首先尝试使用MoviePy的TextClip
            return self._add_subtitles_with_textclip(video, subtitles, style, optimized=False)
        except Exception as e:
            error_msg = str(e).lower()
            if 'imagemagick' in error_msg or 'textclip' in error_msg:
                log_warning(f"⚠️ ImageMagick不可用，使用替代方案: {e}")
                return self._add_subtitles_with_alternative(video, subtitles, style)
            else:
                log_info(f"添加字幕失败: {str(e)}")
                return video

    def video_fusion(self, video_files, output_path, transition_type='fade',
                    num_outputs=3, sticker_files=None, progress_callback=None):
        """
        视频融合功能 - 将多个视频融合生成不同的组合视频
        :param video_files: 视频文件列表
        :param output_path: 输出路径模板
        :param transition_type: 转场类型 ('fade', 'slide', 'zoom', 'none')
        :param num_outputs: 生成视频的个数
        :param sticker_files: 贴纸文件列表（可选）
        :param progress_callback: 进度回调函数
        :return: 生成的视频文件路径列表
        """
        try:
            from moviepy.editor import VideoFileClip, concatenate_videoclips, CompositeVideoClip, ImageClip
            import random
            import time

            log_info(f"开始视频融合: {len(video_files)} 个视频 → {num_outputs} 个融合视频")

            if progress_callback:
                progress_callback(5, 100, "初始化视频融合...")

            # 加载所有视频
            video_clips = []
            for i, video_file in enumerate(video_files):
                try:
                    if progress_callback:
                        progress_callback(5 + (i * 15 // len(video_files)), 100, f"加载视频 {i+1}/{len(video_files)}...")

                    clip = VideoFileClip(video_file)
                    video_clips.append({
                        'clip': clip,
                        'path': video_file,
                        'duration': clip.duration
                    })
                    log_info(f"已加载视频: {os.path.basename(video_file)} (时长: {clip.duration:.2f}秒)")
                except Exception as e:
                    log_warning(f"加载视频失败 {video_file}: {str(e)}")
                    continue

            if len(video_clips) < 2:
                raise ValueError("至少需要2个有效的视频文件进行融合")

            # 加载贴纸（如果有）
            sticker_clips = []
            if sticker_files:
                for sticker_file in sticker_files:
                    try:
                        sticker = ImageClip(sticker_file, duration=1)  # 默认1秒显示时间
                        sticker_clips.append(sticker)
                        log_info(f"已加载贴纸: {os.path.basename(sticker_file)}")
                    except Exception as e:
                        log_warning(f"加载贴纸失败 {sticker_file}: {str(e)}")

            # 生成多个融合视频
            output_paths = []
            for i in range(num_outputs):
                if progress_callback:
                    progress_callback(20 + (i * 70 // num_outputs), 100, f"生成融合视频 {i+1}/{num_outputs}...")

                # 为每个输出视频生成唯一的文件名
                if num_outputs == 1:
                    current_output_path = output_path
                else:
                    base_name, ext = os.path.splitext(output_path)
                    current_output_path = f"{base_name}_融合_{i+1:02d}{ext}"

                # 生成单个融合视频
                result_path = self._create_fusion_video(
                    video_clips, current_output_path, transition_type,
                    sticker_clips, i, progress_callback
                )

                if result_path:
                    output_paths.append(result_path)
                    log_info(f"融合视频 {i+1} 生成完成: {os.path.basename(result_path)}")

            # 清理资源
            for video_data in video_clips:
                try:
                    video_data['clip'].close()
                except:
                    pass

            for sticker in sticker_clips:
                try:
                    sticker.close()
                except:
                    pass

            if progress_callback:
                progress_callback(100, 100, "视频融合完成！")

            log_info(f"视频融合完成，生成了 {len(output_paths)} 个视频")
            return output_paths

        except Exception as e:
            log_error(f"视频融合失败: {str(e)}")
            raise

    def _create_fusion_video(self, video_clips, output_path, transition_type,
                           sticker_clips, video_index, progress_callback):
        """
        创建单个融合视频
        :param video_clips: 视频片段列表
        :param output_path: 输出路径
        :param transition_type: 转场类型
        :param sticker_clips: 贴纸片段列表
        :param video_index: 视频索引
        :param progress_callback: 进度回调
        :return: 输出视频路径
        """
        try:
            from moviepy.editor import concatenate_videoclips, CompositeVideoClip
            import random

            # 设置随机种子，确保每个视频都不同
            random.seed(int(time.time()) + video_index * 1000)

            # 随机选择视频片段进行融合
            selected_clips = []

            # 确定融合策略：随机选择2-4个视频片段
            num_clips_to_use = min(random.randint(2, 4), len(video_clips))
            selected_video_data = random.sample(video_clips, num_clips_to_use)

            log_info(f"融合视频 {video_index + 1}: 选择了 {num_clips_to_use} 个视频片段")

            # 为每个选中的视频随机选择片段
            for video_data in selected_video_data:
                clip = video_data['clip']
                duration = video_data['duration']

                # 随机选择片段的开始时间和长度
                max_segment_duration = min(10, duration * 0.8)  # 最多10秒或视频的80%
                segment_duration = random.uniform(3, max_segment_duration)  # 3-10秒的片段

                if duration > segment_duration:
                    start_time = random.uniform(0, duration - segment_duration)
                    segment = clip.subclip(start_time, start_time + segment_duration)
                else:
                    segment = clip

                selected_clips.append(segment)

            # 随机打乱片段顺序
            random.shuffle(selected_clips)

            # 添加转场效果
            if transition_type != 'none':
                selected_clips = self._add_transitions_for_fusion(selected_clips, transition_type)

            # 合并视频片段
            final_video = concatenate_videoclips(selected_clips, method="compose")

            # 添加贴纸（如果有）
            if sticker_clips and random.choice([True, False]):  # 50%概率添加贴纸
                final_video = self._add_stickers_to_video(final_video, sticker_clips, video_index)

            # 保存视频
            log_info(f"保存融合视频: {os.path.basename(output_path)}")

            # 使用优化的编码参数（针对大量小视频优化）
            encoding_params = self._get_optimized_encoding_params(len(video_clips))
            success = self.safe_write_videofile_enhanced(
                final_video, output_path, **encoding_params
            )

            if not success:
                raise Exception("视频写入失败")

            # 清理资源
            final_video.close()
            for clip in selected_clips:
                try:
                    clip.close()
                except:
                    pass

            return output_path

        except Exception as e:
            log_error(f"创建融合视频失败: {str(e)}")
            return None

    def _add_transitions_for_fusion(self, clips, transition_type):
        """为融合视频添加转场效果"""
        try:
            if len(clips) <= 1:
                return clips

            transition_duration = 0.5
            clips_with_transitions = []

            for i, clip in enumerate(clips):
                if i == 0:
                    # 第一个片段：只添加淡出
                    if transition_type == 'fade':
                        clip = clip.fadeout(transition_duration)
                    clips_with_transitions.append(clip)
                elif i == len(clips) - 1:
                    # 最后一个片段：只添加淡入
                    if transition_type == 'fade':
                        clip = clip.fadein(transition_duration)
                    clips_with_transitions.append(clip)
                else:
                    # 中间片段：添加淡入淡出
                    if transition_type == 'fade':
                        clip = clip.fadein(transition_duration).fadeout(transition_duration)
                    clips_with_transitions.append(clip)

            return clips_with_transitions

        except Exception as e:
            log_warning(f"添加转场效果失败: {str(e)}")
            return clips

    def _add_stickers_to_video(self, video, sticker_clips, video_index):
        """为视频添加贴纸"""
        try:
            from moviepy.editor import CompositeVideoClip
            import random

            if not sticker_clips:
                return video

            # 随机选择1-2个贴纸
            num_stickers = min(random.randint(1, 2), len(sticker_clips))
            selected_stickers = random.sample(sticker_clips, num_stickers)

            composite_clips = [video]

            for i, sticker in enumerate(selected_stickers):
                # 随机设置贴纸的位置、大小和显示时间
                video_w, video_h = video.size
                sticker_size = random.uniform(0.1, 0.2)  # 贴纸大小为视频的10%-20%

                # 调整贴纸大小
                sticker_w = int(video_w * sticker_size)
                sticker_h = int(video_h * sticker_size)
                sticker_resized = sticker.resize((sticker_w, sticker_h))

                # 随机位置
                pos_x = random.randint(0, video_w - sticker_w)
                pos_y = random.randint(0, video_h - sticker_h)

                # 随机显示时间
                start_time = random.uniform(0, max(0, video.duration - 3))
                duration = random.uniform(2, min(3, video.duration - start_time))

                # 设置贴纸属性
                sticker_positioned = (sticker_resized
                                    .set_position((pos_x, pos_y))
                                    .set_start(start_time)
                                    .set_duration(duration))

                composite_clips.append(sticker_positioned)

            # 合成视频
            return CompositeVideoClip(composite_clips)

        except Exception as e:
            log_warning(f"添加贴纸失败: {str(e)}")
            return video

    def video_fusion_optimized(self, video_files, output_folder, transition_type='fade',
                              num_outputs=3, sticker_files=None, segment_duration=8,
                              total_duration=60, filename_prefix="融合视频",
                              sticker_position="随机", sticker_size=0.15, sticker_opacity=0.8,
                              trim_start=0, trim_end=0, trim_mode="全部裁剪",
                              min_duration=5, progress_callback=None):
        """
        优化的视频融合功能 - 不同顺序拼接完整视频，支持头尾裁剪
        增强了内存管理和错误处理机制，专门针对大量小视频进行优化
        :param video_files: 视频文件列表
        :param output_folder: 输出文件夹
        :param transition_type: 转场类型 ('fade', 'slide', 'zoom', 'none')
        :param num_outputs: 生成视频的个数
        :param sticker_files: 贴纸文件列表（可选）
        :param segment_duration: 保留参数（兼容性）
        :param total_duration: 保留参数（兼容性）
        :param filename_prefix: 文件名前缀
        :param sticker_position: 贴纸位置 ('随机', '左上角', '右上角', '左下角', '右下角', '中心')
        :param sticker_size: 贴纸大小比例 (0.05-0.3)
        :param sticker_opacity: 贴纸透明度 (0.1-1.0)
        :param trim_start: 开头裁剪时长（秒）
        :param trim_end: 结尾裁剪时长（秒）
        :param trim_mode: 裁剪模式 ('全部裁剪', '智能保留首尾')
        :param min_duration: 最小保留时长（秒）
        :param progress_callback: 进度回调函数
        :return: 生成的视频文件路径列表
        """
        try:
            from moviepy.editor import VideoFileClip, concatenate_videoclips, CompositeVideoClip, ImageClip
            import random
            import time
            import gc

            log_info(f"开始优化视频融合: {len(video_files)} 个视频 → {num_outputs} 个不同顺序的融合视频")
            if trim_start > 0 or trim_end > 0:
                log_info(f"头尾裁剪模式: {trim_mode}, 开头裁剪: {trim_start}秒, 结尾裁剪: {trim_end}秒")
            else:
                log_info("模式: 完整视频不同顺序拼接（不裁剪）")

            # 检查内存使用情况
            self._check_memory_usage("视频融合开始前")

            # 针对大量小视频的特殊处理
            if len(video_files) > 20:
                log_info(f"检测到大量视频文件({len(video_files)}个)，启用内存优化模式")
                return self._video_fusion_batch_mode(
                    video_files, output_folder, transition_type, num_outputs,
                    sticker_files, filename_prefix, sticker_position,
                    sticker_size, sticker_opacity, trim_start, trim_end,
                    trim_mode, min_duration, progress_callback
                )

            if progress_callback:
                progress_callback(5, 100, "初始化视频融合...")

            # 确保输出文件夹存在
            if not os.path.exists(output_folder):
                os.makedirs(output_folder)

            # 预验证视频文件
            if progress_callback:
                progress_callback(8, 100, "验证视频文件...")

            valid_video_files = self._prevalidate_fusion_videos(video_files, progress_callback)
            if len(valid_video_files) < 2:
                raise ValueError(f"有效视频文件不足，需要至少2个，实际: {len(valid_video_files)}")

            # 加载所有视频并预处理（包含头尾裁剪）
            video_clips = []
            total_duration_actual = 0
            for i, video_file in enumerate(valid_video_files):
                try:
                    if progress_callback:
                        progress_callback(15 + (i * 25 // len(valid_video_files)), 100, f"加载视频 {i+1}/{len(valid_video_files)}...")

                    clip = VideoFileClip(video_file)
                    original_duration = clip.duration

                    # 应用头尾裁剪
                    trimmed_clip = self._apply_video_trimming(
                        clip, i, len(valid_video_files), trim_start, trim_end,
                        trim_mode, min_duration, video_file
                    )

                    video_clips.append({
                        'clip': trimmed_clip,
                        'path': video_file,
                        'duration': trimmed_clip.duration,
                        'original_duration': original_duration,
                        'name': os.path.splitext(os.path.basename(video_file))[0]
                    })
                    total_duration_actual += trimmed_clip.duration

                    if trimmed_clip.duration != original_duration:
                        log_info(f"已加载并裁剪视频: {os.path.basename(video_file)} (原时长: {original_duration:.2f}秒 → 裁剪后: {trimmed_clip.duration:.2f}秒)")
                    else:
                        log_info(f"已加载视频: {os.path.basename(video_file)} (时长: {clip.duration:.2f}秒)")

                except Exception as e:
                    log_warning(f"加载视频失败 {video_file}: {str(e)}")
                    continue

            if len(video_clips) < 2:
                raise ValueError("至少需要2个有效的视频文件进行融合")

            log_info(f"所有视频总时长: {total_duration_actual:.2f}秒")

            # 加载贴纸（如果有）
            sticker_clips = []
            if sticker_files:
                for sticker_file in sticker_files:
                    try:
                        sticker = ImageClip(sticker_file)
                        sticker_clips.append({
                            'clip': sticker,
                            'path': sticker_file,
                            'name': os.path.splitext(os.path.basename(sticker_file))[0]
                        })
                        log_info(f"已加载贴纸: {os.path.basename(sticker_file)}")
                    except Exception as e:
                        log_warning(f"加载贴纸失败 {sticker_file}: {str(e)}")

            # 生成多个融合视频
            output_paths = []
            for i in range(num_outputs):
                if progress_callback:
                    progress_callback(20 + (i * 70 // num_outputs), 100, f"生成融合视频 {i+1}/{num_outputs}...")

                # 生成输出文件名
                output_filename = f"{filename_prefix}_{i+1:03d}.mp4"
                output_path = os.path.join(output_folder, output_filename)

                # 生成单个融合视频（不同顺序拼接）
                result_path = self._create_fusion_video_by_order(
                    video_clips, output_path, transition_type,
                    sticker_clips, sticker_position, sticker_size, sticker_opacity,
                    i, progress_callback
                )

                if result_path:
                    output_paths.append(result_path)
                    log_info(f"融合视频 {i+1} 生成完成: {os.path.basename(result_path)}")

            # 清理资源
            for video_data in video_clips:
                try:
                    video_data['clip'].close()
                except:
                    pass

            for sticker_data in sticker_clips:
                try:
                    sticker_data['clip'].close()
                except:
                    pass

            if progress_callback:
                progress_callback(100, 100, "视频融合完成！")

            log_info(f"优化视频融合完成，生成了 {len(output_paths)} 个视频")
            return output_paths

        except Exception as e:
            log_error(f"优化视频融合失败: {str(e)}")
            raise

    def _apply_video_trimming(self, clip, video_index, total_videos, trim_start, trim_end,
                             trim_mode, min_duration, video_file):
        """
        应用视频头尾裁剪
        :param clip: 视频片段
        :param video_index: 视频索引（从0开始）
        :param total_videos: 总视频数量
        :param trim_start: 开头裁剪时长（秒）
        :param trim_end: 结尾裁剪时长（秒）
        :param trim_mode: 裁剪模式 ('全部裁剪', '智能保留首尾')
        :param min_duration: 最小保留时长（秒）
        :param video_file: 视频文件路径（用于日志）
        :return: 裁剪后的视频片段
        """
        try:
            original_duration = clip.duration

            # 如果没有设置裁剪，直接返回原片段
            if trim_start <= 0 and trim_end <= 0:
                return clip

            # 根据裁剪模式决定是否需要裁剪
            should_trim_start = trim_start > 0
            should_trim_end = trim_end > 0

            if trim_mode == "智能保留首尾":
                # 智能保留模式：保留第一个视频的开头和最后一个视频的结尾
                if video_index == 0:
                    # 第一个视频：不裁剪开头，只裁剪结尾
                    should_trim_start = False
                    log_info(f"智能保留模式: 保留第一个视频 {os.path.basename(video_file)} 的开头")
                elif video_index == total_videos - 1:
                    # 最后一个视频：不裁剪结尾，只裁剪开头
                    should_trim_end = False
                    log_info(f"智能保留模式: 保留最后一个视频 {os.path.basename(video_file)} 的结尾")
                # 中间视频：正常裁剪开头和结尾

            # 计算裁剪后的开始和结束时间
            start_time = trim_start if should_trim_start else 0
            end_time = original_duration - (trim_end if should_trim_end else 0)

            # 确保裁剪后的时长不小于最小保留时长
            trimmed_duration = end_time - start_time
            if trimmed_duration < min_duration:
                log_warning(f"视频 {os.path.basename(video_file)} 裁剪后时长过短 ({trimmed_duration:.2f}秒)，调整为最小时长 {min_duration}秒")
                # 重新计算，优先保证最小时长
                if original_duration <= min_duration:
                    # 如果原视频就很短，不进行裁剪
                    return clip
                else:
                    # 调整裁剪参数以保证最小时长
                    available_trim = original_duration - min_duration
                    if should_trim_start and should_trim_end:
                        # 按比例分配裁剪时长
                        total_trim = trim_start + trim_end
                        start_time = min(trim_start, available_trim * (trim_start / total_trim))
                        end_time = original_duration - min(trim_end, available_trim * (trim_end / total_trim))
                    elif should_trim_start:
                        start_time = min(trim_start, available_trim)
                        end_time = original_duration
                    elif should_trim_end:
                        start_time = 0
                        end_time = original_duration - min(trim_end, available_trim)

            # 应用裁剪
            if start_time > 0 or end_time < original_duration:
                trimmed_clip = clip.subclip(start_time, end_time)
                log_info(f"裁剪视频 {os.path.basename(video_file)}: {start_time:.2f}s - {end_time:.2f}s (时长: {trimmed_clip.duration:.2f}s)")
                return trimmed_clip
            else:
                return clip

        except Exception as e:
            log_warning(f"视频裁剪失败 {os.path.basename(video_file)}: {str(e)}，使用原视频")
            return clip

    def _create_fusion_video_by_order(self, video_clips, output_path, transition_type,
                                     sticker_clips, sticker_position, sticker_size, sticker_opacity,
                                     video_index, progress_callback):
        """
        创建单个融合视频 - 按不同顺序拼接完整视频（不裁剪）
        """
        try:
            from moviepy.editor import concatenate_videoclips, CompositeVideoClip
            import random
            import itertools

            # 设置随机种子，确保每个视频都不同
            random.seed(int(time.time()) + video_index * 1000)

            log_info(f"开始生成融合视频 {video_index + 1}: {os.path.basename(output_path)}")

            # 生成不同的视频顺序
            video_order = self._generate_unique_video_order(video_clips, video_index)
            log_info(f"视频顺序: {' → '.join([v['name'] for v in video_order])}")

            # 使用完整视频按顺序拼接
            selected_clips = []
            total_duration_actual = 0

            for i, video_data in enumerate(video_order):
                clip = video_data['clip']
                clip_duration = video_data['duration']

                # 使用完整视频，不进行裁剪
                selected_clips.append(clip)
                total_duration_actual += clip_duration

                log_info(f"视频 {i+1}: {video_data['name']}, 时长: {clip_duration:.2f}秒")

            if not selected_clips:
                raise ValueError("没有生成任何有效的视频片段")

            log_info(f"融合视频总时长: {total_duration_actual:.2f}秒")

            # 添加转场效果
            if transition_type != 'none':
                selected_clips = self._add_transitions_for_fusion(selected_clips, transition_type)

            # 合并视频片段
            final_video = concatenate_videoclips(selected_clips, method="compose")

            # 添加贴纸（如果有）
            if sticker_clips:
                final_video = self._add_stickers_to_video_optimized(
                    final_video, sticker_clips, sticker_position,
                    sticker_size, sticker_opacity, video_index
                )

            # 保存视频
            log_info(f"保存融合视频: {os.path.basename(output_path)}")

            # 使用优化的编码参数（针对大量小视频优化）
            encoding_params = self._get_optimized_encoding_params(len(selected_clips))
            success = self.safe_write_videofile_enhanced(
                final_video, output_path, **encoding_params
            )

            if not success:
                raise Exception("视频写入失败")

            # 清理资源
            final_video.close()
            for clip in selected_clips:
                try:
                    clip.close()
                except:
                    pass

            return output_path

        except Exception as e:
            log_error(f"创建优化融合视频失败: {str(e)}")
            return None

    def _add_stickers_to_video_optimized(self, video, sticker_clips, position,
                                       size_ratio, opacity, video_index):
        """为视频添加优化的贴纸 - 支持位置、大小、透明度调整"""
        try:
            from moviepy.editor import CompositeVideoClip
            import random

            if not sticker_clips:
                return video

            # 设置随机种子
            random.seed(int(time.time()) + video_index * 100)

            # 随机选择1-2个贴纸
            num_stickers = min(random.randint(1, 2), len(sticker_clips))
            selected_stickers = random.sample(sticker_clips, num_stickers)

            composite_clips = [video]
            video_w, video_h = video.size

            for i, sticker_data in enumerate(selected_stickers):
                sticker = sticker_data['clip']

                # 计算贴纸大小
                sticker_w = int(video_w * size_ratio)
                sticker_h = int(video_h * size_ratio)

                # 调整贴纸大小
                sticker_resized = sticker.resize((sticker_w, sticker_h))

                # 设置透明度
                if hasattr(sticker_resized, 'set_opacity'):
                    sticker_resized = sticker_resized.set_opacity(opacity)

                # 计算位置
                pos_x, pos_y = self._calculate_sticker_position(
                    position, video_w, video_h, sticker_w, sticker_h, i
                )

                # 随机显示时间
                start_time = random.uniform(0, max(0, video.duration - 5))
                duration = random.uniform(3, min(8, video.duration - start_time))

                # 设置贴纸属性
                sticker_positioned = (sticker_resized
                                    .set_position((pos_x, pos_y))
                                    .set_start(start_time)
                                    .set_duration(duration))

                composite_clips.append(sticker_positioned)

                log_info(f"添加贴纸 {i+1}: 位置({pos_x}, {pos_y}), 大小({sticker_w}x{sticker_h}), 透明度{opacity:.1%}")

            # 合成视频
            return CompositeVideoClip(composite_clips)

        except Exception as e:
            log_warning(f"添加优化贴纸失败: {str(e)}")
            return video

    def _video_fusion_batch_mode(self, video_files, output_folder, transition_type, num_outputs,
                                sticker_files, filename_prefix, sticker_position,
                                sticker_size, sticker_opacity, trim_start, trim_end,
                                trim_mode, min_duration, progress_callback):
        """
        批处理模式的视频融合 - 专门处理大量小视频
        通过分批加载和处理来避免内存溢出
        """
        try:
            import random
            import time
            import gc
            from moviepy.editor import VideoFileClip, ImageClip

            log_info("启用批处理模式处理大量视频文件")

            # 预验证所有视频文件
            if progress_callback:
                progress_callback(5, 100, "验证视频文件...")

            valid_video_files = self._validate_video_files_batch(video_files, progress_callback)
            if len(valid_video_files) < 2:
                raise ValueError(f"有效视频文件不足，需要至少2个，实际有效: {len(valid_video_files)}")

            log_info(f"验证完成，有效视频文件: {len(valid_video_files)}/{len(video_files)}")

            # 预处理贴纸文件
            sticker_clips = []
            if sticker_files:
                if progress_callback:
                    progress_callback(15, 100, "预处理贴纸文件...")
                sticker_clips = self._preprocess_stickers_batch(sticker_files)

            # 分批生成融合视频
            result_paths = []
            batch_size = min(8, len(valid_video_files))  # 每批最多8个视频

            for output_index in range(num_outputs):
                if progress_callback:
                    base_progress = 20 + (output_index / num_outputs) * 70
                    progress_callback(int(base_progress), 100, f"生成融合视频 {output_index + 1}/{num_outputs}...")

                try:
                    # 为每个输出视频创建独立的处理
                    output_path = self._create_fusion_video_batch(
                        valid_video_files, output_folder, transition_type,
                        sticker_clips, output_index, filename_prefix,
                        sticker_position, sticker_size, sticker_opacity,
                        trim_start, trim_end, trim_mode, min_duration,
                        batch_size, progress_callback
                    )

                    if output_path and os.path.exists(output_path):
                        result_paths.append(output_path)
                        log_info(f"✅ 融合视频 {output_index + 1} 生成成功: {os.path.basename(output_path)}")
                    else:
                        log_warning(f"❌ 融合视频 {output_index + 1} 生成失败")

                    # 强制垃圾回收
                    gc.collect()

                except Exception as e:
                    log_error(f"生成融合视频 {output_index + 1} 时出错: {str(e)}")
                    continue

            # 清理贴纸资源
            for sticker in sticker_clips:
                try:
                    sticker.close()
                except:
                    pass

            log_info(f"批处理模式完成，成功生成 {len(result_paths)}/{num_outputs} 个融合视频")
            return result_paths

        except Exception as e:
            log_error(f"批处理模式视频融合失败: {str(e)}")
            return []

    def _validate_video_files_batch(self, video_files, progress_callback=None):
        """
        批量验证视频文件的有效性
        """
        try:
            from moviepy.editor import VideoFileClip
            import gc

            valid_files = []
            total_files = len(video_files)

            for i, video_file in enumerate(video_files):
                try:
                    if progress_callback and i % 5 == 0:  # 每5个文件更新一次进度
                        progress = 5 + int((i / total_files) * 10)
                        progress_callback(progress, 100, f"验证视频文件 {i+1}/{total_files}...")

                    # 基本文件检查
                    if not os.path.exists(video_file):
                        log_warning(f"视频文件不存在: {video_file}")
                        continue

                    file_size = os.path.getsize(video_file)
                    if file_size < 1024:  # 小于1KB的文件可能损坏
                        log_warning(f"视频文件过小，可能损坏: {video_file} ({file_size} bytes)")
                        continue

                    # 尝试加载视频文件进行验证
                    try:
                        test_clip = VideoFileClip(video_file)

                        # 检查基本属性
                        if test_clip.duration is None or test_clip.duration <= 0:
                            log_warning(f"视频文件时长无效: {video_file}")
                            test_clip.close()
                            continue

                        if test_clip.size is None or test_clip.size[0] <= 0 or test_clip.size[1] <= 0:
                            log_warning(f"视频文件尺寸无效: {video_file}")
                            test_clip.close()
                            continue

                        # 尝试获取第一帧
                        try:
                            first_frame = test_clip.get_frame(0)
                            if first_frame is None:
                                log_warning(f"无法获取视频帧: {video_file}")
                                test_clip.close()
                                continue
                        except Exception as frame_error:
                            log_warning(f"获取视频帧失败: {video_file} - {str(frame_error)}")
                            test_clip.close()
                            continue

                        # 验证通过
                        valid_files.append(video_file)
                        test_clip.close()

                    except Exception as clip_error:
                        log_warning(f"视频文件加载失败: {video_file} - {str(clip_error)}")
                        continue

                    # 每处理10个文件进行一次垃圾回收
                    if i % 10 == 0:
                        gc.collect()

                except Exception as e:
                    log_warning(f"验证视频文件时出错: {video_file} - {str(e)}")
                    continue

            log_info(f"视频文件验证完成: {len(valid_files)}/{total_files} 个文件有效")
            return valid_files

        except Exception as e:
            log_error(f"批量验证视频文件失败: {str(e)}")
            return video_files  # 如果验证失败，返回原始列表

    def _preprocess_stickers_batch(self, sticker_files):
        """
        批量预处理贴纸文件
        """
        try:
            from moviepy.editor import ImageClip
            import gc

            sticker_clips = []

            for sticker_file in sticker_files:
                try:
                    if not os.path.exists(sticker_file):
                        log_warning(f"贴纸文件不存在: {sticker_file}")
                        continue

                    # 检查文件大小
                    file_size = os.path.getsize(sticker_file)
                    if file_size < 100:  # 小于100字节的图片文件可能损坏
                        log_warning(f"贴纸文件过小: {sticker_file}")
                        continue

                    # 加载贴纸
                    sticker = ImageClip(sticker_file)

                    # 验证贴纸属性
                    if sticker.size is None or sticker.size[0] <= 0 or sticker.size[1] <= 0:
                        log_warning(f"贴纸尺寸无效: {sticker_file}")
                        sticker.close()
                        continue

                    sticker_clips.append(sticker)
                    log_info(f"贴纸加载成功: {os.path.basename(sticker_file)}")

                except Exception as e:
                    log_warning(f"加载贴纸失败: {sticker_file} - {str(e)}")
                    continue

            log_info(f"贴纸预处理完成: {len(sticker_clips)}/{len(sticker_files)} 个贴纸有效")
            return sticker_clips

        except Exception as e:
            log_error(f"批量预处理贴纸失败: {str(e)}")
            return []

    def _create_fusion_video_batch(self, video_files, output_folder, transition_type,
                                  sticker_clips, video_index, filename_prefix,
                                  sticker_position, sticker_size, sticker_opacity,
                                  trim_start, trim_end, trim_mode, min_duration,
                                  batch_size, progress_callback):
        """
        批处理模式创建单个融合视频
        """
        try:
            from moviepy.editor import VideoFileClip, concatenate_videoclips
            import random
            import time
            import gc

            # 设置随机种子
            random.seed(int(time.time()) + video_index * 1000)

            # 随机选择视频文件
            num_videos_to_use = min(random.randint(2, min(6, len(video_files))), len(video_files))
            selected_video_files = random.sample(video_files, num_videos_to_use)

            log_info(f"批处理融合视频 {video_index + 1}: 选择了 {num_videos_to_use} 个视频文件")

            # 分批加载视频片段
            video_clips = []
            loaded_clips = []  # 跟踪已加载的clips用于清理

            try:
                for i, video_file in enumerate(selected_video_files):
                    try:
                        # 加载视频片段
                        clip = VideoFileClip(video_file)
                        loaded_clips.append(clip)

                        # 应用裁剪
                        if trim_start > 0 or trim_end > 0:
                            clip = self._apply_video_trimming(
                                clip, i, len(selected_video_files),
                                trim_start, trim_end, trim_mode, min_duration, video_file
                            )

                        if clip is not None and clip.duration > 0:
                            video_clips.append(clip)
                        else:
                            log_warning(f"跳过无效视频片段: {os.path.basename(video_file)}")

                    except Exception as e:
                        log_warning(f"加载视频失败: {os.path.basename(video_file)} - {str(e)}")
                        continue

                if len(video_clips) < 2:
                    raise ValueError(f"有效视频片段不足: {len(video_clips)}")

                # 添加转场效果
                if transition_type != 'none':
                    video_clips = self._add_transitions_for_fusion_safe(video_clips, transition_type)

                # 使用内存优化的合并方法
                log_info(f"开始合并 {len(video_clips)} 个视频片段...")
                final_video = self._memory_efficient_concatenate_batch(video_clips)

                if final_video is None:
                    raise ValueError("视频合并失败")

                # 添加贴纸（如果有）
                if sticker_clips and random.choice([True, False]):
                    final_video = self._add_stickers_to_video_optimized(
                        final_video, sticker_clips, sticker_position,
                        sticker_size, sticker_opacity, video_index
                    )

                # 生成输出路径
                timestamp = int(time.time())
                output_filename = f"{filename_prefix}_{video_index + 1:02d}_{timestamp}.mp4"
                output_path = os.path.join(output_folder, output_filename)

                # 使用安全的视频写入方法
                log_info(f"保存批处理融合视频: {output_filename}")
                success = self.safe_write_videofile_enhanced(
                    final_video, output_path,
                    codec='libx264',
                    audio_codec='aac',
                    temp_audiofile=self.get_temp_file_path(f"fusion_audio_{video_index}", extension=".m4a"),
                    remove_temp=True,
                    fps=24,
                    preset='medium',
                    ffmpeg_params=['-crf', '23', '-movflags', '+faststart']
                )

                if success and os.path.exists(output_path):
                    return output_path
                else:
                    raise ValueError("视频写入失败")

            finally:
                # 清理所有加载的clips
                for clip in loaded_clips:
                    try:
                        clip.close()
                    except:
                        pass

                # 清理最终视频
                try:
                    if 'final_video' in locals():
                        final_video.close()
                except:
                    pass

                # 强制垃圾回收
                gc.collect()

        except Exception as e:
            log_error(f"批处理创建融合视频失败: {str(e)}")
            return None

    def safe_write_videofile_enhanced(self, clip, output_path, **kwargs):
        """
        增强的安全视频写入方法，专门针对大量小视频优化
        """
        max_retries = 5
        retry_count = 0
        wait_time_base = 2

        # 验证clip有效性
        if clip is None:
            log_error("视频片段为None，无法写入")
            return False

        try:
            # 验证clip的基本属性
            if not hasattr(clip, 'duration') or clip.duration is None or clip.duration <= 0:
                log_error(f"视频片段duration无效: {getattr(clip, 'duration', 'None')}")
                return False

            # 验证是否可以获取帧
            try:
                test_frame = clip.get_frame(0)
                if test_frame is None:
                    log_error("视频片段无法获取帧")
                    return False
            except Exception as frame_error:
                log_error(f"视频片段获取帧失败: {str(frame_error)}")
                return False

            log_info(f"开始写入视频片段，时长: {clip.duration:.2f}秒")

        except Exception as validation_error:
            log_error(f"视频片段验证失败: {str(validation_error)}")
            return False

        # 设置优化的默认参数
        default_kwargs = {
            'codec': 'libx264',
            'audio_codec': 'aac',
            'fps': 24,
            'preset': 'medium',
            'ffmpeg_params': ['-crf', '23', '-movflags', '+faststart', '-max_muxing_queue_size', '1024'],
            'verbose': False,
            'logger': None
        }

        # 合并用户参数
        for key, value in default_kwargs.items():
            if key not in kwargs:
                kwargs[key] = value

        # 确保临时音频文件路径唯一
        if 'temp_audiofile' not in kwargs:
            kwargs['temp_audiofile'] = self.get_temp_file_path("enhanced_audio", extension=".m4a")

        while retry_count < max_retries:
            try:
                # 尝试写入视频
                clip.write_videofile(output_path, **kwargs)

                # 验证输出文件
                if os.path.exists(output_path):
                    file_size = os.path.getsize(output_path)
                    if file_size > 1024:  # 至少1KB
                        log_info(f"视频写入成功: {output_path} ({file_size} bytes)")
                        return True
                    else:
                        log_warning(f"输出文件过小: {file_size} bytes")
                        if os.path.exists(output_path):
                            os.remove(output_path)
                        raise Exception("输出文件过小，可能损坏")
                else:
                    raise Exception("输出文件未创建")

            except Exception as e:
                error_str = str(e).lower()
                retry_count += 1

                log_warning(f"视频写入失败 (尝试 {retry_count}/{max_retries}): {str(e)}")

                # 检查是否是可重试的错误
                retryable_errors = [
                    'subprocess', 'none', 'stdout', 'stderr', 'pipe',
                    'broken pipe', 'connection', 'timeout', 'memory'
                ]

                is_retryable = any(err in error_str for err in retryable_errors)

                if retry_count >= max_retries or not is_retryable:
                    log_error(f"视频写入最终失败: {str(e)}")
                    return False

                # 清理可能存在的损坏文件
                if os.path.exists(output_path):
                    try:
                        os.remove(output_path)
                    except:
                        pass

                # 等待后重试
                wait_time = wait_time_base * (2 ** (retry_count - 1))
                log_info(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)

                # 清理MoviePy状态
                self._cleanup_moviepy_state()

        return False

    def _memory_efficient_concatenate_batch(self, video_clips):
        """
        批处理模式的内存优化视频合并
        """
        try:
            from moviepy.editor import concatenate_videoclips
            import gc

            if not video_clips:
                raise ValueError("没有视频片段可以合并")

            if len(video_clips) == 1:
                return video_clips[0]

            # 对于大量小视频，使用更激进的内存管理
            if len(video_clips) > 10:
                log_info(f"大量视频片段({len(video_clips)}个)，使用分段合并")

                # 分段合并，每段最多5个视频
                segment_size = 5
                merged_segments = []

                for i in range(0, len(video_clips), segment_size):
                    segment = video_clips[i:i + segment_size]
                    log_info(f"合并段 {i//segment_size + 1}: {len(segment)} 个片段")

                    try:
                        merged_segment = concatenate_videoclips(segment, method="compose")
                        merged_segments.append(merged_segment)

                        # 强制垃圾回收
                        gc.collect()

                    except Exception as e:
                        log_warning(f"段合并失败，尝试chain方法: {str(e)}")
                        try:
                            merged_segment = concatenate_videoclips(segment, method="chain")
                            merged_segments.append(merged_segment)
                        except Exception as e2:
                            log_error(f"段合并完全失败: {str(e2)}")
                            # 如果段合并失败，直接使用原始片段
                            merged_segments.extend(segment)

                # 最终合并所有段
                if len(merged_segments) == 1:
                    return merged_segments[0]
                else:
                    log_info(f"最终合并 {len(merged_segments)} 个段")
                    return concatenate_videoclips(merged_segments, method="compose")

            else:
                # 少量视频直接合并
                log_info(f"直接合并 {len(video_clips)} 个视频片段")
                try:
                    return concatenate_videoclips(video_clips, method="compose")
                except Exception as e:
                    log_warning(f"compose方法失败，尝试chain方法: {str(e)}")
                    return concatenate_videoclips(video_clips, method="chain")

        except Exception as e:
            log_error(f"批处理视频合并失败: {str(e)}")
            raise

    def _add_transitions_for_fusion_safe(self, clips, transition_type):
        """
        安全的转场效果添加方法，增强错误处理
        """
        try:
            if len(clips) <= 1 or transition_type == 'none':
                return clips

            transition_duration = 0.3  # 减少转场时长以提高稳定性
            clips_with_transitions = []

            for i, clip in enumerate(clips):
                try:
                    # 检查clip有效性
                    if clip is None:
                        log_warning(f"跳过无效片段 {i}")
                        continue

                    if not hasattr(clip, 'duration') or clip.duration <= transition_duration * 2:
                        log_warning(f"片段 {i} 时长过短，跳过转场效果")
                        clips_with_transitions.append(clip)
                        continue

                    current_clip = clip

                    if i == 0:
                        # 第一个片段：只添加淡出
                        if transition_type == 'fade':
                            try:
                                current_clip = current_clip.fadeout(transition_duration)
                            except Exception as e:
                                log_warning(f"添加淡出效果失败: {str(e)}")

                    elif i == len(clips) - 1:
                        # 最后一个片段：只添加淡入
                        if transition_type == 'fade':
                            try:
                                current_clip = current_clip.fadein(transition_duration)
                            except Exception as e:
                                log_warning(f"添加淡入效果失败: {str(e)}")

                    else:
                        # 中间片段：添加淡入淡出
                        if transition_type == 'fade':
                            try:
                                current_clip = current_clip.fadein(transition_duration).fadeout(transition_duration)
                            except Exception as e:
                                log_warning(f"添加淡入淡出效果失败: {str(e)}")

                    clips_with_transitions.append(current_clip)

                except Exception as e:
                    log_warning(f"处理片段 {i} 转场效果失败: {str(e)}，使用原始片段")
                    clips_with_transitions.append(clip)

            log_info(f"转场效果处理完成: {len(clips_with_transitions)}/{len(clips)} 个片段")
            return clips_with_transitions if clips_with_transitions else clips

        except Exception as e:
            log_error(f"添加转场效果失败: {str(e)}")
            return clips

    def _get_optimized_encoding_params(self, num_clips):
        """
        根据视频片段数量获取优化的编码参数
        """
        try:
            # 基础参数
            base_params = {
                'codec': 'libx264',
                'audio_codec': 'aac',
                'remove_temp': True,
                'verbose': False,
                'logger': None,
                'temp_audiofile': self.get_temp_file_path("fusion_audio", extension=".m4a")
            }

            # 根据片段数量调整参数
            if num_clips <= 5:
                # 少量视频：高质量设置
                base_params.update({
                    'fps': 30,
                    'preset': 'medium',
                    'ffmpeg_params': [
                        '-crf', '20',  # 高质量
                        '-movflags', '+faststart',  # 快速启动
                        '-max_muxing_queue_size', '1024',
                        '-threads', '4'
                    ]
                })
                log_info("使用高质量编码参数（少量视频）")

            elif num_clips <= 15:
                # 中等数量：平衡设置
                base_params.update({
                    'fps': 24,
                    'preset': 'medium',
                    'ffmpeg_params': [
                        '-crf', '23',  # 平衡质量
                        '-movflags', '+faststart',
                        '-max_muxing_queue_size', '2048',
                        '-threads', '2'
                    ]
                })
                log_info("使用平衡编码参数（中等数量视频）")

            else:
                # 大量视频：稳定性优先
                base_params.update({
                    'fps': 24,
                    'preset': 'fast',  # 快速编码
                    'ffmpeg_params': [
                        '-crf', '25',  # 适中质量，更稳定
                        '-movflags', '+faststart',
                        '-max_muxing_queue_size', '4096',  # 更大的队列
                        '-threads', '1',  # 单线程更稳定
                        '-bufsize', '2M',  # 缓冲区大小
                        '-maxrate', '5M',  # 最大码率限制
                        '-g', '50',  # GOP大小
                        '-keyint_min', '25'  # 最小关键帧间隔
                    ]
                })
                log_info("使用稳定性优先编码参数（大量视频）")

            return base_params

        except Exception as e:
            log_error(f"获取编码参数失败: {str(e)}")
            # 返回最基础的安全参数
            return {
                'codec': 'libx264',
                'audio_codec': 'aac',
                'fps': 24,
                'preset': 'fast',
                'ffmpeg_params': ['-crf', '25'],
                'remove_temp': True,
                'verbose': False,
                'logger': None
            }

    def _prevalidate_fusion_videos(self, video_files, progress_callback=None):
        """
        预验证视频融合的输入文件
        """
        try:
            valid_files = []
            total_files = len(video_files)

            log_info(f"开始预验证 {total_files} 个视频文件...")

            for i, video_file in enumerate(video_files):
                try:
                    if progress_callback and i % 3 == 0:  # 每3个文件更新一次进度
                        progress = 8 + int((i / total_files) * 7)
                        progress_callback(progress, 100, f"验证文件 {i+1}/{total_files}...")

                    # 基本文件检查
                    if not os.path.exists(video_file):
                        log_warning(f"文件不存在: {video_file}")
                        continue

                    # 检查文件大小
                    try:
                        file_size = os.path.getsize(video_file)
                        if file_size < 1024:  # 小于1KB
                            log_warning(f"文件过小: {video_file} ({file_size} bytes)")
                            continue
                    except Exception as size_error:
                        log_warning(f"无法获取文件大小: {video_file} - {str(size_error)}")
                        continue

                    # 检查文件扩展名
                    file_ext = os.path.splitext(video_file)[1].lower()
                    valid_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v']
                    if file_ext not in valid_extensions:
                        log_warning(f"不支持的文件格式: {video_file} ({file_ext})")
                        continue

                    # 快速验证文件头（避免完全加载）
                    try:
                        with open(video_file, 'rb') as f:
                            header = f.read(16)
                            if len(header) < 8:
                                log_warning(f"文件头过短: {video_file}")
                                continue
                    except Exception as header_error:
                        log_warning(f"无法读取文件头: {video_file} - {str(header_error)}")
                        continue

                    # 验证通过
                    valid_files.append(video_file)

                except Exception as e:
                    log_warning(f"验证文件时出错: {video_file} - {str(e)}")
                    continue

            log_info(f"预验证完成: {len(valid_files)}/{total_files} 个文件通过验证")
            return valid_files

        except Exception as e:
            log_error(f"预验证过程失败: {str(e)}")
            return video_files  # 如果预验证失败，返回原始列表

    def video_fusion_enhanced(self, video_files, output_folder, transition_type='fade',
                             num_outputs=3, sticker_files=None, filename_prefix="融合视频",
                             sticker_position="随机", sticker_size=0.15, sticker_opacity=0.8,
                             duration_mode="完整拼接", target_duration=60, duration_strategy="智能选择",
                             enable_trim=False, trim_start=0, trim_end=0, trim_mode="全部裁剪",
                             min_duration=5, progress_callback=None):
        """
        增强的视频融合功能 - 支持可选头尾裁剪和指定时长
        :param video_files: 视频文件列表
        :param output_folder: 输出文件夹
        :param transition_type: 转场类型 ('fade', 'slide', 'zoom', 'none')
        :param num_outputs: 生成视频的个数
        :param sticker_files: 贴纸文件列表（可选）
        :param filename_prefix: 文件名前缀
        :param sticker_position: 贴纸位置
        :param sticker_size: 贴纸大小比例
        :param sticker_opacity: 贴纸透明度
        :param duration_mode: 时长模式 ('完整拼接', '指定时长')
        :param target_duration: 目标时长（秒）
        :param duration_strategy: 时长控制策略 ('智能选择', '循环播放', '随机片段')
        :param enable_trim: 是否启用头尾裁剪
        :param trim_start: 开头裁剪时长（秒）
        :param trim_end: 结尾裁剪时长（秒）
        :param trim_mode: 裁剪模式 ('全部裁剪', '智能保留首尾')
        :param min_duration: 最小保留时长（秒）
        :param progress_callback: 进度回调函数
        :return: 生成的视频文件路径列表
        """
        try:
            from moviepy.editor import VideoFileClip, concatenate_videoclips, CompositeVideoClip, ImageClip
            import random
            import time
            import gc

            log_info(f"开始增强视频融合: {len(video_files)} 个视频 → {num_outputs} 个融合视频")
            log_info(f"时长模式: {duration_mode}")
            if duration_mode == "指定时长":
                log_info(f"目标时长: {target_duration}秒, 策略: {duration_strategy}")
            if enable_trim:
                log_info(f"头尾裁剪: 开头{trim_start}秒, 结尾{trim_end}秒, 模式: {trim_mode}")

            # 检查内存使用情况
            self._check_memory_usage("增强视频融合开始前")

            # 针对大量小视频的特殊处理
            if len(video_files) > 20:
                log_info(f"检测到大量视频文件({len(video_files)}个)，启用内存优化模式")
                return self._video_fusion_enhanced_batch_mode(
                    video_files, output_folder, transition_type, num_outputs,
                    sticker_files, filename_prefix, sticker_position,
                    sticker_size, sticker_opacity, duration_mode, target_duration,
                    duration_strategy, enable_trim, trim_start, trim_end,
                    trim_mode, min_duration, progress_callback
                )

            if progress_callback:
                progress_callback(5, 100, "初始化增强视频融合...")

            # 确保输出文件夹存在
            if not os.path.exists(output_folder):
                os.makedirs(output_folder)

            # 预验证视频文件
            if progress_callback:
                progress_callback(8, 100, "验证视频文件...")

            valid_video_files = self._prevalidate_fusion_videos(video_files, progress_callback)
            if len(valid_video_files) < 2:
                raise ValueError(f"有效视频文件不足，需要至少2个，实际: {len(valid_video_files)}")

            # 加载所有视频并预处理
            video_clips = []
            total_duration_actual = 0
            for i, video_file in enumerate(valid_video_files):
                try:
                    if progress_callback:
                        progress_callback(15 + (i * 25 // len(valid_video_files)), 100, f"加载视频 {i+1}/{len(valid_video_files)}...")

                    clip = VideoFileClip(video_file)
                    original_duration = clip.duration

                    # 根据设置应用头尾裁剪
                    if enable_trim and (trim_start > 0 or trim_end > 0):
                        trimmed_clip = self._apply_video_trimming(
                            clip, i, len(valid_video_files), trim_start, trim_end,
                            trim_mode, min_duration, video_file
                        )
                    else:
                        trimmed_clip = clip

                    video_clips.append({
                        'clip': trimmed_clip,
                        'path': video_file,
                        'duration': trimmed_clip.duration,
                        'original_duration': original_duration,
                        'name': os.path.splitext(os.path.basename(video_file))[0]
                    })
                    total_duration_actual += trimmed_clip.duration

                    if enable_trim and trimmed_clip.duration != original_duration:
                        log_info(f"已加载并裁剪视频: {os.path.basename(video_file)} (原时长: {original_duration:.2f}秒 → 裁剪后: {trimmed_clip.duration:.2f}秒)")
                    else:
                        log_info(f"已加载视频: {os.path.basename(video_file)} (时长: {clip.duration:.2f}秒)")

                except Exception as e:
                    log_warning(f"加载视频失败 {video_file}: {str(e)}")
                    continue

            if len(video_clips) < 2:
                raise ValueError("至少需要2个有效的视频文件进行融合")

            log_info(f"所有视频总时长: {total_duration_actual:.2f}秒")

            # 加载贴纸（如果有）
            sticker_clips = []
            if sticker_files:
                for sticker_file in sticker_files:
                    try:
                        sticker = ImageClip(sticker_file)
                        sticker_clips.append(sticker)
                        log_info(f"加载贴纸: {os.path.basename(sticker_file)}")
                    except Exception as e:
                        log_warning(f"加载贴纸失败 {sticker_file}: {str(e)}")

            # 生成融合视频
            result_paths = []
            for i in range(num_outputs):
                if progress_callback:
                    base_progress = 40 + (i / num_outputs) * 50
                    progress_callback(int(base_progress), 100, f"生成融合视频 {i + 1}/{num_outputs}...")

                # 生成输出路径
                timestamp = int(time.time())
                output_filename = f"{filename_prefix}_{i + 1:02d}_{timestamp}.mp4"
                output_path = os.path.join(output_folder, output_filename)

                # 创建融合视频
                result_path = self._create_enhanced_fusion_video(
                    video_clips, output_path, transition_type, sticker_clips,
                    sticker_position, sticker_size, sticker_opacity, i,
                    duration_mode, target_duration, duration_strategy, progress_callback
                )

                if result_path:
                    result_paths.append(result_path)
                    log_info(f"✅ 融合视频 {i + 1} 生成成功: {os.path.basename(result_path)}")
                else:
                    log_warning(f"❌ 融合视频 {i + 1} 生成失败")

            # 清理资源
            for video_data in video_clips:
                try:
                    video_data['clip'].close()
                except:
                    pass

            for sticker in sticker_clips:
                try:
                    sticker.close()
                except:
                    pass

            log_info(f"增强视频融合完成，生成了 {len(result_paths)} 个视频")
            return result_paths

        except Exception as e:
            log_error(f"增强视频融合失败: {str(e)}")
            import traceback
            log_error(traceback.format_exc())
            return []

    def _create_enhanced_fusion_video(self, video_clips, output_path, transition_type,
                                     sticker_clips, sticker_position, sticker_size,
                                     sticker_opacity, video_index, duration_mode,
                                     target_duration, duration_strategy, progress_callback):
        """
        创建增强的融合视频
        """
        try:
            from moviepy.editor import concatenate_videoclips
            import random
            import time
            import gc

            log_info(f"创建增强融合视频 {video_index + 1}...")

            # 根据时长模式选择和处理视频片段
            if duration_mode == "完整拼接":
                # 完整拼接模式：随机排序所有视频
                selected_clips = self._select_clips_for_full_concatenation(video_clips, video_index)
            else:
                # 指定时长模式：根据策略选择片段
                selected_clips = self._select_clips_for_target_duration(
                    video_clips, target_duration, duration_strategy, video_index
                )

            if not selected_clips:
                log_error("没有选择到有效的视频片段")
                return None

            # 计算实际时长
            actual_duration = sum(clip_data['clip'].duration for clip_data in selected_clips)
            log_info(f"选择了 {len(selected_clips)} 个片段，总时长: {actual_duration:.2f}秒")

            # 提取视频片段
            clips_to_merge = [clip_data['clip'] for clip_data in selected_clips]

            # 添加转场效果
            if transition_type != 'none':
                clips_to_merge = self._add_transitions_for_fusion_safe(clips_to_merge, transition_type)

            # 合并视频
            log_info("合并视频片段...")
            final_video = concatenate_videoclips(clips_to_merge, method="compose")

            # 验证最终视频时长
            final_duration = final_video.duration
            log_info(f"合并后视频时长: {final_duration:.2f}秒")

            # 如果是指定时长模式，检查时长是否符合要求
            if duration_mode == "指定时长":
                duration_diff = abs(final_duration - target_duration)
                if duration_diff > 2:  # 允许2秒误差
                    log_warning(f"视频时长与目标时长差异较大: 目标{target_duration}秒, 实际{final_duration:.2f}秒")

            # 添加贴纸（如果有）
            if sticker_clips and random.choice([True, False]):
                final_video = self._add_stickers_to_video_optimized(
                    final_video, sticker_clips, sticker_position,
                    sticker_size, sticker_opacity, video_index
                )

            # 使用优化的编码参数保存视频
            encoding_params = self._get_optimized_encoding_params(len(selected_clips))
            success = self.safe_write_videofile_enhanced(
                final_video, output_path, **encoding_params
            )

            # 清理资源
            try:
                final_video.close()
            except:
                pass

            gc.collect()

            if success and os.path.exists(output_path):
                # 验证输出文件
                try:
                    output_size = os.path.getsize(output_path)
                    log_info(f"输出文件大小: {self._format_file_size(output_size)}")
                    return output_path
                except:
                    log_warning("无法获取输出文件大小")
                    return output_path
            else:
                log_error("视频保存失败")
                return None

        except Exception as e:
            log_error(f"创建增强融合视频失败: {str(e)}")
            return None

    def _select_clips_for_full_concatenation(self, video_clips, video_index):
        """
        完整拼接模式：选择所有视频并随机排序
        """
        try:
            import random
            import time

            # 设置随机种子，确保每次生成不同的排序
            random.seed(int(time.time()) + video_index * 1000)

            # 复制视频列表并随机排序
            selected_clips = video_clips.copy()
            random.shuffle(selected_clips)

            log_info(f"完整拼接模式: 随机排序 {len(selected_clips)} 个视频")
            for i, clip_data in enumerate(selected_clips):
                log_info(f"  {i+1}. {clip_data['name']} ({clip_data['duration']:.2f}秒)")

            return selected_clips

        except Exception as e:
            log_error(f"完整拼接模式选择片段失败: {str(e)}")
            return video_clips

    def _select_clips_for_target_duration(self, video_clips, target_duration, strategy, video_index):
        """
        指定时长模式：根据策略选择视频片段
        """
        try:
            import random
            import time

            random.seed(int(time.time()) + video_index * 1000)

            log_info(f"指定时长模式: 目标{target_duration}秒, 策略: {strategy}")

            if strategy == "智能选择":
                return self._smart_select_clips(video_clips, target_duration)
            elif strategy == "循环播放":
                return self._loop_select_clips(video_clips, target_duration)
            elif strategy == "随机片段":
                return self._random_select_clips(video_clips, target_duration)
            else:
                log_warning(f"未知策略: {strategy}, 使用智能选择")
                return self._smart_select_clips(video_clips, target_duration)

        except Exception as e:
            log_error(f"指定时长模式选择片段失败: {str(e)}")
            return video_clips[:min(3, len(video_clips))]  # 返回前3个作为备选

    def _smart_select_clips(self, video_clips, target_duration):
        """
        智能选择策略：优先选择时长合适的视频组合
        """
        try:
            import random

            # 按时长排序
            sorted_clips = sorted(video_clips, key=lambda x: x['duration'])
            selected_clips = []
            current_duration = 0

            # 贪心算法选择视频
            remaining_clips = sorted_clips.copy()

            while current_duration < target_duration and remaining_clips:
                remaining_time = target_duration - current_duration

                # 寻找最接近剩余时间的视频
                best_clip = None
                best_diff = float('inf')

                for clip_data in remaining_clips:
                    clip_duration = clip_data['duration']

                    # 如果这个视频正好合适或稍微超出一点
                    if clip_duration <= remaining_time * 1.2:  # 允许20%超出
                        diff = abs(remaining_time - clip_duration)
                        if diff < best_diff:
                            best_diff = diff
                            best_clip = clip_data

                if best_clip:
                    selected_clips.append(best_clip)
                    current_duration += best_clip['duration']
                    remaining_clips.remove(best_clip)
                    log_info(f"智能选择: {best_clip['name']} ({best_clip['duration']:.2f}秒), 累计: {current_duration:.2f}秒")
                else:
                    # 如果没有合适的，选择最短的
                    if remaining_clips:
                        shortest_clip = min(remaining_clips, key=lambda x: x['duration'])
                        selected_clips.append(shortest_clip)
                        current_duration += shortest_clip['duration']
                        remaining_clips.remove(shortest_clip)
                        log_info(f"智能选择(最短): {shortest_clip['name']} ({shortest_clip['duration']:.2f}秒), 累计: {current_duration:.2f}秒")
                    else:
                        break

            # 如果时长不够，随机添加更多视频
            if current_duration < target_duration * 0.8 and remaining_clips:
                additional_clips = random.sample(remaining_clips, min(2, len(remaining_clips)))
                selected_clips.extend(additional_clips)
                for clip_data in additional_clips:
                    current_duration += clip_data['duration']
                    log_info(f"智能选择(补充): {clip_data['name']} ({clip_data['duration']:.2f}秒)")

            log_info(f"智能选择完成: {len(selected_clips)} 个片段, 总时长: {current_duration:.2f}秒")
            return selected_clips

        except Exception as e:
            log_error(f"智能选择失败: {str(e)}")
            return video_clips[:min(3, len(video_clips))]

    def _loop_select_clips(self, video_clips, target_duration):
        """
        循环播放策略：重复播放视频直到达到目标时长
        """
        try:
            import random

            # 随机选择几个视频进行循环
            num_base_clips = min(random.randint(2, 4), len(video_clips))
            base_clips = random.sample(video_clips, num_base_clips)

            selected_clips = []
            current_duration = 0
            cycle_count = 0

            log_info(f"循环播放策略: 选择 {num_base_clips} 个基础视频进行循环")

            while current_duration < target_duration and cycle_count < 10:  # 最多循环10次
                for clip_data in base_clips:
                    if current_duration >= target_duration:
                        break

                    selected_clips.append(clip_data)
                    current_duration += clip_data['duration']
                    log_info(f"循环播放: {clip_data['name']} (第{cycle_count + 1}轮), 累计: {current_duration:.2f}秒")

                cycle_count += 1

            log_info(f"循环播放完成: {len(selected_clips)} 个片段, 总时长: {current_duration:.2f}秒")
            return selected_clips

        except Exception as e:
            log_error(f"循环播放策略失败: {str(e)}")
            return video_clips[:min(3, len(video_clips))]

    def _random_select_clips(self, video_clips, target_duration):
        """
        随机片段策略：随机选择视频片段组合
        """
        try:
            import random

            selected_clips = []
            current_duration = 0
            available_clips = video_clips.copy()

            log_info(f"随机片段策略: 目标时长 {target_duration}秒")

            while current_duration < target_duration and available_clips:
                # 随机选择一个视频
                clip_data = random.choice(available_clips)
                selected_clips.append(clip_data)
                current_duration += clip_data['duration']

                log_info(f"随机选择: {clip_data['name']} ({clip_data['duration']:.2f}秒), 累计: {current_duration:.2f}秒")

                # 决定是否移除这个视频（避免过度重复）
                if random.random() < 0.7:  # 70%概率移除，30%概率保留可重复选择
                    available_clips.remove(clip_data)

                # 如果时长已经接近目标，停止添加
                if current_duration >= target_duration * 0.9:
                    break

            # 如果时长不够且还有可用视频，随机添加一些
            if current_duration < target_duration * 0.8 and video_clips:
                additional_count = random.randint(1, min(3, len(video_clips)))
                additional_clips = random.sample(video_clips, additional_count)

                for clip_data in additional_clips:
                    if clip_data not in selected_clips:  # 避免重复
                        selected_clips.append(clip_data)
                        current_duration += clip_data['duration']
                        log_info(f"随机补充: {clip_data['name']} ({clip_data['duration']:.2f}秒)")

            log_info(f"随机选择完成: {len(selected_clips)} 个片段, 总时长: {current_duration:.2f}秒")
            return selected_clips

        except Exception as e:
            log_error(f"随机片段策略失败: {str(e)}")
            return video_clips[:min(3, len(video_clips))]

    def _format_file_size(self, size_bytes):
        """
        格式化文件大小显示
        """
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

    def _video_fusion_enhanced_batch_mode(self, video_files, output_folder, transition_type, num_outputs,
                                         sticker_files, filename_prefix, sticker_position,
                                         sticker_size, sticker_opacity, duration_mode, target_duration,
                                         duration_strategy, enable_trim, trim_start, trim_end,
                                         trim_mode, min_duration, progress_callback):
        """
        增强批处理模式的视频融合 - 支持新的时长控制功能
        """
        try:
            import random
            import time
            import gc
            from moviepy.editor import VideoFileClip, ImageClip

            log_info("启用增强批处理模式处理大量视频文件")

            # 预验证所有视频文件
            if progress_callback:
                progress_callback(5, 100, "验证视频文件...")

            valid_video_files = self._validate_video_files_batch(video_files, progress_callback)
            if len(valid_video_files) < 2:
                raise ValueError(f"有效视频文件不足，需要至少2个，实际有效: {len(valid_video_files)}")

            log_info(f"验证完成，有效视频文件: {len(valid_video_files)}/{len(video_files)}")

            # 预处理贴纸文件
            sticker_clips = []
            if sticker_files:
                if progress_callback:
                    progress_callback(15, 100, "预处理贴纸文件...")
                sticker_clips = self._preprocess_stickers_batch(sticker_files)

            # 分批生成融合视频
            result_paths = []

            for output_index in range(num_outputs):
                if progress_callback:
                    base_progress = 20 + (output_index / num_outputs) * 70
                    progress_callback(int(base_progress), 100, f"生成融合视频 {output_index + 1}/{num_outputs}...")

                try:
                    # 为每个输出视频创建独立的处理
                    output_path = self._create_enhanced_fusion_video_batch(
                        valid_video_files, output_folder, transition_type,
                        sticker_clips, output_index, filename_prefix,
                        sticker_position, sticker_size, sticker_opacity,
                        duration_mode, target_duration, duration_strategy,
                        enable_trim, trim_start, trim_end, trim_mode, min_duration,
                        progress_callback
                    )

                    if output_path and os.path.exists(output_path):
                        result_paths.append(output_path)
                        log_info(f"✅ 增强融合视频 {output_index + 1} 生成成功: {os.path.basename(output_path)}")
                    else:
                        log_warning(f"❌ 增强融合视频 {output_index + 1} 生成失败")

                    # 强制垃圾回收
                    gc.collect()

                except Exception as e:
                    log_error(f"生成增强融合视频 {output_index + 1} 时出错: {str(e)}")
                    continue

            # 清理贴纸资源
            for sticker in sticker_clips:
                try:
                    sticker.close()
                except:
                    pass

            log_info(f"增强批处理模式完成，成功生成 {len(result_paths)}/{num_outputs} 个融合视频")
            return result_paths

        except Exception as e:
            log_error(f"增强批处理模式视频融合失败: {str(e)}")
            return []

    def _create_enhanced_fusion_video_batch(self, video_files, output_folder, transition_type,
                                           sticker_clips, video_index, filename_prefix,
                                           sticker_position, sticker_size, sticker_opacity,
                                           duration_mode, target_duration, duration_strategy,
                                           enable_trim, trim_start, trim_end, trim_mode, min_duration,
                                           progress_callback):
        """
        批处理模式创建单个增强融合视频
        """
        try:
            from moviepy.editor import VideoFileClip, concatenate_videoclips
            import random
            import time
            import gc

            # 设置随机种子
            random.seed(int(time.time()) + video_index * 1000)

            # 根据时长模式选择视频文件
            if duration_mode == "完整拼接":
                # 完整拼接模式：使用所有视频文件，只改变顺序
                selected_video_files = video_files.copy()
                num_videos_to_use = len(selected_video_files)
                log_info(f"完整拼接模式：使用所有 {num_videos_to_use} 个视频文件")
            else:
                # 指定时长模式：选择部分视频以便控制时长
                num_videos_to_use = min(random.randint(5, min(12, len(video_files))), len(video_files))
                selected_video_files = random.sample(video_files, num_videos_to_use)
                log_info(f"指定时长模式：选择 {num_videos_to_use} 个视频文件")

            log_info(f"批处理增强融合视频 {video_index + 1}: 使用了 {num_videos_to_use} 个视频文件")

            # 加载视频片段
            video_clips = []
            loaded_clips = []  # 跟踪已加载的clips用于清理

            try:
                for i, video_file in enumerate(selected_video_files):
                    try:
                        # 加载视频片段
                        clip = VideoFileClip(video_file)
                        loaded_clips.append(clip)
                        original_duration = clip.duration

                        # 应用裁剪（如果启用）
                        if enable_trim and (trim_start > 0 or trim_end > 0):
                            trimmed_clip = self._apply_video_trimming(
                                clip, i, len(selected_video_files),
                                trim_start, trim_end, trim_mode, min_duration, video_file
                            )
                        else:
                            trimmed_clip = clip

                        if trimmed_clip is not None and trimmed_clip.duration > 0:
                            video_clips.append({
                                'clip': trimmed_clip,
                                'path': video_file,
                                'duration': trimmed_clip.duration,
                                'original_duration': original_duration,
                                'name': os.path.splitext(os.path.basename(video_file))[0]
                            })
                        else:
                            log_warning(f"跳过无效视频片段: {os.path.basename(video_file)}")

                    except Exception as e:
                        log_warning(f"加载视频失败: {os.path.basename(video_file)} - {str(e)}")
                        continue

                if len(video_clips) < 2:
                    raise ValueError(f"有效视频片段不足: {len(video_clips)}")

                # 根据时长模式选择和处理视频片段
                if duration_mode == "完整拼接":
                    selected_clips = self._select_clips_for_full_concatenation(video_clips, video_index)
                else:
                    selected_clips = self._select_clips_for_target_duration(
                        video_clips, target_duration, duration_strategy, video_index
                    )

                # 提取视频片段进行合并
                clips_to_merge = [clip_data['clip'] for clip_data in selected_clips]

                # 添加转场效果
                if transition_type != 'none':
                    clips_to_merge = self._add_transitions_for_fusion_safe(clips_to_merge, transition_type)

                # 使用内存优化的合并方法
                log_info(f"开始合并 {len(clips_to_merge)} 个视频片段...")
                final_video = self._memory_efficient_concatenate_batch(clips_to_merge)

                if final_video is None:
                    raise ValueError("视频合并失败")

                # 验证最终视频时长
                final_duration = final_video.duration
                log_info(f"合并后视频时长: {final_duration:.2f}秒")

                # 添加贴纸（如果有）
                if sticker_clips and random.choice([True, False]):
                    final_video = self._add_stickers_to_video_optimized(
                        final_video, sticker_clips, sticker_position,
                        sticker_size, sticker_opacity, video_index
                    )

                # 生成输出路径
                timestamp = int(time.time())
                output_filename = f"{filename_prefix}_{video_index + 1:02d}_{timestamp}.mp4"
                output_path = os.path.join(output_folder, output_filename)

                # 使用安全的视频写入方法
                log_info(f"保存批处理增强融合视频: {output_filename}")
                success = self.safe_write_videofile_enhanced(
                    final_video, output_path,
                    codec='libx264',
                    audio_codec='aac',
                    temp_audiofile=self.get_temp_file_path(f"enhanced_fusion_audio_{video_index}", extension=".m4a"),
                    remove_temp=True,
                    fps=24,
                    preset='medium',
                    ffmpeg_params=['-crf', '23', '-movflags', '+faststart']
                )

                if success and os.path.exists(output_path):
                    return output_path
                else:
                    raise ValueError("视频写入失败")

            finally:
                # 清理所有加载的clips
                for clip in loaded_clips:
                    try:
                        clip.close()
                    except:
                        pass

                # 清理最终视频
                try:
                    if 'final_video' in locals():
                        final_video.close()
                except:
                    pass

                # 强制垃圾回收
                gc.collect()

        except Exception as e:
            log_error(f"批处理创建增强融合视频失败: {str(e)}")
            return None

    def _calculate_sticker_position(self, position, video_w, video_h, sticker_w, sticker_h, index):
        """计算贴纸位置"""
        import random

        margin = 20  # 边距

        if position == "随机":
            pos_x = random.randint(margin, max(margin, video_w - sticker_w - margin))
            pos_y = random.randint(margin, max(margin, video_h - sticker_h - margin))
        elif position == "左上角":
            pos_x = margin
            pos_y = margin + (index * (sticker_h + 10))  # 多个贴纸错开
        elif position == "右上角":
            pos_x = video_w - sticker_w - margin
            pos_y = margin + (index * (sticker_h + 10))
        elif position == "左下角":
            pos_x = margin
            pos_y = video_h - sticker_h - margin - (index * (sticker_h + 10))
        elif position == "右下角":
            pos_x = video_w - sticker_w - margin
            pos_y = video_h - sticker_h - margin - (index * (sticker_h + 10))
        elif position == "中心":
            pos_x = (video_w - sticker_w) // 2 + (index * 30)  # 多个贴纸错开
            pos_y = (video_h - sticker_h) // 2 + (index * 30)
        else:
            # 默认随机
            pos_x = random.randint(margin, max(margin, video_w - sticker_w - margin))
            pos_y = random.randint(margin, max(margin, video_h - sticker_h - margin))

        # 确保位置在视频范围内
        pos_x = max(0, min(pos_x, video_w - sticker_w))
        pos_y = max(0, min(pos_y, video_h - sticker_h))

        return pos_x, pos_y

    def _find_best_aux_position_sequential(self, aux_video_path, main_features, main_duration, similarity_threshold, aux_video_scenes, processed_regions):
        """
        在辅助视频中按顺序寻找与主视频段最相似的位置（避免重复使用区域）
        :param aux_video_path: 辅助视频路径
        :param main_features: 主视频段特征
        :param main_duration: 主视频段时长
        :param similarity_threshold: 相似度阈值
        :param aux_video_scenes: 辅助视频场景列表
        :param processed_regions: 已处理的辅助视频区域列表
        :return: 最佳匹配位置信息
        """
        try:
            from moviepy.editor import VideoFileClip
            import gc

            # 获取辅助视频总时长
            aux_clip = VideoFileClip(aux_video_path)
            aux_total_duration = aux_clip.duration
            aux_clip.close()

            best_match = None
            best_similarity = 0.0

            log_info(f"在辅助视频中搜索 {main_duration:.2f}秒 的最佳匹配位置（避开已用区域）")

            # 方法1：基于场景的搜索（优先）
            for aux_scene in aux_video_scenes:
                aux_scene_duration = aux_scene['end_time'] - aux_scene['start_time']

                # 检查场景是否足够长
                if aux_scene_duration < main_duration * 0.8:
                    continue

                # 检查是否与已处理区域重叠
                scene_start = aux_scene['start_time']
                scene_end = aux_scene['end_time']

                if self._is_region_overlapping(scene_start, scene_end, processed_regions):
                    continue

                # 在场景内寻找最佳匹配位置
                best_scene_match = self._search_in_scene(
                    aux_video_path, main_features, main_duration,
                    aux_scene, similarity_threshold
                )

                if best_scene_match and best_scene_match['similarity'] > best_similarity:
                    best_similarity = best_scene_match['similarity']
                    best_match = best_scene_match

            # 方法2：如果场景匹配效果不好，使用滑动窗口搜索
            if best_similarity < similarity_threshold:
                log_info("场景匹配未达到阈值，使用滑动窗口搜索")

                sliding_match = self._sliding_window_search(
                    aux_video_path, main_features, main_duration,
                    similarity_threshold, aux_total_duration, processed_regions
                )

                if sliding_match and sliding_match['similarity'] > best_similarity:
                    best_match = sliding_match

            if best_match:
                log_info(f"找到最佳匹配: 相似度={best_match['similarity']:.3f}, "
                       f"位置={best_match['start_time']:.2f}-{best_match['end_time']:.2f}秒")
            else:
                log_info(f"未找到满足阈值 {similarity_threshold:.3f} 的匹配")

            return best_match

        except Exception as e:
            log_info(f"Error in _find_best_aux_position_sequential: {str(e)}")
            return None

    def _is_region_overlapping(self, start_time, end_time, processed_regions):
        """
        检查指定时间区域是否与已处理区域重叠
        """
        for region in processed_regions:
            # 检查是否有重叠
            if not (end_time <= region['start_time'] or start_time >= region['end_time']):
                return True
        return False

    def _search_in_scene(self, aux_video_path, main_features, main_duration, aux_scene, similarity_threshold):
        """
        在指定场景内搜索最佳匹配位置
        """
        try:
            scene_start = aux_scene['start_time']
            scene_end = aux_scene['end_time']
            scene_duration = scene_end - scene_start

            # 如果场景刚好匹配主视频段时长
            if abs(scene_duration - main_duration) <= 0.5:
                aux_features = self.extract_frame_features(
                    aux_video_path, scene_start, scene_end, num_frames=8
                )
                if aux_features:
                    similarity = self.calculate_similarity(main_features, aux_features)
                    if similarity >= similarity_threshold:
                        return {
                            'start_time': scene_start,
                            'end_time': scene_end,
                            'similarity': similarity,
                            'method': 'exact_scene_match'
                        }

            # 如果场景比主视频段长，在场景内滑动搜索
            if scene_duration > main_duration:
                best_match = None
                best_similarity = 0.0

                # 在场景内进行滑动窗口搜索
                step_size = max(0.5, main_duration * 0.1)  # 步长
                search_positions = []

                current_pos = scene_start
                while current_pos + main_duration <= scene_end:
                    search_positions.append(current_pos)
                    current_pos += step_size

                # 限制搜索位置数量
                max_positions = min(20, len(search_positions))
                if len(search_positions) > max_positions:
                    step = len(search_positions) / max_positions
                    search_positions = [search_positions[int(i * step)] for i in range(max_positions)]

                for start_pos in search_positions:
                    end_pos = start_pos + main_duration

                    aux_features = self.extract_frame_features(
                        aux_video_path, start_pos, end_pos, num_frames=6
                    )

                    if aux_features:
                        similarity = self.calculate_similarity(main_features, aux_features)
                        if similarity > best_similarity and similarity >= similarity_threshold:
                            best_similarity = similarity
                            best_match = {
                                'start_time': start_pos,
                                'end_time': end_pos,
                                'similarity': similarity,
                                'method': 'scene_sliding_window'
                            }

                return best_match

            return None

        except Exception as e:
            log_info(f"Error in _search_in_scene: {str(e)}")
            return None

    def _sliding_window_search(self, aux_video_path, main_features, main_duration, similarity_threshold, aux_total_duration, processed_regions):
        """
        使用滑动窗口在整个辅助视频中搜索最佳匹配位置
        """
        try:
            import gc

            best_match = None
            best_similarity = 0.0

            # 滑动窗口参数
            window_step = max(1.0, main_duration * 0.2)  # 步长为主视频段时长的20%
            search_positions = []

            # 生成搜索位置（避开已处理区域）
            current_pos = 0
            while current_pos + main_duration <= aux_total_duration:
                end_pos = current_pos + main_duration

                # 检查是否与已处理区域重叠
                if not self._is_region_overlapping(current_pos, end_pos, processed_regions):
                    search_positions.append(current_pos)

                current_pos += window_step

            # 限制搜索位置数量以控制计算时间
            max_positions = min(30, len(search_positions))
            if len(search_positions) > max_positions:
                # 均匀采样
                step = len(search_positions) / max_positions
                search_positions = [search_positions[int(i * step)] for i in range(max_positions)]

            log_info(f"滑动窗口搜索: {len(search_positions)} 个位置")

            for i, start_pos in enumerate(search_positions):
                end_pos = start_pos + main_duration

                # 提取辅助视频段特征
                aux_features = self.extract_frame_features(
                    aux_video_path, start_pos, end_pos, num_frames=6
                )

                if aux_features:
                    similarity = self.calculate_similarity(main_features, aux_features)

                    if similarity > best_similarity and similarity >= similarity_threshold:
                        best_similarity = similarity
                        best_match = {
                            'start_time': start_pos,
                            'end_time': end_pos,
                            'similarity': similarity,
                            'method': 'global_sliding_window'
                        }

                # 每10个位置清理一次内存
                if i % 10 == 0:
                    gc.collect()

            return best_match

        except Exception as e:
            log_info(f"Error in _sliding_window_search: {str(e)}")
            return None

    def _find_best_aux_position(self, aux_video_path, main_features, main_duration, similarity_threshold, aux_video_scenes):
        """
        在辅助视频中寻找与主视频段最相似的位置
        :param aux_video_path: 辅助视频路径
        :param main_features: 主视频段特征
        :param main_duration: 主视频段时长
        :param similarity_threshold: 相似度阈值
        :param aux_video_scenes: 辅助视频场景列表
        :return: 最佳匹配位置信息
        """
        try:
            from moviepy.editor import VideoFileClip
            import gc

            # 获取辅助视频总时长
            aux_clip = VideoFileClip(aux_video_path)
            aux_total_duration = aux_clip.duration
            aux_clip.close()

            best_match = None
            best_similarity = 0.0

            # 搜索策略：在辅助视频场景中寻找最佳匹配
            log_info(f"在辅助视频中搜索 {main_duration:.2f}秒 的最佳匹配位置")

            # 方法1：基于场景的搜索
            for aux_scene in aux_video_scenes:
                aux_scene_duration = aux_scene['end_time'] - aux_scene['start_time']

                # 如果辅助场景时长足够，尝试匹配
                if aux_scene_duration >= main_duration * 0.8:  # 至少80%的时长
                    # 提取辅助视频段特征进行比较
                    aux_features = self.extract_frame_features(
                        aux_video_path,
                        aux_scene['start_time'],
                        min(aux_scene['start_time'] + main_duration, aux_scene['end_time']),
                        num_frames=8
                    )

                    if aux_features:
                        similarity = self.calculate_similarity(main_features, aux_features)

                        if similarity > best_similarity and similarity >= similarity_threshold:
                            best_similarity = similarity
                            best_match = {
                                'start_time': aux_scene['start_time'],
                                'end_time': aux_scene['start_time'] + main_duration,
                                'similarity': similarity,
                                'method': 'scene_based'
                            }

            # 方法2：如果场景匹配效果不好，使用滑动窗口搜索
            if best_similarity < similarity_threshold * 1.2:  # 如果匹配质量不够好
                log_info("场景匹配质量不佳，使用滑动窗口搜索")

                # 滑动窗口参数
                window_step = max(1.0, main_duration * 0.1)  # 步长为主视频段时长的10%
                search_positions = []

                # 生成搜索位置
                current_pos = 0
                while current_pos + main_duration <= aux_total_duration:
                    search_positions.append(current_pos)
                    current_pos += window_step

                # 限制搜索位置数量以控制计算时间
                max_positions = min(50, len(search_positions))
                if len(search_positions) > max_positions:
                    # 均匀采样
                    step = len(search_positions) / max_positions
                    search_positions = [search_positions[int(i * step)] for i in range(max_positions)]

                log_info(f"滑动窗口搜索: {len(search_positions)} 个位置")

                for i, start_pos in enumerate(search_positions):
                    end_pos = start_pos + main_duration

                    # 提取辅助视频段特征
                    aux_features = self.extract_frame_features(
                        aux_video_path,
                        start_pos,
                        end_pos,
                        num_frames=6  # 滑动窗口使用较少帧数以提高速度
                    )

                    if aux_features:
                        similarity = self.calculate_similarity(main_features, aux_features)

                        if similarity > best_similarity and similarity >= similarity_threshold:
                            best_similarity = similarity
                            best_match = {
                                'start_time': start_pos,
                                'end_time': end_pos,
                                'similarity': similarity,
                                'method': 'sliding_window'
                            }

                    # 每10个位置清理一次内存
                    if i % 10 == 0:
                        gc.collect()

            if best_match:
                log_info(f"找到最佳匹配: 相似度={best_similarity:.3f}, 方法={best_match['method']}, "
                       f"位置={best_match['start_time']:.2f}-{best_match['end_time']:.2f}秒")
            else:
                log_info(f"未找到满足阈值 {similarity_threshold:.3f} 的匹配")

            return best_match

        except Exception as e:
            log_info(f"Error in _find_best_aux_position: {str(e)}")
            return None

    def _generate_unique_video_order(self, video_clips, video_index):
        """
        生成不同的视频顺序
        :param video_clips: 视频片段列表
        :param video_index: 视频索引，用于生成不同的顺序
        :return: 重新排序的视频列表
        """
        import random
        import itertools

        # 创建视频列表的副本
        video_list = video_clips.copy()

        # 根据视频索引生成不同的排列
        if video_index == 0:
            # 第一个视频保持原始顺序
            return video_list
        elif video_index == 1:
            # 第二个视频反转顺序
            return list(reversed(video_list))
        else:
            # 其他视频使用随机排列
            # 使用视频索引作为随机种子，确保可重现
            random.seed(video_index * 42)
            random.shuffle(video_list)
            return video_list


if __name__ == '__main__':
    processor = VideoProcessor()

    # 示例用法
    # 请替换为实际的视频文件路径
    main_video = "main_video.mp4"
    aux_video = "aux_video.mp4"
    output_video = "output_video.mp4"

    try:
        # 1. 切割主视频和辅助视频
        log_info(f"切割主视频 {main_video}...")
        main_scenes = processor.split_video_into_scenes(main_video)
        log_info(f"主视频切割完成，共 {len(main_scenes)} 段")

        log_info(f"切割辅助视频 {aux_video}...")
        aux_scenes = processor.split_video_into_scenes(aux_video)
        log_info(f"辅助视频切割完成，共 {len(aux_scenes)} 段")

        # 2. 查找相似视频段
        log_info("查找相似视频段...")
        similar_matches = processor.find_similar_scenes(main_scenes, aux_scenes, main_video, aux_video)
        log_info(f"找到 {len(similar_matches)} 对相似视频段")

        # 3. 替换并合成视频
        log_info("替换并合成视频...")
        processor.replace_and_concatenate_videos(main_video, aux_video, main_scenes, aux_scenes, similar_matches, output_video)
        log_info(f"新视频已保存到 {output_video}")
    except Exception as e:
        log_info(f"处理过程中发生错误: {str(e)}")


