import request from '@/utils/request'

// 基础配置
const BASE_URL = '/api/activation'

/**
 * 激活码管理API
 */
export default {
  /**
   * 获取所有激活码列表
   */
  getActivationCodeList() {
    return request({
      url: `${BASE_URL}/list`,
      method: 'get'
    })
  },

  /**
   * 获取激活码详细信息
   * @param {string} activationCode 激活码
   */
  getActivationCodeInfo(activationCode) {
    return request({
      url: `${BASE_URL}/info/${activationCode}`,
      method: 'get'
    })
  },

  /**
   * 生成新的激活码
   * @param {Object} data 激活码数据
   * @param {string} data.userId 用户ID
   * @param {string} data.expireTime 过期时间 (ISO格式)
   * @param {number} data.maxActivations 最大激活次数
   * @param {string} data.remark 备注
   */
  generateActivationCode(data) {
    return request({
      url: `${BASE_URL}/generate`,
      method: 'post',
      data
    })
  },

  /**
   * 禁用激活码
   * @param {string} activationCode 激活码
   */
  disableActivationCode(activationCode) {
    return request({
      url: `${BASE_URL}/disable/${activationCode}`,
      method: 'post'
    })
  },

  /**
   * 启用激活码
   * @param {string} activationCode 激活码
   */
  enableActivationCode(activationCode) {
    return request({
      url: `${BASE_URL}/enable/${activationCode}`,
      method: 'post'
    })
  },

  /**
   * 删除激活码
   * @param {string} activationCode 激活码
   */
  deleteActivationCode(activationCode) {
    return request({
      url: `${BASE_URL}/delete/${activationCode}`,
      method: 'delete'
    })
  },

  /**
   * 编辑激活码
   * @param {string} activationCode 激活码
   * @param {Object} data 更新数据
   * @param {string} data.userId 用户ID
   * @param {string} data.expireTime 过期时间 (ISO格式)
   * @param {number} data.maxActivations 最大激活次数
   * @param {string} data.remark 备注
   */
  updateActivationCode(activationCode, data) {
    return request({
      url: `${BASE_URL}/update/${activationCode}`,
      method: 'put',
      data
    })
  },

  /**
   * 验证激活码
   * @param {Object} data 验证数据
   * @param {string} data.activationCode 激活码
   * @param {string} data.machineCode 机器码
   * @param {Object} data.machineDetails 机器详细信息
   */
  verifyActivation(data) {
    return request({
      url: `${BASE_URL}/verify`,
      method: 'post',
      data
    })
  },

  /**
   * 检查机器激活状态
   * @param {string} machineCode 机器码
   */
  checkMachineActivation(machineCode) {
    return request({
      url: `${BASE_URL}/check/${machineCode}`,
      method: 'get'
    })
  },

  /**
   * 获取所有机器信息
   */
  getAllMachineInfo() {
    return request({
      url: `${BASE_URL}/machines`,
      method: 'get'
    })
  },

  /**
   * 获取激活统计信息
   */
  getActivationStatistics() {
    return request({
      url: `${BASE_URL}/statistics`,
      method: 'get'
    })
  },

  /**
   * 清理过期激活码
   */
  cleanupExpiredCodes() {
    return request({
      url: `${BASE_URL}/cleanup`,
      method: 'post'
    })
  },

  /**
   * 健康检查
   */
  healthCheck() {
    return request({
      url: `${BASE_URL}/health`,
      method: 'get'
    })
  }
}
