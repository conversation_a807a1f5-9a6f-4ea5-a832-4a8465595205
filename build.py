#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化打包脚本
使用PyInstaller将程序打包为可执行文件
"""

import os
import sys
import shutil
import subprocess
import platform
import locale
from pathlib import Path

# 导入日志系统
try:
    from logger_system import log_info, log_error, log_warning, log_debug
except ImportError:
    # 备用函数
    def log_info(msg): print(msg)
    def log_error(msg): print(f"ERROR: {msg}")
    def log_warning(msg): print(f"WARNING: {msg}")
    def log_debug(msg): print(f"DEBUG: {msg}")

# 设置环境编码
if platform.system() == "Windows":
    # Windows系统编码设置
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    # 注释掉stdout重定向，避免与日志系统冲突
    # try:
    #     import codecs
    #     sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    #     sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())
    # except:
    #     pass

class BuildManager:
    """打包管理器"""

    def __init__(self):
        self.app_name = "余下视频混剪工具"
        self.version = "2.0"
        self.main_script = "enhanced_video_deduplication_gui.py"
        self.build_dir = "build"
        self.dist_dir = "dist"
        self.spec_file = f"{self.app_name}.spec"
        self.ffmpeg_paths = self._find_local_ffmpeg()

    def _find_local_ffmpeg(self):
        """查找本地FFmpeg文件"""
        ffmpeg_paths = []

        # 检查常见的FFmpeg安装路径
        if platform.system() == "Windows":
            possible_paths = [
                # 当前目录
                "ffmpeg.exe",
                # 常见安装路径
                r"C:\ffmpeg\bin\ffmpeg.exe",
                r"C:\Program Files\ffmpeg\bin\ffmpeg.exe",
                r"C:\Program Files (x86)\ffmpeg\bin\ffmpeg.exe",
                # 用户目录
                os.path.join(os.path.expanduser("~"), "ffmpeg", "bin", "ffmpeg.exe"),
                # 检查PATH环境变量中的路径
            ]

            # 检查PATH环境变量
            path_env = os.environ.get('PATH', '')
            for path_dir in path_env.split(os.pathsep):
                if path_dir.strip():
                    ffmpeg_exe = os.path.join(path_dir.strip(), 'ffmpeg.exe')
                    if os.path.exists(ffmpeg_exe):
                        possible_paths.append(ffmpeg_exe)
        else:
            possible_paths = [
                "/usr/bin/ffmpeg",
                "/usr/local/bin/ffmpeg",
                "/opt/homebrew/bin/ffmpeg",  # macOS with Homebrew
                "/snap/bin/ffmpeg",  # Ubuntu snap
            ]

        # 验证路径是否存在且可执行
        for path in possible_paths:
            if os.path.exists(path):
                try:
                    # 测试FFmpeg是否可用
                    result = subprocess.run([path, '-version'],
                                          capture_output=True,
                                          text=True,
                                          timeout=5)
                    if result.returncode == 0:
                        ffmpeg_paths.append(path)
                        print(f"✅ 找到FFmpeg: {path}")
                except Exception:
                    continue

        if not ffmpeg_paths:
            print("⚠️ 未找到本地FFmpeg，将构建不包含FFmpeg的版本")
            print("建议：")
            if platform.system() == "Windows":
                print("1. 下载FFmpeg并解压到 C:\\ffmpeg")
                print("2. 或将ffmpeg.exe放到项目目录")
            else:
                print("1. 安装FFmpeg: sudo apt install ffmpeg (Ubuntu)")
                print("2. 或使用: brew install ffmpeg (macOS)")

        return ffmpeg_paths

    def check_environment(self):
        """检查打包环境"""
        print(f"🔍 检查打包环境...")
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version < (3, 8):
            print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
            print("需要Python 3.8或更高版本")
            return False
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # 检查PyInstaller
        try:
            import PyInstaller
            print(f"✅ PyInstaller版本: {PyInstaller.__version__}")
        except ImportError:
            print(f"❌ 未安装PyInstaller")
            print("请运行: pip install pyinstaller")
            return False
        
        # 检查主程序文件
        if not os.path.exists(self.main_script):
            print(f"❌ 未找到主程序文件: {self.main_script}")
            return False
        print(f"✅ 找到主程序文件: {self.main_script}")
        
        # 检查关键依赖
        required_modules = [
            'PyQt5', 'cv2', 'numpy', 'moviepy', 'scenedetect'
        ]
        
        missing_modules = []
        for module in required_modules:
            try:
                __import__(module)
                print(f"✅ {module}")
            except ImportError:
                missing_modules.append(module)
                print(f"❌ {module}")
        
        if missing_modules:
            print(f"缺少依赖模块: {', '.join(missing_modules)}")
            print("请运行: pip install -r requirements.txt")
            return False
        
        return True
    
    def clean_build(self):
        """清理构建目录"""
        print("🧹 清理构建目录...")
        
        dirs_to_clean = [self.build_dir, self.dist_dir]
        files_to_clean = [self.spec_file]
        
        for dir_path in dirs_to_clean:
            if os.path.exists(dir_path):
                shutil.rmtree(dir_path)
                print(f"  删除目录: {dir_path}")
        
        for file_path in files_to_clean:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"  删除文件: {file_path}")
    
    def generate_spec_file(self):
        """生成包含FFmpeg的spec文件"""
        print("📝 生成PyInstaller配置文件...")

        try:
            # 创建自定义spec文件内容
            spec_content = self._create_spec_content()

            # 写入spec文件
            with open(self.spec_file, 'w', encoding='utf-8') as f:
                f.write(spec_content)

            print(f"✅ spec文件生成成功: {self.spec_file}")
            if self.ffmpeg_paths:
                print(f"✅ 已包含FFmpeg: {len(self.ffmpeg_paths)} 个文件")
            return True

        except Exception as e:
            print(f"❌ 生成spec文件时出错: {e}")
            return False

    def _create_spec_content(self):
        """创建spec文件内容"""
        # 准备二进制文件列表
        binaries = []

        # 添加FFmpeg到二进制文件
        if self.ffmpeg_paths:
            for ffmpeg_path in self.ffmpeg_paths:
                if platform.system() == "Windows":
                    binaries.append(f"(r'{ffmpeg_path}', '.')")
                else:
                    binaries.append(f"('{ffmpeg_path}', '.')")

        binaries_str = ',\n        '.join(binaries) if binaries else ''

        # 准备数据文件列表
        datas = [
            "('img', 'img')",
            "('moviepy_config.py', '.')",
            "('ffmpeg_installer.py', '.')",
        ]

        # 检查可选文件
        optional_files = [
            'activation_config.json',
            'config.ini',
            'README.md'
        ]

        for file_name in optional_files:
            if os.path.exists(file_name):
                datas.append(f"('{file_name}', '.')")

        datas_str = ',\n        '.join(datas)

        # 生成spec文件内容
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['{self.main_script}'],
    pathex=[],
    binaries=[
        {binaries_str}
    ],
    datas=[
        {datas_str}
    ],
    hiddenimports=[
        'cv2',
        'numpy',
        'moviepy',
        'moviepy.editor',
        'moviepy.config',
        'scenedetect',
        'PyQt5',
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'requests',
        'PIL',
        'PIL.Image',
        'whisper',
        'openai',
        'urllib.request',
        'urllib.parse',
        'urllib.error',
        'zipfile',
        'shutil',
        'platform',
        'tempfile',
        'json',
        'threading',
        'queue',
        'multiprocessing',
        'concurrent.futures',
        'subprocess',
        'traceback',
        'uuid',
        'time',
        'random',
        'glob',
        'itertools',
        'collections',
        'functools',
        'operator',
        'math',
        'statistics',
        'hashlib',
        'base64',
        'datetime',
        'logging',
        'configparser',
        'pathlib',
        'typing',
        'dataclasses',
        'enum',
        'abc',
        'contextlib',
        'weakref',
        'copy',
        'pickle',
        'sqlite3',
        'http.client',
        'ssl',
        'socket',
        'email',
        'mimetypes',
        'encodings',
        'codecs',
        'unicodedata',
        'string',
        're',
        'fnmatch',
        'linecache',
        'tokenize',
        'keyword',
        'builtins',
        'sys',
        'os',
        'io',
        'gc',
        'warnings',
        'importlib',
        'pkgutil',
        'inspect',
        'types',
        'ctypes',
        'struct',
        'array',
        'heapq',
        'bisect',
        'decimal',
        'fractions',
        'numbers',
        'cmath',
        'imageio',
        'imageio_ffmpeg',
        # 修复jaraco.text相关依赖
        'jaraco.text',
        'jaraco.functools',
        'jaraco.context',
        'autocommand',
        'more_itertools',
        # 其他可能缺失的pkg_resources相关依赖
        'pkg_resources',
        'pkg_resources._vendor',
        'pkg_resources.extern',
        'setuptools',
        'setuptools.extern',
        'setuptools._vendor'
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'scipy',
        'pandas',
        'jupyter',
        'notebook',
        'IPython',
        'sphinx',
        'pytest',
        'setuptools',
        'pip',
        'wheel'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{self.app_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='img/logo.ico' if os.path.exists('img/logo.ico') else None
)
'''
        return spec_content
    
    def build_executable(self):
        """构建可执行文件"""
        print("🔨 开始构建可执行文件...")
        
        if not os.path.exists(self.spec_file):
            print(f"❌ 未找到spec文件: {self.spec_file}")
            return False
        
        # 构建命令
        cmd = [
            "pyinstaller",
            "--clean",  # 清理缓存
            "--noconfirm",  # 不询问覆盖
            self.spec_file
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        try:
            # 执行构建 - 修复编码问题
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                encoding='utf-8',
                errors='ignore',  # 忽略编码错误
                universal_newlines=True
            )

            # 实时输出构建日志
            while True:
                try:
                    output = process.stdout.readline()
                    if output == '' and process.poll() is not None:
                        break
                    if output:
                        # 清理输出中的特殊字符
                        clean_output = output.strip().encode('utf-8', errors='ignore').decode('utf-8')
                        print(clean_output)
                except UnicodeDecodeError:
                    # 如果仍有编码错误，跳过这行
                    continue
            
            # 检查构建结果
            if process.returncode == 0:
                print(f"✅ 构建成功完成")
                return True
            else:
                print(f"❌ 构建失败，退出码: {process.returncode}")
                return False
                
        except Exception as e:
            print(f"❌ 构建过程出错: {e}")
            return False
    
    def post_build_tasks(self):
        """构建后处理"""
        print(f"📦 执行构建后处理...")

        # 检查输出文件
        if platform.system() == "Windows":
            exe_name = f"{self.app_name}.exe"
        else:
            exe_name = self.app_name

        exe_path = os.path.join(self.dist_dir, exe_name)

        if not os.path.exists(exe_path):
            print(f"❌ 未找到可执行文件: {exe_path}")
            return False

        print(f"✅ 可执行文件已生成: {exe_path}")

        # 获取文件大小
        file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
        print(f"📏 文件大小: {file_size:.1f} MB")

        # 验证FFmpeg是否已包含
        self._verify_ffmpeg_inclusion()

        # 复制必要文件到dist目录
        files_to_copy = [
            'README.md',
            '余下视频混剪工具v2.0完整说明书.md',
            'config_example.ini',
            'FFMPEG_FIX_README.md'
        ]

        for file_name in files_to_copy:
            if os.path.exists(file_name):
                dest_path = os.path.join(self.dist_dir, file_name)
                shutil.copy2(file_name, dest_path)
                print(f"  复制文件: {file_name}")

        # 创建启动脚本
        self.create_launcher_scripts()

        # 创建FFmpeg验证脚本
        self._create_ffmpeg_test_script()

        return True

    def _verify_ffmpeg_inclusion(self):
        """验证FFmpeg是否已包含在打包中"""
        print("🔍 验证FFmpeg包含情况...")

        if not self.ffmpeg_paths:
            print("⚠️ 未包含FFmpeg - 程序可能需要用户手动安装FFmpeg")
            return False

        # 检查dist目录中是否有ffmpeg
        dist_ffmpeg = os.path.join(self.dist_dir, 'ffmpeg.exe' if platform.system() == "Windows" else 'ffmpeg')

        if os.path.exists(dist_ffmpeg):
            print(f"✅ FFmpeg已包含在打包中: {dist_ffmpeg}")
            return True
        else:
            print("⚠️ FFmpeg可能未正确包含，请检查打包配置")
            return False

    def _create_ffmpeg_test_script(self):
        """创建FFmpeg测试脚本"""
        print("📜 创建FFmpeg测试脚本...")

        if platform.system() == "Windows":
            test_content = '''@echo off
chcp 65001 >nul
echo ========================================
echo     FFmpeg 测试脚本
echo ========================================
echo.

echo 正在测试FFmpeg...

if exist "ffmpeg.exe" (
    echo ✅ 找到FFmpeg: ffmpeg.exe
    ffmpeg.exe -version
    if %errorlevel% == 0 (
        echo.
        echo ✅ FFmpeg测试通过！
    ) else (
        echo.
        echo ❌ FFmpeg测试失败
    )
) else (
    echo ❌ 未找到FFmpeg
    echo 请确保ffmpeg.exe在程序目录中
)

echo.
pause
'''
            test_path = os.path.join(self.dist_dir, "测试FFmpeg.bat")
            with open(test_path, 'w', encoding='utf-8') as f:
                f.write(test_content)
            print(f"  创建FFmpeg测试脚本: 测试FFmpeg.bat")

        else:
            test_content = '''#!/bin/bash
echo "========================================"
echo "     FFmpeg 测试脚本"
echo "========================================"
echo

echo "正在测试FFmpeg..."

if [ -f "ffmpeg" ]; then
    echo "✅ 找到FFmpeg: ffmpeg"
    ./ffmpeg -version
    if [ $? -eq 0 ]; then
        echo
        echo "✅ FFmpeg测试通过！"
    else
        echo
        echo "❌ FFmpeg测试失败"
    fi
else
    echo "❌ 未找到FFmpeg"
    echo "请确保ffmpeg在程序目录中"
fi

echo
read -p "按Enter键退出..."
'''
            test_path = os.path.join(self.dist_dir, "测试FFmpeg.sh")
            with open(test_path, 'w', encoding='utf-8') as f:
                f.write(test_content)

            # 设置执行权限
            os.chmod(test_path, 0o755)
            print(f"  创建FFmpeg测试脚本: 测试FFmpeg.sh")
    
    def create_launcher_scripts(self):
        """创建启动脚本"""
        log_info("📜 创建启动脚本...")
        
        if platform.system() == "Windows":
            # Windows批处理文件 - 移除emoji字符以兼容GBK编码
            bat_content = f'''@echo off
chcp 65001 >nul
title {self.app_name} v{self.version}
echo {self.app_name} v{self.version}
echo 正在启动程序...
echo.

"{self.app_name}.exe"

if errorlevel 1 (
    echo.
    echo 程序运行出错
    echo 请检查系统要求和依赖
    pause
)
'''
            bat_path = os.path.join(self.dist_dir, "启动程序.bat")
            with open(bat_path, 'w', encoding='utf-8') as f:
                f.write(bat_content)
            print(f"  创建Windows启动脚本: 启动程序.bat")
        
        # Linux/Mac shell脚本 - 保留emoji字符，因为shell支持UTF-8
        sh_content = f'''#!/bin/bash
echo "{self.app_name} v{self.version}"
echo "正在启动程序..."
echo

./{self.app_name}

if [ $? -ne 0 ]; then
    echo
    echo "程序运行出错"
    echo "请检查系统要求和依赖"
    read -p "按Enter键退出..."
fi
'''
        sh_path = os.path.join(self.dist_dir, "启动程序.sh")
        with open(sh_path, 'w', encoding='utf-8') as f:
            f.write(sh_content)

        # 设置执行权限
        if platform.system() != "Windows":
            os.chmod(sh_path, 0o755)

        log_info(f"  创建Shell启动脚本: 启动程序.sh")
    
    def create_installer(self):
        """创建安装包（可选）"""
        print(f"📦 创建发布包...")
        
        # 创建发布目录
        release_dir = f"{self.app_name}_v{self.version}_{platform.system()}"
        if os.path.exists(release_dir):
            shutil.rmtree(release_dir)
        
        # 复制dist目录内容
        shutil.copytree(self.dist_dir, release_dir)
        
        # 创建压缩包
        archive_name = f"{release_dir}.zip"
        shutil.make_archive(release_dir, 'zip', release_dir)
        
        print(f"✅ 发布包已创建: {archive_name}")
        
        # 清理临时目录
        shutil.rmtree(release_dir)
        
        return archive_name
    
    def build(self, clean=True, create_installer=True):
        """执行完整构建流程"""
        print("🚀 开始自动化打包流程（包含FFmpeg）")
        print("=" * 60)

        # 0. 显示FFmpeg状态
        print(f"🔍 FFmpeg检查结果:")
        if self.ffmpeg_paths:
            print(f"✅ 找到 {len(self.ffmpeg_paths)} 个FFmpeg文件:")
            for path in self.ffmpeg_paths:
                print(f"   - {path}")
            print("✅ 将包含FFmpeg到打包中")
        else:
            print("⚠️ 未找到FFmpeg，将构建不包含FFmpeg的版本")
            print("⚠️ 用户需要手动安装FFmpeg才能使用视频合成功能")
        print()

        # 1. 检查环境
        if not self.check_environment():
            return False

        # 2. 清理构建目录
        if clean:
            self.clean_build()

        # 3. 生成spec文件
        if not self.generate_spec_file():
            return False

        # 4. 构建可执行文件
        if not self.build_executable():
            return False

        # 5. 构建后处理
        if not self.post_build_tasks():
            return False

        # 6. 创建安装包
        if create_installer:
            archive_name = self.create_installer()

        print("\n" + "=" * 60)
        print("🎉 打包完成！")
        print(f"📁 输出目录: {self.dist_dir}")
        if create_installer:
            print(f"📦 发布包: {archive_name}")

        # 显示FFmpeg状态总结
        print(f"\n🔧 FFmpeg状态:")
        if self.ffmpeg_paths:
            print("✅ FFmpeg已包含在程序中")
            print("✅ 程序应该可以正常进行视频合成")
        else:
            print("⚠️ FFmpeg未包含在程序中")
            print("⚠️ 用户需要手动安装FFmpeg")
            print("📖 请参考 FFMPEG_FIX_README.md 获取安装指导")

        print("\n💡 使用说明:")
        print("1. 运行可执行文件测试功能")
        print("2. 测试视频合成功能是否正常")
        print("3. 如有FFmpeg问题，运行'测试FFmpeg'脚本")
        print("4. 在不同系统上测试兼容性")

        return True

def main():
    """主函数"""
    builder = BuildManager()
    
    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description="自动化打包脚本")
    parser.add_argument("--no-clean", action="store_true", help="不清理构建目录")
    parser.add_argument("--no-installer", action="store_true", help="不创建安装包")
    
    args = parser.parse_args()
    
    # 执行构建
    success = builder.build(
        clean=not args.no_clean,
        create_installer=not args.no_installer
    )
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
