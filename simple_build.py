#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的打包脚本，避免编码问题
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def log_info(msg):
    print(f"[INFO] {msg}")

def log_error(msg):
    print(f"[ERROR] {msg}")

def log_warning(msg):
    print(f"[WARNING] {msg}")

class SimpleBuild:
    def __init__(self):
        self.project_dir = os.path.dirname(os.path.abspath(__file__))
        self.dist_dir = os.path.join(self.project_dir, 'dist')
        
    def create_spec_file(self):
        """创建spec文件避免命令行编码问题"""
        spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['enhanced_video_deduplication_gui.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('img', 'img'),
        ('moviepy_config.py', '.'),
        ('ffmpeg_installer.py', '.'),
    ],
    hiddenimports=[
        'cv2',
        'numpy',
        'moviepy',
        'scenedetect',
        'PyQt5',
        'requests',
        'PIL',
        'whisper',
        'openai',
        'urllib.request',
        'zipfile',
        'shutil',
        'platform',
        'tempfile',
        'json',
        'threading',
        'queue',
        'multiprocessing',
        'concurrent.futures',
        'subprocess',
        'traceback',
        'uuid',
        'time',
        'random',
        'glob',
        'itertools',
        'collections',
        'functools',
        'operator',
        'math',
        'statistics',
        'hashlib',
        'base64',
        'datetime',
        'calendar',
        'locale',
        'logging',
        'configparser',
        'argparse',
        'pathlib',
        'typing',
        'dataclasses',
        'enum',
        'abc',
        'contextlib',
        'weakref',
        'copy',
        'pickle',
        'sqlite3',
        'http.client',
        'urllib.parse',
        'urllib.error',
        'ssl',
        'socket',
        'email',
        'mimetypes',
        'encodings',
        'codecs',
        'unicodedata',
        'string',
        're',
        'fnmatch',
        'linecache',
        'tokenize',
        'keyword',
        'builtins',
        'sys',
        'os',
        'io',
        'gc',
        'warnings',
        'importlib',
        'pkgutil',
        'inspect',
        'types',
        'ctypes',
        'struct',
        'array',
        'collections.abc',
        'heapq',
        'bisect',
        'decimal',
        'fractions',
        'numbers',
        'cmath',
        'random',
        'itertools',
        'functools',
        'operator',
        'pathlib',
        'fileinput',
        'stat',
        'filecmp',
        'tempfile',
        'glob',
        'fnmatch',
        'linecache',
        'shutil',
        'pickle',
        'copyreg',
        'shelve',
        'marshal',
        'dbm',
        'sqlite3',
        'zlib',
        'gzip',
        'bz2',
        'lzma',
        'zipfile',
        'tarfile',
        'csv',
        'configparser',
        'netrc',
        'xdrlib',
        'plistlib',
        'hashlib',
        'hmac',
        'secrets',
        'os',
        'io',
        'time',
        'argparse',
        'getopt',
        'logging',
        'getpass',
        'curses',
        'platform',
        'errno',
        'ctypes',
        'threading',
        '_thread',
        'multiprocessing',
        'concurrent.futures',
        'subprocess',
        'sched',
        'queue',
        'dummy_threading',
        'contextvars',
        'asyncio',
        'socket',
        'ssl',
        'select',
        'selectors',
        'asyncore',
        'asynchat',
        'signal',
        'mmap',
        'email',
        'json',
        'mailcap',
        'mailbox',
        'mimetypes',
        'base64',
        'binhex',
        'binascii',
        'quopri',
        'uu',
        'html',
        'xml',
        'urllib',
        'http',
        'ftplib',
        'poplib',
        'imaplib',
        'nntplib',
        'smtplib',
        'smtpd',
        'telnetlib',
        'uuid',
        'socketserver',
        'xmlrpc',
        'ipaddress',
        'audioop',
        'aifc',
        'sunau',
        'wave',
        'chunk',
        'colorsys',
        'imghdr',
        'sndhdr',
        'ossaudiodev',
        'gettext',
        'locale',
        'codecs',
        'encodings',
        'unicodedata',
        'stringprep',
        'readline',
        'rlcompleter',
        'pydoc',
        'doctest',
        'unittest',
        'test',
        '2to3',
        'lib2to3',
        'tkinter',
        'turtle',
        'turtledemo',
        'idle',
        'pdb',
        'profile',
        'pstats',
        'timeit',
        'trace',
        'tracemalloc',
        'faulthandler',
        'cgitb',
        'pyclbr',
        'py_compile',
        'compileall',
        'dis',
        'pickletools',
        'distutils',
        'ensurepip',
        'venv',
        'zipapp',
        'modulefinder',
        'runpy',
        'importlib',
        'pkgutil',
        'pkgutil',
        'zipimport',
        'imp',
        'importlib',
        'sys',
        'builtins',
        '__main__',
        '__future__',
        'gc',
        'weakref',
        'winreg',
        'winsound',
        'msvcrt',
        '_winapi',
        'msilib',
        'nt',
        'posix',
        'pwd',
        'spwd',
        'grp',
        'crypt',
        'termios',
        'tty',
        'pty',
        'fcntl',
        'pipes',
        'resource',
        'nis',
        'syslog',
        'optparse',
        'imp'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'scipy',
        'pandas',
        'jupyter',
        'notebook',
        'IPython',
        'sphinx',
        'pytest',
        'setuptools',
        'pip',
        'wheel',
        'distutils'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='VideoDeduplicationTool_v2.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='img/logo.ico'
)
'''
        
        spec_file = os.path.join(self.project_dir, 'video_tool.spec')
        try:
            with open(spec_file, 'w', encoding='utf-8') as f:
                f.write(spec_content)
            log_info(f"✅ 已创建spec文件: {spec_file}")
            return spec_file
        except Exception as e:
            log_error(f"❌ 创建spec文件失败: {e}")
            return None
    
    def build_with_spec(self, spec_file):
        """使用spec文件构建"""
        try:
            log_info("开始使用spec文件构建...")
            
            # 设置环境变量避免编码问题
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            
            cmd = ['pyinstaller', '--clean', '--noconfirm', spec_file]
            
            log_info(f"执行命令: {' '.join(cmd)}")
            
            # 使用Popen避免编码问题
            process = subprocess.Popen(
                cmd,
                cwd=self.project_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=env,
                universal_newlines=True,
                encoding='utf-8',
                errors='replace'
            )
            
            # 实时输出
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    print(output.strip())
            
            # 获取返回码
            rc = process.poll()
            
            if rc == 0:
                log_info("✅ 构建成功")
                return True
            else:
                # 读取错误信息
                stderr = process.stderr.read()
                log_error(f"❌ 构建失败: {stderr}")
                return False
                
        except Exception as e:
            log_error(f"❌ 构建过程中发生错误: {e}")
            return False
    
    def post_build_setup(self):
        """构建后设置"""
        log_info("执行构建后设置...")
        
        try:
            # 查找生成的exe文件
            exe_file = None
            for root, dirs, files in os.walk(self.dist_dir):
                for file in files:
                    if file.endswith('.exe'):
                        exe_file = os.path.join(root, file)
                        break
                if exe_file:
                    break
            
            if not exe_file:
                log_error("❌ 未找到生成的exe文件")
                return False
            
            exe_dir = os.path.dirname(exe_file)
            log_info(f"✅ 找到exe文件: {exe_file}")
            
            # 复制必要文件
            necessary_files = [
                'activation_config.json',
                'config.ini'
            ]
            
            for file_name in necessary_files:
                src_file = os.path.join(self.project_dir, file_name)
                if os.path.exists(src_file):
                    dst_file = os.path.join(exe_dir, file_name)
                    shutil.copy2(src_file, dst_file)
                    log_info(f"✅ 已复制: {file_name}")
            
            log_info("✅ 构建后设置完成")
            return True
            
        except Exception as e:
            log_error(f"❌ 构建后设置失败: {e}")
            return False
    
    def build(self):
        """执行构建"""
        log_info("=== 开始简化打包流程 ===")
        
        # 创建spec文件
        spec_file = self.create_spec_file()
        if not spec_file:
            return False
        
        # 使用spec文件构建
        if not self.build_with_spec(spec_file):
            return False
        
        # 构建后设置
        if not self.post_build_setup():
            return False
        
        log_info("=== 构建完成 ===")
        log_info(f"输出目录: {self.dist_dir}")
        
        return True

def main():
    """主函数"""
    builder = SimpleBuild()
    
    if builder.build():
        print("\n🎉 构建成功！")
        print(f"📁 输出目录: {builder.dist_dir}")
        print("✨ 程序已成功打包！")
    else:
        print("\n❌ 构建失败！")
        print("请检查错误信息并重试。")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
