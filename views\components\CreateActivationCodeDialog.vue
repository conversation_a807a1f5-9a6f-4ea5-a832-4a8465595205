<template>
  <el-dialog
    title="生成激活码"
    :visible.sync="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="用户ID" prop="userId">
        <el-input
          v-model="form.userId"
          placeholder="请输入用户ID（可选）"
          clearable
        />
        <div class="form-tip">用于标识激活码的归属用户</div>
      </el-form-item>
      
      <el-form-item label="最大激活次数" prop="maxActivations">
        <el-input-number
          v-model="form.maxActivations"
          :min="1"
          :max="100"
          placeholder="最大激活次数"
          style="width: 100%"
        />
        <div class="form-tip">该激活码最多可以激活的设备数量</div>
      </el-form-item>
      
      <el-form-item label="过期时间" prop="expireTime">
        <el-date-picker
          v-model="form.expireTime"
          type="datetime"
          placeholder="选择过期时间"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="width: 100%"
        />
        <div class="form-tip">留空表示永不过期</div>
      </el-form-item>
      
      <el-form-item label="备注信息" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">
        生成激活码
      </el-button>
    </div>
    
    <!-- 生成成功结果对话框 -->
    <el-dialog
      title="激活码生成成功"
      :visible.sync="resultDialogVisible"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="result-content">
        <div class="success-icon">
          <i class="el-icon-success"></i>
        </div>
        <div class="activation-code-display">
          <label>激活码：</label>
          <div class="code-value">
            {{ generatedCode }}
            <el-button
              type="text"
              icon="el-icon-copy-document"
              @click="copyToClipboard"
            >
              复制
            </el-button>
          </div>
        </div>
        <div class="code-info">
          <p><strong>用户ID：</strong>{{ form.userId || '未设置' }}</p>
          <p><strong>最大激活次数：</strong>{{ form.maxActivations }}</p>
          <p><strong>过期时间：</strong>{{ form.expireTime || '永不过期' }}</p>
          <p v-if="form.remark"><strong>备注：</strong>{{ form.remark }}</p>
        </div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleResultClose">确定</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import activationApi from '@/api/activation'

export default {
  name: 'CreateActivationCodeDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      resultDialogVisible: false,
      loading: false,
      generatedCode: '',
      
      form: {
        userId: '',
        maxActivations: 1,
        expireTime: '',
        remark: ''
      },
      
      rules: {
        maxActivations: [
          { required: true, message: '请输入最大激活次数', trigger: 'blur' },
          { type: 'number', min: 1, max: 100, message: '激活次数必须在1-100之间', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    // 提交表单
    async handleSubmit() {
      try {
        await this.$refs.form.validate()
        
        this.loading = true
        
        // 准备提交数据
        const submitData = {
          userId: this.form.userId || null,
          maxActivations: this.form.maxActivations,
          expireTime: this.form.expireTime ? this.formatDateTime(this.form.expireTime) : null,
          remark: this.form.remark || null
        }
        
        const response = await activationApi.generateActivationCode(submitData)
        
        if (response.success) {
          this.generatedCode = response.activationCode.activationCode
          this.resultDialogVisible = true
          this.$message.success('激活码生成成功')
        } else {
          this.$message.error(response.message || '生成激活码失败')
        }
      } catch (error) {
        if (error !== false) { // 表单验证失败时不显示错误
          console.error('生成激活码失败:', error)
          this.$message.error('生成激活码失败')
        }
      } finally {
        this.loading = false
      }
    },
    
    // 关闭对话框
    handleClose() {
      this.dialogVisible = false
      this.resetForm()
    },
    
    // 关闭结果对话框
    handleResultClose() {
      this.resultDialogVisible = false
      this.dialogVisible = false
      this.resetForm()
      this.$emit('success')
    },
    
    // 重置表单
    resetForm() {
      this.form = {
        userId: '',
        maxActivations: 1,
        expireTime: '',
        remark: ''
      }
      this.generatedCode = ''
      
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
    },
    
    // 复制到剪贴板
    async copyToClipboard() {
      try {
        await navigator.clipboard.writeText(this.generatedCode)
        this.$message.success('激活码已复制到剪贴板')
      } catch (error) {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = this.generatedCode
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success('激活码已复制到剪贴板')
      }
    },
    
    // 格式化日期时间为ISO格式
    formatDateTime(dateTime) {
      if (!dateTime) return null
      const date = new Date(dateTime)
      return date.toISOString().slice(0, 19) // 移除毫秒和时区信息
    }
  }
}
</script>

<style lang="scss" scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.result-content {
  text-align: center;
  
  .success-icon {
    margin-bottom: 20px;
    
    i {
      font-size: 48px;
      color: #67C23A;
    }
  }
  
  .activation-code-display {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #F5F7FA;
    border-radius: 4px;
    
    label {
      display: block;
      font-weight: bold;
      margin-bottom: 10px;
      color: #303133;
    }
    
    .code-value {
      font-family: 'Courier New', monospace;
      font-size: 18px;
      font-weight: bold;
      color: #409EFF;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
    }
  }
  
  .code-info {
    text-align: left;
    
    p {
      margin: 8px 0;
      color: #606266;
      
      strong {
        color: #303133;
      }
    }
  }
}
</style>
