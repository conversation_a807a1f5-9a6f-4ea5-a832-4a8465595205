2025-08-08 09:43:54 [INFO] ✅ 激活验证模块加载成功
2025-08-08 09:43:57 [INFO] ✅ 单实例应用初始化成功
2025-08-08 09:43:57 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-08-08 09:43:58 [INFO] 🔑 机器码: B609-6939-8E74-6EFF
2025-08-08 09:43:58 [INFO] 📁 配置文件路径: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\activation_config.json
2025-08-08 09:43:58 [INFO] 🔍 检查本地保存的激活码: 71C9-4CBE-4B7F-48BB
2025-08-08 09:43:58 [INFO] 🔍 与服务器验证激活码有效性
2025-08-08 09:43:58 [INFO] ✅ 服务器验证成功: 设备已激活
2025-08-08 09:43:58 [INFO] ✅ 激活码验证通过
2025-08-08 09:43:58 [INFO] ✅ 本地激活验证通过
2025-08-08 09:43:58 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-08-08 09:44:01 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-08-08 09:44:01 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-08-08 09:44:01 [INFO] 未检测到CUDA设备
2025-08-08 09:44:01 [INFO] VideoProcessor初始化:
2025-08-08 09:44:01 [INFO]   CPU线程数: 8
2025-08-08 09:44:01 [INFO]   GPU支持: 否
2025-08-08 09:44:01 [INFO]   GPU加速: 禁用
2025-08-08 09:44:01 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-08-08 09:44:01 [INFO] ✅ FFmpeg已配置: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-08-08 09:44:01 [INFO] 🔍 测试FFmpeg安装...
2025-08-08 09:44:01 [INFO] ✅ FFmpeg安装正确: ffmpeg version 7.0.2-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
2025-08-08 09:44:01 [INFO] ✅ 支持PCM音频编码
2025-08-08 09:44:01 [INFO] ✅ FFmpeg测试通过
2025-08-08 09:44:37 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-08-08 09:44:37 [INFO] 未检测到CUDA设备
2025-08-08 09:44:37 [INFO] VideoProcessor初始化:
2025-08-08 09:44:37 [INFO]   CPU线程数: 4
2025-08-08 09:44:37 [INFO]   GPU支持: 否
2025-08-08 09:44:37 [INFO]   GPU加速: 禁用
2025-08-08 09:44:37 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-08-08 09:44:37 [INFO] 使用PySceneDetect版本: 0.6.6
2025-08-08 09:44:37 [INFO] 成功导入ContentDetector(方法1)
2025-08-08 09:44:37 [INFO] 视频文件大小: 50.26 MB
2025-08-08 09:44:37 [INFO] 视频信息: 30.0 FPS, 7362 帧, 245.40 秒
2025-08-08 09:44:37 [INFO] 尝试使用PySceneDetect原生方法打开视频...
2025-08-08 09:44:37 [INFO] 开始执行场景检测...
2025-08-08 09:45:19 [INFO] 场景检测完成，耗时 41.92 秒
2025-08-08 09:45:19 [INFO] 检测到 7 个场景
2025-08-08 09:45:19 [INFO] 使用PySceneDetect版本: 0.6.6
2025-08-08 09:45:19 [INFO] 成功导入ContentDetector(方法1)
2025-08-08 09:45:19 [INFO] 视频文件大小: 19.39 MB
2025-08-08 09:45:19 [INFO] 视频信息: 24.0 FPS, 5890 帧, 245.42 秒
2025-08-08 09:45:19 [INFO] 尝试使用PySceneDetect原生方法打开视频...
2025-08-08 09:45:19 [INFO] 开始执行场景检测...
2025-08-08 09:45:40 [INFO] 场景检测完成，耗时 20.67 秒
2025-08-08 09:45:40 [INFO] 检测到 7 个场景
2025-08-08 09:45:40 [INFO] 优化相似度匹配: 主视频7场景, 辅助视频7场景
2025-08-08 09:45:40 [INFO] 正在并行提取主视频特征...
2025-08-08 09:45:40 [INFO] 增强精度模式: 视频段19.90秒，提取10帧
2025-08-08 09:45:40 [INFO] 增强精度模式: 视频段9.93秒，提取10帧
2025-08-08 09:45:40 [INFO] 增强精度模式: 视频段22.57秒，提取10帧
2025-08-08 09:45:40 [INFO] 增强精度模式: 视频段9.93秒，提取10帧
2025-08-08 09:45:45 [INFO] 增强精度模式: 视频段146.93秒，提取10帧
2025-08-08 09:45:45 [INFO] 增强精度模式: 视频段7.70秒，提取10帧
2025-08-08 09:45:45 [INFO] 增强精度模式: 视频段28.43秒，提取10帧
2025-08-08 09:45:50 [INFO] 正在并行提取辅助视频特征...
2025-08-08 09:45:50 [INFO] 增强精度模式: 视频段66.58秒，提取10帧
2025-08-08 09:45:50 [INFO] 增强精度模式: 视频段7.88秒，提取10帧
2025-08-08 09:45:50 [INFO] 增强精度模式: 视频段17.88秒，提取10帧
2025-08-08 09:45:50 [INFO] 增强精度模式: 视频段7.92秒，提取10帧
2025-08-08 09:45:56 [INFO] 增强精度模式: 视频段116.50秒，提取10帧
2025-08-08 09:45:57 [INFO] 增强精度模式: 视频段6.08秒，提取10帧
2025-08-08 09:45:57 [INFO] 增强精度模式: 视频段22.58秒，提取10帧
2025-08-08 09:46:01 [INFO] 正在并行计算相似度矩阵...
2025-08-08 09:46:01 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:01 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:01 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:01 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 09:46:02 [INFO] 正在查找最佳匹配...
2025-08-08 09:46:02 [INFO] 优化匹配完成: 找到 7 个匹配（限制: 7）
2025-08-08 09:46:02 [INFO] 使用临时路径处理视频: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\temp_output_0c276920_1754617562665_f39e2f1c.mp4
2025-08-08 09:46:02 [INFO] 最终输出路径: D:/25125/Videos/video_test/ttt.mp4
2025-08-08 09:46:02 [INFO] 加载主视频: D:/25125/Videos/测试自媒体ab.mp4
2025-08-08 09:46:03 [INFO] 主视频加载成功，时长: 245.41秒
2025-08-08 09:46:03 [INFO] 开始按顺序处理 7 个主视频片段...
2025-08-08 09:46:03 [INFO] 其中 7 个片段将被替换
2025-08-08 09:46:03 [INFO] 片段 1 标记为替换 (时长: 19.90秒)
2025-08-08 09:46:03 [INFO] 片段 2 标记为替换 (时长: 9.93秒)
2025-08-08 09:46:03 [INFO] 片段 3 标记为替换 (时长: 22.57秒)
2025-08-08 09:46:03 [INFO] 片段 4 标记为替换 (时长: 9.93秒)
2025-08-08 09:46:03 [INFO] 片段 5 标记为替换 (时长: 146.93秒)
2025-08-08 09:46:03 [INFO] 片段 6 标记为替换 (时长: 7.70秒)
2025-08-08 09:46:03 [INFO] 片段 7 标记为替换 (时长: 28.43秒)
2025-08-08 09:46:03 [INFO] 处理 7 个替换片段...
2025-08-08 09:46:03 [INFO] 使用传统匹配格式（场景索引）
2025-08-08 09:46:03 [INFO] 加载辅助视频: D:/25125/Videos/www333.mp4
2025-08-08 09:48:32 [INFO] 📦 窗口已最小化到任务栏
2025-08-08 09:48:32 [INFO] 📦 窗口已最小化到任务栏
2025-08-08 13:34:36 [INFO] 🚪 用户选择完全退出程序
2025-08-08 13:34:36 [ERROR] ❌ 退出程序时发生错误: 'VideoProcessingThread' object has no attribute 'stop'
2025-08-08 13:34:40 [INFO] ✅ 激活验证模块加载成功
2025-08-08 13:34:42 [INFO] ✅ 单实例应用初始化成功
2025-08-08 13:34:42 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-08-08 13:34:43 [INFO] 🔑 机器码: B609-6939-8E74-6EFF
2025-08-08 13:34:43 [INFO] 📁 配置文件路径: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\activation_config.json
2025-08-08 13:34:43 [INFO] 🔍 检查本地保存的激活码: 71C9-4CBE-4B7F-48BB
2025-08-08 13:34:43 [INFO] 🔍 与服务器验证激活码有效性
2025-08-08 13:34:43 [INFO] ✅ 服务器验证成功: 设备已激活
2025-08-08 13:34:43 [INFO] ✅ 激活码验证通过
2025-08-08 13:34:43 [INFO] ✅ 本地激活验证通过
2025-08-08 13:34:43 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-08-08 13:34:46 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-08-08 13:34:46 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-08-08 13:34:46 [INFO] 未检测到CUDA设备
2025-08-08 13:34:46 [INFO] VideoProcessor初始化:
2025-08-08 13:34:46 [INFO]   CPU线程数: 8
2025-08-08 13:34:46 [INFO]   GPU支持: 否
2025-08-08 13:34:46 [INFO]   GPU加速: 禁用
2025-08-08 13:34:46 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-08-08 13:34:46 [INFO] ✅ FFmpeg已配置: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-08-08 13:34:46 [INFO] 🔍 测试FFmpeg安装...
2025-08-08 13:34:46 [INFO] ✅ FFmpeg安装正确: ffmpeg version 7.0.2-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
2025-08-08 13:34:46 [INFO] ✅ 支持PCM音频编码
2025-08-08 13:34:46 [INFO] ✅ FFmpeg测试通过
2025-08-08 13:35:35 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-08-08 13:35:35 [INFO] 未检测到CUDA设备
2025-08-08 13:35:35 [INFO] VideoProcessor初始化:
2025-08-08 13:35:35 [INFO]   CPU线程数: 4
2025-08-08 13:35:35 [INFO]   GPU支持: 否
2025-08-08 13:35:35 [INFO]   GPU加速: 禁用
2025-08-08 13:35:35 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-08-08 13:35:35 [INFO] 使用PySceneDetect版本: 0.6.6
2025-08-08 13:35:35 [INFO] 成功导入ContentDetector(方法1)
2025-08-08 13:35:35 [INFO] 视频文件大小: 50.26 MB
2025-08-08 13:35:35 [INFO] 视频信息: 30.0 FPS, 7362 帧, 245.40 秒
2025-08-08 13:35:35 [INFO] 尝试使用PySceneDetect原生方法打开视频...
2025-08-08 13:35:35 [INFO] 开始执行场景检测...
2025-08-08 13:36:07 [INFO] 场景检测完成，耗时 32.30 秒
2025-08-08 13:36:07 [INFO] 检测到 7 个场景
2025-08-08 13:36:07 [INFO] 使用PySceneDetect版本: 0.6.6
2025-08-08 13:36:07 [INFO] 成功导入ContentDetector(方法1)
2025-08-08 13:36:07 [INFO] 视频文件大小: 19.39 MB
2025-08-08 13:36:07 [INFO] 视频信息: 24.0 FPS, 5890 帧, 245.42 秒
2025-08-08 13:36:07 [INFO] 尝试使用PySceneDetect原生方法打开视频...
2025-08-08 13:36:08 [INFO] 开始执行场景检测...
2025-08-08 13:36:33 [INFO] 场景检测完成，耗时 25.07 秒
2025-08-08 13:36:33 [INFO] 检测到 7 个场景
2025-08-08 13:36:33 [INFO] 优化相似度匹配: 主视频7场景, 辅助视频7场景
2025-08-08 13:36:33 [INFO] 正在并行提取主视频特征...
2025-08-08 13:36:33 [INFO] 增强精度模式: 视频段19.90秒，提取10帧
2025-08-08 13:36:33 [INFO] 增强精度模式: 视频段9.93秒，提取10帧
2025-08-08 13:36:33 [INFO] 增强精度模式: 视频段22.57秒，提取10帧
2025-08-08 13:36:33 [INFO] 增强精度模式: 视频段9.93秒，提取10帧
2025-08-08 13:36:37 [INFO] 增强精度模式: 视频段146.93秒，提取10帧
2025-08-08 13:36:38 [INFO] 增强精度模式: 视频段7.70秒，提取10帧
2025-08-08 13:36:38 [INFO] 增强精度模式: 视频段28.43秒，提取10帧
2025-08-08 13:36:43 [INFO] 正在并行提取辅助视频特征...
2025-08-08 13:36:43 [INFO] 增强精度模式: 视频段66.58秒，提取10帧
2025-08-08 13:36:43 [INFO] 增强精度模式: 视频段7.88秒，提取10帧
2025-08-08 13:36:43 [INFO] 增强精度模式: 视频段17.88秒，提取10帧
2025-08-08 13:36:43 [INFO] 增强精度模式: 视频段7.92秒，提取10帧
2025-08-08 13:36:48 [INFO] 增强精度模式: 视频段116.50秒，提取10帧
2025-08-08 13:36:49 [INFO] 增强精度模式: 视频段6.08秒，提取10帧
2025-08-08 13:36:49 [INFO] 增强精度模式: 视频段22.58秒，提取10帧
2025-08-08 13:36:54 [INFO] 正在并行计算相似度矩阵...
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:54 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:55 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:55 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:55 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:55 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:55 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:55 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:55 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:55 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:55 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:55 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:55 [DEBUG] 自适应特征权重: {'phash': 0.411764705882353, 'color_hist': 0.35294117647058826, 'edge': 0.23529411764705888}
2025-08-08 13:36:55 [INFO] 正在查找最佳匹配...
2025-08-08 13:36:55 [INFO] 优化匹配完成: 找到 7 个匹配（限制: 7）
2025-08-08 13:36:55 [INFO] 使用临时路径处理视频: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\temp_output_da6e7aa3_1754631415311_62aceb91.mp4
2025-08-08 13:36:55 [INFO] 最终输出路径: D:/25125/Videos/video_test/ttt.mp4
2025-08-08 13:36:55 [INFO] 加载主视频: D:/25125/Videos/测试自媒体ab.mp4
2025-08-08 13:36:55 [INFO] 视频文件大小: 50.26 MB
2025-08-08 13:36:55 [INFO] 开始加载视频: 测试自媒体ab.mp4
2025-08-08 13:36:55 [INFO] 视频路径: D:/25125/Videos/测试自媒体ab.mp4
2025-08-08 13:36:55 [INFO] 正在创建VideoFileClip对象...
2025-08-08 13:36:56 [INFO] VideoFileClip对象创建成功
2025-08-08 13:36:56 [INFO] 检查视频时长...
2025-08-08 13:36:56 [INFO] 视频时长: 245.41秒
2025-08-08 13:36:56 [INFO] 验证视频可读性...
2025-08-08 13:36:56 [INFO] ✅ 视频加载验证成功: 时长 245.41秒
2025-08-08 13:36:56 [INFO] 主视频加载成功，时长: 245.41秒
2025-08-08 13:36:56 [INFO] 开始按顺序处理 7 个主视频片段...
2025-08-08 13:36:56 [INFO] 其中 7 个片段将被替换
2025-08-08 13:36:56 [INFO] 片段 1 标记为替换 (时长: 19.90秒)
2025-08-08 13:36:56 [INFO] 片段 2 标记为替换 (时长: 9.93秒)
2025-08-08 13:36:56 [INFO] 片段 3 标记为替换 (时长: 22.57秒)
2025-08-08 13:36:56 [INFO] 片段 4 标记为替换 (时长: 9.93秒)
2025-08-08 13:36:56 [INFO] 片段 5 标记为替换 (时长: 146.93秒)
2025-08-08 13:36:56 [INFO] 片段 6 标记为替换 (时长: 7.70秒)
2025-08-08 13:36:56 [INFO] 片段 7 标记为替换 (时长: 28.43秒)
2025-08-08 13:36:56 [INFO] 处理 7 个替换片段...
2025-08-08 13:36:56 [INFO] 使用传统匹配格式（场景索引）
2025-08-08 13:36:56 [INFO] 加载辅助视频: D:/25125/Videos/www333.mp4
2025-08-08 13:36:56 [INFO] 视频文件大小: 19.39 MB
2025-08-08 13:36:56 [INFO] 开始加载视频: www333.mp4
2025-08-08 13:36:56 [INFO] 视频路径: D:/25125/Videos/www333.mp4
2025-08-08 13:36:56 [INFO] 正在创建VideoFileClip对象...
2025-08-08 13:37:56 [ERROR] 视频加载超时（60秒）: D:/25125/Videos/www333.mp4
2025-08-08 13:37:56 [INFO] 处理过程中发生错误: 无法加载辅助视频文件: D:/25125/Videos/www333.mp4
2025-08-08 13:37:56 [INFO] 主视频对象已关闭
2025-08-08 13:37:56 [INFO] 片段引用已清理
2025-08-08 13:37:56 [INFO] 内存清理完成
2025-08-08 13:37:56 [INFO] Error in replace_and_concatenate_videos: 无法加载辅助视频文件: D:/25125/Videos/www333.mp4
2025-08-08 13:37:56 [INFO] Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4936, in replace_and_concatenate_videos
    raise Exception(f"无法加载辅助视频文件: {aux_video_path}")
Exception: 无法加载辅助视频文件: D:/25125/Videos/www333.mp4

2025-08-08 13:37:56 [INFO] Error in replace_and_concatenate_videos: 无法加载辅助视频文件: D:/25125/Videos/www333.mp4
2025-08-08 13:37:56 [INFO] Traceback (most recent call last):
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\performance_optimizer.py", line 291, in replace_and_concatenate_videos
    return self.base_processor.replace_and_concatenate_videos(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\video_processor.py", line 4936, in replace_and_concatenate_videos
    raise Exception(f"无法加载辅助视频文件: {aux_video_path}")
Exception: 无法加载辅助视频文件: D:/25125/Videos/www333.mp4

2025-08-08 13:42:35 [INFO] 🚪 用户选择完全退出程序
2025-08-08 13:42:39 [INFO] ✅ 激活验证模块加载成功
2025-08-08 13:42:40 [INFO] ✅ 单实例应用初始化成功
2025-08-08 13:42:40 [INFO] ✅ 成功设置应用程序图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-08-08 13:42:40 [INFO] 🔑 机器码: B609-6939-8E74-6EFF
2025-08-08 13:42:40 [INFO] 📁 配置文件路径: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\activation_config.json
2025-08-08 13:42:40 [INFO] 🔍 检查本地保存的激活码: 71C9-4CBE-4B7F-48BB
2025-08-08 13:42:40 [INFO] 🔍 与服务器验证激活码有效性
2025-08-08 13:42:40 [INFO] ✅ 服务器验证成功: 设备已激活
2025-08-08 13:42:40 [INFO] ✅ 激活码验证通过
2025-08-08 13:42:40 [INFO] ✅ 本地激活验证通过
2025-08-08 13:42:40 [INFO] ✅ 成功加载窗口图标: D:/projects/Python/learn01/ai_tool/04/video_deduplication_tool_v2.0\img\logo.ico
2025-08-08 13:42:42 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-08-08 13:42:42 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-08-08 13:42:42 [INFO] 未检测到CUDA设备
2025-08-08 13:42:42 [INFO] VideoProcessor初始化:
2025-08-08 13:42:42 [INFO]   CPU线程数: 8
2025-08-08 13:42:42 [INFO]   GPU支持: 否
2025-08-08 13:42:42 [INFO]   GPU加速: 禁用
2025-08-08 13:42:42 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-08-08 13:42:42 [INFO] ✅ FFmpeg已配置: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-08-08 13:42:42 [INFO] 🔍 测试FFmpeg安装...
2025-08-08 13:42:42 [INFO] ✅ FFmpeg安装正确: ffmpeg version 7.0.2-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers
2025-08-08 13:42:42 [INFO] ✅ 支持PCM音频编码
2025-08-08 13:42:42 [INFO] ✅ FFmpeg测试通过
2025-08-08 13:43:22 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-08-08 13:43:22 [INFO] 未检测到CUDA设备
2025-08-08 13:43:22 [INFO] VideoProcessor初始化:
2025-08-08 13:43:22 [INFO]   CPU线程数: 4
2025-08-08 13:43:22 [INFO]   GPU支持: 否
2025-08-08 13:43:22 [INFO]   GPU加速: 禁用
2025-08-08 13:43:22 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-08-08 13:43:22 [INFO] 使用PySceneDetect版本: 0.6.6
2025-08-08 13:43:22 [INFO] 成功导入ContentDetector(方法1)
2025-08-08 13:43:22 [INFO] 视频文件大小: 50.26 MB
2025-08-08 13:43:22 [INFO] 视频信息: 30.0 FPS, 7362 帧, 245.40 秒
2025-08-08 13:43:22 [INFO] 尝试使用PySceneDetect原生方法打开视频...
2025-08-08 13:43:22 [INFO] 开始执行场景检测...
2025-08-08 13:43:48 [INFO] 场景检测完成，耗时 25.62 秒
2025-08-08 13:43:48 [INFO] 检测到 7 个场景
2025-08-08 13:43:48 [INFO] 使用PySceneDetect版本: 0.6.6
2025-08-08 13:43:48 [INFO] 成功导入ContentDetector(方法1)
2025-08-08 13:43:48 [INFO] 视频文件大小: 19.39 MB
2025-08-08 13:43:48 [INFO] 视频信息: 24.0 FPS, 5890 帧, 245.42 秒
2025-08-08 13:43:48 [INFO] 尝试使用PySceneDetect原生方法打开视频...
2025-08-08 13:43:48 [INFO] 开始执行场景检测...
2025-08-08 13:44:12 [INFO] 场景检测完成，耗时 24.09 秒
2025-08-08 13:44:12 [INFO] 检测到 7 个场景
2025-08-08 13:44:12 [INFO] 优化相似度匹配: 主视频7场景, 辅助视频7场景
2025-08-08 13:44:12 [INFO] 正在并行提取主视频特征...
2025-08-08 13:44:12 [INFO] 增强精度模式: 视频段19.90秒，提取10帧
2025-08-08 13:44:12 [INFO] 增强精度模式: 视频段9.93秒，提取10帧
2025-08-08 13:44:12 [INFO] 增强精度模式: 视频段22.57秒，提取10帧
2025-08-08 13:44:12 [INFO] 增强精度模式: 视频段9.93秒，提取10帧
2025-08-08 13:44:17 [INFO] 增强精度模式: 视频段146.93秒，提取10帧
2025-08-08 13:44:17 [INFO] 增强精度模式: 视频段7.70秒，提取10帧
2025-08-08 13:44:17 [INFO] 增强精度模式: 视频段28.43秒，提取10帧
2025-08-08 13:44:23 [INFO] 正在并行提取辅助视频特征...
2025-08-08 13:44:23 [INFO] 增强精度模式: 视频段66.58秒，提取10帧
2025-08-08 13:44:23 [INFO] 增强精度模式: 视频段7.88秒，提取10帧
2025-08-08 13:44:23 [INFO] 增强精度模式: 视频段17.88秒，提取10帧
2025-08-08 13:44:23 [INFO] 增强精度模式: 视频段7.92秒，提取10帧
2025-08-08 13:44:29 [INFO] 增强精度模式: 视频段116.50秒，提取10帧
2025-08-08 13:44:29 [INFO] 增强精度模式: 视频段6.08秒，提取10帧
2025-08-08 13:44:29 [INFO] 增强精度模式: 视频段22.58秒，提取10帧
2025-08-08 13:44:33 [INFO] 正在并行计算相似度矩阵...
2025-08-08 13:44:33 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:33 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:33 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:33 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:33 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:33 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:33 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:33 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:33 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:33 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:33 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:33 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:33 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:33 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:33 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [DEBUG] 自适应特征权重: {'color_hist': 0.35294117647058826, 'phash': 0.411764705882353, 'edge': 0.23529411764705888}
2025-08-08 13:44:34 [INFO] 正在查找最佳匹配...
2025-08-08 13:44:34 [INFO] 优化匹配完成: 找到 7 个匹配（限制: 7）
2025-08-08 13:44:34 [INFO] 使用临时路径处理视频: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp\temp_output_6d45a338_1754631874397_d376824a.mp4
2025-08-08 13:44:34 [INFO] 最终输出路径: D:/25125/Videos/video_test/ttt.mp4
2025-08-08 13:44:34 [INFO] 加载主视频: D:/25125/Videos/测试自媒体ab.mp4
2025-08-08 13:44:34 [INFO] 开始加载视频: D:/25125/Videos/测试自媒体ab.mp4
2025-08-08 13:44:34 [INFO] 尝试策略1: 直接简单加载...
2025-08-08 13:44:34 [INFO] ✅ 策略1成功: 时长 245.41秒
2025-08-08 13:44:34 [INFO] 主视频加载成功，时长: 245.41秒
2025-08-08 13:44:34 [INFO] 开始按顺序处理 7 个主视频片段...
2025-08-08 13:44:34 [INFO] 其中 7 个片段将被替换
2025-08-08 13:44:34 [INFO] 片段 1 标记为替换 (时长: 19.90秒)
2025-08-08 13:44:34 [INFO] 片段 2 标记为替换 (时长: 9.93秒)
2025-08-08 13:44:34 [INFO] 片段 3 标记为替换 (时长: 22.57秒)
2025-08-08 13:44:34 [INFO] 片段 4 标记为替换 (时长: 9.93秒)
2025-08-08 13:44:34 [INFO] 片段 5 标记为替换 (时长: 146.93秒)
2025-08-08 13:44:34 [INFO] 片段 6 标记为替换 (时长: 7.70秒)
2025-08-08 13:44:34 [INFO] 片段 7 标记为替换 (时长: 28.43秒)
2025-08-08 13:44:34 [INFO] 处理 7 个替换片段...
2025-08-08 13:44:34 [INFO] 使用传统匹配格式（场景索引）
2025-08-08 13:44:34 [INFO] 加载辅助视频: D:/25125/Videos/www333.mp4
2025-08-08 13:44:34 [INFO] 开始加载视频: D:/25125/Videos/www333.mp4
2025-08-08 13:44:34 [INFO] 尝试策略1: 直接简单加载...
2025-08-08 13:44:35 [INFO] ✅ 策略1成功: 时长 245.42秒
2025-08-08 13:44:35 [INFO] 辅助视频加载成功，时长: 245.42秒
2025-08-08 13:44:35 [INFO] ✓ 替换片段 7: 辅助视频场景 6 (222.83-245.42秒)
2025-08-08 13:44:36 [INFO] ✓ 替换片段 6: 辅助视频场景 5 (216.75-222.83秒)
2025-08-08 13:44:36 [INFO] ✓ 替换片段 2: 辅助视频场景 1 (66.58-74.46秒)
2025-08-08 13:44:36 [INFO] ✓ 替换片段 3: 辅助视频场景 2 (74.46-92.33秒)
2025-08-08 13:44:37 [INFO] ✓ 替换片段 4: 辅助视频场景 3 (92.33-100.25秒)
2025-08-08 13:44:37 [INFO] ✓ 替换片段 5: 辅助视频场景 4 (100.25-216.75秒)
2025-08-08 13:44:37 [INFO] ✓ 替换片段 1: 辅助视频场景 0 (0.00-66.58秒)
2025-08-08 13:44:37 [INFO] 替换处理完成: 成功替换 7/7 个片段
2025-08-08 13:44:37 [INFO] 执行精确时长验证和校正...
2025-08-08 13:44:37 [INFO] 总时长验证: 预期=245.400s, 实际=245.417s, 误差=0.017s, 通过=True
2025-08-08 13:44:37 [INFO] 片段分析: 主视频场景数=7, 匹配数=7, 成功片段数=7
2025-08-08 13:44:37 [INFO] 开始合成 7 个视频片段...
2025-08-08 13:44:37 [INFO] 片段 0 验证通过，时长: 66.58秒
2025-08-08 13:44:37 [INFO] 片段 1 验证通过，时长: 7.88秒
2025-08-08 13:44:38 [INFO] 片段 2 验证通过，时长: 17.88秒
2025-08-08 13:44:38 [INFO] 片段 3 验证通过，时长: 7.92秒
2025-08-08 13:44:38 [INFO] 片段 4 验证通过，时长: 116.50秒
2025-08-08 13:44:38 [INFO] 片段 5 验证通过，时长: 6.08秒
2025-08-08 13:44:39 [INFO] 片段 6 验证通过，时长: 22.58秒
2025-08-08 13:44:39 [INFO] 有效片段数量: 7
2025-08-08 13:44:39 [INFO] 尝试方法1：使用内存优化方法合成视频...
2025-08-08 13:44:39 [INFO] 片段 0 验证通过，时长: 66.58秒
2025-08-08 13:44:39 [INFO] 片段 1 验证通过，时长: 7.88秒
2025-08-08 13:44:39 [INFO] 片段 2 验证通过，时长: 17.88秒
2025-08-08 13:44:39 [INFO] 片段 3 验证通过，时长: 7.92秒
2025-08-08 13:44:40 [INFO] 片段 4 验证通过，时长: 116.50秒
2025-08-08 13:44:40 [INFO] 片段 5 验证通过，时长: 6.08秒
2025-08-08 13:44:40 [INFO] 片段 6 验证通过，时长: 22.58秒
2025-08-08 13:44:40 [INFO] 合并 7 个有效片段，总时长: 245.42秒
2025-08-08 13:44:40 [INFO] 使用分批合并方法 (7 个片段)
2025-08-08 13:44:40 [INFO] 合并批次 1/4
2025-08-08 13:44:40 [INFO] 合并批次 2/4
2025-08-08 13:44:40 [INFO] 合并批次 3/4
2025-08-08 13:44:40 [INFO] 最终合并 4 个批次
2025-08-08 13:44:40 [INFO] 递归合并: 分为 2 和 2 个片段
2025-08-08 13:44:40 [INFO] 片段 0 验证通过，时长: 74.46秒
2025-08-08 13:44:41 [INFO] 片段 1 验证通过，时长: 25.79秒
2025-08-08 13:44:41 [INFO] 合并 2 个有效片段，总时长: 100.25秒
2025-08-08 13:44:41 [INFO] 使用标准合并方法 (2 个片段)
2025-08-08 13:44:41 [INFO] 片段 0 验证通过，时长: 122.58秒
2025-08-08 13:44:41 [INFO] 片段 1 验证通过，时长: 22.58秒
2025-08-08 13:44:41 [INFO] 合并 2 个有效片段，总时长: 145.17秒
2025-08-08 13:44:41 [INFO] 使用标准合并方法 (2 个片段)
2025-08-08 13:44:41 [INFO] 视频合成成功，总时长: 245.42秒
2025-08-08 13:44:42 [INFO] 开始写入视频片段，时长: 245.42秒
2025-08-08 13:49:42 [WARNING] 视频写入超时（300秒），强制终止...
2025-08-08 13:49:42 [ERROR] 视频写入过程失败: 视频写入超时（300秒），已终止写入进程
2025-08-08 13:49:42 [WARNING] 视频写入失败，重试 1/3: 视频写入超时（300秒），已终止写入进程
2025-08-08 13:49:44 [ERROR] 视频写入过程失败: MoviePy error: failed to read the first frame of video file D:/25125/Videos/www333.mp4. That might mean that the file is corrupted. That may also mean that you are using a deprecated version of FFMPEG. On Ubuntu/Debian for instance the version in the repos is deprecated. Please update to a recent version from the website.
2025-08-08 13:49:44 [WARNING] 视频写入失败，重试 2/3: MoviePy error: failed to read the first frame of video file D:/25125/Videos/www333.mp4. That might mean that the file is corrupted. That may also mean that you are using a deprecated version of FFMPEG. On Ubuntu/Debian for instance the version in the repos is deprecated. Please update to a recent version from the website.
2025-08-08 13:50:59 [INFO] 🚪 用户选择完全退出程序
2025-08-08 13:50:59 [ERROR] ❌ 退出程序时发生错误: 'VideoProcessingThread' object has no attribute 'stop'
2025-08-08 13:51:04 [INFO] ✅ 已配置ImageMagick: D:\environment\imagemagick\magick.exe
2025-08-08 13:51:05 [INFO] 通过where命令找到FFmpeg: D:\environment\ffmpeg\7.0.2\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-08-08 13:51:05 [INFO] 未检测到CUDA设备
2025-08-08 13:51:05 [INFO] VideoProcessor初始化:
2025-08-08 13:51:05 [INFO]   CPU线程数: 8
2025-08-08 13:51:05 [INFO]   GPU支持: 否
2025-08-08 13:51:05 [INFO]   GPU加速: 禁用
2025-08-08 13:51:05 [INFO]   临时目录: D:\projects\Python\learn01\ai_tool\04\video_deduplication_tool_v2.0\temp
2025-08-08 13:51:05 [INFO] 简单加载视频: www333.mp4
2025-08-08 13:51:05 [INFO] ✅ 简单加载成功: 时长 245.42秒
2025-08-08 13:51:05 [INFO] 简单加载视频: www333.mp4
2025-08-08 13:51:05 [INFO] ✅ 简单加载成功: 时长 245.42秒
2025-08-08 13:51:05 [INFO] 简单加载视频: www333.mp4
2025-08-08 13:51:05 [INFO] ✅ 简单加载成功: 时长 245.42秒
