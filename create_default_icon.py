"""
创建默认图标文件
如果用户没有logo.ico文件，运行此脚本创建一个默认图标
"""

import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QPixmap, QPainter, QPen, QBrush, QColor, QFont
from PyQt5.QtCore import Qt

def create_default_icon():
    """创建默认图标"""
    app = QApplication([])
    
    # 创建64x64的图标
    pixmap = QPixmap(64, 64)
    pixmap.fill(QColor(52, 152, 219))  # 蓝色背景
    
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.Antialiasing)
    
    # 绘制视频播放图标
    painter.setPen(QPen(QColor(255, 255, 255), 3))
    painter.setBrush(QBrush(QColor(255, 255, 255)))
    
    # 绘制播放按钮三角形
    from PyQt5.QtCore import QPointF
    triangle = [
        QPointF(20, 16),
        QPoint<PERSON>(20, 48),
        QPoint<PERSON>(44, 32)
    ]
    painter.drawPolygon(triangle)
    
    # 绘制边框
    painter.setPen(QPen(QColor(255, 255, 255), 2))
    painter.setBrush(QBrush())
    painter.drawRect(2, 2, 60, 60)
    
    painter.end()
    
    # 确保img目录存在
    img_dir = os.path.join(os.path.dirname(__file__), 'img')
    if not os.path.exists(img_dir):
        os.makedirs(img_dir)
        print(f"✅ 创建img目录: {img_dir}")
    
    # 保存为PNG和ICO格式
    png_path = os.path.join(img_dir, 'logo.png')
    ico_path = os.path.join(img_dir, 'logo.ico')
    
    # 保存PNG
    if pixmap.save(png_path, 'PNG'):
        print(f"✅ 创建PNG图标: {png_path}")
    else:
        print(f"❌ 创建PNG图标失败: {png_path}")
    
    # 保存ICO（Windows图标格式）
    if pixmap.save(ico_path, 'ICO'):
        print(f"✅ 创建ICO图标: {ico_path}")
    else:
        print(f"❌ 创建ICO图标失败: {ico_path}")
    
    app.quit()

def main():
    """主函数"""
    print("🎨 创建默认图标...")
    create_default_icon()
    print("✅ 默认图标创建完成")

if __name__ == "__main__":
    main()
