import sys
import os
import warnings
import re

# 抑制PyQt5的弃用警告
warnings.filterwarnings("ignore", category=DeprecationWarning)

# 在导入MoviePy之前配置ImageMagick
def configure_imagemagick():
    """配置ImageMagick路径"""
    try:
        # 检查是否存在配置文件
        config_file = os.path.join(os.path.dirname(__file__), 'moviepy_config.py')
        if os.path.exists(config_file):
            # 读取配置文件
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'IMAGEMAGICK_BINARY' in content:
                    # 提取路径
                    match = re.search(r'IMAGEMAGICK_BINARY\s*=\s*r?"([^"]+)"', content)
                    if match:
                        binary_path = match.group(1)
                        if os.path.exists(binary_path):
                            os.environ['IMAGEMAGICK_BINARY'] = binary_path
                            # 这里使用print，因为日志系统还未导入
                            print(f"✅ GUI已配置ImageMagick: {binary_path}")
                            return True

        # 尝试默认路径
        default_path = r"D:\environment\imagemagick\magick.exe"
        if os.path.exists(default_path):
            os.environ['IMAGEMAGICK_BINARY'] = default_path
            # 这里使用print，因为日志系统还未导入
            print(f"✅ GUI使用默认ImageMagick路径: {default_path}")
            return True

        # 这里使用print，因为日志系统还未导入
        print("⚠️ GUI未找到ImageMagick配置，将使用备用方案")
        return False

    except Exception as e:
        # 这里使用print，因为日志系统还未导入
        print(f"⚠️ GUI ImageMagick配置失败: {e}")
        return False

# 配置ImageMagick
configure_imagemagick()

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QPushButton, QLabel, QLineEdit, QFileDialog,
                             QProgressBar, QTextEdit, QGroupBox, QSlider, QSpinBox,
                             QGridLayout, QFrame, QMessageBox, QTabWidget, QSplitter,
                             QCheckBox, QComboBox, QToolTip, QStatusBar, QMenuBar,
                             QAction, QSystemTrayIcon, QMenu, QDialog, QListWidget,
                             QListWidgetItem, QButtonGroup, QRadioButton, QFormLayout,
                             QSizePolicy, QSpacerItem, QScrollArea, QProgressDialog)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QRect, QSize
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor, QPixmap, QPainter, QLinearGradient, QImage, QCursor, QPen, \
    QBrush
from PyQt5.QtNetwork import QLocalServer, QLocalSocket

# 导入日志系统
try:
    from logger_system import log_info, log_error, log_warning, log_debug, safe_print, set_gui_log_callback
    LOG_SYSTEM_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 日志系统导入失败: {e}")
    LOG_SYSTEM_AVAILABLE = False
    # 定义备用函数
    def log_info(msg): print(msg)
    def log_error(msg): print(f"ERROR: {msg}")
    def log_warning(msg): print(f"WARNING: {msg}")
    def log_debug(msg): print(f"DEBUG: {msg}")
    def safe_print(*args, **kwargs): print(*args, **kwargs)
    def set_gui_log_callback(callback): pass

# 导入激活验证相关模块
try:
    from activation_dialog import ActivationDialog
    from machine_code_generator import get_machine_code
    from api_manager import api_manager
    ACTIVATION_AVAILABLE = True
    log_info("✅ 激活验证模块加载成功")
except ImportError as e:
    log_error(f"⚠️ 激活验证模块导入失败: {e}")
    ACTIVATION_AVAILABLE = False

# 单实例应用类
class SingletonApplication(QApplication):
    """单例应用程序类，确保只能运行一个实例"""

    def __init__(self, argv):
        super().__init__(argv)
        self.server_name = "余下视频混剪工具_SingleInstance"
        self.local_server = None
        self.main_window = None

        # 尝试连接到现有实例
        socket = QLocalSocket()
        socket.connectToServer(self.server_name)

        if socket.waitForConnected(1000):
            # 已有实例在运行，发送激活信号
            socket.write(b"ACTIVATE")
            socket.waitForBytesWritten(1000)
            socket.disconnectFromServer()
            log_warning("⚠️ 应用程序已在运行，激活现有窗口")
            sys.exit(0)

        # 创建本地服务器
        self.local_server = QLocalServer()
        self.local_server.removeServer(self.server_name)
        self.local_server.listen(self.server_name)
        self.local_server.newConnection.connect(self.handle_new_connection)

        log_info("✅ 单实例应用初始化成功")

    def set_main_window(self, window):
        """设置主窗口引用"""
        self.main_window = window

    def handle_new_connection(self):
        """处理新的连接请求"""
        if self.local_server.hasPendingConnections():
            socket = self.local_server.nextPendingConnection()
            socket.waitForReadyRead(1000)
            data = socket.readAll().data()

            if data == b"ACTIVATE" and self.main_window:
                # 激活现有窗口
                self.main_window.show_and_activate_window()
                log_info("📢 收到激活信号，显示主窗口")

            socket.disconnectFromServer()

# 导入必要的库
try:
    import cv2
    import numpy as np

    CV2_AVAILABLE = True
except ImportError:
    log_warning("警告: 未能导入OpenCV (cv2)。字幕区域手动选择功能将不可用。")
    log_warning("请安装OpenCV: pip install opencv-python")
    CV2_AVAILABLE = False

# Check if required packages are installed
required_packages = {
    'cv2': 'opencv-python',
    'scenedetect': 'scenedetect',
    'moviepy.editor': 'moviepy',
    'numpy': 'numpy'
}

missing_packages = []
for module, package in required_packages.items():
    try:
        __import__(module)
    except ImportError:
        missing_packages.append(f"{module} ({package})")

if missing_packages:
    log_error(f"Missing required packages: {', '.join(missing_packages)}")
    log_error("Please install them using: pip install " + " ".join([p.split('(')[1].rstrip(')') for p in missing_packages]))
    sys.exit(1)

# Import after checking dependencies
try:
    from performance_optimizer import OptimizedVideoProcessor
except ImportError as e:
    log_error(f"Error importing OptimizedVideoProcessor: {e}")
    sys.exit(1)


class SubtitleRegionSelector(QDialog):
    """字幕区域选择器"""
    region_selected = pyqtSignal(tuple)  # 发送选定的区域 (y_start, y_end)

    def __init__(self, video_path, parent=None):
        super().__init__(parent)
        self.video_path = video_path
        self.current_frame = None
        self.scaled_frame = None
        self.selection_start = None
        self.selection_end = None
        self.is_selecting = False
        self.scale_factor = 1.0
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("字幕区域选择")
        self.setGeometry(100, 100, 800, 600)
        self.setWindowFlags(self.windowFlags() | Qt.WindowMaximizeButtonHint)

        layout = QVBoxLayout()

        # 提示标签
        instruction_label = QLabel("请在视频画面上拖动鼠标选择字幕区域（仅需选择垂直范围，水平会自动应用到整个宽度）")
        instruction_label.setStyleSheet("color: #28a745; font-weight: bold; font-size: 14px;")
        instruction_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(instruction_label)

        # 视频帧显示区域
        self.frame_display = QLabel()
        self.frame_display.setAlignment(Qt.AlignCenter)
        self.frame_display.setStyleSheet("border: 2px solid #ced4da; background-color: #212529;")
        self.frame_display.setMinimumSize(640, 360)
        self.frame_display.setCursor(Qt.CrossCursor)  # 设置鼠标光标为十字形
        self.frame_display.mousePressEvent = self.mouse_press_event
        self.frame_display.mouseMoveEvent = self.mouse_move_event
        self.frame_display.mouseReleaseEvent = self.mouse_release_event
        layout.addWidget(self.frame_display)

        # 滑块控制
        slider_layout = QHBoxLayout()

        slider_layout.addWidget(QLabel("视频位置:"))
        self.position_slider = QSlider(Qt.Horizontal)
        self.position_slider.valueChanged.connect(self.update_frame_from_position)
        slider_layout.addWidget(self.position_slider)

        self.frame_label = QLabel("0 / 0")
        slider_layout.addWidget(self.frame_label)

        layout.addLayout(slider_layout)

        # 按钮
        btn_layout = QHBoxLayout()

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        btn_layout.addWidget(self.cancel_btn)

        self.confirm_btn = QPushButton("确认选择")
        self.confirm_btn.setEnabled(False)
        self.confirm_btn.clicked.connect(self.confirm_selection)
        self.confirm_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #28a745, stop:1 #20c997);
                color: white;
                font-weight: bold;
            }
        """)
        btn_layout.addWidget(self.confirm_btn)

        layout.addLayout(btn_layout)

        self.setLayout(layout)

        # 加载视频第一帧
        self.load_video()

    def load_video(self):
        """加载视频并设置滑块范围"""
        try:
            # 使用全局导入的cv2
            self.cap = cv2.VideoCapture(self.video_path)
            if not self.cap.isOpened():
                QMessageBox.critical(self, "错误", f"无法打开视频文件: {self.video_path}")
                self.reject()
                return

            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)
            self.width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            self.height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            # 设置滑块范围
            self.position_slider.setRange(0, self.total_frames - 1)
            self.position_slider.setValue(self.total_frames // 3)  # 默认位置在1/3处
            self.frame_label.setText(f"1 / {self.total_frames}")

            # 加载第一帧
            self.update_frame_from_position(self.total_frames // 3)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载视频时出错: {str(e)}")
            import traceback
            log_info(f"加载视频详细错误: {traceback.format_exc()}")
            self.reject()

    def update_frame_from_position(self, position):
        """根据滑块位置更新当前帧"""
        try:
            if not hasattr(self, 'cap') or self.cap is None:
                log_info("错误: 视频捕获对象未初始化")
                return

            self.cap.set(cv2.CAP_PROP_POS_FRAMES, position)
            ret, frame = self.cap.read()
            if ret:
                self.current_frame = frame
                self.update_frame_display()
                self.frame_label.setText(f"{position + 1} / {self.total_frames}")
            else:
                log_info(f"无法读取帧 {position}")
        except Exception as e:
            import traceback
            log_info(f"更新帧时出错: {str(e)}")
            log_error(f"详细错误: {traceback.format_exc()}")

    def update_frame_display(self):
        """更新帧显示并绘制选择区域"""
        if self.current_frame is None:
            return

        # 将OpenCV的BGR格式转换为RGB
        rgb_frame = cv2.cvtColor(self.current_frame, cv2.COLOR_BGR2RGB)

        # 转换为QImage
        h, w, ch = rgb_frame.shape
        bytes_per_line = ch * w
        q_img = QImage(rgb_frame.data, w, h, bytes_per_line, QImage.Format_RGB888)

        # 缩放图像以适应显示区域
        display_size = self.frame_display.size()
        pixmap = QPixmap.fromImage(q_img)
        self.scaled_frame = pixmap.scaled(display_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)

        # 计算缩放比例
        self.scale_factor = self.scaled_frame.height() / h

        # 如果有选择区域，则绘制
        if self.selection_start is not None and self.selection_end is not None:
            # 创建一个绘图器
            painter = QPainter(self.scaled_frame)

            # 设置半透明红色
            overlay_color = QColor(255, 0, 0, 100)  # 红色，透明度100/255
            painter.setPen(QPen(QColor(255, 0, 0), 2, Qt.SolidLine))
            painter.setBrush(QBrush(overlay_color))

            # 计算选择区域的顶部和底部y坐标
            y_min = min(self.selection_start.y(), self.selection_end.y())
            y_max = max(self.selection_start.y(), self.selection_end.y())

            # 绘制矩形
            painter.drawRect(0, y_min, self.scaled_frame.width(), y_max - y_min)

            # 绘制完成
            painter.end()

        # 显示图像
        self.frame_display.setPixmap(self.scaled_frame)

    def mouse_press_event(self, event):
        """鼠标按下事件"""
        self.is_selecting = True
        self.selection_start = event.pos()
        self.selection_end = event.pos()
        self.update_frame_display()

    def mouse_move_event(self, event):
        """鼠标移动事件"""
        if self.is_selecting:
            self.selection_end = event.pos()
            self.update_frame_display()

    def mouse_release_event(self, event):
        """鼠标释放事件"""
        self.is_selecting = False
        self.selection_end = event.pos()
        self.update_frame_display()

        # 启用确认按钮
        if self.selection_start is not None and self.selection_end is not None:
            self.confirm_btn.setEnabled(True)

    def confirm_selection(self):
        """确认选择区域"""
        if self.selection_start is None or self.selection_end is None:
            return

        # 计算选择区域的顶部和底部y坐标
        y_start = min(self.selection_start.y(), self.selection_end.y())
        y_end = max(self.selection_start.y(), self.selection_end.y())

        # 将坐标从缩放后的图像转换回原始图像坐标
        original_y_start = int(y_start / self.scale_factor)
        original_y_end = int(y_end / self.scale_factor)

        # 发送信号
        self.region_selected.emit((original_y_start, original_y_end))
        self.accept()

    def closeEvent(self, event):
        """关闭事件，确保释放视频资源"""
        if hasattr(self, 'cap') and self.cap:
            self.cap.release()
        super().closeEvent(event)


class AnimatedButton(QPushButton):
    """带动画效果的按钮"""

    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(150)  # 加快动画速度
        self.animation.setEasingCurve(QEasingCurve.OutCubic)
        self.setFont(QFont("Microsoft YaHei UI", 14))  # 设置默认字体

    def enterEvent(self, event):
        # 鼠标悬停时的动画效果 - 删除transform属性
        self.setStyleSheet(self.styleSheet() + """
            QPushButton { 
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #20c997, stop:1 #17a2b8);
            }
        """)
        super().enterEvent(event)

    def leaveEvent(self, event):
        # 鼠标离开时恢复
        current_style = self.styleSheet()
        # 删除对transform属性的引用
        current_style = current_style.replace("""background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #20c997, stop:1 #17a2b8);""", "")
        self.setStyleSheet(current_style)
        super().leaveEvent(event)


class VideoProcessingThread(QThread):
    """优化版视频处理线程 - 纯视频去重功能（通过去重达到去除字幕和水印的效果）"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_processing = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    detailed_progress = pyqtSignal(str, int)  # 详细进度信息

    def __init__(self, main_video_path, aux_video_path, output_path, threshold, similarity_threshold,
                 use_optimization=True, replacement_strategy=0, replacement_rate=0.5,
                 feature_frames=10, thread_count=4):
        super().__init__()
        self.main_video_path = main_video_path  # 主要视频（有字幕/水印的剪辑视频）
        self.aux_video_path = aux_video_path    # 原视频（未剪辑的干净原始视频）
        self.output_path = output_path
        self.threshold = threshold
        self.similarity_threshold = similarity_threshold
        self.use_optimization = use_optimization
        self.replacement_strategy = replacement_strategy
        self.replacement_rate = replacement_rate
        # 高级参数
        self.feature_frames = feature_frames
        self.thread_count = thread_count
        self.processor = None
        
    def run(self):
        try:
            # 初始化处理器
            if self.use_optimization:
                try:
                    from performance_optimizer import OptimizedVideoProcessor
                    self.processor = OptimizedVideoProcessor(
                        enable_gpu=True,
                        num_threads=self.thread_count
                    )
                    self.status_updated.emit(f"已启用优化处理器 (线程数: {self.thread_count})")
                except ImportError:
                    from video_processor import VideoProcessor
                    self.processor = VideoProcessor(
                        enable_gpu=True,
                        num_threads=self.thread_count
                    )
                    self.status_updated.emit(f"优化处理器导入失败，使用标准处理器 (线程数: {self.thread_count})")
            else:
                from video_processor import VideoProcessor
                self.processor = VideoProcessor(
                    enable_gpu=False,
                    num_threads=self.thread_count
                )
                self.status_updated.emit(f"使用标准处理器 (线程数: {self.thread_count})")

            # 新的工作流程：纯视频去重
            # 1. 对主要视频（有字幕/水印）进行场景检测
            # 2. 对原视频（干净）进行场景检测
            # 3. 寻找相似场景并用干净的原视频片段替换
            # 4. 生成最终的去除字幕/水印的剪辑视频

            self.status_updated.emit("🔄 开始智能视频去重处理：通过场景替换去除字幕和水印")
            self.progress_updated.emit(2)

            import cv2
            import os

            # 检查视频时长
            cap_main = cv2.VideoCapture(self.main_video_path)
            if not cap_main.isOpened():
                self.error_occurred.emit("无法打开主要视频文件")
                return
            main_fps = cap_main.get(cv2.CAP_PROP_FPS)
            main_frame_count = cap_main.get(cv2.CAP_PROP_FRAME_COUNT)
            main_duration = main_frame_count / main_fps if main_fps > 0 else 0
            cap_main.release()

            cap_aux = cv2.VideoCapture(self.aux_video_path)
            if not cap_aux.isOpened():
                self.error_occurred.emit("无法打开原视频文件")
                return
            aux_fps = cap_aux.get(cv2.CAP_PROP_FPS)
            aux_frame_count = cap_aux.get(cv2.CAP_PROP_FRAME_COUNT)
            aux_duration = aux_frame_count / aux_fps if aux_fps > 0 else 0
            cap_aux.release()

            self.detailed_progress.emit(f"主要视频时长: {main_duration:.2f}秒, 原视频时长: {aux_duration:.2f}秒", 8)

            # 步骤1: 对主要视频进行场景检测
            self.status_updated.emit("🎬 步骤1: 分析主要视频场景...")
            self.progress_updated.emit(10)

            try:
                # 直接对主要视频进行场景检测
                main_scenes = self.processor.split_video_into_scenes(self.main_video_path, self.threshold)
                if not main_scenes:
                    self.error_occurred.emit("主要视频场景检测失败，未找到任何场景")
                    return
                self.detailed_progress.emit(f"主要视频检测到 {len(main_scenes)} 个场景", 20)
            except Exception as e:
                self.error_occurred.emit(f"主要视频场景检测失败: {str(e)}")
                return

            # 步骤2: 分析原视频场景
            self.status_updated.emit("🎬 步骤2: 分析原视频场景...")
            self.progress_updated.emit(30)

            try:
                aux_scenes = self.processor.split_video_into_scenes(self.aux_video_path, self.threshold)
                if not aux_scenes:
                    self.error_occurred.emit("原视频场景检测失败，未找到任何场景")
                    return
                self.detailed_progress.emit(f"原视频检测到 {len(aux_scenes)} 个场景", 40)
            except Exception as e:
                self.error_occurred.emit(f"原视频场景检测失败: {str(e)}")
                return

            # 步骤3: 根据选择的策略查找替换场景
            similar_matches = []
            strategy_name = ["相似度匹配", "均匀替换", "随机替换"][self.replacement_strategy]

            try:
                # 相似度匹配策略
                if self.replacement_strategy == 0:
                    self.status_updated.emit("🔍 步骤3: 在原视频中寻找相似场景...")
                    self.progress_updated.emit(50)

                    if self.use_optimization and hasattr(self.processor, 'find_similar_scenes_optimized'):
                        similar_matches = self.processor.find_similar_scenes_optimized(
                            main_scenes, aux_scenes, self.main_video_path, self.aux_video_path,
                            self.similarity_threshold, feature_frames=self.feature_frames
                        )
                    else:
                        # 检查是否支持特征帧数参数
                        if hasattr(self.processor, 'find_similar_scenes'):
                            import inspect
                            sig = inspect.signature(self.processor.find_similar_scenes)
                            if 'feature_frames' in sig.parameters:
                                similar_matches = self.processor.find_similar_scenes(
                                    main_scenes, aux_scenes, self.main_video_path, self.aux_video_path,
                                    self.similarity_threshold, feature_frames=self.feature_frames
                                )
                            else:
                                similar_matches = self.processor.find_similar_scenes(
                                    main_scenes, aux_scenes, self.main_video_path, self.aux_video_path,
                                    self.similarity_threshold
                                )
                        else:
                            self.error_occurred.emit("处理器不支持相似度匹配功能")
                            return

                    # 检查相似度匹配结果
                    if not similar_matches:
                        error_msg = f"""❌ 相似度匹配失败

🔍 当前设置：
• 相似度阈值：{self.similarity_threshold:.2f}
• 主视频场景数：{len(main_scenes)}
• 辅助视频场景数：{len(aux_scenes)}

❗ 问题：未找到任何相似场景

💡 建议解决方案：
1. 降低相似度阈值（当前：{self.similarity_threshold:.2f} → 建议：0.6-0.7）
2. 更换辅助视频（选择内容更相似的视频）
3. 使用"均匀替换"或"随机替换"策略
4. 检查辅助视频是否包含有效内容

🔄 您可以：
• 调整相似度阈值后重试
• 切换到其他替换策略
• 更换辅助视频文件"""
                        self.error_occurred.emit(error_msg)
                        return

                    self.detailed_progress.emit(f"找到 {len(similar_matches)} 对相似场景", 70)

                # 均匀替换策略
                elif self.replacement_strategy == 1:
                    self.status_updated.emit("正在进行均匀场景替换...")
                    self.progress_updated.emit(50)

                    similar_matches = self.processor.find_uniform_replacement_scenes(
                        main_scenes, aux_scenes, self.replacement_rate
                    )

                    self.detailed_progress.emit(f"将均匀替换 {len(similar_matches)} 个场景", 60)

                # 随机替换策略
                elif self.replacement_strategy == 2:
                    self.status_updated.emit("正在进行随机场景替换...")
                    self.progress_updated.emit(50)

                    similar_matches = self.processor.find_random_replacement_scenes(
                        main_scenes, aux_scenes, self.replacement_rate
                    )

                    self.detailed_progress.emit(f"将随机替换 {len(similar_matches)} 个场景", 60)

            except Exception as e:
                self.error_occurred.emit(f"场景匹配失败: {str(e)}")
                return

            # 步骤4: 替换并合成最终视频
            self.status_updated.emit("🎬 步骤4: 合成去除字幕/水印的剪辑视频...")
            self.progress_updated.emit(70)

            try:
                # 直接使用主要视频和原视频进行合成，通过场景替换去除字幕/水印
                self.processor.replace_and_concatenate_videos(
                    self.main_video_path, self.aux_video_path, main_scenes, aux_scenes, similar_matches,
                    self.output_path
                )
            except Exception as e:
                error_msg = str(e)

                # 针对不同错误类型提供专门的用户提示
                if "所有片段都无效" in error_msg or "没有有效的视频片段" in error_msg:
                    strategy_name = ["相似度匹配", "均匀替换", "随机替换"][self.replacement_strategy]
                    detailed_error = f"""❌ 视频合成失败：无有效片段

🔍 当前设置：
• 替换策略：{strategy_name}
• 匹配场景数：{len(similar_matches)}
• 主视频场景数：{len(main_scenes)}
• 辅助视频场景数：{len(aux_scenes)}

❗ 问题分析：
{error_msg}

💡 解决方案：
"""
                    if self.replacement_strategy == 0:  # 相似度匹配
                        detailed_error += f"""1. 降低相似度阈值（当前：{self.similarity_threshold:.2f} → 建议：0.5-0.7）
2. 更换内容更相似的原视频
3. 切换到"均匀替换"或"随机替换"策略
4. 检查原视频格式和质量"""
                    else:
                        detailed_error += """1. 检查原视频文件是否损坏
2. 尝试使用不同格式的原视频
3. 降低替换率设置
4. 切换到"相似度匹配"策略"""

                    detailed_error += f"""

🔄 建议操作：
• 点击"重新选择原视频"更换视频文件
• 调整策略参数后重试
• 检查视频文件完整性"""

                    self.error_occurred.emit(detailed_error)
                else:
                    # 其他类型的错误
                    self.error_occurred.emit(f"视频合成失败: {error_msg}")
                return

            self.progress_updated.emit(100)
            self.status_updated.emit("视频处理完成！")

            # 检查输出文件是否生成
            if not os.path.exists(self.output_path):
                self.error_occurred.emit(f"处理完成但未找到输出文件: {self.output_path}")
                return

            # 计算替换率
            actual_replacement_rate = (len(similar_matches) / len(main_scenes)) * 100 if main_scenes else 0

            # 获取替换策略名称
            strategy_name = ["相似度匹配", "均匀替换", "随机替换"][self.replacement_strategy]

            result_text = f"""🎉 智能去重处理成功完成！

📊 处理统计：
• 主要视频（有字幕/水印）时长：{main_duration:.2f}秒
• 原视频（干净）时长：{aux_duration:.2f}秒
• 主要视频场景数：{len(main_scenes)}
• 原视频场景数：{len(aux_scenes)}
• 替换场景数：{len(similar_matches)}
• 替换率：{actual_replacement_rate:.1f}%
• 替换策略：{strategy_name}

📁 输出文件：{os.path.basename(self.output_path)}
📏 文件大小：{self.get_file_size(self.output_path)}

✨ 恭喜！您的去除字幕/水印的剪辑视频已生成完成！
🔥 智能去重技术：通过场景替换自动去除字幕和水印"""

            self.finished_processing.emit(result_text)

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            self.error_occurred.emit(f"处理过程中发生错误: {str(e)}\n\n详细错误信息:\n{error_details}")

    def get_file_size(self, file_path):
        """获取文件大小的友好显示"""
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            for unit in ['B', 'KB', 'MB', 'GB']:
                if size < 1024.0:
                    return f"{size:.1f} {unit}"
                size /= 1024.0
            return f"{size:.1f} TB"
        return "未知"


class SubtitleRemovalThread(QThread):
    """字幕移除处理线程"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_processing = pyqtSignal(str)
    error_occurred = pyqtSignal(str)

    def __init__(self, input_path, output_path, subtitle_region=None, subtitle_regions=None,
                 detection_sensitivity=0.5, removal_method='inpaint', use_optimization=True,
                 is_manual_mode=False, performance_mode='auto'):
        super().__init__()
        self.input_path = input_path
        self.output_path = output_path
        self.subtitle_region = subtitle_region  # 单矩形区域 (y_start, y_end)
        self.subtitle_regions = subtitle_regions  # 多矩形区域列表 [(x1, y1, x2, y2), ...]
        self.detection_sensitivity = detection_sensitivity
        self.removal_method = removal_method
        self.use_optimization = use_optimization
        self.processor = None
        self.is_manual_mode = is_manual_mode
        self.performance_mode = performance_mode

    def run(self):
        try:
            # 初始化处理器
            self.status_updated.emit("正在初始化处理器...")
            self.progress_updated.emit(5)

            try:
                if self.use_optimization:
                    self.processor = OptimizedVideoProcessor()
                    # 设置性能模式
                    if hasattr(self.processor, 'set_performance_mode'):
                        self.processor.set_performance_mode(self.performance_mode)
                else:
                    from video_processor import VideoProcessor
                    self.processor = VideoProcessor()
                    # 设置性能模式
                    self.processor.set_performance_mode(self.performance_mode)
            except Exception as e:
                self.error_occurred.emit(f"初始化处理器失败: {str(e)}")
                return

            # 检查视频文件是否存在且可访问
            if not os.path.exists(self.input_path):
                self.error_occurred.emit(f"输入视频文件不存在: {self.input_path}")
                return

            # 检查输出目录是否可写
            output_dir = os.path.dirname(self.output_path)
            if output_dir and not os.path.exists(output_dir):
                try:
                    os.makedirs(output_dir)
                except Exception as e:
                    self.error_occurred.emit(f"无法创建输出目录: {str(e)}")
                    return

            # 开始处理
            if self.is_manual_mode:
                if self.subtitle_regions:  # 多矩形模式
                    self.status_updated.emit(f"正在移除 {len(self.subtitle_regions)} 个手动选定的字幕区域...")
                elif self.subtitle_region:  # 单矩形模式
                    self.status_updated.emit(f"正在移除手动选定的字幕区域 ({self.subtitle_region[0]}-{self.subtitle_region[1]})...")
                else:
                    self.status_updated.emit("正在移除手动选定的字幕区域...")
            else:
                self.status_updated.emit("正在移除字幕...")
            self.progress_updated.emit(20)

            try:
                if self.subtitle_regions:  # 多矩形模式
                    self.processor.remove_subtitles_multi_regions(
                        self.input_path,
                        self.output_path,
                        self.subtitle_regions,
                        self.removal_method
                    )
                else:  # 单矩形或自动检测模式
                    self.processor.remove_subtitles(
                        self.input_path,
                        self.output_path,
                        self.subtitle_region,
                        self.detection_sensitivity,
                        self.removal_method
                    )
                self.progress_updated.emit(100)
                self.status_updated.emit("字幕移除完成！")
            except Exception as e:
                self.error_occurred.emit(f"字幕移除失败: {str(e)}")
                return

            # 检查输出文件是否生成
            if not os.path.exists(self.output_path):
                self.error_occurred.emit(f"处理完成但未找到输出文件: {self.output_path}")
                return

            # 准备结果文本
            region_info = ""
            if self.is_manual_mode:
                if self.subtitle_regions:  # 多矩形模式
                    region_info = f"\n• 处理模式：多矩形手动选择 ({len(self.subtitle_regions)} 个区域)"
                    for i, (x1, y1, x2, y2) in enumerate(self.subtitle_regions, 1):
                        region_info += f"\n  区域{i}: ({x1},{y1}) - ({x2},{y2})"
                elif self.subtitle_region:  # 单矩形模式
                    y_start, y_end = self.subtitle_region
                    region_info = f"\n• 处理模式：单矩形手动选择 ({y_start}-{y_end})"
                else:
                    region_info = "\n• 处理模式：手动选择"
            else:
                region_info = "\n• 处理模式：自动检测字幕区域"

            # 转换移除方法显示名称
            method_names = {
                'inpaint': '智能填充',
                'background_fill': '背景色填充',
                'black_fill': '黑色填充',
                'blur': '模糊处理'
            }
            method_display = method_names.get(self.removal_method, self.removal_method)

            result_text = f"""🎉 字幕移除成功完成！

📊 处理统计：
• 输入视频：{os.path.basename(self.input_path)}
• 输出视频：{os.path.basename(self.output_path)}{region_info}
• 移除方法：{method_display}
• 文件大小：{self.get_file_size(self.output_path)}

✨ 恭喜！无字幕视频已生成完成！"""

            self.finished_processing.emit(result_text)

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            self.error_occurred.emit(f"处理过程中发生错误: {str(e)}\n\n详细错误信息:\n{error_details}")

    def get_file_size(self, file_path):
        """获取文件大小的友好显示"""
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            for unit in ['B', 'KB', 'MB', 'GB']:
                if size < 1024.0:
                    return f"{size:.1f} {unit}"
                size /= 1024.0
            return f"{size:.1f} TB"
        return "未知"


class SubtitleExtractionThread(QThread):
    """字幕提取处理线程"""
    progress_updated = pyqtSignal(int)
    result_ready = pyqtSignal(str)
    error_occurred = pyqtSignal(str)

    def __init__(self, video_path, method, language, output_format, output_path, convert_to_simplified=True):
        super().__init__()
        self.video_path = video_path
        self.method = method
        self.language = language
        self.output_format = output_format
        self.output_path = output_path
        self.convert_to_simplified = convert_to_simplified  # 是否转换为简体中文

    def run(self):
        try:
            from video_processor import VideoProcessor
            processor = VideoProcessor()

            self.progress_updated.emit(10)

            # 转换方法参数
            method_map = {
                "自动检测": "auto",
                "内嵌字幕": "embedded",
                "语音识别": "speech_recognition"
            }

            language_map = {
                "中文": "zh",
                "英文": "en",
                "自动检测": "auto"
            }

            method = method_map.get(self.method, "auto")
            language = language_map.get(self.language, "zh")

            self.progress_updated.emit(30)

            # 提取字幕
            subtitle_text = processor.extract_subtitles_to_string(
                self.video_path, method, language
            )

            # 检查是否是FFmpeg相关错误
            if "FFmpeg未找到" in subtitle_text and method == "auto":
                # 如果是自动模式且FFmpeg不可用，尝试直接使用语音识别
                subtitle_text = processor._extract_subtitles_by_speech_recognition(
                    self.video_path, language
                )

            self.progress_updated.emit(60)

            # 如果是中文并且需要转换为简体中文
            if language == "zh" and self.convert_to_simplified and subtitle_text:
                try:
                    from opencc import OpenCC
                    cc = OpenCC('t2s')  # 繁体转简体
                    subtitle_text = cc.convert(subtitle_text)
                    self.progress_updated.emit(70)
                except ImportError:
                    # 如果没有安装OpenCC，添加提示信息
                    subtitle_text = subtitle_text + "\n\n[注意: 未安装opencc-python-reimplemented库，无法进行繁体转简体]"
                except Exception as e:
                    subtitle_text = subtitle_text + f"\n\n[繁体转简体时出错: {str(e)}]"

            self.progress_updated.emit(80)

            # 根据输出格式处理结果
            if self.output_format == "显示在界面":
                self.result_ready.emit(subtitle_text)
            else:
                # 保存到文件
                if self.output_path:
                    with open(self.output_path, 'w', encoding='utf-8') as f:
                        f.write(subtitle_text)
                    result_msg = f"字幕已保存到: {self.output_path}\n\n内容预览:\n{subtitle_text[:500]}..."
                else:
                    result_msg = subtitle_text

                self.result_ready.emit(result_msg)

            self.progress_updated.emit(100)

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            self.error_occurred.emit(f"字幕提取失败: {str(e)}\n\n详细错误信息:\n{error_details}")


class VideoAudioSeparationThread(QThread):
    """视频音频分离处理线程"""
    progress_updated = pyqtSignal(int)
    result_ready = pyqtSignal(str)
    error_occurred = pyqtSignal(str)

    def __init__(self, video_path, video_output_path, audio_output_path):
        super().__init__()
        self.video_path = video_path
        self.video_output_path = video_output_path
        self.audio_output_path = audio_output_path

    def run(self):
        try:
            from video_processor import VideoProcessor
            processor = VideoProcessor()

            self.progress_updated.emit(10)

            # 执行分离
            video_path, audio_path = processor.separate_video_audio(
                self.video_path,
                self.video_output_path,
                self.audio_output_path
            )

            self.progress_updated.emit(90)

            # 生成结果信息
            result_lines = ["🎉 视频音频分离完成！\n"]

            if video_path and os.path.exists(video_path):
                video_size = self.get_file_size(video_path)
                result_lines.append(f"📹 视频文件: {video_path}")
                result_lines.append(f"   文件大小: {video_size}")

            if audio_path and os.path.exists(audio_path):
                audio_size = self.get_file_size(audio_path)
                result_lines.append(f"🎵 音频文件: {audio_path}")
                result_lines.append(f"   文件大小: {audio_size}")

            result_text = "\n".join(result_lines)
            self.result_ready.emit(result_text)

            self.progress_updated.emit(100)

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            self.error_occurred.emit(f"视频音频分离失败: {str(e)}\n\n详细错误信息:\n{error_details}")

    def get_file_size(self, file_path):
        """获取文件大小的友好显示"""
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            for unit in ['B', 'KB', 'MB', 'GB']:
                if size < 1024.0:
                    return f"{size:.1f} {unit}"
                size /= 1024.0
            return f"{size:.1f} TB"
        return "未知"


class VideoMergeThread(QThread):
    """视频合并处理线程"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    result_ready = pyqtSignal(str)
    error_occurred = pyqtSignal(str)

    def __init__(self, video_files, subtitle_files, output_path, transition_type, subtitle_style,
                 single_subtitle_mode=False, performance_mode='auto'):
        super().__init__()
        self.video_files = video_files
        self.subtitle_files = subtitle_files
        self.output_path = output_path
        self.transition_type = transition_type
        self.subtitle_style = subtitle_style
        self.single_subtitle_mode = single_subtitle_mode
        self.performance_mode = performance_mode

    def run(self):
        try:
            from video_processor import VideoProcessor
            processor = VideoProcessor()

            # 设置性能模式
            processor.set_performance_mode(self.performance_mode)

            self.progress_updated.emit(5)
            self.status_updated.emit("初始化视频处理器...")

            # 创建进度回调函数
            def progress_callback(current, total, message):
                """进度回调函数"""
                if total > 0:
                    progress = int((current / total) * 85) + 5  # 5-90%的进度范围
                    self.progress_updated.emit(progress)
                self.status_updated.emit(message)

            # 执行视频合并
            result_path = processor.merge_videos_with_subtitles(
                self.video_files,
                self.subtitle_files,
                self.output_path,
                self.transition_type,
                self.subtitle_style,
                self.single_subtitle_mode,
                progress_callback=progress_callback
            )

            self.progress_updated.emit(90)
            self.status_updated.emit("生成结果信息...")

            # 生成结果信息
            result_lines = ["🎉 视频混剪完成！\n"]

            if os.path.exists(result_path):
                file_size = self.get_file_size(result_path)
                result_lines.append(f"📹 输出文件: {result_path}")
                result_lines.append(f"   文件大小: {file_size}")
                result_lines.append(f"🎬 处理了 {len(self.video_files)} 个视频文件")
                result_lines.append(f"📝 处理了 {len(self.subtitle_files)} 个字幕文件")
                result_lines.append(f"🎨 转场效果: {self.transition_type}")

                if self.subtitle_style:
                    result_lines.append("📄 已添加字幕")
                else:
                    result_lines.append("📄 未添加字幕")

            result_text = "\n".join(result_lines)
            self.result_ready.emit(result_text)

            self.progress_updated.emit(100)

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            self.error_occurred.emit(f"视频混剪失败: {str(e)}\n\n详细错误信息:\n{error_details}")

    def get_file_size(self, file_path):
        """获取文件大小的友好显示"""
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            for unit in ['B', 'KB', 'MB', 'GB']:
                if size < 1024.0:
                    return f"{size:.1f} {unit}"
                size /= 1024.0
            return f"{size:.1f} TB"
        return "未知"


class SmartMixingThread(QThread):
    """智能混剪处理线程"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    result_ready = pyqtSignal(str)
    error_occurred = pyqtSignal(str)

    def __init__(self, video_files, output_path, video_duration, num_outputs,
                 transition_type, background_music, remove_original_audio, performance_mode='auto',
                 scene_detection_sensitivity=30.0, output_folder=None, use_random_seed=True):
        super().__init__()
        self.video_files = video_files
        self.output_path = output_path
        self.video_duration = video_duration
        self.num_outputs = num_outputs
        self.transition_type = transition_type
        self.background_music = background_music
        self.remove_original_audio = remove_original_audio
        self.performance_mode = performance_mode
        self.scene_detection_sensitivity = scene_detection_sensitivity
        self.output_folder = output_folder
        self.use_random_seed = use_random_seed

    def run(self):
        try:
            from video_processor import VideoProcessor
            import random
            import time
            
            processor = VideoProcessor()

            # 设置性能模式
            processor.set_performance_mode(self.performance_mode)

            self.progress_updated.emit(5)
            self.status_updated.emit("初始化智能混剪处理器...")

            # 创建进度回调函数
            def progress_callback(current, total, message):
                """进度回调函数"""
                if total > 0:
                    progress = int((current / total) * 85) + 5  # 5-90%的进度范围
                    self.progress_updated.emit(progress)
                self.status_updated.emit(message)
            
            # 为每个输出视频设置不同的随机种子，确保唯一性
            seeds = []
            if self.use_random_seed:
                current_time = int(time.time())
                for i in range(self.num_outputs):
                    # 使用时间戳加索引作为基础种子
                    seeds.append(current_time + i * 1000)
            else:
                # 使用固定种子
                for i in range(self.num_outputs):
                    seeds.append(42 + i * 1000)

            # 执行智能混剪
            result_paths = processor.smart_video_mixing(
                video_files=self.video_files,
                output_path=self.output_path,
                video_duration=self.video_duration,
                num_outputs=self.num_outputs,
                transition_type=self.transition_type,
                background_music=self.background_music,
                remove_original_audio=self.remove_original_audio,
                use_acceleration=True,
                progress_callback=progress_callback,
                scene_detection=True,
                scene_detection_sensitivity=self.scene_detection_sensitivity,
                output_folder=self.output_folder,
                random_seeds=seeds
            )

            self.progress_updated.emit(90)
            self.status_updated.emit("生成结果信息...")

            # 生成结果信息
            result_lines = ["🎉 智能混剪完成！\n"]

            if result_paths:
                total_size = 0
                for i, result_path in enumerate(result_paths):
                    if os.path.exists(result_path):
                        file_size_bytes = os.path.getsize(result_path)
                        total_size += file_size_bytes
                        file_size = self.get_file_size(result_path)
                        result_lines.append(f"📹 输出文件 {i+1}: {result_path}")
                        result_lines.append(f"   文件大小: {file_size}")

                result_lines.append(f"\n🎬 处理了 {len(self.video_files)} 个源视频文件")
                result_lines.append(f"🎯 生成了 {len(result_paths)} 个混剪视频")
                result_lines.append(f"⏱️ 每个视频时长: {self.video_duration} 秒")
                result_lines.append(f"🔍 画面检测灵敏度: {self.scene_detection_sensitivity}")
                result_lines.append(f"🎵 背景音乐: {'是' if self.background_music else '否'}")
                result_lines.append(f"🔇 去除原音频: {'是' if self.remove_original_audio else '否'}")
                result_lines.append(f"📊 总文件大小: {self.get_file_size_from_bytes(total_size)}")
            else:
                result_lines.append("❌ 没有生成任何视频文件")

            self.progress_updated.emit(100)
            self.result_ready.emit("\n".join(result_lines))

        except Exception as e:
            error_msg = f"智能混剪失败: {str(e)}"
            log_info(error_msg)
            import traceback
            log_info(traceback.format_exc())
            self.error_occurred.emit(error_msg)

    def get_file_size(self, file_path):
        """获取文件大小的可读格式"""
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            return self.get_file_size_from_bytes(size)
        return "未知"

    def get_file_size_from_bytes(self, size):
        """将字节数转换为可读格式"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"


class VideoFusionThread(QThread):
    """视频融合处理线程"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    result_ready = pyqtSignal(str)
    error_occurred = pyqtSignal(str)

    def __init__(self, video_files, output_folder, transition_type, num_outputs,
                 sticker_files=None, performance_mode='auto', segment_duration=8,
                 total_duration=60, filename_prefix="融合视频", sticker_position="随机",
                 sticker_size=15, sticker_opacity=80, trim_start=0, trim_end=0,
                 trim_mode="全部裁剪", min_duration=5, duration_mode="完整拼接",
                 target_duration=60, duration_strategy="智能选择", enable_trim=False):
        super().__init__()
        self.video_files = video_files
        self.output_folder = output_folder
        self.transition_type = transition_type
        self.num_outputs = num_outputs
        self.sticker_files = sticker_files or []
        self.performance_mode = performance_mode
        self.segment_duration = segment_duration
        self.total_duration = total_duration
        self.filename_prefix = filename_prefix
        self.sticker_position = sticker_position
        self.sticker_size = sticker_size
        self.sticker_opacity = sticker_opacity
        self.trim_start = trim_start
        self.trim_end = trim_end
        self.trim_mode = trim_mode
        self.min_duration = min_duration
        self.duration_mode = duration_mode
        self.target_duration = target_duration
        self.duration_strategy = duration_strategy
        self.enable_trim = enable_trim

    def run(self):
        try:
            from video_processor import VideoProcessor
            processor = VideoProcessor()

            # 设置性能模式
            processor.set_performance_mode(self.performance_mode)

            self.progress_updated.emit(5)
            self.status_updated.emit("初始化视频融合处理器...")

            # 创建进度回调函数
            def progress_callback(current, total, message):
                """进度回调函数"""
                if total > 0:
                    progress = int((current / total) * 85) + 5  # 5-90%的进度范围
                    self.progress_updated.emit(progress)
                self.status_updated.emit(message)

            # 执行视频融合
            result_paths = processor.video_fusion_enhanced(
                video_files=self.video_files,
                output_folder=self.output_folder,
                transition_type=self.transition_type,
                num_outputs=self.num_outputs,
                sticker_files=self.sticker_files,
                filename_prefix=self.filename_prefix,
                sticker_position=self.sticker_position,
                sticker_size=self.sticker_size / 100.0,  # 转换为小数
                sticker_opacity=self.sticker_opacity / 100.0,  # 转换为小数
                duration_mode=self.duration_mode,
                target_duration=self.target_duration,
                duration_strategy=self.duration_strategy,
                enable_trim=self.enable_trim,
                trim_start=self.trim_start if self.enable_trim else 0,
                trim_end=self.trim_end if self.enable_trim else 0,
                trim_mode=self.trim_mode,
                min_duration=self.min_duration,
                progress_callback=progress_callback
            )

            self.progress_updated.emit(90)
            self.status_updated.emit("生成结果信息...")

            # 生成结果信息
            result_lines = ["🎉 视频融合完成！\n"]

            if result_paths:
                total_size = 0
                for i, result_path in enumerate(result_paths):
                    if os.path.exists(result_path):
                        file_size_bytes = os.path.getsize(result_path)
                        total_size += file_size_bytes
                        file_size = self.get_file_size(result_path)
                        result_lines.append(f"📹 输出文件 {i+1}: {result_path}")
                        result_lines.append(f"   文件大小: {file_size}")

                result_lines.append(f"\n🎬 处理了 {len(self.video_files)} 个源视频文件")
                result_lines.append(f"🎯 生成了 {len(result_paths)} 个融合视频")
                result_lines.append(f"🎨 转场效果: {self.transition_type}")
                if self.sticker_files:
                    result_lines.append(f"🏷️ 添加了 {len(self.sticker_files)} 个贴纸")
                result_lines.append(f"📊 总文件大小: {self.get_file_size_from_bytes(total_size)}")
            else:
                result_lines.append("❌ 没有生成任何视频文件")

            self.progress_updated.emit(100)
            self.result_ready.emit("\n".join(result_lines))

        except Exception as e:
            error_msg = f"视频融合失败: {str(e)}"
            log_info(error_msg)
            import traceback
            log_info(traceback.format_exc())
            self.error_occurred.emit(error_msg)

    def get_file_size(self, file_path):
        """获取文件大小的可读格式"""
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            return self.get_file_size_from_bytes(size)
        return "未知"

    def get_file_size_from_bytes(self, size):
        """将字节数转换为可读格式"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"


class VideoSplitThread(QThread):
    """视频分割处理线程 - 增强版"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_processing = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    # 新增信号用于详细进度更新
    detailed_progress = pyqtSignal(str, int)  # 详细进度信息(消息, 进度值)

    def __init__(self, input_path, output_dir, threshold=30.0, remove_audio=False):
        super().__init__()
        self.input_path = input_path
        self.output_dir = output_dir
        self.threshold = threshold
        self.remove_audio = remove_audio
        self.processor = None
        self.is_running = True
        
        # 设置最大重试次数
        self.max_retries = 3
        
        # 创建临时目录，用于分阶段处理
        self.temp_dir = None

    def run(self):
        try:
            from video_processor import VideoProcessor
            import shutil
            import tempfile
            
            self.is_running = True
            self.processor = VideoProcessor()
            
            # 创建临时工作目录
            self.temp_dir = tempfile.mkdtemp(prefix="video_split_")
            self.status_updated.emit(f"创建临时工作目录: {self.temp_dir}")
            
            def progress_callback(current, total, message):
                self.progress_updated.emit(current)
                self.status_updated.emit(message)
                
            # 阶段1: 场景检测
            self.status_updated.emit("🔍 正在检测视频场景...")
            self.progress_updated.emit(10)
            
            try:
                scenes = self.processor.split_video_into_scenes(self.input_path, self.threshold)
                if not scenes:
                    raise Exception("未检测到任何场景")
                    
                self.status_updated.emit(f"✅ 检测到 {len(scenes)} 个场景")
                self.progress_updated.emit(30)
                
            except Exception as e:
                self.error_occurred.emit(f"场景检测失败: {str(e)}")
                self._cleanup_temp()
                return
            
            # 阶段2: 分割视频
            total_scenes = len(scenes)
            successful_scenes = []
            failed_scenes = []
            
            # 获取原视频文件名（不含扩展名）
            base_name = os.path.splitext(os.path.basename(self.input_path))[0]
            
            # 使用ffmpeg直接分割视频
            self._split_with_ffmpeg(scenes, base_name, successful_scenes, failed_scenes)
            
            # 如果ffmpeg分割失败的场景太多，尝试使用MoviePy
            if len(failed_scenes) > total_scenes // 2:
                self.status_updated.emit("⚠️ FFmpeg分割失败场景过多，尝试使用MoviePy...")
                self._split_with_moviepy(scenes, base_name, successful_scenes, failed_scenes)
            
            # 阶段3: 移动成功的视频到最终目录
            self.status_updated.emit("📦 正在移动处理好的视频文件...")
            self.progress_updated.emit(90)
            
            # 确保输出目录存在
            if not os.path.exists(self.output_dir):
                os.makedirs(self.output_dir)
            
            # 移动成功处理的文件到输出目录
            output_files = []
            for temp_file in successful_scenes:
                if os.path.exists(temp_file):
                    target_file = os.path.join(self.output_dir, os.path.basename(temp_file))
                    shutil.move(temp_file, target_file)
                    output_files.append(target_file)
            
            # 生成结果信息
            total_size = 0
            for file_path in output_files:
                if os.path.exists(file_path):
                    total_size += os.path.getsize(file_path)

            size_str = self._format_file_size(total_size)
            
            # 清理临时目录
            self._cleanup_temp()

            result_text = f"""🎉 视频分割完成！

📊 分割统计:
• 输入视频: {os.path.basename(self.input_path)}
• 总场景数: {total_scenes}
• 成功分割: {len(output_files)}
• 失败场景: {len(failed_scenes)}
• 输出目录: {self.output_dir}
• 总文件大小: {size_str}
• 音频处理: {'已去除' if self.remove_audio else '已保留'}

📁 生成的文件:
{chr(10).join([f"• {os.path.basename(f)}" for f in output_files[:10]])}
{'...' if len(output_files) > 10 else ''}

✅ 所有视频片段已保存到输出目录中！"""

            self.finished_processing.emit(result_text)

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            self.error_occurred.emit(f"视频分割失败: {str(e)}\n\n详细错误信息:\n{error_details}")
            self._cleanup_temp()
    
    def _split_with_ffmpeg(self, scenes, base_name, successful_scenes, failed_scenes):
        """使用FFmpeg直接分割视频"""
        try:
            # 查找FFmpeg路径
            ffmpeg_path = self.processor._find_ffmpeg_path()
            if not ffmpeg_path:
                self.status_updated.emit("⚠️ 未找到FFmpeg，跳过FFmpeg分割")
                return
                
            total_scenes = len(scenes)
            for i, scene in enumerate(scenes):
                if not self.is_running:
                    break
                    
                # 生成输出文件名
                scene_filename = f"{base_name}_scene_{i+1:03d}.mp4"
                scene_output_path = os.path.join(self.temp_dir, scene_filename)
                
                # 提取场景时间
                start_time = scene['start_time']
                end_time = scene['end_time']
                duration = end_time - start_time
                
                # 跳过过短的片段
                if duration < 0.5:
                    self.status_updated.emit(f"跳过过短的场景 {i+1}: {duration:.2f}秒")
                    continue
                
                # 使用FFmpeg分割视频
                cmd = [
                    ffmpeg_path,
                    '-y',  # 覆盖输出文件
                    '-ss', str(start_time),  # 开始时间
                    '-i', self.input_path,  # 输入文件
                    '-t', str(duration),  # 持续时间
                    '-c:v', 'libx264',  # 视频编码
                    '-preset', 'ultrafast',  # 使用最快预设
                    '-max_muxing_queue_size', '9999',  # 增加复用队列大小
                ]
                
                # 处理音频
                if self.remove_audio:
                    cmd.extend(['-an'])  # 去除音频
                else:
                    cmd.extend(['-c:a', 'aac'])  # 音频编码
                
                # 添加输出文件
                cmd.append(scene_output_path)
                
                try:
                    self.status_updated.emit(f"正在处理场景 {i+1}/{total_scenes}...")
                    self.progress_updated.emit(30 + int((i + 1) / total_scenes * 50))
                    
                    # 执行FFmpeg命令
                    import subprocess
                    result = subprocess.run(
                        cmd,
                        capture_output=True,
                        text=True,
                        timeout=60  # 设置超时
                    )
                    
                    if result.returncode == 0 and os.path.exists(scene_output_path) and os.path.getsize(scene_output_path) > 1024:
                        successful_scenes.append(scene_output_path)
                        self.status_updated.emit(f"✅ 场景 {i+1}/{total_scenes} 处理成功")
                    else:
                        failed_scenes.append(i)
                        self.status_updated.emit(f"❌ 场景 {i+1}/{total_scenes} 处理失败")
                        
                except Exception as e:
                    failed_scenes.append(i)
                    self.status_updated.emit(f"❌ 场景 {i+1}/{total_scenes} 处理失败: {str(e)}")
        
        except Exception as e:
            self.status_updated.emit(f"FFmpeg分割过程出错: {str(e)}")
    
    def _split_with_moviepy(self, scenes, base_name, successful_scenes, failed_scenes):
        """使用MoviePy分割视频"""
        try:
            from moviepy.editor import VideoFileClip
            import gc
            
            # 加载原视频
            video_clip = VideoFileClip(self.input_path)
            
            # 获取仍然需要处理的场景
            remaining_scenes = [i for i, scene in enumerate(scenes) if i not in [successful_scenes.index(s) if s in successful_scenes else -1 for s in successful_scenes]]
            total_remaining = len(remaining_scenes)
            
            if total_remaining == 0:
                video_clip.close()
                return
                
            self.status_updated.emit(f"使用MoviePy处理剩余 {total_remaining} 个场景...")
            
            for idx, i in enumerate(remaining_scenes):
                if not self.is_running:
                    break
                    
                scene = scenes[i]
                
                # 生成输出文件名
                scene_filename = f"{base_name}_scene_{i+1:03d}.mp4"
                scene_output_path = os.path.join(self.temp_dir, scene_filename)
                
                # 提取场景时间
                start_time = scene['start_time']
                end_time = scene['end_time']
                duration = end_time - start_time
                
                # 跳过过短的片段
                if duration < 0.5:
                    continue
                
                try:
                    # 强制垃圾回收
                    gc.collect()
                    
                    # 更新状态
                    self.status_updated.emit(f"MoviePy处理场景 {i+1}/{len(scenes)} ({idx+1}/{total_remaining})...")
                    self.progress_updated.emit(70 + int((idx + 1) / total_remaining * 20))
                    
                    # 提取片段
                    scene_clip = video_clip.subclip(start_time, end_time)
                    
                    # 根据选项处理音频
                    if self.remove_audio and scene_clip.audio is not None:
                        scene_clip = scene_clip.without_audio()
                    
                    # 保存场景视频
                    write_kwargs = {
                        'codec': "libx264",
                        'audio_codec': "aac" if not self.remove_audio and scene_clip.audio is not None else None,
                        'threads': 1,  # 强制单线程
                        'preset': 'ultrafast',  # 使用最快预设
                        'verbose': False,
                        'logger': None,
                        'ffmpeg_params': ['-avoid_negative_ts', 'make_zero', '-max_muxing_queue_size', '9999']
                    }
                    
                    # 使用安全写入方法
                    success = self.processor.safe_write_videofile(scene_clip, scene_output_path, **write_kwargs)
                    
                    # 关闭片段释放资源
                    scene_clip.close()
                    
                    if success:
                        successful_scenes.append(scene_output_path)
                        self.status_updated.emit(f"✅ MoviePy: 场景 {i+1} 处理成功")
                    else:
                        self.status_updated.emit(f"❌ MoviePy: 场景 {i+1} 处理失败")
                    
                except Exception as e:
                    self.status_updated.emit(f"❌ MoviePy: 场景 {i+1} 处理失败: {str(e)}")
            
            # 关闭视频释放资源
            video_clip.close()
            
        except Exception as e:
            self.status_updated.emit(f"MoviePy分割过程出错: {str(e)}")
    
    def _cleanup_temp(self):
        """清理临时目录"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                import shutil
                shutil.rmtree(self.temp_dir)
            except Exception as e:
                pass  # 忽略清理错误
    
    def _format_file_size(self, size_bytes):
        """格式化文件大小显示"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"
        
    def terminate(self):
        """安全终止线程"""
        self.is_running = False
        super().terminate()


class EnhancedVideoDeduplicationGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.processing_thread = None
        self.split_thread = None
        self.server_url = "http://api_script.pg-code-go.com"  # 后端服务器地址
        self.is_activated = False
        self.machine_code = ""

        # 在初始化UI之前检查激活状态
        try:
            if not self.check_activation_on_startup():
                # 如果激活验证失败，程序将在此退出
                sys.exit(0)
        except Exception as e:
            log_error(f"❌ 激活验证过程中发生错误: {e}")
            # 确保属性已设置，即使验证失败
            if not hasattr(self, 'is_activated'):
                self.is_activated = False
            if not hasattr(self, 'machine_code'):
                self.machine_code = ""
            sys.exit(0)

        self.init_ui()
        self.setup_menu_bar()
        self.setup_status_bar()
        self.setup_system_tray()

        # 检查FFmpeg配置
        self.check_ffmpeg_on_startup()

    def check_activation_on_startup(self):
        """启动时检查激活状态"""
        if not ACTIVATION_AVAILABLE:
            log_warning(f"⚠️ 激活验证模块不可用，跳过激活检查")
            self.is_activated = True
            return True

        try:
            # 获取机器码
            self.machine_code = get_machine_code()
            log_info(f"🔑 机器码: {self.machine_code}")

            # 检查本地激活状态
            if self.check_local_activation():
                log_info(f"✅ 本地激活验证通过")
                self.is_activated = True
                return True
            else:
                log_error(f"❌ 本地激活验证失败，需要重新激活")
                # 显示激活对话框
                return self.show_activation_dialog()

        except Exception as e:
            log_error(f"❌ 激活检查失败: {str(e)}")
            QMessageBox.critical(None, "激活验证错误", f"激活检查失败: {str(e)}\n\n程序将退出。")
            return False

    def check_ffmpeg_on_startup(self):
        """启动时检查FFmpeg配置"""
        try:
            from video_processor import VideoProcessor
            processor = VideoProcessor()

            if processor.ffmpeg_path:
                log_info(f"✅ FFmpeg已配置: {processor.ffmpeg_path}")
                # 测试FFmpeg是否正常工作
                if processor.test_ffmpeg_installation():
                    log_info("✅ FFmpeg测试通过")
                    return True
                else:
                    log_warning("⚠️ FFmpeg测试失败")
            else:
                log_warning("⚠️ 未找到FFmpeg")

            # 如果FFmpeg有问题，显示修复对话框
            self.show_ffmpeg_fix_dialog()
            return False

        except Exception as e:
            log_error(f"❌ FFmpeg检查失败: {e}")
            return False

    def show_ffmpeg_fix_dialog(self):
        """显示FFmpeg修复对话框"""
        try:
            reply = QMessageBox.question(
                self,
                "FFmpeg配置问题",
                "检测到FFmpeg未正确配置，这可能导致视频合成失败。\n\n"
                "是否要自动下载并配置FFmpeg？\n\n"
                "选择'是'：自动下载FFmpeg到程序目录\n"
                "选择'否'：手动配置FFmpeg\n"
                "选择'取消'：跳过此步骤",
                QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                self.auto_install_ffmpeg()
            elif reply == QMessageBox.No:
                self.show_manual_ffmpeg_guide()
            # 如果选择取消，则什么都不做

        except Exception as e:
            log_error(f"显示FFmpeg修复对话框失败: {e}")

    def auto_install_ffmpeg(self):
        """自动安装FFmpeg"""
        try:
            from ffmpeg_installer import FFmpegInstaller

            # 显示进度对话框
            progress_dialog = QProgressDialog("正在下载和配置FFmpeg...", "取消", 0, 0, self)
            progress_dialog.setWindowModality(Qt.WindowModal)
            progress_dialog.setAutoClose(True)
            progress_dialog.setAutoReset(True)
            progress_dialog.show()

            # 创建安装器并安装
            installer = FFmpegInstaller()
            result = installer.install_ffmpeg()

            progress_dialog.close()

            if result:
                QMessageBox.information(
                    self,
                    "安装成功",
                    f"FFmpeg已成功安装到: {result}\n\n"
                    "程序现在应该可以正常进行视频合成了！"
                )
                log_info(f"✅ FFmpeg自动安装成功: {result}")
            else:
                QMessageBox.warning(
                    self,
                    "安装失败",
                    "FFmpeg自动安装失败。\n\n"
                    "请点击'手动配置'按钮查看详细安装指导。"
                )
                self.show_manual_ffmpeg_guide()

        except ImportError:
            QMessageBox.warning(
                self,
                "模块缺失",
                "FFmpeg安装器模块不可用。\n\n"
                "请手动下载并配置FFmpeg。"
            )
            self.show_manual_ffmpeg_guide()
        except Exception as e:
            QMessageBox.critical(
                self,
                "安装错误",
                f"FFmpeg自动安装过程中发生错误:\n{str(e)}\n\n"
                "请尝试手动配置FFmpeg。"
            )
            log_error(f"FFmpeg自动安装失败: {e}")

    def show_manual_ffmpeg_guide(self):
        """显示手动FFmpeg配置指导"""
        import platform

        if platform.system() == "Windows":
            guide_text = """=== Windows FFmpeg手动安装指导 ===

方法1（推荐）：
1. 下载FFmpeg: https://www.gyan.dev/ffmpeg/builds/ffmpeg-release-essentials.zip
2. 解压后将ffmpeg.exe复制到程序目录
3. 重启程序

方法2：
1. 下载FFmpeg完整版: https://ffmpeg.org/download.html
2. 解压到 C:\\ffmpeg
3. 将 C:\\ffmpeg\\bin 添加到系统PATH环境变量
4. 重启程序

注意：如果您已经安装了FFmpeg但仍然出现此提示，
请确保ffmpeg.exe在系统PATH中或程序目录下。"""
        else:
            guide_text = """=== Linux/macOS FFmpeg安装指导 ===

Ubuntu/Debian:
sudo apt update
sudo apt install ffmpeg

CentOS/RHEL:
sudo yum install ffmpeg

macOS:
brew install ffmpeg

安装完成后重启程序。"""

        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("FFmpeg手动安装指导")
        msg_box.setText(guide_text)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.exec_()

    def get_config_file_path(self):
        """获取配置文件路径，确保在打包后的exe中也能正常工作"""
        try:
            import sys
            import os

            # 如果是打包后的exe程序
            if getattr(sys, 'frozen', False):
                # 使用exe文件所在目录
                app_dir = os.path.dirname(sys.executable)
            else:
                # 开发环境，使用脚本文件所在目录
                app_dir = os.path.dirname(__file__)

            config_file = os.path.join(app_dir, 'activation_config.json')

            # 测试是否可写
            try:
                test_file = os.path.join(app_dir, 'test_write.tmp')
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
                log_info(f"📁 配置文件路径: {config_file}")
                return config_file
            except (PermissionError, OSError):
                # 如果exe目录不可写，使用用户数据目录
                import tempfile
                user_data_dir = os.path.join(tempfile.gettempdir(), 'VideoDeduplicationTool')
                os.makedirs(user_data_dir, exist_ok=True)
                config_file = os.path.join(user_data_dir, 'activation_config.json')
                log_info(f"📁 使用用户数据目录: {config_file}")
                return config_file

        except Exception as e:
            log_error(f"❌ 获取配置文件路径失败: {e}")
            # 回退到当前目录
            return 'activation_config.json'

    def check_local_activation(self):
        """检查本地激活配置"""
        try:
            import json
            import os
            import requests
            import datetime

            config_file = self.get_config_file_path()
            if not os.path.exists(config_file):
                log_info(f"📄 激活配置文件不存在")
                return False

            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 检查是否已激活且有激活码
            if config.get('activated', False) and config.get('activation_code'):
                activation_code = config.get('activation_code')
                log_info(f"🔍 检查本地保存的激活码: {activation_code}")

                # 每次启动都与服务器验证激活码是否仍然有效
                log_info(f"🔍 与服务器验证激活码有效性")
                if self.verify_activation_code_with_server(activation_code):
                    log_info(f"✅ 激活码验证通过")
                    return True
                else:
                    log_error(f"❌ 激活码服务器验证失败")
                    # 清除无效的本地配置
                    self.clear_local_activation_config()
                    return False
            else:
                log_error(f"❌ 本地激活配置验证失败：未激活或无激活码")
                return False

        except Exception as e:
            log_error(f"❌ 读取激活配置失败: {e}")
            return False

    def verify_activation_code_with_server(self, activation_code):
        """与服务器验证激活码是否有效"""
        try:
            import time

            # 设置API管理器的服务器URL
            api_manager.set_base_url(self.server_url)

            # 使用API管理器进行验证
            result = api_manager.verify_activation(activation_code, self.machine_code)

            success = result.get('success', False)
            if success:
                # 验证服务器返回的时间戳，防止重放攻击
                server_timestamp = result.get('timestamp')
                if server_timestamp:
                    current_timestamp = int(time.time())
                    if abs(current_timestamp - server_timestamp) > 300:  # 5分钟容差
                        log_warning(f"⚠️ 时间戳验证失败，可能存在重放攻击")
                        return False
                # 验证成功
                log_info(f"✅ 服务器验证成功: {result.get('message', '')}")
                return True
            else:
                log_error(f"❌ 服务器验证失败: {result.get('message', '未知错误')}")

                # 网络连接错误时不允许离线使用
                error_type = result.get('error_type')
                if error_type in ['timeout', 'connection_error', 'request_error']:
                    log_error(f"❌ 网络连接失败，无法验证激活码: {result.get('message')}")
                    log_error("❌ 离线模式已禁用，必须在线验证激活码")
                    return False

                return False

        except Exception as e:
            log_warning(f"⚠️ 激活码服务器验证异常: {e}")
            return False

    def validate_activation_code_format(self, activation_code):
        """验证激活码格式"""
        import re
        # 激活码格式：XXXX-XXXX-XXXX-XXXXX (4-4-4-5位字母数字组合，只接受大写)
        pattern = r'^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{5}$'
        return bool(re.match(pattern, activation_code))

    def check_offline_grace_period(self):
        """检查离线宽限期（已禁用）"""
        log_error("❌ 离线模式已禁用，必须在线验证激活码")
        return False



    def clear_local_activation_config(self):
        """清除本地激活配置"""
        try:
            import os
            config_file = self.get_config_file_path()
            if os.path.exists(config_file):
                os.remove(config_file)
                log_info(f"🗑️ 已清除无效的本地激活配置")
        except Exception as e:
            log_warning(f"⚠️ 清除本地配置失败: {e}")

    def show_activation_dialog(self):
        """显示激活对话框"""
        try:
            dialog = ActivationDialog(None, self.server_url)

            while True:
                result = dialog.exec_()

                if result == ActivationDialog.Accepted:
                    # 激活成功
                    log_info(f"✅ 激活验证成功")
                    self.is_activated = True
                    QMessageBox.information(None, "激活成功", "软件已成功激活，欢迎使用！")
                    return True
                else:
                    # 激活失败或取消
                    reply = QMessageBox.question(
                        None, "激活验证",
                        "软件未激活，无法使用。\n\n是否继续尝试激活？",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.Yes
                    )

                    if reply == QMessageBox.Yes:
                        # 继续尝试激活
                        continue
                    else:
                        # 用户选择退出
                        log_error(f"❌ 用户取消激活，程序退出")
                        return False

        except Exception as e:
            log_error(f"❌ 显示激活对话框失败: {e}")
            QMessageBox.critical(None, "激活验证错误", f"显示激活对话框失败: {str(e)}\n\n程序将退出。")
            return False

    def verify_activation_before_action(self, action_name="此功能"):
        """在执行重要操作前验证激活状态"""
        if not ACTIVATION_AVAILABLE:
            return True  # 如果激活模块不可用，允许使用

        if not self.is_activated:
            reply = QMessageBox.warning(
                self, "未激活",
                f"软件未激活，无法使用{action_name}。\n\n是否现在激活？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                return self.show_activation_dialog()
            else:
                return False

        return True

    def setup_window_icon(self):
        """设置窗口图标"""
        try:
            # 尝试加载logo图片
            logo_path = os.path.join(os.path.dirname(__file__), 'img', 'logo.ico')
            if os.path.exists(logo_path):
                icon = QIcon(logo_path)
                self.setWindowIcon(icon)
                log_info(f"✅ 成功加载窗口图标: {logo_path}")
            else:
                log_warning(f"⚠️ 未找到logo文件: {logo_path}")
                # 创建一个简单的默认图标
                self.create_default_icon()
        except Exception as e:
            log_error(f"❌ 设置窗口图标失败: {e}")
            self.create_default_icon()

    def create_default_icon(self):
        """创建默认图标"""
        try:
            # 创建一个简单的默认图标
            pixmap = QPixmap(64, 64)
            pixmap.fill(QColor(52, 152, 219))  # 蓝色背景

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # 绘制一个简单的视频图标
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setBrush(QBrush(QColor(255, 255, 255)))

            # 绘制播放按钮三角形
            from PyQt5.QtCore import QPointF
            triangle = [
                QPointF(20, 16),
                QPointF(20, 48),
                QPointF(44, 32)
            ]
            painter.drawPolygon(triangle)

            painter.end()

            icon = QIcon(pixmap)
            self.setWindowIcon(icon)
            log_info(f"✅ 创建默认图标成功")
        except Exception as e:
            log_error(f"❌ 创建默认图标失败: {e}")

    def init_ui(self):
        # 设置窗口标志，确保在任务栏中正确显示
        self.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint |
                           Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)

        # 设置窗口标题和几何
        # self.setWindowTitle("🎬 余下视频混剪工具 v2.0 - 专业去重解决方案")
        self.setGeometry(100, 100, 1400, 900)

        # 设置窗口图标
        self.setup_window_icon()

        # 确保窗口在任务栏中显示
        self.setAttribute(Qt.WA_ShowWithoutActivating, False)
        self.setAttribute(Qt.WA_DeleteOnClose, False)

        # 设置现代化样式 - 优化字体清晰度和界面美观度
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
            }
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
                border: 2px solid #dee2e6;
                border-radius: 14px;
                margin-top: 18px;
                padding-top: 20px;
                background-color: rgba(255, 255, 255, 0.95);
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 18px;
                padding: 0 12px 0 12px;
                color: #2c3e50;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #20c997);
                border: none;
                color: white;
                padding: 10px 20px;
                text-align: center;
                font-size: 14px;
                font-weight: bold;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
                margin: 4px 2px;
                border-radius: 8px;
                min-width: 100px;
                min-height: 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #20c997, stop:1 #17a2b8);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1e7e34, stop:1 #155724);
            }
            QPushButton:disabled {
                background: #6c757d;
                color: #adb5bd;
            }
            QLineEdit {
                padding: 8px;
                border: 2px solid #ced4da;
                border-radius: 6px;
                font-size: 14px;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
                background-color: white;
                color: #2c3e50;
                min-height: 20px;
            }
            QLineEdit:focus {
                border-color: #007bff;
            }
            QProgressBar {
                border: 2px solid #ced4da;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                font-size: 14px;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
                background-color: #f8f9fa;
                color: #2c3e50;
                min-height: 25px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #28a745, stop:0.5 #20c997, stop:1 #17a2b8);
                border-radius: 6px;
                margin: 2px;
            }
            QTextEdit {
                border: 2px solid #ced4da;
                border-radius: 8px;
                font-family: 'Microsoft YaHei UI', 'Consolas', 'Monaco', monospace;
                font-size: 14px;
                background-color: white;
                padding: 10px;
                color: #2c3e50;
                line-height: 1.5;
            }
            QSlider::groove:horizontal {
                border: 1px solid #ced4da;
                background: #f8f9fa;
                height: 14px;
                border-radius: 7px;
            }
            QSlider::sub-page:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #28a745, stop:1 #20c997);
                border: 1px solid #20c997;
                height: 14px;
                border-radius: 7px;
            }
            QSlider::add-page:horizontal {
                background: #f8f9fa;
                border: 1px solid #ced4da;
                height: 14px;
                border-radius: 7px;
            }
            QSlider::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #e9ecef);
                border: 3px solid #28a745;
                width: 20px;
                margin-top: -5px;
                margin-bottom: -5px;
                border-radius: 10px;
            }
            QSlider::handle:horizontal:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 3px solid #20c997;
            }
            QTabWidget::pane {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background: #e9ecef;
                border: 2px solid #dee2e6;
                padding: 10px 15px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
                color: #495057;
                min-width: 80px;
                min-height: 20px;
            }
            QTabBar::tab:selected {
                background: white;
                border-bottom-color: white;
                color: #2c3e50;
                font-size: 15px;
            }
            QTabBar::tab:hover {
                background: #f8f9fa;
                color: #2c3e50;
            }
            QComboBox {
                padding: 8px 12px;
                border: 2px solid #ced4da;
                border-radius: 6px;
                background-color: white;
                min-width: 120px;
                font-size: 14px;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
                color: #2c3e50;
                min-height: 20px;
            }
            QComboBox:focus {
                border-color: #007bff;
            }
            QComboBox::drop-down {
                border: none;
                width: 25px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #495057;
                margin-right: 10px;
            }
            QCheckBox {
                font-size: 14px;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
                spacing: 10px;
                color: #2c3e50;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 4px;
                border: 2px solid #ced4da;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #28a745;
                border-color: #28a745;
                image: none;
            }
            QCheckBox::indicator:checked:hover {
                background-color: #20c997;
                border-color: #20c997;
            }
            QLabel {
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
                color: #2c3e50;
                font-size: 14px;
            }
            QSpinBox {
                padding: 8px;
                border: 2px solid #ced4da;
                border-radius: 6px;
                background-color: white;
                font-size: 14px;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
                color: #2c3e50;
                min-height: 20px;
                min-width: 80px;
            }
            QSpinBox:focus {
                border-color: #007bff;
            }
            QStatusBar {
                background-color: #f8f9fa;
                border-top: 1px solid #dee2e6;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
                font-size: 14px;
                color: #495057;
            }
            QToolTip {
                background-color: #2c3e50;
                color: white;
                border: 1px solid #495057;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
                font-size: 13px;
            }
        """)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(15)

        # 创建标题区域 - 提高显眼度并确保在全屏时保持合适大小
        title_frame = QFrame()
        title_frame.setFixedHeight(180)  # 调整为180px
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E3DB8, stop:1 #3050D0);
                margin: 8px 10px;
                padding: 15px;
                min-height: 180px;
                max-height: 180px;
            }
        """)
        title_layout = QVBoxLayout(title_frame)
        title_layout.setContentsMargins(10, 20, 10, 20)
        title_layout.setSpacing(15)  # 调整间距

        title_label = QLabel("🎬 余下视频混剪工具")
        title_label.setAlignment(Qt.AlignCenter)
        # 在样式表中设置字体大小
        title_label.setStyleSheet("""
            color: white;
            margin: 0px;
            padding: 0px;
            font-weight: 900;
            letter-spacing: 5px;
            min-height: 70px;
            max-height: 70px;
            font-family: 'Microsoft YaHei UI';
            font-size: 50px;
        """)

        subtitle_label = QLabel("专业级视频去重解决方案 | 智能场景识别 | 高效内容替换")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            color: rgba(255, 255, 255, 0.95);
            margin: 0px;
            padding: 0px;
            font-weight: 700;
            letter-spacing: 2px;
            min-height: 30px;
            max-height: 30px;
            font-family: 'Microsoft YaHei UI';
            font-size: 16px;
        """)

        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)
        main_layout.addWidget(title_frame)

        # 创建标签页 - 优化全屏布局
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet("""
            QTabWidget {
                background-color: transparent;
            }
            QTabWidget::pane {
                border: 2px solid #dee2e6;
                border-radius: 10px;
                background-color: white;
                margin-top: 3px;
            }
        """)
        main_layout.addWidget(tab_widget)

        # 主要功能标签页
        main_tab = QWidget()
        tab_widget.addTab(main_tab, "🎯 主要功能")

        # 高级设置标签页
        advanced_tab = QWidget()
        tab_widget.addTab(advanced_tab, "⚙️ 高级设置")

        # 字幕移除标签页
        subtitle_removal_tab = QWidget()
        tab_widget.addTab(subtitle_removal_tab, "📝 字幕移除")

        # 字幕提取标签页
        subtitle_extraction_tab = QWidget()
        tab_widget.addTab(subtitle_extraction_tab, "🎤 字幕提取")

        # 视频音频分离标签页
        video_audio_separation_tab = QWidget()
        tab_widget.addTab(video_audio_separation_tab, "🎵 视频音频分离")

        # 视频分割标签页
        video_split_tab = QWidget()
        tab_widget.addTab(video_split_tab, "✂️ 视频分割")

        # 视频合并标签页
        video_merge_tab = QWidget()
        tab_widget.addTab(video_merge_tab, "🎬 智能混剪")

        # 视频融合标签页
        video_fusion_tab = QWidget()
        tab_widget.addTab(video_fusion_tab, "🎭 视频融合")

        # 帮助标签页
        help_tab = QWidget()
        tab_widget.addTab(help_tab, "❓ 使用帮助")

        self.setup_main_tab(main_tab)
        self.setup_advanced_tab(advanced_tab)
        self.setup_subtitle_removal_tab(subtitle_removal_tab)
        self.setup_subtitle_extraction_tab(subtitle_extraction_tab)
        self.setup_video_audio_separation_tab(video_audio_separation_tab)
        self.setup_video_split_tab(video_split_tab)
        self.setup_video_merge_tab(video_merge_tab)
        self.setup_video_fusion_tab(video_fusion_tab)
        self.setup_help_tab(help_tab)

    def setup_main_tab(self, tab):
        """设置主要功能标签页"""
        layout = QHBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)

        # 左侧面板
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(10, 10, 10, 10)
        left_layout.setSpacing(15)

        # 文件选择区域
        file_group = QGroupBox("📁 文件选择")
        file_layout = QGridLayout(file_group)
        file_layout.setContentsMargins(15, 20, 15, 15)
        file_layout.setSpacing(12)
        file_layout.setColumnStretch(1, 1)  # 让输入框列可以拉伸

        # 主要视频选择（有字幕/水印的剪辑视频）
        main_video_label = QLabel("🎥 主要视频（有字幕/水印）:")
        main_video_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #e74c3c; padding: 3px;")
        main_video_label.setToolTip("选择已剪辑并包含字幕或水印的视频文件")
        file_layout.addWidget(main_video_label, 0, 0)
        self.main_video_path = QLineEdit()
        self.main_video_path.setPlaceholderText("选择包含字幕或水印的主要视频文件...")
        self.main_video_path.setMinimumHeight(30)  # 确保在全屏时大小合适
        file_layout.addWidget(self.main_video_path, 0, 1)
        self.main_video_btn = AnimatedButton("浏览")
        self.main_video_btn.setFixedWidth(80)  # 固定宽度
        self.main_video_btn.setFixedHeight(30)  # 固定高度
        self.main_video_btn.clicked.connect(self.select_main_video)
        file_layout.addWidget(self.main_video_btn, 0, 2)

        # 原视频选择（未剪辑的原始视频）
        aux_video_label = QLabel("🎬 原视频（未剪辑）:")
        aux_video_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #27ae60; padding: 3px;")
        aux_video_label.setToolTip("选择未剪辑的原始视频文件，用于替换主要视频中的场景")
        file_layout.addWidget(aux_video_label, 1, 0)
        self.aux_video_path = QLineEdit()
        self.aux_video_path.setPlaceholderText("选择未剪辑的原始视频文件...")
        self.aux_video_path.setMinimumHeight(30)
        file_layout.addWidget(self.aux_video_path, 1, 1)
        self.aux_video_btn = AnimatedButton("浏览")
        self.aux_video_btn.setFixedWidth(80)
        self.aux_video_btn.setFixedHeight(30)
        self.aux_video_btn.clicked.connect(self.select_aux_video)
        file_layout.addWidget(self.aux_video_btn, 1, 2)

        # 输出文件选择
        output_label = QLabel("💾 输出文件:")
        output_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50; padding: 3px;")
        file_layout.addWidget(output_label, 2, 0)
        self.output_path = QLineEdit()
        self.output_path.setPlaceholderText("选择原创视频的保存路径...")
        self.output_path.setMinimumHeight(30)
        file_layout.addWidget(self.output_path, 2, 1)
        self.output_btn = AnimatedButton("浏览")
        self.output_btn.setFixedWidth(80)
        self.output_btn.setFixedHeight(30)
        self.output_btn.clicked.connect(self.select_output_path)
        file_layout.addWidget(self.output_btn, 2, 2)

        left_layout.addWidget(file_group)

        # 参数设置区域
        param_group = QGroupBox("🔧 智能参数设置")
        param_layout = QGridLayout(param_group)
        param_layout.setContentsMargins(15, 20, 15, 15)
        param_layout.setSpacing(15)
        param_layout.setColumnStretch(1, 1)  # 让滑块列可以拉伸

        # 场景检测阈值
        threshold_label = QLabel("🎯 场景检测敏感度:")
        threshold_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50; padding: 3px;")
        param_layout.addWidget(threshold_label, 0, 0)
        self.threshold_slider = QSlider(Qt.Horizontal)
        self.threshold_slider.setRange(10, 100)
        self.threshold_slider.setValue(30)
        self.threshold_slider.setMinimumHeight(25)
        self.threshold_slider.valueChanged.connect(self.update_threshold_label)
        param_layout.addWidget(self.threshold_slider, 0, 1)
        self.threshold_label = QLabel("30.0 (推荐)")
        self.threshold_label.setStyleSheet("color: #28a745; font-weight: bold; font-size: 14px; padding: 3px;")
        self.threshold_label.setMinimumWidth(100)  # 确保标签宽度足够
        param_layout.addWidget(self.threshold_label, 0, 2)

        # 相似度阈值
        similarity_label = QLabel("🔍 相似度匹配精度:")
        similarity_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50; padding: 3px;")
        param_layout.addWidget(similarity_label, 1, 0)
        self.similarity_slider = QSlider(Qt.Horizontal)
        self.similarity_slider.setRange(50, 95)
        self.similarity_slider.setValue(80)
        self.similarity_slider.setMinimumHeight(25)
        self.similarity_slider.valueChanged.connect(self.update_similarity_label)
        param_layout.addWidget(self.similarity_slider, 1, 1)
        self.similarity_label = QLabel("0.80 (推荐)")
        self.similarity_label.setStyleSheet("color: #28a745; font-weight: bold; font-size: 14px; padding: 3px;")
        self.similarity_label.setMinimumWidth(100)
        param_layout.addWidget(self.similarity_label, 1, 2)

        # 替换策略选择
        strategy_label = QLabel("🔄 替换策略:")
        strategy_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50; padding: 3px;")
        param_layout.addWidget(strategy_label, 2, 0)
        self.replacement_strategy = QComboBox()
        self.replacement_strategy.addItems(["相似度匹配", "均匀替换", "随机替换"])
        self.replacement_strategy.setCurrentIndex(0)
        self.replacement_strategy.setMinimumHeight(30)
        self.replacement_strategy.currentIndexChanged.connect(self.update_replacement_options)
        param_layout.addWidget(self.replacement_strategy, 2, 1, 1, 2)

        # 替换率（仅用于均匀和随机替换）
        rate_label = QLabel("📊 替换率:")
        rate_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50; padding: 3px;")
        param_layout.addWidget(rate_label, 3, 0)
        self.replacement_rate_slider = QSlider(Qt.Horizontal)
        self.replacement_rate_slider.setRange(10, 90)
        self.replacement_rate_slider.setValue(50)
        self.replacement_rate_slider.setMinimumHeight(25)
        self.replacement_rate_slider.valueChanged.connect(self.update_replacement_rate_label)
        self.replacement_rate_slider.setEnabled(False)
        param_layout.addWidget(self.replacement_rate_slider, 3, 1)
        self.replacement_rate_label = QLabel("50%")
        self.replacement_rate_label.setStyleSheet("color: #6c757d; font-size: 14px; font-weight: bold; padding: 3px;")
        self.replacement_rate_label.setMinimumWidth(100)
        param_layout.addWidget(self.replacement_rate_label, 3, 2)

        # 性能优化选项
        self.use_optimization = QCheckBox("🚀 启用性能优化 (多线程处理)")
        self.use_optimization.setChecked(True)
        self.use_optimization.setStyleSheet("font-size: 14px;")
        self.use_optimization.setToolTip("启用多线程并行处理，显著提升处理速度")
        param_layout.addWidget(self.use_optimization, 4, 0, 1, 3)

        left_layout.addWidget(param_group)

        # 控制按钮区域
        control_group = QGroupBox("🎮 操作控制")
        control_layout = QVBoxLayout(control_group)
        control_layout.setContentsMargins(15, 20, 15, 15)

        self.start_btn = AnimatedButton("🚀 开始智能处理")
        self.start_btn.setMinimumHeight(40)  # 增加按钮高度
        self.start_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #ff6b6b, stop:1 #ee5a24);
                font-size: 15px; 
                padding: 10px;
                min-height: 40px;
                border-radius: 8px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #ff5252, stop:1 #ff3838);
            }
        """)
        self.start_btn.clicked.connect(self.start_processing)
        control_layout.addWidget(self.start_btn)

        self.stop_btn = AnimatedButton("⏹️ 停止处理")
        self.stop_btn.setMinimumHeight(40)
        self.stop_btn.setStyleSheet("""
            QPushButton { 
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #f44336, stop:1 #d32f2f);
                min-height: 40px;
                border-radius: 8px;
                font-size: 15px;
            } 
            QPushButton:hover { 
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #da190b, stop:1 #b71c1c); 
            }
        """)
        self.stop_btn.clicked.connect(self.stop_processing)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)

        left_layout.addWidget(control_group)
        left_layout.addStretch()

        layout.addWidget(left_panel, 1)  # 设置为1的比例

        # 右侧面板
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(10, 10, 10, 10)
        right_layout.setSpacing(15)

        # 进度显示区域
        progress_group = QGroupBox("📊 处理进度")
        progress_layout = QVBoxLayout(progress_group)
        progress_layout.setContentsMargins(15, 20, 15, 15)
        progress_layout.setSpacing(10)

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setMinimumHeight(25)
        progress_layout.addWidget(self.progress_bar)

        self.status_label = QLabel("🟢 系统就绪，等待开始处理...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            font-size: 15px;
            color: #28a745;
            margin: 8px;
            padding: 5px;
            font-weight: bold;
            font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
        """)
        progress_layout.addWidget(self.status_label)

        # 详细进度信息
        self.detailed_progress_label = QLabel("")
        self.detailed_progress_label.setAlignment(Qt.AlignCenter)
        self.detailed_progress_label.setStyleSheet("""
            font-size: 14px;
            color: #6c757d;
            margin: 5px;
            padding: 3px;
            font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
        """)
        progress_layout.addWidget(self.detailed_progress_label)

        right_layout.addWidget(progress_group)

        # 处理日志区域
        log_group = QGroupBox("📝 处理日志")
        log_layout = QVBoxLayout(log_group)
        log_layout.setContentsMargins(15, 20, 15, 15)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(180)  # 减小高度以适应全屏布局
        self.log_text.setStyleSheet("""
            font-family: 'Microsoft YaHei UI', 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            background-color: #f8f9fa;
            color: #2c3e50;
            border: 2px solid #dee2e6;
            border-radius: 6px;
            padding: 8px;
            line-height: 1.3;
        """)
        log_layout.addWidget(self.log_text)

        right_layout.addWidget(log_group)

        # 结果显示区域
        result_group = QGroupBox("🎉 处理结果")
        result_layout = QVBoxLayout(result_group)
        result_layout.setContentsMargins(15, 20, 15, 15)

        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setStyleSheet("""
            font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
            font-size: 14px;
            background-color: white;
            color: #2c3e50;
            border: 2px solid #dee2e6;
            border-radius: 6px;
            padding: 10px;
            line-height: 1.5;
        """)
        result_layout.addWidget(self.result_text)

        right_layout.addWidget(result_group)

        layout.addWidget(right_panel, 2)  # 设置为2的比例，使右侧更宽

        # 初始化日志
        self.log_message("🎬 余下视频混剪工具已启动")
        self.log_message("💡 提示：请选择主要视频（有字幕/水印）和原视频（干净）开始处理")
        self.log_message("🔄 智能去重技术：通过场景替换自动去除字幕和水印")

    def setup_advanced_tab(self, tab):
        """设置高级设置标签页"""
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(25)

        # 高级参数设置
        advanced_group = QGroupBox("🔬 高级算法参数")
        advanced_layout = QGridLayout(advanced_group)
        advanced_layout.setContentsMargins(20, 25, 20, 20)
        advanced_layout.setSpacing(18)
        advanced_layout.setColumnStretch(1, 1)

        # 特征提取帧数
        feature_frames_label = QLabel("🎞️ 特征提取帧数:")
        feature_frames_label.setStyleSheet("font-size: 17px; font-weight: bold; color: #2c3e50; padding: 5px;")
        advanced_layout.addWidget(feature_frames_label, 0, 0)
        self.feature_frames_spin = QSpinBox()
        self.feature_frames_spin.setRange(5, 50)
        self.feature_frames_spin.setValue(10)
        self.feature_frames_spin.setToolTip("每个视频段提取的关键帧数量，越多越精确但处理越慢")
        advanced_layout.addWidget(self.feature_frames_spin, 0, 1)

        # 处理线程数
        thread_count_label = QLabel("⚡ 处理线程数:")
        thread_count_label.setStyleSheet("font-size: 17px; font-weight: bold; color: #2c3e50; padding: 5px;")
        advanced_layout.addWidget(thread_count_label, 1, 0)
        self.thread_count_spin = QSpinBox()
        self.thread_count_spin.setRange(1, 16)
        self.thread_count_spin.setValue(4)
        self.thread_count_spin.setToolTip("并行处理的线程数量，建议设置为CPU核心数")
        advanced_layout.addWidget(self.thread_count_spin, 1, 1)



        layout.addWidget(advanced_group)

        # 实验性功能
        # experimental_group = QGroupBox("🧪 实验性功能")
        # experimental_layout = QVBoxLayout(experimental_group)
        #
        # self.audio_processing = QCheckBox("🔊 启用音频智能处理")
        # self.audio_processing.setToolTip("实验性功能：对音频进行智能处理以增强去重效果")
        # experimental_layout.addWidget(self.audio_processing)
        #
        # self.smart_crop = QCheckBox("✂️ 智能裁剪优化")
        # self.smart_crop.setToolTip("实验性功能：自动优化视频裁剪以提高原创度")
        # experimental_layout.addWidget(self.smart_crop)
        #
        # layout.addWidget(experimental_group)
        layout.addStretch()

    def setup_subtitle_removal_tab(self, tab):
        """设置字幕移除标签页"""
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # 创建文件选择区域
        file_group = QGroupBox("📁 视频文件选择")
        file_layout = QGridLayout(file_group)
        file_layout.setContentsMargins(15, 20, 15, 15)
        file_layout.setSpacing(12)
        file_layout.setColumnStretch(1, 1)  # 让输入框列可以拉伸

        # 输入视频选择
        input_label = QLabel("🎥 输入视频:")
        input_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        file_layout.addWidget(input_label, 0, 0)

        self.subtitle_input_path = QLineEdit()
        self.subtitle_input_path.setPlaceholderText("选择需要移除字幕的视频文件...")
        self.subtitle_input_path.setMinimumHeight(30)
        file_layout.addWidget(self.subtitle_input_path, 0, 1)

        self.subtitle_input_btn = AnimatedButton("浏览")
        self.subtitle_input_btn.setFixedWidth(80)
        self.subtitle_input_btn.setFixedHeight(30)
        self.subtitle_input_btn.clicked.connect(self.select_subtitle_input)
        file_layout.addWidget(self.subtitle_input_btn, 0, 2)

        # 输出文件选择
        output_label = QLabel("💾 输出文件:")
        output_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        file_layout.addWidget(output_label, 1, 0)

        self.subtitle_output_path = QLineEdit()
        self.subtitle_output_path.setPlaceholderText("选择无字幕视频的保存路径...")
        self.subtitle_output_path.setMinimumHeight(30)
        file_layout.addWidget(self.subtitle_output_path, 1, 1)

        self.subtitle_output_btn = AnimatedButton("浏览")
        self.subtitle_output_btn.setFixedWidth(80)
        self.subtitle_output_btn.setFixedHeight(30)
        self.subtitle_output_btn.clicked.connect(self.select_subtitle_output)
        file_layout.addWidget(self.subtitle_output_btn, 1, 2)

        layout.addWidget(file_group)

        # 字幕区域设置
        subtitle_group = QGroupBox("📏 字幕区域设置")
        subtitle_layout = QGridLayout(subtitle_group)
        subtitle_layout.setContentsMargins(15, 20, 15, 15)
        subtitle_layout.setSpacing(12)
        subtitle_layout.setColumnStretch(1, 1)  # 让右侧列可以拉伸

        # 模式选择
        mode_label = QLabel("模式选择:")
        mode_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        subtitle_layout.addWidget(mode_label, 0, 0)

        self.subtitle_mode = QComboBox()
        self.subtitle_mode.addItems(["自动检测字幕区域", "单矩形手动选择", "多矩形手动选择"])
        self.subtitle_mode.setMinimumHeight(30)
        if not CV2_AVAILABLE:
            # 如果OpenCV不可用，禁用手动框选选项
            self.subtitle_mode.model().item(1).setEnabled(False)
            self.subtitle_mode.model().item(2).setEnabled(False)
            subtitle_note = QLabel("注意: 手动框选功能需要安装OpenCV (opencv-python)")
            subtitle_note.setStyleSheet("color: #dc3545; font-style: italic; font-size: 12px;")
            subtitle_layout.addWidget(subtitle_note, 4, 0, 1, 3)

        self.subtitle_mode.currentIndexChanged.connect(self.toggle_region_selection)
        subtitle_layout.addWidget(self.subtitle_mode, 0, 1, 1, 2)

        # 框选字幕区域按钮
        self.select_region_btn = AnimatedButton("框选字幕区域")
        self.select_region_btn.setMinimumHeight(35)
        self.select_region_btn.setEnabled(False)
        self.select_region_btn.clicked.connect(self.select_subtitle_region)
        subtitle_layout.addWidget(self.select_region_btn, 1, 0, 1, 3)

        # 当前选择的区域显示
        region_label = QLabel("当前选择:")
        region_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        subtitle_layout.addWidget(region_label, 2, 0)

        self.selected_region_label = QLabel("未选择")
        self.selected_region_label.setStyleSheet("color: #dc3545; font-weight: bold; font-size: 14px;")
        subtitle_layout.addWidget(self.selected_region_label, 2, 1, 1, 2)

        # 移除方法
        method_label = QLabel("移除方法:")
        method_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        subtitle_layout.addWidget(method_label, 3, 0)

        self.removal_method_combo = QComboBox()
        self.removal_method_combo.setMinimumHeight(30)
        self.removal_method_combo.addItems([
            "智能填充 (推荐)",
            "背景色填充",
            "黑色填充",
            "模糊处理"
        ])
        self.removal_method_combo.setCurrentIndex(0)
        self.removal_method_combo.currentTextChanged.connect(self.update_removal_method_description)
        subtitle_layout.addWidget(self.removal_method_combo, 3, 1)

        self.removal_method_desc = QLabel("使用AI算法智能填充字幕区域")
        self.removal_method_desc.setStyleSheet("color: #28a745; font-size: 12px;")
        subtitle_layout.addWidget(self.removal_method_desc, 3, 2)

        # 性能设置
        performance_label = QLabel("性能模式:")
        performance_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        subtitle_layout.addWidget(performance_label, 4, 0)

        self.performance_mode_combo = QComboBox()
        self.performance_mode_combo.setMinimumHeight(30)
        self.performance_mode_combo.addItems([
            "自动优化",
            "GPU加速",
            "CPU多线程",
            "单线程",
            "低内存模式"
        ])
        self.performance_mode_combo.setCurrentIndex(0)
        self.performance_mode_combo.currentTextChanged.connect(self.update_performance_description)
        subtitle_layout.addWidget(self.performance_mode_combo, 4, 1)

        self.performance_desc = QLabel("自动选择最佳性能模式")
        self.performance_desc.setStyleSheet("color: #17a2b8; font-size: 12px;")
        subtitle_layout.addWidget(self.performance_desc, 4, 2)

        layout.addWidget(subtitle_group)

        # 控制按钮区域
        control_group = QGroupBox("🎮 操作控制")
        control_layout = QVBoxLayout(control_group)
        control_layout.setContentsMargins(15, 20, 15, 15)
        control_layout.setSpacing(10)

        self.start_subtitle_removal_btn = AnimatedButton("🚀 开始移除字幕")
        self.start_subtitle_removal_btn.setMinimumHeight(40)
        self.start_subtitle_removal_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #ff6b6b, stop:1 #ee5a24);
                font-size: 15px; 
                padding: 10px;
                min-height: 40px;
                border-radius: 8px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #ff5252, stop:1 #ff3838);
            }
        """)
        self.start_subtitle_removal_btn.clicked.connect(self.start_subtitle_removal)
        control_layout.addWidget(self.start_subtitle_removal_btn)

        # 停止按钮
        self.stop_subtitle_removal_btn = AnimatedButton("⏹️ 停止移除")
        self.stop_subtitle_removal_btn.setMinimumHeight(40)
        self.stop_subtitle_removal_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                font-size: 15px;
                padding: 10px;
                color: white;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c0392b, stop:1 #a93226);
            }
            QPushButton:disabled {
                background: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.stop_subtitle_removal_btn.clicked.connect(self.stop_subtitle_removal)
        self.stop_subtitle_removal_btn.setEnabled(False)
        control_layout.addWidget(self.stop_subtitle_removal_btn)

        # 进度条
        self.subtitle_removal_progress = QProgressBar()
        self.subtitle_removal_progress.setMinimumHeight(25)
        self.subtitle_removal_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                font-size: 12px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #74b9ff, stop:1 #0984e3);
                border-radius: 6px;
            }
        """)
        self.subtitle_removal_progress.setVisible(False)
        control_layout.addWidget(self.subtitle_removal_progress)

        # 状态标签
        self.subtitle_removal_status = QLabel("")
        self.subtitle_removal_status.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #2c3e50;
                font-weight: bold;
                padding: 5px;
            }
        """)
        self.subtitle_removal_status.setVisible(False)
        control_layout.addWidget(self.subtitle_removal_status)

        layout.addWidget(control_group)
        layout.addStretch()

        # 初始化字幕区域变量
        self.subtitle_region = None  # 单矩形区域 (y_start, y_end)
        self.subtitle_regions = None  # 多矩形区域列表 [(x1, y1, x2, y2), ...]

    def toggle_region_selection(self, index):
        """切换字幕区域选择模式"""
        # 如果切换到手动模式但OpenCV不可用，则切换回自动模式
        if index > 0 and not CV2_AVAILABLE:
            QMessageBox.warning(self, "⚠️ 警告", "未安装OpenCV库，无法使用手动框选功能。已切换为自动检测模式。")
            self.log_message("⚠️ 警告: 未安装OpenCV库，无法使用手动框选功能")
            self.subtitle_mode.setCurrentIndex(0)
            return

        # 更新按钮状态和文本
        self.select_region_btn.setEnabled(index > 0)  # 手动模式时启用框选按钮

        if index == 0:  # 自动检测模式
            self.subtitle_region = None
            self.subtitle_regions = None
            self.selected_region_label.setText("自动检测")
            self.selected_region_label.setStyleSheet("color: #28a745; font-weight: bold;")
            self.select_region_btn.setText("框选字幕区域")
        elif index == 1:  # 单矩形手动模式
            self.subtitle_region = None
            self.subtitle_regions = None
            self.selected_region_label.setText("未选择")
            self.selected_region_label.setStyleSheet("color: #dc3545; font-weight: bold;")
            self.select_region_btn.setText("框选单个矩形区域")
        else:  # 多矩形手动模式
            self.subtitle_region = None
            self.subtitle_regions = None
            self.selected_region_label.setText("未选择")
            self.selected_region_label.setStyleSheet("color: #dc3545; font-weight: bold;")
            self.select_region_btn.setText("框选多个矩形区域")

    def select_subtitle_region(self):
        """打开字幕区域选择器"""
        if not self.subtitle_input_path.text() or not os.path.exists(self.subtitle_input_path.text()):
            QMessageBox.warning(self, "⚠️ 警告", "请先选择输入视频文件！")
            return

        try:
            # 检查是否安装了OpenCV
            if not CV2_AVAILABLE:
                QMessageBox.critical(self, "❌ 错误", "未安装OpenCV库，无法使用框选功能。请安装opencv-python包。")
                self.log_message("❌ 错误: 未安装OpenCV库，无法使用框选功能")
                return

            mode_index = self.subtitle_mode.currentIndex()

            if mode_index == 1:  # 单矩形模式
                self.log_message("打开单矩形字幕区域选择器...")
                selector = SubtitleRegionSelector(self.subtitle_input_path.text(), self)
                selector.region_selected.connect(self.on_single_region_selected)
                selector.exec_()
            elif mode_index == 2:  # 多矩形模式
                self.log_message("打开多矩形字幕区域选择器...")
                from multi_rectangle_selector import MultiRectangleSelector
                selector = MultiRectangleSelector(self.subtitle_input_path.text(), self)
                selector.regions_selected.connect(self.on_multiple_regions_selected)
                selector.exec_()

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            QMessageBox.critical(self, "❌ 错误", f"打开选择器时发生错误: {str(e)}")
            self.log_message(f"❌ 错误: 打开选择器失败: {str(e)}")
            log_error(f"详细错误: {error_details}")

    def on_single_region_selected(self, region):
        """处理选择的单个字幕区域"""
        self.subtitle_region = region
        self.subtitle_regions = None
        y_start, y_end = region
        self.selected_region_label.setText(f"单矩形: {y_start} - {y_end} 像素")
        self.selected_region_label.setStyleSheet("color: #28a745; font-weight: bold;")
        self.log_message(f"✅ 已选择单矩形字幕区域: 从 {y_start} 到 {y_end} 像素")

    def on_multiple_regions_selected(self, regions):
        """处理选择的多个字幕区域"""
        self.subtitle_region = None
        self.subtitle_regions = regions
        region_count = len(regions)
        self.selected_region_label.setText(f"多矩形: 已选择 {region_count} 个区域")
        self.selected_region_label.setStyleSheet("color: #28a745; font-weight: bold;")

        # 记录详细信息
        region_details = []
        for i, (x1, y1, x2, y2) in enumerate(regions, 1):
            region_details.append(f"区域{i}: ({x1},{y1}) - ({x2},{y2})")

        self.log_message(f"✅ 已选择 {region_count} 个矩形字幕区域:")
        for detail in region_details:
            self.log_message(f"  {detail}")

    # 保持向后兼容性
    def on_region_selected(self, region):
        """处理选择的字幕区域（向后兼容）"""
        self.on_single_region_selected(region)

    def update_removal_method_description(self, method_text):
        """更新移除方法描述"""
        descriptions = {
            "智能填充 (推荐)": "使用AI算法智能填充字幕区域",
            "背景色填充": "分析背景色并填充字幕区域",
            "黑色填充": "使用黑色填充字幕区域",
            "模糊处理": "对字幕区域进行模糊处理"
        }

        colors = {
            "智能填充 (推荐)": "#28a745",
            "背景色填充": "#17a2b8",
            "黑色填充": "#6c757d",
            "模糊处理": "#ffc107"
        }

        desc = descriptions.get(method_text, "")
        color = colors.get(method_text, "#28a745")

        self.removal_method_desc.setText(desc)
        self.removal_method_desc.setStyleSheet(f"color: {color}; font-size: 11px;")

    def update_performance_description(self, mode_text):
        """更新性能模式描述"""
        descriptions = {
            "自动优化": "自动选择最佳性能模式",
            "GPU加速": "使用GPU加速处理（需要CUDA支持）",
            "CPU多线程": "使用多线程CPU并行处理",
            "单线程": "使用单线程处理（兼容性最好）",
            "低内存模式": "使用低内存模式处理（适合内存不足的设备）"
        }

        colors = {
            "自动优化": "#17a2b8",
            "GPU加速": "#28a745",
            "CPU多线程": "#ffc107",
            "单线程": "#6c757d",
            "低内存模式": "#ffc107"
        }

        desc = descriptions.get(mode_text, "")
        color = colors.get(mode_text, "#17a2b8")

        self.performance_desc.setText(desc)
        self.performance_desc.setStyleSheet(f"color: {color}; font-size: 11px;")

    def select_subtitle_input(self):
        """选择需要移除字幕的视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择输入视频文件", "", "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv)"
        )
        if file_path:
            self.subtitle_input_path.setText(file_path)
            # 自动设置输出路径
            dir_path = os.path.dirname(file_path)
            base_name = os.path.basename(file_path)
            name, ext = os.path.splitext(base_name)
            output_path = os.path.join(dir_path, f"{name}_无字幕{ext}")
            self.subtitle_output_path.setText(output_path)
            self.log_message(f"✅ 已选择输入视频: {os.path.basename(file_path)}")

            # 重置字幕区域选择
            if self.subtitle_mode.currentIndex() == 1:  # 手动模式
                self.subtitle_region = None
                self.selected_region_label.setText("未选择")
                self.selected_region_label.setStyleSheet("color: #dc3545; font-weight: bold;")

    def select_subtitle_output(self):
        """选择无字幕视频的保存路径"""
        # 设置默认保存路径
        default_path = ""
        if self.subtitle_input_path.text():
            input_path = self.subtitle_input_path.text()
            dir_path = os.path.dirname(input_path)
            base_name = os.path.basename(input_path)
            name, ext = os.path.splitext(base_name)
            default_path = os.path.join(dir_path, f"{name}_无字幕.mp4")

        file_path, _ = QFileDialog.getSaveFileName(
            self, "选择输出文件路径", default_path, "视频文件 (*.mp4);;所有文件 (*)"
        )
        if file_path:
            if not file_path.endswith('.mp4'):
                file_path += '.mp4'
            self.subtitle_output_path.setText(file_path)
            self.log_message(f"✅ 已设置输出路径: {os.path.basename(file_path)}")
            log_debug(f"Debug: 设置输出路径为: {file_path}")  # 调试信息

    def start_subtitle_removal(self):
        """开始移除字幕处理"""
        # 验证输入
        if not self.subtitle_input_path.text():
            QMessageBox.warning(self, "⚠️ 警告", "请选择输入视频文件！")
            return
        if not self.subtitle_output_path.text():
            QMessageBox.warning(self, "⚠️ 警告", "请设置输出文件路径！")
            return

        # 检查文件是否存在
        if not os.path.exists(self.subtitle_input_path.text()):
            QMessageBox.warning(self, "⚠️ 警告", "输入视频文件不存在！")
            return

        # 验证手动模式的区域选择
        mode_index = self.subtitle_mode.currentIndex()
        if mode_index == 1 and self.subtitle_region is None:
            QMessageBox.warning(self, "⚠️ 警告", "请先使用框选字幕区域按钮选择单矩形区域！")
            return
        elif mode_index == 2 and (self.subtitle_regions is None or len(self.subtitle_regions) == 0):
            QMessageBox.warning(self, "⚠️ 警告", "请先使用框选字幕区域按钮选择多矩形区域！")
            return

        # 更新UI状态
        self.start_subtitle_removal_btn.setEnabled(False)
        self.stop_subtitle_removal_btn.setEnabled(True)
        self.subtitle_removal_progress.setVisible(True)
        self.subtitle_removal_status.setVisible(True)
        self.subtitle_removal_progress.setValue(0)
        self.subtitle_removal_status.setText("准备开始...")

        # 获取参数
        input_path = self.subtitle_input_path.text()
        output_path = self.subtitle_output_path.text()

        # 转换移除方法
        method_map = {
            "智能填充 (推荐)": "inpaint",
            "背景色填充": "background_fill",
            "黑色填充": "black_fill",
            "模糊处理": "blur"
        }
        removal_method = method_map.get(self.removal_method_combo.currentText(), "inpaint")

        # 转换性能模式
        performance_map = {
            "自动优化": "auto",
            "GPU加速": "gpu",
            "CPU多线程": "cpu",
            "单线程": "single_thread",
            "低内存模式": "low_memory"
        }
        performance_mode = performance_map.get(self.performance_mode_combo.currentText(), "auto")

        # 根据模式设置字幕区域参数
        mode_index = self.subtitle_mode.currentIndex()
        is_manual_mode = mode_index > 0

        if mode_index == 0:  # 自动检测
            subtitle_region = None
            subtitle_regions = None
        elif mode_index == 1:  # 单矩形
            subtitle_region = self.subtitle_region
            subtitle_regions = None
        else:  # 多矩形
            subtitle_region = None
            subtitle_regions = self.subtitle_regions

        # 创建并启动处理线程
        self.subtitle_thread = SubtitleRemovalThread(
            input_path,
            output_path,
            subtitle_region,
            subtitle_regions,
            0.5,  # 默认检测敏感度
            removal_method,
            self.use_optimization.isChecked(),
            is_manual_mode,
            performance_mode
        )

        # 连接信号
        self.subtitle_thread.progress_updated.connect(self.subtitle_removal_progress.setValue)
        self.subtitle_thread.status_updated.connect(self.subtitle_removal_status.setText)
        self.subtitle_thread.finished_processing.connect(self.subtitle_removal_finished)
        self.subtitle_thread.error_occurred.connect(self.subtitle_removal_error)

        # 启动线程
        self.subtitle_thread.start()

        # 日志记录
        mode_text = "自动检测" if not is_manual_mode else "手动框选"
        method_text = self.removal_method_combo.currentText()
        self.log_message(f"🚀 开始移除字幕... (模式: {mode_text}, 方法: {method_text})")
        self.status_bar.showMessage("移除字幕中...")

    def subtitle_removal_finished(self, result):
        """字幕移除完成回调"""
        # 重置UI状态
        self.reset_subtitle_removal_ui()

        # 显示结果
        self.result_text.setText(result)
        self.log_message("🎉 字幕移除完成！")
        self.status_label.setText("🎉 字幕移除完成！")
        self.detailed_progress_label.setText("✨ 无字幕视频生成成功！")

        # 显示完成对话框
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Information)
        msg.setWindowTitle("🎉 处理完成")
        msg.setText("字幕移除完成！")
        msg.setDetailedText(result)
        msg.exec_()

        self.status_bar.showMessage("完成")

    def stop_subtitle_removal(self):
        """停止字幕移除处理"""
        if hasattr(self, 'subtitle_thread') and self.subtitle_thread and self.subtitle_thread.isRunning():
            self.subtitle_thread.terminate()
            self.subtitle_thread.wait()
            self.log_message("⏹️ 字幕移除已停止")
            self.status_bar.showMessage("已停止")

        # 重置UI状态
        self.reset_subtitle_removal_ui()

    def reset_subtitle_removal_ui(self):
        """重置字幕移除UI状态"""
        self.start_subtitle_removal_btn.setEnabled(True)
        self.stop_subtitle_removal_btn.setEnabled(False)
        self.subtitle_removal_progress.setVisible(False)
        self.subtitle_removal_status.setVisible(False)
        self.subtitle_removal_status.setText("")

    def subtitle_removal_error(self, error):
        """字幕移除错误回调"""
        # 重置UI状态
        self.reset_subtitle_removal_ui()

        # 显示错误信息
        self.result_text.setText(f"❌ 字幕移除失败: {error}")
        self.log_message(f"❌ 字幕移除错误: {error}")
        self.status_label.setText("❌ 字幕移除失败")

        # 显示错误对话框
        QMessageBox.critical(self, "❌ 错误", f"字幕移除过程中发生错误:\n{error}")
        self.status_bar.showMessage("错误")

    def processing_error(self, error):
        self.result_text.setText(f"❌ 错误: {error}")
        self.log_message(f"❌ 错误: {error}")
        self.status_label.setText("❌ 处理失败")
        self.reset_ui_state()
        QMessageBox.critical(self, "❌ 错误", f"处理过程中发生错误:\n{error}")
        self.status_bar.showMessage("错误")

    def reset_ui_state(self):
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)

    def log_message(self, message):
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)

    def open_project(self):
        # 实现项目打开功能
        QMessageBox.information(self, "信息", "项目打开功能开发中...")

    def save_project(self):
        # 实现项目保存功能
        QMessageBox.information(self, "信息", "项目保存功能开发中...")

    def show_about(self):
        QMessageBox.about(self, "关于",
                          "🎬 余下视频混剪工具 v1.0\n\n"
                          "专业级视频去重解决方案\n"
                          "基于AI技术的智能场景识别与内容替换\n\n"
                          "技术栈：Python + PyQt5 + OpenCV + MoviePy\n"
                          "开发者：PG-Code\n"
                          "版本：1.0.0")

    def setup_help_tab(self, tab):
        """设置帮助标签页"""
        layout = QVBoxLayout(tab)

        help_text = QTextEdit()
        help_text.setReadOnly(True)
        help_text.setHtml("""
        <h2>🎬 余下视频混剪工具 v1.0 使用指南</h2>

        <h3>📖 软件介绍</h3>
        <p>本工具是一款专业的视频处理解决方案，集成了视频去重、字幕处理、音视频分离、智能混剪等多种功能，
        通过AI技术和智能算法，帮助您高效处理视频内容。</p>

        <h3>🎯 主要功能</h3>
        <ul>
            <li><b>🎬 智能去重</b>：基于场景识别的视频去重处理</li>
            <li><b>📝 字幕移除</b>：智能识别并移除视频中的字幕</li>
            <li><b>🎤 字幕提取</b>：从视频中提取字幕文本</li>
            <li><b>🔊 音视频分离</b>：分离视频中的音频和视频轨道</li>
            <li><b>✂️ 视频分割</b>：基于场景变化智能分割视频</li>
            <li><b>🎬 智能混剪</b>：多视频智能混合剪辑</li>
            <li><b>🎭 视频融合</b>：多视频融合生成不同组合视频</li>
        </ul>

        <h3>🚀 快速开始</h3>
        <h4>1. 智能去重功能（新版本）</h4>
        <ol>
            <li><b>选择主要视频（有字幕/水印）</b>：选择已剪辑并包含字幕或水印的视频文件</li>
            <li><b>选择原视频（干净）</b>：选择未剪辑的干净原始视频文件，用于替换相似场景</li>
            <li><b>设置输出路径</b>：指定处理后的去除字幕/水印剪辑视频保存位置</li>
            <li><b>选择替换策略</b>：
                <ul>
                    <li><b>相似度匹配</b>：在原视频中寻找相似画面进行智能替换</li>
                    <li><b>均匀替换</b>：在整个视频中均匀分布替换内容</li>
                    <li><b>随机替换</b>：随机选择替换位置</li>
                </ul>
            </li>
            <li><b>调整参数</b>：设置相似度阈值、替换比例等参数</li>
            <li><b>开始处理</b>：点击"开始智能处理"按钮，系统将自动：
                <ul>
                    <li>🎬 分析主要视频场景</li>
                    <li>🔍 分析原视频场景</li>
                    <li>🔄 寻找相似场景并用干净片段替换</li>
                    <li>✨ 生成去除字幕/水印的剪辑视频</li>
                </ul>
            </li>
        </ol>

        <h4>2. 字幕移除功能</h4>
        <ol>
            <li><b>选择视频文件</b>：选择包含字幕的视频文件</li>
            <li><b>选择字幕区域模式</b>：
                <ul>
                    <li>自动检测：程序自动识别字幕位置</li>
                    <li>单矩形手动选择：手动框选一个字幕区域</li>
                    <li>多矩形手动选择：手动框选多个字幕区域</li>
                </ul>
            </li>
            <li><b>选择移除方法</b>：智能填充（推荐）、背景填充、模糊处理、黑色填充</li>
            <li><b>选择性能模式</b>：根据设备性能选择合适的处理模式</li>
            <li><b>开始处理</b>：点击"开始移除字幕"按钮</li>
        </ol>

        <h4>3. 视频融合功能</h4>
        <ol>
            <li><b>添加视频文件</b>：选择需要融合的多个视频文件（至少2个）</li>
            <li><b>添加贴纸</b>（可选）：选择要添加到融合视频中的贴纸图片</li>
            <li><b>设置融合参数</b>：
                <ul>
                    <li><b>转场方式</b>：淡入淡出、滑动、缩放、无转场</li>
                    <li><b>生成个数</b>：指定要生成的融合视频数量（1-20个）</li>
                    <li><b>拼接模式</b>：完整视频不同顺序拼接（不裁剪）</li>
                </ul>
            </li>
            <li><b>贴纸设置</b>（可选）：
                <ul>
                    <li><b>位置</b>：随机、左上角、右上角、左下角、右下角、中心</li>
                    <li><b>大小</b>：调整贴纸相对于视频的大小比例（5%-30%）</li>
                    <li><b>透明度</b>：调整贴纸的透明度（10%-100%）</li>
                </ul>
            </li>
            <li><b>输出设置</b>：
                <ul>
                    <li><b>输出文件夹</b>：选择保存融合视频的文件夹</li>
                    <li><b>文件名前缀</b>：设置生成文件的名称前缀</li>
                </ul>
            </li>
            <li><b>开始融合</b>：点击"开始视频融合"按钮开始处理</li>
        </ol>

        <h3>⚙️ 参数说明</h3>
        <ul>
            <li><b>场景检测敏感度</b>：控制视频场景切割的精细程度，值越小检测越敏感</li>
            <li><b>相似度匹配精度</b>：控制场景相似度判断的严格程度，值越高要求越严格</li>
            <li><b>替换策略</b>：
                <ul>
                    <li><b>相似度匹配</b>：基于视觉内容相似度进行智能匹配替换</li>
                    <li><b>均匀替换</b>：在主视频中均匀选择场景进行替换</li>
                    <li><b>随机替换</b>：在主视频中随机选择场景进行替换</li>
                </ul>
            </li>
            <li><b>替换率</b>：控制均匀替换和随机替换时替换场景的比例</li>
            <li><b>性能优化</b>：启用多线程并行处理，显著提升处理速度</li>
        </ul>

        <h3>📝 字幕移除功能</h3>
        <p>本工具提供了智能字幕移除功能，可以检测并去除视频中的字幕区域。</p>

        <p><b>使用方法：</b></p>
        <ol>
            <li><b>选择输入视频</b>：选择需要移除字幕的视频文件</li>
            <li><b>设置输出路径</b>：指定处理后的无字幕视频保存位置</li>
            <li><b>选择字幕处理模式</b>：
                <ul>
                    <li><b>自动检测字幕区域</b>：系统会自动分析并检测视频中的字幕区域</li>
                    <li><b>手动框选字幕区域</b>：您可以精确地框选字幕所在区域，特别适合自动检测失败的情况</li>
                </ul>
            </li>
            <li><b>手动框选操作</b>（如果选择手动模式）：
                <ul>
                    <li>点击"框选字幕区域"按钮</li>
                    <li>在打开的视频预览窗口中，拖动鼠标选择字幕区域</li>
                    <li>可以使用底部滑块调整视频位置，找到清晰的字幕帧</li>
                    <li>选择完成后点击"确认选择"按钮</li>
                </ul>
            </li>
            <li><b>调整模糊强度</b>：根据需要调整字幕区域的模糊程度</li>
            <li><b>开始处理</b>：点击"开始移除字幕"按钮进行处理</li>
        </ol>

        <p>字幕移除功能通过模糊处理使字幕不可见，同时保持视频其他部分不受影响。手动框选功能能够更精确地处理字幕区域，解决自动检测不够理想的问题。</p>

        <h3>⚙️ 高级功能</h3>
        <ul>
            <li><b>性能优化</b>：支持GPU加速、多线程处理和低内存模式</li>
            <li><b>智能算法</b>：自适应场景识别和内容感知处理</li>
            <li><b>多格式支持</b>：支持MP4、AVI、MOV、MKV等主流格式</li>
            <li><b>批量处理</b>：可同时处理多个视频文件</li>
            <li><b>自定义参数</b>：可调整相似度阈值、替换比例等参数</li>
            <li><b>实时预览</b>：处理过程中可实时查看进度和效果</li>
        </ul>

        <h3>💡 使用技巧</h3>
        <ul>
            <li><b>视频质量</b>：选择高质量的辅助视频可以获得更好的替换效果</li>
            <li><b>参数调整</b>：适当调整相似度阈值可以控制替换的精确度</li>
            <li><b>性能优化</b>：使用GPU加速可以显著提升处理速度</li>
            <li><b>内存管理</b>：处理大文件时建议使用低内存模式</li>
            <li><b>字幕处理</b>：智能填充效果最佳，多矩形模式适合复杂字幕</li>
            <li><b>测试优先</b>：建议在处理大文件前先用小文件测试参数</li>
            <li><b>替换策略选择</b>：
                <ul>
                    <li>相似度匹配：适合需要保持视频内容连贯性的场景</li>
                    <li>均匀替换：适合需要在整个视频中均衡分布替换内容的场景</li>
                    <li>随机替换：适合需要增加原创性但对内容连贯性要求不高的场景</li>
                </ul>
            </li>
            <li><b>视频融合技巧</b>：
                <ul>
                    <li>选择内容相关的视频进行融合效果更佳</li>
                    <li>使用PNG格式的透明背景图片作为贴纸效果最佳</li>
                    <li>每个融合视频都是完整视频的不同顺序组合</li>
                    <li>生成多个融合视频可以获得完全不同的播放顺序</li>
                    <li>贴纸透明度设置在60%-90%之间效果较好</li>
                    <li>转场效果会影响视频的观感，根据内容选择合适的转场方式</li>
                    <li>文件名前缀可以帮助区分不同批次的融合视频</li>
                    <li>视频总时长等于所有输入视频时长的总和</li>
                </ul>
            </li>
        </ul>

        <h3>🔧 故障排除</h3>
        <p><b>常见问题解决方案：</b></p>
        <ul>
            <li><b>视频格式问题</b>：确保使用支持的格式（推荐MP4、AVI、MOV）</li>
            <li><b>路径问题</b>：避免文件路径包含特殊字符或中文</li>
            <li><b>内存不足</b>：使用低内存模式或减少同时处理的文件数量</li>
            <li><b>GPU问题</b>：如果GPU加速失败，切换到CPU模式</li>
            <li><b>字幕识别失败</b>：尝试手动选择字幕区域</li>
            <li><b>处理速度慢</b>：检查系统资源使用情况，关闭不必要的程序</li>
        </ul>

        <h3>📋 系统要求</h3>
        <ul>
            <li><b>操作系统</b>：Windows 10/11 (64位)</li>
            <li><b>内存</b>：建议8GB以上，处理4K视频需要16GB以上</li>
            <li><b>处理器</b>：Intel i5或AMD Ryzen 5以上</li>
            <li><b>显卡</b>：支持CUDA的NVIDIA显卡（可选，用于GPU加速）</li>
            <li><b>存储空间</b>：至少10GB可用空间</li>
        </ul>

        <h3>⚠️ 注意事项</h3>
        <ul>
            <li>请确保有足够的磁盘空间存储输出文件</li>
            <li>处理时间取决于视频长度和复杂度</li>
            <li>建议在处理前备份原始视频文件</li>
            <li>本工具仅供学习和研究使用</li>
            <li>遵守相关法律法规，尊重版权</li>
        </ul>

        <h3>📞 技术支持</h3>
        <p>如果您在使用过程中遇到问题，请：</p>
        <ul>
            <li>查看软件日志获取详细错误信息</li>
            <li>确保系统满足最低配置要求</li>
            <li>尝试重启软件或系统</li>
            <li>联系技术支持获取帮助</li>
        </ul>
        """)

        layout.addWidget(help_text)

    def setup_subtitle_extraction_tab(self, tab):
        """设置字幕提取标签页"""
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("🎤 视频字幕提取工具")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 8px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #74b9ff, stop:1 #0984e3);
                color: white;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # 输入文件选择
        input_group = QGroupBox("📁 输入视频文件")
        input_layout = QHBoxLayout(input_group)
        input_layout.setContentsMargins(15, 20, 15, 15)
        input_layout.setSpacing(10)

        self.subtitle_extract_input_path = QLineEdit()
        self.subtitle_extract_input_path.setPlaceholderText("请选择要提取字幕的视频文件...")
        self.subtitle_extract_input_path.setMinimumHeight(30)
        input_layout.addWidget(self.subtitle_extract_input_path)

        browse_input_btn = QPushButton("📂 浏览")
        browse_input_btn.setFixedWidth(80)
        browse_input_btn.setFixedHeight(30)
        browse_input_btn.clicked.connect(self.browse_subtitle_extract_input)
        input_layout.addWidget(browse_input_btn)

        layout.addWidget(input_group)

        # 提取设置
        settings_group = QGroupBox("⚙️ 提取设置")
        settings_layout = QGridLayout(settings_group)
        settings_layout.setContentsMargins(15, 20, 15, 15)
        settings_layout.setSpacing(12)
        settings_layout.setColumnStretch(1, 1)  # 让右侧列可以拉伸

        # 提取方法
        method_label = QLabel("提取方法:")
        method_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        settings_layout.addWidget(method_label, 0, 0)

        self.subtitle_extract_method = QComboBox()
        self.subtitle_extract_method.addItems(["自动检测", "内嵌字幕", "语音识别"])
        self.subtitle_extract_method.setCurrentIndex(0)
        self.subtitle_extract_method.setMinimumHeight(30)
        settings_layout.addWidget(self.subtitle_extract_method, 0, 1)

        # 语言设置
        lang_label = QLabel("语言:")
        lang_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        settings_layout.addWidget(lang_label, 1, 0)

        self.subtitle_language = QComboBox()
        self.subtitle_language.addItems(["中文", "英文", "自动检测"])
        self.subtitle_language.setCurrentIndex(0)
        self.subtitle_language.setMinimumHeight(30)
        settings_layout.addWidget(self.subtitle_language, 1, 1)

        # 繁体转简体选项
        self.convert_to_simplified = QCheckBox("繁体转简体")
        self.convert_to_simplified.setChecked(True)
        self.convert_to_simplified.setToolTip("将提取的繁体中文字幕转换为简体中文")
        self.convert_to_simplified.setStyleSheet("font-size: 14px;")
        settings_layout.addWidget(self.convert_to_simplified, 2, 0, 1, 2)

        layout.addWidget(settings_group)

        # 输出设置
        output_group = QGroupBox("💾 输出设置")
        output_layout = QVBoxLayout(output_group)
        output_layout.setContentsMargins(15, 20, 15, 15)
        output_layout.setSpacing(12)

        # 输出格式选择
        format_layout = QHBoxLayout()
        format_layout.setSpacing(10)

        format_label = QLabel("输出格式:")
        format_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        format_layout.addWidget(format_label)

        self.subtitle_output_format = QComboBox()
        self.subtitle_output_format.addItems(["文本文件", "显示在界面"])
        self.subtitle_output_format.setCurrentIndex(2)
        self.subtitle_output_format.setMinimumHeight(30)
        format_layout.addWidget(self.subtitle_output_format)
        format_layout.addStretch()

        output_layout.addLayout(format_layout)

        # 输出文件路径（当选择文件输出时）
        file_output_layout = QHBoxLayout()
        file_output_layout.setSpacing(10)

        self.subtitle_extract_output_path = QLineEdit()
        self.subtitle_extract_output_path.setPlaceholderText("选择输出文件路径（可选）...")
        self.subtitle_extract_output_path.setMinimumHeight(30)
        file_output_layout.addWidget(self.subtitle_extract_output_path)

        browse_output_btn = QPushButton("📂 浏览")
        browse_output_btn.setFixedWidth(80)
        browse_output_btn.setFixedHeight(30)
        browse_output_btn.clicked.connect(self.browse_subtitle_extract_output)
        file_output_layout.addWidget(browse_output_btn)

        output_layout.addLayout(file_output_layout)
        layout.addWidget(output_group)

        # 控制按钮
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        self.start_subtitle_extract_btn = QPushButton("🚀 开始提取字幕")
        self.start_subtitle_extract_btn.setMinimumHeight(40)
        self.start_subtitle_extract_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #00b894, stop:1 #00a085);
                color: white;
                border: none;
                padding: 10px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 15px;
                min-height: 40px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #00a085, stop:1 #008f75);
            }
            QPushButton:disabled {
                background: #bdc3c7;
            }
        """)
        self.start_subtitle_extract_btn.clicked.connect(self.start_subtitle_extraction)
        button_layout.addWidget(self.start_subtitle_extract_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        # 结果显示
        result_group = QGroupBox("📄 提取结果")
        result_layout = QVBoxLayout(result_group)
        result_layout.setContentsMargins(15, 20, 15, 15)

        self.subtitle_result_text = QTextEdit()
        self.subtitle_result_text.setPlaceholderText("提取的字幕内容将显示在这里...")
        self.subtitle_result_text.setMinimumHeight(180)
        result_layout.addWidget(self.subtitle_result_text)

        layout.addWidget(result_group)

        # 进度条
        self.subtitle_extract_progress = QProgressBar()
        self.subtitle_extract_progress.setMinimumHeight(25)
        self.subtitle_extract_progress.setVisible(False)
        layout.addWidget(self.subtitle_extract_progress)

    def setup_video_audio_separation_tab(self, tab):
        """设置视频音频分离标签页"""
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("🎵 视频音频分离工具")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 8px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #fd79a8, stop:1 #e84393);
                color: white;
                border-radius: 8px;
                margin-bottom: 8px;
            }
        """)
        layout.addWidget(title_label)

        # 输入文件选择
        input_group = QGroupBox("📁 输入视频文件")
        input_layout = QHBoxLayout(input_group)
        input_layout.setContentsMargins(15, 20, 15, 15)
        input_layout.setSpacing(10)

        self.separation_input_path = QLineEdit()
        self.separation_input_path.setPlaceholderText("请选择要分离的视频文件...")
        self.separation_input_path.setMinimumHeight(30)
        input_layout.addWidget(self.separation_input_path)

        browse_input_btn = QPushButton("📂 浏览")
        browse_input_btn.setFixedWidth(80)
        browse_input_btn.setFixedHeight(30)
        browse_input_btn.clicked.connect(self.browse_separation_input)
        input_layout.addWidget(browse_input_btn)

        layout.addWidget(input_group)

        # 输出设置
        output_group = QGroupBox("💾 输出设置")
        output_layout = QGridLayout(output_group)
        output_layout.setContentsMargins(15, 20, 15, 15)
        output_layout.setSpacing(12)
        output_layout.setColumnStretch(1, 1)  # 让输入框列可以拉伸

        # 视频输出路径
        video_label = QLabel("🎬 视频输出:")
        video_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        output_layout.addWidget(video_label, 0, 0)

        self.video_output_path = QLineEdit()
        self.video_output_path.setPlaceholderText("视频文件输出路径（无音频）...")
        self.video_output_path.setMinimumHeight(30)
        output_layout.addWidget(self.video_output_path, 0, 1)

        browse_video_btn = QPushButton("📂 浏览")
        browse_video_btn.setFixedWidth(80)
        browse_video_btn.setFixedHeight(30)
        browse_video_btn.clicked.connect(self.browse_video_output)
        output_layout.addWidget(browse_video_btn, 0, 2)

        # 音频输出路径
        audio_label = QLabel("🎵 音频输出:")
        audio_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        output_layout.addWidget(audio_label, 1, 0)

        self.audio_output_path = QLineEdit()
        self.audio_output_path.setPlaceholderText("音频文件输出路径...")
        self.audio_output_path.setMinimumHeight(30)
        output_layout.addWidget(self.audio_output_path, 1, 1)

        browse_audio_btn = QPushButton("📂 浏览")
        browse_audio_btn.setFixedWidth(80)
        browse_audio_btn.setFixedHeight(30)
        browse_audio_btn.clicked.connect(self.browse_audio_output)
        output_layout.addWidget(browse_audio_btn, 1, 2)

        layout.addWidget(output_group)

        # 控制按钮
        control_group = QGroupBox("🎮 操作控制")
        control_layout = QVBoxLayout(control_group)
        control_layout.setContentsMargins(15, 20, 15, 15)
        control_layout.setSpacing(10)

        self.start_separation_btn = QPushButton("🚀 开始分离")
        self.start_separation_btn.setMinimumHeight(40)
        self.start_separation_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fd79a8, stop:1 #e84393);
                color: white;
                border: none;
                padding: 10px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 15px;
                min-height: 40px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e84393, stop:1 #d63384);
            }
            QPushButton:disabled {
                background: #bdc3c7;
            }
        """)
        self.start_separation_btn.clicked.connect(self.start_video_audio_separation)
        control_layout.addWidget(self.start_separation_btn)

        layout.addWidget(control_group)

        # 结果显示
        result_group = QGroupBox("📊 分离结果")
        result_layout = QVBoxLayout(result_group)
        result_layout.setContentsMargins(15, 20, 15, 15)

        self.separation_result_text = QTextEdit()
        self.separation_result_text.setPlaceholderText("分离结果信息将显示在这里...")
        self.separation_result_text.setMaximumHeight(150)
        self.separation_result_text.setStyleSheet("""
            font-size: 14px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            padding: 8px;
        """)
        result_layout.addWidget(self.separation_result_text)

        layout.addWidget(result_group)

        # 进度条
        self.separation_progress = QProgressBar()
        self.separation_progress.setMinimumHeight(25)
        self.separation_progress.setVisible(False)
        layout.addWidget(self.separation_progress)

        layout.addStretch()

    def setup_menu_bar(self):
        """设置菜单栏 - 已移除菜单选项"""
        # 隐藏菜单栏
        self.menuBar().setVisible(False)

    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")

    def setup_system_tray(self):
        """设置系统托盘"""
        if QSystemTrayIcon.isSystemTrayAvailable():
            self.tray_icon = QSystemTrayIcon(self)

            # 设置托盘图标
            try:
                logo_path = os.path.join(os.path.dirname(__file__), 'img', 'logo.ico')
                if os.path.exists(logo_path):
                    self.tray_icon.setIcon(QIcon(logo_path))
                else:
                    # 使用窗口图标作为托盘图标
                    self.tray_icon.setIcon(self.windowIcon())
            except Exception as e:
                log_info(f"设置托盘图标失败: {e}")
                # 使用窗口图标作为托盘图标
                self.tray_icon.setIcon(self.windowIcon())

            # 设置托盘提示文本
            self.tray_icon.setToolTip("余下视频混剪工具 v1.0")

            # 连接双击事件
            self.tray_icon.activated.connect(self.tray_icon_activated)

            # 创建托盘菜单
            tray_menu = QMenu()
            show_action = tray_menu.addAction("🔍 显示主窗口")
            show_action.triggered.connect(self.show_and_activate_window)

            tray_menu.addSeparator()

            minimize_taskbar_action = tray_menu.addAction("📋 最小化到任务栏")
            minimize_taskbar_action.triggered.connect(self.minimize_to_taskbar)

            minimize_tray_action = tray_menu.addAction("📦 最小化到托盘")
            minimize_tray_action.triggered.connect(self.hide)

            tray_menu.addSeparator()

            quit_action = tray_menu.addAction("❌ 退出程序")
            quit_action.triggered.connect(self.quit_application_completely)

            self.tray_icon.setContextMenu(tray_menu)
            self.tray_icon.show()
        else:
            log_warning(f"⚠️ 系统不支持系统托盘功能")

    def tray_icon_activated(self, reason):
        """处理托盘图标激活事件"""
        if reason == QSystemTrayIcon.DoubleClick:
            # 双击托盘图标时显示并激活窗口
            self.show_and_activate_window()
        elif reason == QSystemTrayIcon.Trigger:
            # 单击托盘图标时也显示窗口（某些系统上的行为）
            self.show_and_activate_window()

    def show_and_activate_window(self):
        """显示并激活窗口"""
        # 如果窗口被最小化，先恢复
        if self.isMinimized():
            self.showNormal()

        # 显示窗口
        self.show()

        # 激活窗口并置于前台
        self.activateWindow()
        self.raise_()

        # 确保窗口获得焦点
        self.setWindowState(self.windowState() & ~Qt.WindowMinimized | Qt.WindowActive)

    def minimize_to_taskbar(self):
        """最小化到任务栏"""
        self.showMinimized()
        log_info(f"📋 程序已最小化到任务栏")

    def quit_application(self):
        """退出应用程序"""
        # 隐藏托盘图标
        if hasattr(self, 'tray_icon'):
            self.tray_icon.hide()

        # 退出应用程序
        QApplication.quit()

    def quit_application_completely(self):
        """完全退出应用程序，确保所有资源被释放"""
        try:
            # 停止所有正在运行的线程
            if hasattr(self, 'processing_thread') and self.processing_thread and self.processing_thread.isRunning():
                self.processing_thread.stop()
                self.processing_thread.wait(3000)  # 等待最多3秒

            if hasattr(self, 'merge_thread') and self.merge_thread and self.merge_thread.isRunning():
                self.merge_thread.stop()
                self.merge_thread.wait(3000)  # 等待最多3秒

            if hasattr(self, 'split_thread') and self.split_thread and self.split_thread.isRunning():
                self.split_thread.stop()
                self.split_thread.wait(3000)  # 等待最多3秒

            # 隐藏托盘图标
            if hasattr(self, 'tray_icon') and self.tray_icon:
                self.tray_icon.hide()
                self.tray_icon.deleteLater()

            # 强制退出应用程序
            QApplication.instance().quit()

            # 如果QApplication.quit()没有生效，使用更强制的方法
            import sys
            sys.exit(0)

        except Exception as e:
            log_error(f"❌ 退出程序时发生错误: {e}")
            # 最后的备选方案
            import sys
            sys.exit(1)

    def update_threshold_label(self, value):
        if value <= 20:
            level = "高敏感"
            color = "#dc3545"
        elif value <= 40:
            level = "推荐"
            color = "#28a745"
        else:
            level = "低敏感"
            color = "#ffc107"

        self.threshold_label.setText(f"{value}.0 ({level})")
        self.threshold_label.setStyleSheet(f"""
            color: {color};
            font-weight: bold;
            font-size: 16px;
            padding: 5px;
            font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
        """)

    def update_similarity_label(self, value):
        similarity = value / 100.0
        if value >= 85:
            level = "严格"
            color = "#dc3545"
        elif value >= 70:
            level = "推荐"
            color = "#28a745"
        else:
            level = "宽松"
            color = "#ffc107"

        self.similarity_label.setText(f"{similarity:.2f} ({level})")
        self.similarity_label.setStyleSheet(f"""
            color: {color};
            font-weight: bold;
            font-size: 16px;
            padding: 5px;
            font-family: 'Microsoft YaHei UI', 'Segoe UI', 'Arial', sans-serif;
        """)

    def select_main_video(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择主要视频文件（有字幕/水印）", "", "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv)"
        )
        if file_path:
            self.main_video_path.setText(file_path)
            self.log_message(f"✅ 已选择主要视频（有字幕/水印）: {os.path.basename(file_path)}")
            self.status_bar.showMessage(f"主要视频: {os.path.basename(file_path)}")

    def select_aux_video(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择原视频文件（未剪辑）", "", "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv)"
        )
        if file_path:
            self.aux_video_path.setText(file_path)
            self.log_message(f"✅ 已选择原视频（未剪辑）: {os.path.basename(file_path)}")
            self.status_bar.showMessage(f"原视频: {os.path.basename(file_path)}")

    def select_output_path(self):
        file_path, _ = QFileDialog.getSaveFileName(
            self, "选择输出文件路径", "", "视频文件 (*.mp4)"
        )
        if file_path:
            if not file_path.endswith('.mp4'):
                file_path += '.mp4'
            self.output_path.setText(file_path)
            self.log_message(f"✅ 已设置输出路径: {os.path.basename(file_path)}")
            self.status_bar.showMessage(f"输出: {os.path.basename(file_path)}")

    def update_replacement_options(self, index):
        """根据选择的替换策略更新相关选项的可用性"""
        # 相似度匹配策略
        if index == 0:
            self.similarity_slider.setEnabled(True)
            self.similarity_label.setStyleSheet("color: #28a745; font-weight: bold; font-size: 14px; padding: 3px;")
            self.replacement_rate_slider.setEnabled(False)
            self.replacement_rate_label.setStyleSheet("color: #6c757d; font-size: 14px; font-weight: bold; padding: 3px;")
            self.log_message("ℹ️ 已选择相似度匹配策略")
        # 均匀替换或随机替换策略
        else:
            self.similarity_slider.setEnabled(False)
            self.similarity_label.setStyleSheet("color: #6c757d; font-size: 14px; font-weight: bold; padding: 3px;")
            self.replacement_rate_slider.setEnabled(True)
            self.replacement_rate_label.setStyleSheet("color: #28a745; font-weight: bold; font-size: 14px; padding: 3px;")

            if index == 1:
                self.log_message("ℹ️ 已选择均匀替换策略")
            else:
                self.log_message("ℹ️ 已选择随机替换策略")

    def update_replacement_rate_label(self, value):
        """更新替换率标签"""
        self.replacement_rate_label.setText(f"{value}%")

    def start_processing(self):
        # 验证输入
        if not self.main_video_path.text():
            QMessageBox.warning(self, "⚠️ 警告", "请选择主要视频文件（有字幕/水印）！")
            return
        if not self.aux_video_path.text():
            QMessageBox.warning(self, "⚠️ 警告", "请选择原视频文件（干净）！")
            return
        if not self.output_path.text():
            QMessageBox.warning(self, "⚠️ 警告", "请设置输出文件路径！")
            return

        # 检查文件是否存在
        if not os.path.exists(self.main_video_path.text()):
            QMessageBox.warning(self, "⚠️ 警告", "主要视频文件不存在！")
            return
        if not os.path.exists(self.aux_video_path.text()):
            QMessageBox.warning(self, "⚠️ 警告", "原视频文件不存在！")
            return

        # 禁用开始按钮，启用停止按钮
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)

        # 清空结果显示
        self.result_text.clear()
        self.progress_bar.setValue(0)
        self.detailed_progress_label.setText("")

        # 获取基本参数
        threshold = float(self.threshold_slider.value())
        similarity_threshold = self.similarity_slider.value() / 100.0
        use_optimization = self.use_optimization.isChecked()
        replacement_strategy = self.replacement_strategy.currentIndex()
        replacement_rate = self.replacement_rate_slider.value() / 100.0

        # 获取高级设置参数
        feature_frames = self.feature_frames_spin.value()
        thread_count = self.thread_count_spin.value()

        self.log_message(f"🔧 高级参数: 特征帧数={feature_frames}, 线程数={thread_count}")

        # 创建并启动处理线程（纯视频去重流程）
        self.processing_thread = VideoProcessingThread(
            self.main_video_path.text(),
            self.aux_video_path.text(),
            self.output_path.text(),
            threshold,
            similarity_threshold,
            use_optimization,
            replacement_strategy,
            replacement_rate,
            feature_frames=feature_frames,  # 传递特征帧数
            thread_count=thread_count       # 传递线程数
        )

        # 连接信号
        self.processing_thread.progress_updated.connect(self.update_progress)
        self.processing_thread.status_updated.connect(self.update_status)
        self.processing_thread.detailed_progress.connect(self.update_detailed_progress)
        self.processing_thread.finished_processing.connect(self.processing_finished)
        self.processing_thread.error_occurred.connect(self.processing_error)

        # 启动线程
        self.processing_thread.start()
        self.log_message("🚀 开始智能视频处理...")
        self.status_bar.showMessage("处理中...")

    def stop_processing(self):
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.terminate()
            self.processing_thread.wait()
            self.log_message("⏹️ 处理已停止")
            self.reset_ui_state()

    def update_progress(self, value):
        self.progress_bar.setValue(value)

    def update_status(self, status):
        self.status_label.setText(f"🔄 {status}")
        self.log_message(status)
        self.status_bar.showMessage(status)

    def update_detailed_progress(self, detail, progress):
        self.detailed_progress_label.setText(f"📋 {detail}")
        self.progress_bar.setValue(progress)

    def processing_finished(self, result):
        self.result_text.setText(result)
        self.log_message("🎉 处理完成！")
        self.status_label.setText("🎉 处理完成！")
        self.detailed_progress_label.setText("✨ 原创视频生成成功！")
        self.reset_ui_state()

        # 显示完成对话框
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Information)
        msg.setWindowTitle("🎉 处理完成")
        msg.setText("视频处理完成！")
        msg.setDetailedText(result)
        msg.exec_()

        self.status_bar.showMessage("完成")

    # 字幕提取功能的事件处理方法
    def browse_subtitle_extract_input(self):
        """浏览字幕提取输入文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择视频文件", "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv);;所有文件 (*)"
        )
        if file_path:
            self.subtitle_extract_input_path.setText(file_path)

    def browse_subtitle_extract_output(self):
        """浏览字幕输出文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存字幕文件", "",
            "文本文件 (*.txt);;SRT字幕文件 (*.srt);;所有文件 (*)"
        )
        if file_path:
            self.subtitle_extract_output_path.setText(file_path)

    def start_subtitle_extraction(self):
        """开始字幕提取"""
        input_path = self.subtitle_extract_input_path.text()
        if not input_path:
            QMessageBox.warning(self, "警告", "请先选择输入视频文件！")
            return

        if not os.path.exists(input_path):
            QMessageBox.warning(self, "警告", "输入文件不存在！")
            return

        # 显示进度条
        self.subtitle_extract_progress.setVisible(True)
        self.subtitle_extract_progress.setValue(0)
        self.start_subtitle_extract_btn.setEnabled(False)

        # 创建并启动字幕提取线程
        self.subtitle_extract_thread = SubtitleExtractionThread(
            input_path,
            self.subtitle_extract_method.currentText(),
            self.subtitle_language.currentText(),
            self.subtitle_output_format.currentText(),
            self.subtitle_extract_output_path.text()
        )

        self.subtitle_extract_thread.progress_updated.connect(self.subtitle_extract_progress.setValue)
        self.subtitle_extract_thread.result_ready.connect(self.subtitle_extraction_finished)
        self.subtitle_extract_thread.error_occurred.connect(self.subtitle_extraction_error)
        self.subtitle_extract_thread.start()

    def subtitle_extraction_finished(self, result):
        """字幕提取完成"""
        self.subtitle_result_text.setText(result)
        self.subtitle_extract_progress.setVisible(False)
        self.start_subtitle_extract_btn.setEnabled(True)

        QMessageBox.information(self, "完成", "字幕提取完成！")

    def subtitle_extraction_error(self, error_msg):
        """字幕提取错误"""
        self.subtitle_result_text.setText(f"提取失败: {error_msg}")
        self.subtitle_extract_progress.setVisible(False)
        self.start_subtitle_extract_btn.setEnabled(True)

        QMessageBox.critical(self, "错误", f"字幕提取失败：{error_msg}")

    # 视频音频分离功能的事件处理方法
    def browse_separation_input(self):
        """浏览分离输入文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择视频文件", "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv);;所有文件 (*)"
        )
        if file_path:
            self.separation_input_path.setText(file_path)
            # 自动生成输出路径
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            base_dir = os.path.dirname(file_path)
            self.video_output_path.setText(os.path.join(base_dir, f"{base_name}_video_only.mp4"))
            self.audio_output_path.setText(os.path.join(base_dir, f"{base_name}_audio_only.wav"))

    def browse_video_output(self):
        """浏览视频输出文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存视频文件", "",
            "视频文件 (*.mp4);;所有文件 (*)"
        )
        if file_path:
            self.video_output_path.setText(file_path)

    def browse_audio_output(self):
        """浏览音频输出文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存音频文件", "",
            "音频文件 (*.wav *.mp3);;所有文件 (*)"
        )
        if file_path:
            self.audio_output_path.setText(file_path)

    def start_video_audio_separation(self):
        """开始视频音频分离"""
        input_path = self.separation_input_path.text()
        video_output = self.video_output_path.text()
        audio_output = self.audio_output_path.text()

        if not input_path:
            QMessageBox.warning(self, "警告", "请先选择输入视频文件！")
            return

        if not os.path.exists(input_path):
            QMessageBox.warning(self, "警告", "输入文件不存在！")
            return

        if not video_output and not audio_output:
            QMessageBox.warning(self, "警告", "请至少指定一个输出文件路径！")
            return

        # 显示进度条
        self.separation_progress.setVisible(True)
        self.separation_progress.setValue(0)
        self.start_separation_btn.setEnabled(False)

        # 创建并启动分离线程
        self.separation_thread = VideoAudioSeparationThread(
            input_path,
            video_output if video_output else None,
            audio_output if audio_output else None
        )

        self.separation_thread.progress_updated.connect(self.separation_progress.setValue)
        self.separation_thread.result_ready.connect(self.separation_finished)
        self.separation_thread.error_occurred.connect(self.separation_error)
        self.separation_thread.start()

    def separation_finished(self, result):
        """视频音频分离完成"""
        self.separation_result_text.setText(result)
        self.separation_progress.setVisible(False)
        self.start_separation_btn.setEnabled(True)

        QMessageBox.information(self, "完成", "视频音频分离完成！")

    def separation_error(self, error_msg):
        """视频音频分离错误"""
        self.separation_result_text.setText(f"分离失败: {error_msg}")
        self.separation_progress.setVisible(False)
        self.start_separation_btn.setEnabled(True)

        QMessageBox.critical(self, "错误", f"视频音频分离失败：{error_msg}")

    # 视频合并功能的事件处理方法
    def add_video_files(self):
        """添加视频文件"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "选择视频文件", "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv);;所有文件 (*)"
        )
        if file_paths:
            self.video_files.extend(file_paths)
            self.update_video_files_display()

    def browse_background_music(self):
        """浏览选择背景音乐文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择背景音乐文件", "",
            "音频文件 (*.mp3 *.wav *.aac *.m4a *.ogg *.flac);;所有文件 (*)"
        )
        if file_path:
            self.background_music_path.setText(file_path)

    def clear_background_music(self):
        """清除背景音乐"""
        self.background_music_path.clear()

    def clear_video_files(self):
        """清空视频文件列表"""
        self.video_files.clear()
        self.update_video_files_display()



    def update_video_files_display(self):
        """更新视频文件显示"""
        if self.video_files:
            display_text = "\n".join([f"{i + 1}. {os.path.basename(f)}" for i, f in enumerate(self.video_files)])
            self.video_files_list.setText(display_text)
        else:
            self.video_files_list.clear()



    def on_output_mode_changed(self, mode):
        """输出模式改变时的处理"""
        if mode == "选择文件夹":
            self.browse_merge_output_btn.setText("📁 选择文件夹")
            self.merge_output_path.setPlaceholderText("选择输出文件夹...")
            self.filename_widget.setVisible(True)
        else:
            self.browse_merge_output_btn.setText("📂 浏览文件")
            self.merge_output_path.setPlaceholderText("选择输出文件路径...")
            self.filename_widget.setVisible(False)

    def browse_merge_output(self):
        """浏览混剪输出文件或文件夹"""
        if self.output_mode_combo.currentText() == "选择文件夹":
            # 选择文件夹模式
            folder_path = QFileDialog.getExistingDirectory(
                self, "选择输出文件夹"
            )
            if folder_path:
                self.merge_output_path.setText(folder_path)
        else:
            # 选择文件模式
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存混剪视频", "",
                "视频文件 (*.mp4);;所有文件 (*)"
            )
            if file_path:
                self.merge_output_path.setText(file_path)

    def start_video_merge(self):
        """开始智能视频混剪"""
        if not self.video_files:
            QMessageBox.warning(self, "警告", "请先添加视频文件！")
            return

        output_path = self.merge_output_path.text()
        if not output_path:
            QMessageBox.warning(self, "警告", "请选择输出文件路径！")
            return

        # 检查所有视频文件是否存在
        for video_file in self.video_files:
            if not os.path.exists(video_file):
                QMessageBox.warning(self, "警告", f"视频文件不存在：{video_file}")
                return

        # 检查背景音乐文件（如果有）
        background_music = self.background_music_path.text().strip()
        if background_music and not os.path.exists(background_music):
            QMessageBox.warning(self, "警告", f"背景音乐文件不存在：{background_music}")
            return

        # 显示进度条
        self.merge_progress.setVisible(True)
        self.merge_progress.setValue(0)
        self.start_merge_btn.setEnabled(False)

        # 处理输出路径
        output_folder = None
        if self.output_mode_combo.currentText() == "选择文件夹":
            output_folder = output_path
            filename = self.output_filename.text().strip()
            if not filename:
                filename = "mixed_video.mp4"
            output_path = os.path.join(output_folder, filename)

        # 获取设置
        video_duration = self.video_duration_spin.value()
        num_outputs = self.output_count_spin.value()
        remove_original_audio = self.remove_original_audio.isChecked()

        transition_map = {
            "淡入淡出": "fade",
            "滑动": "slide",
            "缩放": "zoom",
            "无转场": "none"
        }
        transition_type = transition_map.get(self.transition_combo.currentText(), "fade")

        # 获取画面检测灵敏度
        scene_detection_sensitivity = float(self.scene_detection_slider.value())
        
        # 获取随机种子设置
        use_random_seed = self.random_seed_checkbox.isChecked()

        # 获取性能模式
        merge_performance_map = {
            "自动优化": "auto",
            "GPU加速": "gpu",
            "CPU多线程": "cpu",
            "单线程": "single_thread",
            "低内存模式": "low_memory"
        }
        merge_performance_mode = merge_performance_map.get(self.merge_performance_combo.currentText(), "auto")

        # 显示一条提示信息
        if self.merge_performance_combo.currentText() == "低内存模式":
            self.log_message("⚠️ 已启用低内存模式，视频质量可能降低以减少内存使用")
            self.merge_result_text.setText("⚠️ 低内存模式下视频分辨率会降低，渲染速度较慢，但可以减少内存使用")

        self.log_message(f"🎬 开始智能混剪: {len(self.video_files)}个视频 → {num_outputs}个混剪视频 (每个{video_duration}秒)")
        self.log_message(f"🔍 画面检测灵敏度: {scene_detection_sensitivity}")
        self.log_message(f"🎲 唯一混剪模式: {'启用' if use_random_seed else '禁用'}")
        if background_music:
            self.log_message(f"🎵 背景音乐: {os.path.basename(background_music)}")
        if remove_original_audio:
            self.log_message("🔇 已启用去除原视频音频")
        if output_folder:
            self.log_message(f"📁 输出文件夹: {output_folder}")

        # 创建并启动混剪线程
        self.merge_thread = SmartMixingThread(
            self.video_files.copy(),
            output_path,
            video_duration,
            num_outputs,
            transition_type,
            background_music if background_music else None,
            remove_original_audio,
            merge_performance_mode,
            scene_detection_sensitivity,
            output_folder,
            use_random_seed
        )

        self.merge_thread.progress_updated.connect(self.merge_progress.setValue)
        self.merge_thread.status_updated.connect(self.update_merge_status)
        self.merge_thread.result_ready.connect(self.merge_finished)
        self.merge_thread.error_occurred.connect(self.merge_error)
        self.merge_thread.start()

    def update_detection_sensitivity_label(self):
        """更新画面检测灵敏度标签"""
        value = self.scene_detection_slider.value()
        self.detection_sensitivity_label.setText(f"{value}.0")
        
    def update_merge_status(self, status):
        """更新混剪状态"""
        # 在结果文本区域显示当前状态
        current_text = self.merge_result_text.toPlainText()
        if not current_text or not current_text.endswith(status):
            self.merge_result_text.append(f"🔄 {status}")
            # 自动滚动到底部
            scrollbar = self.merge_result_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

    def merge_finished(self, result):
        """视频混剪完成"""
        self.merge_result_text.setText(result)
        self.merge_progress.setVisible(False)
        self.start_merge_btn.setEnabled(True)

        QMessageBox.information(self, "完成", "视频混剪完成！")

    def merge_error(self, error_msg):
        """视频混剪错误"""
        self.merge_result_text.setText(f"混剪失败: {error_msg}")
        self.merge_progress.setVisible(False)
        self.start_merge_btn.setEnabled(True)

        QMessageBox.critical(self, "错误", f"视频混剪失败：{error_msg}")

    # 视频融合相关方法
    def add_fusion_videos(self):
        """添加融合视频文件"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "选择视频文件", "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm);;所有文件 (*)"
        )

        if file_paths:
            for file_path in file_paths:
                if file_path not in self.fusion_video_files:
                    self.fusion_video_files.append(file_path)

            self.update_fusion_video_list()
            self.log_message(f"📹 已添加 {len(file_paths)} 个视频文件到融合列表")

    def add_fusion_videos_from_folder(self):
        """从文件夹添加视频文件"""
        folder_path = QFileDialog.getExistingDirectory(
            self, "选择包含视频文件的文件夹", ""
        )

        if folder_path:
            # 扫描文件夹中的视频文件
            video_files = self.scan_video_files_in_folder(folder_path)

            if not video_files:
                QMessageBox.information(self, "提示", "所选文件夹中没有找到支持的视频文件！")
                return

            # 显示视频选择对话框
            self.show_video_selection_dialog(video_files, folder_path)

    def scan_video_files_in_folder(self, folder_path, recursive=True):
        """扫描文件夹中的视频文件"""
        try:
            video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'}
            video_files = []

            if recursive:
                # 递归扫描子文件夹
                for root, dirs, files in os.walk(folder_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        file_ext = os.path.splitext(file)[1].lower()
                        if file_ext in video_extensions:
                            # 检查文件大小，过滤掉过小的文件
                            try:
                                if os.path.getsize(file_path) > 1024:  # 大于1KB
                                    video_files.append(file_path)
                            except:
                                continue
            else:
                # 只扫描当前文件夹
                for file in os.listdir(folder_path):
                    file_path = os.path.join(folder_path, file)
                    if os.path.isfile(file_path):
                        file_ext = os.path.splitext(file)[1].lower()
                        if file_ext in video_extensions:
                            try:
                                if os.path.getsize(file_path) > 1024:
                                    video_files.append(file_path)
                            except:
                                continue

            # 按文件名排序
            video_files.sort(key=lambda x: os.path.basename(x).lower())
            return video_files

        except Exception as e:
            self.log_message(f"❌ 扫描文件夹失败: {str(e)}")
            return []

    def show_video_selection_dialog(self, video_files, folder_path):
        """显示视频选择对话框"""
        dialog = VideoSelectionDialog(video_files, folder_path, self)
        if dialog.exec_() == QDialog.Accepted:
            selected_files = dialog.get_selected_files()
            if selected_files:
                # 添加选中的视频文件
                added_count = 0
                for file_path in selected_files:
                    if file_path not in self.fusion_video_files:
                        self.fusion_video_files.append(file_path)
                        added_count += 1

                self.update_fusion_video_list()
                self.log_message(f"📁 从文件夹添加了 {added_count} 个视频文件到融合列表")
                self.log_message(f"📂 文件夹: {os.path.basename(folder_path)}")
            else:
                self.log_message("⚠️ 未选择任何视频文件")

    def clear_fusion_videos(self):
        """清空融合视频文件列表"""
        self.fusion_video_files.clear()
        self.update_fusion_video_list()
        self.log_message("🗑️ 已清空融合视频文件列表")

    def update_fusion_video_list(self):
        """更新融合视频文件列表显示"""
        if self.fusion_video_files:
            file_list = []
            total_size = 0

            for i, file_path in enumerate(self.fusion_video_files, 1):
                file_name = os.path.basename(file_path)

                # 获取文件大小
                try:
                    file_size = os.path.getsize(file_path)
                    total_size += file_size
                    size_str = self.format_file_size(file_size)
                except:
                    size_str = "未知大小"

                # 获取文件所在文件夹
                folder_name = os.path.basename(os.path.dirname(file_path))

                file_list.append(f"{i}. {file_name}")
                file_list.append(f"   📁 {folder_name} | 📏 {size_str}")
                file_list.append("")  # 空行分隔

            # 添加总计信息
            if len(self.fusion_video_files) > 1:
                total_size_str = self.format_file_size(total_size)
                file_list.append(f"📊 总计: {len(self.fusion_video_files)} 个文件, {total_size_str}")

            self.fusion_video_list.setText("\n".join(file_list))
        else:
            self.fusion_video_list.setText("暂无选择的视频文件\n\n💡 提示:\n• 点击 '📁 添加视频' 选择单个或多个视频文件\n• 点击 '📂 选择文件夹' 从文件夹中选择视频")

    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

    def add_fusion_stickers(self):
        """添加融合贴纸文件"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "选择贴纸文件", "",
            "图片文件 (*.png *.jpg *.jpeg *.gif *.bmp);;所有文件 (*)"
        )

        if file_paths:
            for file_path in file_paths:
                if file_path not in self.fusion_sticker_files:
                    self.fusion_sticker_files.append(file_path)

            self.update_fusion_sticker_list()
            self.log_message(f"🏷️ 已添加 {len(file_paths)} 个贴纸文件到融合列表")

    def clear_fusion_stickers(self):
        """清空融合贴纸文件列表"""
        self.fusion_sticker_files.clear()
        self.update_fusion_sticker_list()
        self.log_message("🗑️ 已清空融合贴纸文件列表")

    def update_fusion_sticker_list(self):
        """更新融合贴纸文件列表显示"""
        if self.fusion_sticker_files:
            file_list = []
            for i, file_path in enumerate(self.fusion_sticker_files, 1):
                file_name = os.path.basename(file_path)
                file_list.append(f"{i}. {file_name}")
            self.fusion_sticker_list.setText("\n".join(file_list))
        else:
            self.fusion_sticker_list.setText("")

    def browse_fusion_output_folder(self):
        """浏览选择融合输出文件夹"""
        folder_path = QFileDialog.getExistingDirectory(
            self, "选择输出文件夹", ""
        )

        if folder_path:
            self.fusion_output_folder.setText(folder_path)
            self.log_message(f"📁 已设置融合输出文件夹: {os.path.basename(folder_path)}")

    def start_video_fusion(self):
        """开始视频融合处理"""
        # 验证输入
        if not self.fusion_video_files:
            QMessageBox.warning(self, "警告", "请至少选择一个视频文件！")
            return

        if len(self.fusion_video_files) < 2:
            QMessageBox.warning(self, "警告", "视频融合需要至少2个视频文件！")
            return

        output_folder = self.fusion_output_folder.text().strip()
        if not output_folder:
            QMessageBox.warning(self, "警告", "请选择输出文件夹！")
            return

        # 检查输出文件夹是否存在
        if not os.path.exists(output_folder):
            try:
                os.makedirs(output_folder)
            except Exception as e:
                QMessageBox.critical(self, "错误", f"无法创建输出文件夹：{str(e)}")
                return

        # 获取设置参数
        transition_map = {
            '淡入淡出': 'fade',
            '滑动': 'slide',
            '缩放': 'zoom',
            '无转场': 'none'
        }
        transition_type = transition_map.get(self.fusion_transition_combo.currentText(), 'fade')
        num_outputs = self.fusion_num_outputs.value()
        filename_prefix = self.fusion_filename_prefix.text().strip() or "融合视频"
        sticker_position = self.fusion_sticker_position.currentText()
        sticker_size = self.fusion_sticker_size.value()
        sticker_opacity = self.fusion_sticker_opacity.value()

        # 获取时长控制参数
        duration_mode = self.fusion_duration_mode.currentText()
        target_duration = self.fusion_target_duration.value()
        duration_strategy = self.fusion_duration_strategy.currentText()

        # 获取头尾裁剪参数
        enable_trim = self.fusion_enable_trim.isChecked()
        trim_mode = self.fusion_trim_mode.currentText()
        trim_start = self.fusion_trim_start.value() if enable_trim else 0
        trim_end = self.fusion_trim_end.value() if enable_trim else 0
        min_duration = self.fusion_min_duration.value()

        # 禁用开始按钮
        self.fusion_start_btn.setEnabled(False)
        self.fusion_progress.setVisible(True)
        self.fusion_progress.setValue(0)
        self.fusion_result_text.clear()

        self.log_message(f"🎭 开始视频融合: {len(self.fusion_video_files)}个视频 → {num_outputs}个不同顺序的融合视频")
        self.log_message(f"🎨 转场效果: {self.fusion_transition_combo.currentText()}")

        # 显示时长控制信息
        if duration_mode == "指定时长":
            self.log_message(f"⏰ 时长控制: {duration_mode}, 目标时长: {target_duration}秒, 策略: {duration_strategy}")
        else:
            self.log_message(f"📋 时长控制: {duration_mode}（使用完整视频时长）")

        # 根据裁剪设置显示不同的拼接模式信息
        if enable_trim and (trim_start > 0 or trim_end > 0):
            self.log_message(f"✂️ 头尾裁剪: {trim_mode}, 开头: {trim_start}秒, 结尾: {trim_end}秒, 最小时长: {min_duration}秒")
        else:
            self.log_message(f"📋 拼接模式: 完整视频不同顺序拼接（不裁剪）")

        self.log_message(f"📁 输出文件夹: {os.path.basename(output_folder)}")
        if self.fusion_sticker_files:
            self.log_message(f"🏷️ 贴纸数量: {len(self.fusion_sticker_files)}, 位置: {sticker_position}, 大小: {sticker_size}%, 透明度: {sticker_opacity}%")

        # 创建并启动融合线程
        self.fusion_thread = VideoFusionThread(
            self.fusion_video_files.copy(),
            output_folder,
            transition_type,
            num_outputs,
            self.fusion_sticker_files.copy() if self.fusion_sticker_files else None,
            'auto',  # 性能模式
            8,  # segment_duration (保留兼容性)
            60,  # total_duration (保留兼容性)
            filename_prefix,
            sticker_position,
            sticker_size,
            sticker_opacity,
            trim_start,
            trim_end,
            trim_mode,
            min_duration,
            duration_mode,
            target_duration,
            duration_strategy,
            enable_trim
        )

        # 连接信号
        self.fusion_thread.progress_updated.connect(self.fusion_progress.setValue)
        self.fusion_thread.status_updated.connect(self.log_message)
        self.fusion_thread.result_ready.connect(self.fusion_finished)
        self.fusion_thread.error_occurred.connect(self.fusion_error)

        # 启动线程
        self.fusion_thread.start()

    def fusion_finished(self, result_text):
        """视频融合完成"""
        self.fusion_result_text.setText(result_text)
        self.fusion_progress.setVisible(False)
        self.fusion_start_btn.setEnabled(True)

        self.log_message("✅ 视频融合处理完成！")
        QMessageBox.information(self, "完成", "视频融合处理完成！")

    def fusion_error(self, error_msg):
        """视频融合错误"""
        self.fusion_result_text.setText(f"融合失败: {error_msg}")
        self.fusion_progress.setVisible(False)
        self.fusion_start_btn.setEnabled(True)

        QMessageBox.critical(self, "错误", f"视频融合失败：{error_msg}")

    def on_duration_mode_changed(self, mode):
        """时长模式改变时的处理"""
        is_specified = (mode == "指定时长")

        # 启用/禁用相关控件
        self.fusion_target_duration.setEnabled(is_specified)
        self.fusion_duration_strategy.setEnabled(is_specified)

        # 更新提示信息
        if is_specified:
            self.log_message("📏 已切换到指定时长模式，可以设置目标时长和控制策略")
        else:
            self.log_message("📋 已切换到完整拼接模式，将使用所有视频的完整时长")

    def on_trim_enabled_changed(self, enabled):
        """头尾裁剪启用状态改变时的处理"""
        # 启用/禁用裁剪相关控件
        self.fusion_trim_mode.setEnabled(enabled)
        self.fusion_trim_start.setEnabled(enabled)
        self.fusion_trim_end.setEnabled(enabled)
        self.fusion_min_duration.setEnabled(enabled)

        # 更新提示信息
        if enabled:
            self.log_message("✂️ 已启用头尾裁剪功能，可以设置裁剪参数")
        else:
            self.log_message("📋 已禁用头尾裁剪功能，将使用完整视频")

    def setup_video_split_tab(self, tab):
        """设置视频分割标签页"""
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("✂️ 智能视频分割")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # 说明文字
        desc_label = QLabel("根据画面变化自动检测场景并分割视频为独立片段")
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #7f8c8d;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(desc_label)

        # 输入文件选择
        input_group = QGroupBox("📁 输入设置")
        input_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        input_layout = QVBoxLayout(input_group)

        # 视频文件选择
        video_file_layout = QHBoxLayout()
        video_file_layout.addWidget(QLabel("输入视频:"))
        self.split_input_path = QLineEdit()
        self.split_input_path.setPlaceholderText("选择要分割的视频文件...")
        video_file_layout.addWidget(self.split_input_path)

        self.split_browse_btn = QPushButton("浏览")
        self.split_browse_btn.clicked.connect(self.browse_split_input_file)
        video_file_layout.addWidget(self.split_browse_btn)
        input_layout.addLayout(video_file_layout)

        # 输出目录选择
        output_dir_layout = QHBoxLayout()
        output_dir_layout.addWidget(QLabel("输出目录:"))
        self.split_output_dir = QLineEdit()
        self.split_output_dir.setPlaceholderText("选择分割后视频的保存目录...")
        output_dir_layout.addWidget(self.split_output_dir)

        self.split_output_browse_btn = QPushButton("浏览")
        self.split_output_browse_btn.clicked.connect(self.browse_split_output_dir)
        output_dir_layout.addWidget(self.split_output_browse_btn)
        input_layout.addLayout(output_dir_layout)

        layout.addWidget(input_group)

        # 分割设置
        settings_group = QGroupBox("⚙️ 分割设置")
        settings_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        settings_layout = QVBoxLayout(settings_group)

        # 场景检测阈值
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(QLabel("场景检测阈值:"))
        self.split_threshold_slider = QSlider(Qt.Horizontal)
        self.split_threshold_slider.setRange(10, 100)
        self.split_threshold_slider.setValue(30)
        self.split_threshold_slider.valueChanged.connect(self.update_split_threshold_label)
        threshold_layout.addWidget(self.split_threshold_slider)

        self.split_threshold_label = QLabel("30.0")
        self.split_threshold_label.setMinimumWidth(50)
        threshold_layout.addWidget(self.split_threshold_label)
        settings_layout.addLayout(threshold_layout)

        # 阈值说明
        threshold_desc = QLabel("阈值越小，检测到的场景越多；阈值越大，场景分割越少")
        threshold_desc.setStyleSheet("color: #7f8c8d; font-size: 12px;")
        settings_layout.addWidget(threshold_desc)

        # 音频处理选项
        audio_layout = QHBoxLayout()
        self.split_remove_audio = QCheckBox("去除音频")
        self.split_remove_audio.setToolTip("勾选此选项将在分割后的视频中去除音频轨道")
        audio_layout.addWidget(self.split_remove_audio)
        audio_layout.addStretch()
        settings_layout.addLayout(audio_layout)

        layout.addWidget(settings_group)

        # 控制按钮
        button_layout = QHBoxLayout()
        self.start_split_btn = QPushButton("🚀 开始分割")
        self.start_split_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:pressed {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        self.start_split_btn.clicked.connect(self.start_video_split)
        button_layout.addWidget(self.start_split_btn)

        self.stop_split_btn = QPushButton("⏹️ 停止")
        self.stop_split_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        self.stop_split_btn.clicked.connect(self.stop_video_split)
        self.stop_split_btn.setEnabled(False)
        button_layout.addWidget(self.stop_split_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        # 进度显示
        self.split_progress = QProgressBar()
        self.split_progress.setVisible(False)
        layout.addWidget(self.split_progress)

        self.split_status_label = QLabel("")
        self.split_status_label.setVisible(False)
        layout.addWidget(self.split_status_label)

        # 结果显示
        self.split_result_text = QTextEdit()
        self.split_result_text.setMaximumHeight(200)
        self.split_result_text.setPlaceholderText("分割结果将在这里显示...")
        layout.addWidget(self.split_result_text)

        layout.addStretch()

    def setup_video_merge_tab(self, tab):
        """设置视频合并标签页"""
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("🎬 智能视频混剪工具")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 8px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border-radius: 8px;
                margin-bottom: 8px;
            }
        """)
        layout.addWidget(title_label)

        # 创建两列布局
        main_content = QHBoxLayout()
        main_content.setSpacing(15)

        # 左侧面板
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(15)

        # 视频文件管理
        video_group = QGroupBox("📹 视频文件管理")
        video_layout = QVBoxLayout(video_group)
        video_layout.setContentsMargins(15, 20, 15, 15)
        video_layout.setSpacing(10)

        # 视频文件列表
        self.video_files_list = QTextEdit()
        self.video_files_list.setMaximumHeight(120)
        self.video_files_list.setPlaceholderText("已添加的视频文件将显示在这里...")
        self.video_files_list.setReadOnly(True)
        self.video_files_list.setStyleSheet("""
            font-size: 14px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            padding: 8px;
        """)
        video_layout.addWidget(self.video_files_list)

        # 视频文件操作按钮
        video_buttons_layout = QHBoxLayout()
        video_buttons_layout.setSpacing(10)

        self.add_video_btn = QPushButton("📂 添加视频")
        self.add_video_btn.setFixedHeight(35)
        self.add_video_btn.clicked.connect(self.add_video_files)
        video_buttons_layout.addWidget(self.add_video_btn)

        self.clear_videos_btn = QPushButton("🗑️ 清空列表")
        self.clear_videos_btn.setFixedHeight(35)
        self.clear_videos_btn.clicked.connect(self.clear_video_files)
        video_buttons_layout.addWidget(self.clear_videos_btn)

        video_buttons_layout.addStretch()
        video_layout.addLayout(video_buttons_layout)

        left_layout.addWidget(video_group)

        # 背景音乐管理
        music_group = QGroupBox("🎵 背景音乐设置")
        music_layout = QVBoxLayout(music_group)
        music_layout.setContentsMargins(15, 20, 15, 15)
        music_layout.setSpacing(10)

        # 背景音乐文件选择
        music_file_layout = QHBoxLayout()
        music_file_layout.setSpacing(10)

        music_label = QLabel("背景音乐:")
        music_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        music_file_layout.addWidget(music_label)

        self.background_music_path = QLineEdit()
        self.background_music_path.setPlaceholderText("选择背景音乐文件（可选）...")
        self.background_music_path.setMinimumHeight(30)
        music_file_layout.addWidget(self.background_music_path)

        browse_music_btn = QPushButton("📂 浏览")
        browse_music_btn.setFixedWidth(80)
        browse_music_btn.setFixedHeight(30)
        browse_music_btn.clicked.connect(self.browse_background_music)
        music_file_layout.addWidget(browse_music_btn)

        clear_music_btn = QPushButton("🗑️ 清除")
        clear_music_btn.setFixedWidth(80)
        clear_music_btn.setFixedHeight(30)
        clear_music_btn.clicked.connect(self.clear_background_music)
        music_file_layout.addWidget(clear_music_btn)

        music_layout.addLayout(music_file_layout)

        # 音频控制选项
        audio_control_layout = QHBoxLayout()
        audio_control_layout.setSpacing(15)

        self.remove_original_audio = QCheckBox("去除原视频音频")
        self.remove_original_audio.setStyleSheet("font-size: 14px; color: #2c3e50;")
        self.remove_original_audio.setToolTip("勾选后将去除原视频的音频，只保留背景音乐")
        audio_control_layout.addWidget(self.remove_original_audio)

        audio_control_layout.addStretch()
        music_layout.addLayout(audio_control_layout)

        left_layout.addWidget(music_group)

        # 添加左侧面板
        main_content.addWidget(left_panel)

        # 右侧面板
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(15)

        # 混剪设置
        settings_group = QGroupBox("⚙️ 混剪设置")
        settings_layout = QGridLayout(settings_group)
        settings_layout.setContentsMargins(15, 20, 15, 15)
        settings_layout.setSpacing(12)

        # 转场效果
        transition_label = QLabel("转场效果:")
        transition_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        settings_layout.addWidget(transition_label, 0, 0)

        self.transition_combo = QComboBox()
        self.transition_combo.addItems(["淡入淡出", "滑动", "缩放", "无转场"])
        self.transition_combo.setCurrentIndex(0)
        self.transition_combo.setMinimumHeight(30)
        settings_layout.addWidget(self.transition_combo, 0, 1)

        # 视频时长设置
        duration_label = QLabel("视频时长:")
        duration_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        settings_layout.addWidget(duration_label, 1, 0)

        duration_layout = QHBoxLayout()
        self.video_duration_spin = QSpinBox()
        self.video_duration_spin.setRange(10, 600)  # 10秒到10分钟
        self.video_duration_spin.setValue(30)  # 默认30秒
        self.video_duration_spin.setSuffix(" 秒")
        self.video_duration_spin.setMinimumHeight(30)
        duration_layout.addWidget(self.video_duration_spin)
        duration_layout.addStretch()

        duration_widget = QWidget()
        duration_widget.setLayout(duration_layout)
        settings_layout.addWidget(duration_widget, 1, 1)

        # 生成个数设置
        count_label = QLabel("生成个数:")
        count_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        settings_layout.addWidget(count_label, 2, 0)

        count_layout = QHBoxLayout()
        self.output_count_spin = QSpinBox()
        self.output_count_spin.setRange(1, 10)  # 1到10个视频
        self.output_count_spin.setValue(1)  # 默认1个
        self.output_count_spin.setSuffix(" 个")
        self.output_count_spin.setMinimumHeight(30)
        count_layout.addWidget(self.output_count_spin)
        count_layout.addStretch()

        count_widget = QWidget()
        count_widget.setLayout(count_layout)
        settings_layout.addWidget(count_widget, 2, 1)

        # 性能模式
        performance_label = QLabel("性能模式:")
        performance_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        settings_layout.addWidget(performance_label, 3, 0)

        self.merge_performance_combo = QComboBox()
        self.merge_performance_combo.addItems([
            "自动优化",
            "GPU加速",
            "CPU多线程",
            "单线程",
            "低内存模式"
        ])
        self.merge_performance_combo.setCurrentIndex(0)
        self.merge_performance_combo.setMinimumHeight(30)
        self.merge_performance_combo.setToolTip("低内存模式: 降低视频分辨率和质量，适用于内存不足的设备")
        settings_layout.addWidget(self.merge_performance_combo, 3, 1)

        # 画面检测灵敏度设置
        detection_label = QLabel("画面检测灵敏度:")
        detection_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        settings_layout.addWidget(detection_label, 4, 0)

        detection_slider_layout = QHBoxLayout()
        self.scene_detection_slider = QSlider(Qt.Horizontal)
        self.scene_detection_slider.setRange(10, 100)
        self.scene_detection_slider.setValue(30)
        self.scene_detection_slider.setMinimumHeight(30)
        self.scene_detection_slider.valueChanged.connect(self.update_detection_sensitivity_label)
        detection_slider_layout.addWidget(self.scene_detection_slider)
        
        self.detection_sensitivity_label = QLabel("30.0")
        self.detection_sensitivity_label.setMinimumWidth(50)
        detection_slider_layout.addWidget(self.detection_sensitivity_label)
        
        detection_slider_widget = QWidget()
        detection_slider_widget.setLayout(detection_slider_layout)
        settings_layout.addWidget(detection_slider_widget, 4, 1)
        
        # 灵敏度说明
        sensitivity_desc = QLabel("灵敏度越低，检测到的场景变化越多，混剪越频繁；灵敏度越高，混剪越平缓")
        sensitivity_desc.setStyleSheet("color: #7f8c8d; font-size: 12px;")
        settings_layout.addWidget(sensitivity_desc, 5, 0, 1, 2)
        
        # 随机种子设置
        seed_label = QLabel("混剪模式:")
        seed_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        settings_layout.addWidget(seed_label, 6, 0)

        seed_layout = QVBoxLayout()
        self.random_seed_checkbox = QCheckBox("每次生成唯一混剪")
        self.random_seed_checkbox.setChecked(True)
        self.random_seed_checkbox.setToolTip("启用后每次生成的混剪视频都会不同，禁用后使用固定种子")
        seed_layout.addWidget(self.random_seed_checkbox)

        seed_widget = QWidget()
        seed_widget.setLayout(seed_layout)
        settings_layout.addWidget(seed_widget, 6, 1)

        right_layout.addWidget(settings_group)

        # 输出设置
        output_group = QGroupBox("💾 输出设置")
        output_layout = QVBoxLayout(output_group)
        output_layout.setContentsMargins(15, 20, 15, 15)
        output_layout.setSpacing(10)

        # 输出模式选择
        output_mode_layout = QHBoxLayout()
        output_mode_label = QLabel("输出模式:")
        output_mode_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        output_mode_layout.addWidget(output_mode_label)

        self.output_mode_combo = QComboBox()
        self.output_mode_combo.addItems(["指定文件", "选择文件夹"])
        self.output_mode_combo.setMinimumHeight(30)
        self.output_mode_combo.currentTextChanged.connect(self.on_output_mode_changed)
        output_mode_layout.addWidget(self.output_mode_combo)
        output_mode_layout.addStretch()
        output_layout.addLayout(output_mode_layout)

        # 输出路径设置
        path_layout = QHBoxLayout()
        self.merge_output_path = QLineEdit()
        self.merge_output_path.setPlaceholderText("选择输出文件路径...")
        self.merge_output_path.setMinimumHeight(30)
        path_layout.addWidget(self.merge_output_path)

        self.browse_merge_output_btn = QPushButton("📂 浏览文件")
        self.browse_merge_output_btn.setFixedWidth(100)
        self.browse_merge_output_btn.setFixedHeight(30)
        self.browse_merge_output_btn.clicked.connect(self.browse_merge_output)
        path_layout.addWidget(self.browse_merge_output_btn)

        output_layout.addLayout(path_layout)

        # 文件名设置（仅在文件夹模式下显示）
        self.filename_layout = QHBoxLayout()
        filename_label = QLabel("文件名:")
        filename_label.setStyleSheet("font-size: 12px; color: #2c3e50;")
        self.filename_layout.addWidget(filename_label)

        self.output_filename = QLineEdit()
        self.output_filename.setPlaceholderText("mixed_video.mp4")
        self.output_filename.setText("mixed_video.mp4")
        self.output_filename.setMinimumHeight(25)
        self.filename_layout.addWidget(self.output_filename)

        self.filename_widget = QWidget()
        self.filename_widget.setLayout(self.filename_layout)
        self.filename_widget.setVisible(False)  # 默认隐藏
        output_layout.addWidget(self.filename_widget)

        right_layout.addWidget(output_group)

        # 控制按钮
        button_group = QGroupBox("🎮 操作控制")
        button_layout = QVBoxLayout(button_group)
        button_layout.setContentsMargins(15, 20, 15, 15)
        button_layout.setSpacing(10)

        self.start_merge_btn = QPushButton("🚀 开始智能混剪")
        self.start_merge_btn.setMinimumHeight(40)
        self.start_merge_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border: none;
                padding: 10px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 15px;
                min-height: 40px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #764ba2, stop:1 #667eea);
            }
            QPushButton:disabled {
                background: #bdc3c7;
            }
        """)
        self.start_merge_btn.clicked.connect(self.start_video_merge)
        button_layout.addWidget(self.start_merge_btn)

        right_layout.addWidget(button_group)

        # 添加右侧面板
        main_content.addWidget(right_panel)
        
        # 设置左右面板比例
        main_content.setStretch(0, 1)  # 左侧面板
        main_content.setStretch(1, 1)  # 右侧面板
        
        # 添加主内容到布局
        layout.addLayout(main_content)

        # 结果显示
        result_group = QGroupBox("📊 处理结果")
        result_layout = QVBoxLayout(result_group)
        result_layout.setContentsMargins(15, 20, 15, 15)

        self.merge_result_text = QTextEdit()
        self.merge_result_text.setPlaceholderText("混剪结果信息将显示在这里...")
        self.merge_result_text.setMaximumHeight(150)
        self.merge_result_text.setStyleSheet("""
            font-size: 14px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            padding: 8px;
        """)
        result_layout.addWidget(self.merge_result_text)

        layout.addWidget(result_group)

        # 进度条
        self.merge_progress = QProgressBar()
        self.merge_progress.setVisible(False)
        self.merge_progress.setMinimumHeight(25)
        layout.addWidget(self.merge_progress)

        # 初始化文件列表
        self.video_files = []

        # 视频融合相关属性
        self.fusion_video_files = []
        self.fusion_sticker_files = []
        self.fusion_thread = None

    def setup_video_fusion_tab(self, tab):
        """设置视频融合标签页"""
        # 主布局
        main_layout = QVBoxLayout(tab)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # 标题
        title_label = QLabel("🎭 视频融合工具")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ff6b6b, stop:1 #ffa726);
                color: white;
                border-radius: 10px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)

        # 说明文字
        desc_label = QLabel("🎬 上传多个视频，按不同顺序拼接生成多个融合视频。支持完整拼接和指定时长两种模式，可选择是否启用头尾剪裁。")
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #7f8c8d;
                margin-bottom: 15px;
                padding: 8px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
            }
        """)
        main_layout.addWidget(desc_label)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                background-color: #ffffff;
            }
            QScrollBar:vertical {
                background: #f8f9fa;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: #6c757d;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #495057;
            }
        """)

        # 滚动内容容器
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(15, 15, 15, 15)
        scroll_layout.setSpacing(15)

        # 创建两列布局
        main_content = QHBoxLayout()
        main_content.setSpacing(20)

        # 左侧面板
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(15)

        # 视频文件选择区域
        video_group = QGroupBox("📹 视频文件选择")
        video_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        video_layout = QVBoxLayout(video_group)

        # 视频文件列表
        self.fusion_video_list = QTextEdit()
        self.fusion_video_list.setMaximumHeight(120)
        self.fusion_video_list.setPlaceholderText("选择的视频文件将显示在这里...")
        self.fusion_video_list.setReadOnly(True)
        video_layout.addWidget(self.fusion_video_list)

        # 视频文件操作按钮
        video_btn_layout = QHBoxLayout()

        self.fusion_add_videos_btn = AnimatedButton("📁 添加视频")
        self.fusion_add_videos_btn.clicked.connect(self.add_fusion_videos)
        self.fusion_add_videos_btn.setToolTip("选择单个或多个视频文件")
        video_btn_layout.addWidget(self.fusion_add_videos_btn)

        self.fusion_add_folder_btn = AnimatedButton("📂 选择文件夹")
        self.fusion_add_folder_btn.clicked.connect(self.add_fusion_videos_from_folder)
        self.fusion_add_folder_btn.setToolTip("选择包含视频文件的文件夹")
        self.fusion_add_folder_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f39c12, stop:1 #e67e22);
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 6px;
                border: none;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e67e22, stop:1 #f39c12);
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                transform: translateY(1px);
            }
        """)
        video_btn_layout.addWidget(self.fusion_add_folder_btn)

        self.fusion_clear_videos_btn = AnimatedButton("🗑️ 清空列表")
        self.fusion_clear_videos_btn.clicked.connect(self.clear_fusion_videos)
        video_btn_layout.addWidget(self.fusion_clear_videos_btn)

        video_layout.addLayout(video_btn_layout)
        left_layout.addWidget(video_group)

        # 贴纸文件选择区域
        sticker_group = QGroupBox("🏷️ 贴纸文件选择（可选）")
        sticker_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        sticker_layout = QVBoxLayout(sticker_group)

        # 贴纸文件列表
        self.fusion_sticker_list = QTextEdit()
        self.fusion_sticker_list.setMaximumHeight(80)
        self.fusion_sticker_list.setPlaceholderText("选择的贴纸文件将显示在这里...")
        self.fusion_sticker_list.setReadOnly(True)
        sticker_layout.addWidget(self.fusion_sticker_list)

        # 贴纸文件操作按钮
        sticker_btn_layout = QHBoxLayout()

        self.fusion_add_stickers_btn = AnimatedButton("🖼️ 添加贴纸")
        self.fusion_add_stickers_btn.clicked.connect(self.add_fusion_stickers)
        sticker_btn_layout.addWidget(self.fusion_add_stickers_btn)

        self.fusion_clear_stickers_btn = AnimatedButton("🗑️ 清空贴纸")
        self.fusion_clear_stickers_btn.clicked.connect(self.clear_fusion_stickers)
        sticker_btn_layout.addWidget(self.fusion_clear_stickers_btn)

        sticker_layout.addLayout(sticker_btn_layout)
        left_layout.addWidget(sticker_group)

        main_content.addWidget(left_panel, 1)

        # 右侧面板
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(15)

        # 融合设置区域
        settings_group = QGroupBox("⚙️ 融合设置")
        settings_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        settings_layout = QGridLayout(settings_group)

        # 转场方式选择
        settings_layout.addWidget(QLabel("🎨 转场方式:"), 0, 0)
        self.fusion_transition_combo = QComboBox()
        self.fusion_transition_combo.addItems(['淡入淡出', '滑动', '缩放', '无转场'])
        self.fusion_transition_combo.setCurrentText('淡入淡出')
        settings_layout.addWidget(self.fusion_transition_combo, 0, 1)

        # 生成视频个数
        settings_layout.addWidget(QLabel("🎯 生成个数:"), 1, 0)
        self.fusion_num_outputs = QSpinBox()
        self.fusion_num_outputs.setRange(1, 20)
        self.fusion_num_outputs.setValue(3)
        settings_layout.addWidget(self.fusion_num_outputs, 1, 1)

        # 拼接模式说明
        mode_label = QLabel("📋 拼接模式: 完整视频不同顺序拼接")
        mode_label.setStyleSheet("""
            QLabel {
                color: #28a745;
                font-weight: bold;
                font-size: 12px;
                padding: 5px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
            }
        """)
        settings_layout.addWidget(mode_label, 2, 0, 1, 2)

        right_layout.addWidget(settings_group)

        # 视频时长控制区域
        duration_group = QGroupBox("⏱️ 视频时长控制")
        duration_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        duration_layout = QGridLayout(duration_group)

        # 时长控制模式
        duration_layout.addWidget(QLabel("🎯 时长模式:"), 0, 0)
        self.fusion_duration_mode = QComboBox()
        self.fusion_duration_mode.addItems(['完整拼接', '指定时长'])
        self.fusion_duration_mode.setCurrentText('完整拼接')
        self.fusion_duration_mode.setToolTip("完整拼接：使用所有视频的完整时长\n指定时长：生成指定时长的融合视频")
        duration_layout.addWidget(self.fusion_duration_mode, 0, 1)

        # 指定时长设置
        duration_layout.addWidget(QLabel("⏰ 目标时长:"), 1, 0)
        self.fusion_target_duration = QSpinBox()
        self.fusion_target_duration.setRange(10, 600)  # 10秒到10分钟
        self.fusion_target_duration.setValue(60)
        self.fusion_target_duration.setSuffix(" 秒")
        self.fusion_target_duration.setEnabled(False)
        self.fusion_target_duration.setToolTip("指定融合视频的目标时长（10-600秒）")
        duration_layout.addWidget(self.fusion_target_duration, 1, 1)

        # 时长控制策略
        duration_layout.addWidget(QLabel("📋 控制策略:"), 2, 0)
        self.fusion_duration_strategy = QComboBox()
        self.fusion_duration_strategy.addItems(['智能选择', '循环播放', '随机片段'])
        self.fusion_duration_strategy.setCurrentText('智能选择')
        self.fusion_duration_strategy.setEnabled(False)
        self.fusion_duration_strategy.setToolTip("智能选择：自动选择合适的视频片段\n循环播放：重复播放视频直到达到目标时长\n随机片段：随机选择视频片段组合")
        duration_layout.addWidget(self.fusion_duration_strategy, 2, 1)

        right_layout.addWidget(duration_group)

        # 头尾裁剪设置区域
        trim_group = QGroupBox("✂️ 头尾裁剪设置（可选）")
        trim_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        trim_layout = QGridLayout(trim_group)

        # 启用头尾裁剪开关
        self.fusion_enable_trim = QCheckBox("启用头尾裁剪")
        self.fusion_enable_trim.setChecked(False)
        self.fusion_enable_trim.setToolTip("启用后可以对视频进行头尾裁剪")
        trim_layout.addWidget(self.fusion_enable_trim, 0, 0, 1, 2)

        # 裁剪模式选择
        trim_layout.addWidget(QLabel("🎯 裁剪模式:"), 1, 0)
        self.fusion_trim_mode = QComboBox()
        self.fusion_trim_mode.addItems(['全部裁剪', '智能保留首尾'])
        self.fusion_trim_mode.setCurrentText('全部裁剪')
        self.fusion_trim_mode.setEnabled(False)
        self.fusion_trim_mode.setToolTip("全部裁剪：对所有视频进行头尾裁剪\n智能保留首尾：保留第一个视频开头和最后一个视频结尾")
        trim_layout.addWidget(self.fusion_trim_mode, 1, 1)

        # 开头裁剪时长
        trim_layout.addWidget(QLabel("⏪ 开头裁剪:"), 2, 0)
        self.fusion_trim_start = QSpinBox()
        self.fusion_trim_start.setRange(0, 60)
        self.fusion_trim_start.setValue(0)
        self.fusion_trim_start.setEnabled(False)
        self.fusion_trim_start.setSuffix(" 秒")
        self.fusion_trim_start.setToolTip("去除视频开头的秒数（0-60秒）")
        trim_layout.addWidget(self.fusion_trim_start, 2, 1)

        # 结尾裁剪时长
        trim_layout.addWidget(QLabel("⏩ 结尾裁剪:"), 3, 0)
        self.fusion_trim_end = QSpinBox()
        self.fusion_trim_end.setRange(0, 60)
        self.fusion_trim_end.setValue(0)
        self.fusion_trim_end.setEnabled(False)
        self.fusion_trim_end.setSuffix(" 秒")
        self.fusion_trim_end.setToolTip("去除视频结尾的秒数（0-60秒）")
        trim_layout.addWidget(self.fusion_trim_end, 3, 1)

        # 最小保留时长
        trim_layout.addWidget(QLabel("⏱️ 最小时长:"), 4, 0)
        self.fusion_min_duration = QSpinBox()
        self.fusion_min_duration.setRange(1, 30)
        self.fusion_min_duration.setValue(5)
        self.fusion_min_duration.setEnabled(False)
        self.fusion_min_duration.setSuffix(" 秒")
        self.fusion_min_duration.setToolTip("确保裁剪后视频不会过短的最小时长")
        trim_layout.addWidget(self.fusion_min_duration, 4, 1)

        # 裁剪说明
        trim_desc_label = QLabel("💡 智能保留模式：保留第一个视频的开头和最后一个视频的结尾，对中间视频进行头尾裁剪")
        trim_desc_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 11px;
                padding: 5px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                margin-top: 5px;
            }
        """)
        trim_desc_label.setWordWrap(True)
        trim_layout.addWidget(trim_desc_label, 5, 0, 1, 2)

        right_layout.addWidget(trim_group)

        # 设置信号连接
        self.fusion_duration_mode.currentTextChanged.connect(self.on_duration_mode_changed)
        self.fusion_enable_trim.toggled.connect(self.on_trim_enabled_changed)

        # 贴纸设置区域
        sticker_settings_group = QGroupBox("🏷️ 贴纸设置")
        sticker_settings_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        sticker_settings_layout = QGridLayout(sticker_settings_group)

        # 贴纸位置
        sticker_settings_layout.addWidget(QLabel("📍 位置:"), 0, 0)
        self.fusion_sticker_position = QComboBox()
        self.fusion_sticker_position.addItems(['随机', '左上角', '右上角', '左下角', '右下角', '中心'])
        sticker_settings_layout.addWidget(self.fusion_sticker_position, 0, 1)

        # 贴纸大小
        sticker_settings_layout.addWidget(QLabel("📏 大小:"), 1, 0)
        self.fusion_sticker_size = QSlider(Qt.Horizontal)
        self.fusion_sticker_size.setRange(5, 30)
        self.fusion_sticker_size.setValue(15)
        self.fusion_sticker_size_label = QLabel("15%")
        self.fusion_sticker_size.valueChanged.connect(
            lambda v: self.fusion_sticker_size_label.setText(f"{v}%")
        )
        sticker_size_layout = QHBoxLayout()
        sticker_size_layout.addWidget(self.fusion_sticker_size)
        sticker_size_layout.addWidget(self.fusion_sticker_size_label)
        sticker_settings_layout.addLayout(sticker_size_layout, 1, 1)

        # 贴纸透明度
        sticker_settings_layout.addWidget(QLabel("🔍 透明度:"), 2, 0)
        self.fusion_sticker_opacity = QSlider(Qt.Horizontal)
        self.fusion_sticker_opacity.setRange(10, 100)
        self.fusion_sticker_opacity.setValue(80)
        self.fusion_sticker_opacity_label = QLabel("80%")
        self.fusion_sticker_opacity.valueChanged.connect(
            lambda v: self.fusion_sticker_opacity_label.setText(f"{v}%")
        )
        opacity_layout = QHBoxLayout()
        opacity_layout.addWidget(self.fusion_sticker_opacity)
        opacity_layout.addWidget(self.fusion_sticker_opacity_label)
        sticker_settings_layout.addLayout(opacity_layout, 2, 1)

        right_layout.addWidget(sticker_settings_group)

        # 输出设置区域
        output_group = QGroupBox("📁 输出设置")
        output_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        output_layout = QVBoxLayout(output_group)

        # 输出文件夹选择
        output_folder_layout = QHBoxLayout()
        self.fusion_output_folder = QLineEdit()
        self.fusion_output_folder.setPlaceholderText("选择输出文件夹...")
        output_folder_layout.addWidget(self.fusion_output_folder)

        self.fusion_browse_folder_btn = AnimatedButton("浏览文件夹")
        self.fusion_browse_folder_btn.clicked.connect(self.browse_fusion_output_folder)
        output_folder_layout.addWidget(self.fusion_browse_folder_btn)

        output_layout.addLayout(output_folder_layout)

        # 文件名前缀
        prefix_layout = QHBoxLayout()
        prefix_layout.addWidget(QLabel("📝 文件名前缀:"))
        self.fusion_filename_prefix = QLineEdit()
        self.fusion_filename_prefix.setText("融合视频")
        self.fusion_filename_prefix.setPlaceholderText("融合视频")
        prefix_layout.addWidget(self.fusion_filename_prefix)
        output_layout.addLayout(prefix_layout)

        right_layout.addWidget(output_group)

        # 开始处理按钮
        self.fusion_start_btn = AnimatedButton("🚀 开始视频融合")
        self.fusion_start_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff6b6b, stop:1 #ffa726);
                color: white;
                font-weight: bold;
                font-size: 16px;
                padding: 12px;
                border-radius: 8px;
                border: none;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff5252, stop:1 #ff9800);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e53935, stop:1 #f57c00);
            }
            QPushButton:disabled {
                background: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.fusion_start_btn.clicked.connect(self.start_video_fusion)
        right_layout.addWidget(self.fusion_start_btn)

        main_content.addWidget(right_panel, 1)
        scroll_layout.addLayout(main_content)

        # 设置滚动区域
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area, 1)  # 让滚动区域占据大部分空间

        # 底部固定区域（进度条和结果显示）
        bottom_layout = QVBoxLayout()
        bottom_layout.setSpacing(10)

        # 进度条
        self.fusion_progress = QProgressBar()
        self.fusion_progress.setVisible(False)
        self.fusion_progress.setMinimumHeight(25)
        self.fusion_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                background-color: #f8f9fa;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ff6b6b, stop:1 #ffa726);
                border-radius: 6px;
            }
        """)
        bottom_layout.addWidget(self.fusion_progress)

        # 结果显示区域
        self.fusion_result_text = QTextEdit()
        self.fusion_result_text.setMaximumHeight(120)
        self.fusion_result_text.setMinimumHeight(120)
        self.fusion_result_text.setPlaceholderText("融合结果将显示在这里...")
        self.fusion_result_text.setReadOnly(True)
        self.fusion_result_text.setStyleSheet("""
            QTextEdit {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 8px;
                background-color: #f8f9fa;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        bottom_layout.addWidget(self.fusion_result_text)

        main_layout.addLayout(bottom_layout)

    # 视频分割相关方法
    def browse_split_input_file(self):
        """浏览选择输入视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择视频文件", "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.flv *.wmv *.webm);;所有文件 (*)"
        )
        if file_path:
            self.split_input_path.setText(file_path)
            # 自动设置输出目录为输入文件所在目录的split子目录
            input_dir = os.path.dirname(file_path)
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            default_output_dir = os.path.join(input_dir, f"{base_name}_split")
            self.split_output_dir.setText(default_output_dir)

    def browse_split_output_dir(self):
        """浏览选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.split_output_dir.setText(dir_path)

    def update_split_threshold_label(self):
        """更新分割阈值标签"""
        value = self.split_threshold_slider.value()
        self.split_threshold_label.setText(f"{value}.0")

    def start_video_split(self):
        """开始视频分割"""
        # 验证输入
        input_path = self.split_input_path.text().strip()
        output_dir = self.split_output_dir.text().strip()

        if not input_path:
            QMessageBox.warning(self, "警告", "请选择输入视频文件！")
            return

        if not output_dir:
            QMessageBox.warning(self, "警告", "请选择输出目录！")
            return

        if not os.path.exists(input_path):
            QMessageBox.warning(self, "警告", "输入视频文件不存在！")
            return

        # 禁用开始按钮，启用停止按钮
        self.start_split_btn.setEnabled(False)
        self.stop_split_btn.setEnabled(True)

        # 显示进度条和状态标签
        self.split_progress.setVisible(True)
        self.split_status_label.setVisible(True)
        self.split_progress.setValue(0)
        self.split_result_text.clear()

        # 获取参数
        threshold = float(self.split_threshold_slider.value())
        remove_audio = self.split_remove_audio.isChecked()

        # 创建并启动分割线程
        self.split_thread = VideoSplitThread(
            input_path,
            output_dir,
            threshold,
            remove_audio
        )

        self.split_thread.progress_updated.connect(self.split_progress.setValue)
        self.split_thread.status_updated.connect(self.split_status_label.setText)
        self.split_thread.finished_processing.connect(self.split_finished)
        self.split_thread.error_occurred.connect(self.split_error)
        self.split_thread.detailed_progress.connect(self.update_detailed_split_progress)
        self.split_thread.start()

    def stop_video_split(self):
        """停止视频分割"""
        if self.split_thread and self.split_thread.isRunning():
            # 先设置停止标志，让线程有机会清理资源
            self.split_thread.is_running = False
            self.split_status_label.setText("正在安全停止处理...")
            
            # 等待最多3秒，如果线程没有自行结束，则强制终止
            if not self.split_thread.wait(3000):
                self.split_thread.terminate()
                self.split_thread.wait()

        # 重置UI状态
        self.start_split_btn.setEnabled(True)
        self.stop_split_btn.setEnabled(False)
        self.split_progress.setVisible(False)
        self.split_status_label.setVisible(False)
        self.split_status_label.setText("")

    def split_finished(self, result_text):
        """视频分割完成"""
        # 重置UI状态
        self.start_split_btn.setEnabled(True)
        self.stop_split_btn.setEnabled(False)
        self.split_progress.setVisible(False)
        self.split_status_label.setVisible(False)

        # 显示结果
        self.split_result_text.setText(result_text)

        # 显示完成消息
        QMessageBox.information(self, "完成", "视频分割完成！")

    def split_error(self, error_msg):
        """视频分割出错"""
        # 重置UI状态
        self.start_split_btn.setEnabled(True)
        self.stop_split_btn.setEnabled(False)
        self.split_progress.setVisible(False)
        self.split_status_label.setVisible(False)

        # 显示错误信息
        self.split_result_text.setText(f"❌ 分割失败:\n{error_msg}")
        QMessageBox.critical(self, "错误", f"视频分割失败：{error_msg}")
        
    def update_detailed_split_progress(self, detail, progress):
        """更新详细进度信息"""
        # 更新进度条
        if progress >= 0:
            self.split_progress.setValue(progress)
            
        # 更新状态文本
        if detail:
            self.split_status_label.setText(detail)
            
        # 添加到结果文本，但不覆盖现有内容
        if detail:
            current_text = self.split_result_text.toPlainText()
            if current_text:
                self.split_result_text.append(f"{detail}")
            else:
                self.split_result_text.setText(f"{detail}")

    def closeEvent(self, event):
        """重写关闭事件，提供最小化选项"""
        if hasattr(self, 'tray_icon') and self.tray_icon.isVisible():
            # 如果系统托盘可用，询问用户选择
            reply = QMessageBox.question(
                self,
                "关闭选项",
                "请选择关闭方式：\n\n"
                "• 点击 'Yes' - 最小化到任务栏\n"
                "• 点击 'No' - 完全退出程序\n"
                "• 点击 'Cancel' - 最小化到系统托盘",
                QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                # 最小化到任务栏
                self.showMinimized()
                log_info(f"📦 程序已最小化到任务栏")
                event.ignore()
            elif reply == QMessageBox.No:
                # 完全退出程序
                log_info(f"🚪 用户选择完全退出程序")
                self.quit_application_completely()
                event.accept()
            else:
                # 最小化到系统托盘
                self.hide()
                log_info(f"📦 程序已最小化到系统托盘")
                event.ignore()
        else:
            # 如果系统托盘不可用，询问是否最小化到任务栏
            reply = QMessageBox.question(
                self,
                "关闭选项",
                "请选择关闭方式：\n\n"
                "• 点击 'Yes' - 最小化到任务栏\n"
                "• 点击 'No' - 完全退出程序",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                # 最小化到任务栏
                self.showMinimized()
                log_info(f"📦 程序已最小化到任务栏")
                event.ignore()
            else:
                # 完全退出程序
                log_info(f"🚪 用户选择完全退出程序")
                self.quit_application_completely()
                event.accept()

    def changeEvent(self, event):
        """处理窗口状态变化事件"""
        if event.type() == event.WindowStateChange:
            if self.isMinimized():
                # 最小化时保持在任务栏，不隐藏到托盘
                # 确保窗口图标在任务栏中保持可见
                self.setWindowState(Qt.WindowMinimized)
                log_info(f"📦 窗口已最小化到任务栏")
                event.accept()
                return
        super().changeEvent(event)


def main():
    app = SingletonApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("余下视频混剪工具")
    app.setApplicationDisplayName("余下视频混剪工具 v1.0")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("一款视频混剪工具")
    # app.setOrganizationDomain("ai-video-tools.com")

    # 设置应用程序图标（任务栏图标）
    try:
        logo_path = os.path.join(os.path.dirname(__file__), 'img', 'logo.ico')
        if os.path.exists(logo_path):
            app.setWindowIcon(QIcon(logo_path))
            log_info(f"✅ 成功设置应用程序图标: {logo_path}")
        else:
            # 尝试PNG格式
            logo_png_path = os.path.join(os.path.dirname(__file__), 'img', 'logo.png')
            if os.path.exists(logo_png_path):
                app.setWindowIcon(QIcon(logo_png_path))
                log_info(f"✅ 成功设置应用程序图标: {logo_png_path}")
            else:
                log_warning(f"⚠️ 未找到logo文件")
    except Exception as e:
        log_error(f"❌ 设置应用程序图标失败: {e}")

    # 设置应用程序属性，确保双击任务栏图标能正常工作
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)

    # 确保应用程序在任务栏中正确显示
    app.setAttribute(Qt.AA_DisableWindowContextHelpButton, True)

    # 设置应用程序不在最后一个窗口关闭时退出（支持系统托盘）
    app.setQuitOnLastWindowClosed(False)

    # 创建主窗口
    window = EnhancedVideoDeduplicationGUI()

    # 设置单实例应用的主窗口引用
    app.set_main_window(window)

    window.show()

    # 确保窗口能够正确响应激活事件
    app.setActiveWindow(window)

    sys.exit(app.exec_())

class VideoSelectionDialog(QDialog):
    """视频选择对话框"""

    def __init__(self, video_files, folder_path, parent=None):
        super().__init__(parent)
        self.video_files = video_files
        self.folder_path = folder_path
        self.selected_files = []

        self.setWindowTitle("选择视频文件")
        self.setModal(True)
        self.resize(800, 600)

        self.setup_ui()
        self.setup_connections()

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题和信息
        title_label = QLabel(f"📁 文件夹: {os.path.basename(self.folder_path)}")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-radius: 8px;
            }
        """)
        layout.addWidget(title_label)

        info_label = QLabel(f"找到 {len(self.video_files)} 个视频文件")
        info_label.setStyleSheet("font-size: 14px; color: #7f8c8d; margin: 5px;")
        layout.addWidget(info_label)

        # 选择模式
        mode_group = QGroupBox("选择模式")
        mode_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        mode_layout = QHBoxLayout(mode_group)

        self.mode_group = QButtonGroup()

        self.all_mode_radio = QRadioButton("全部选择")
        self.all_mode_radio.setChecked(True)
        self.mode_group.addButton(self.all_mode_radio, 0)
        mode_layout.addWidget(self.all_mode_radio)

        self.random_mode_radio = QRadioButton("随机选择")
        self.mode_group.addButton(self.random_mode_radio, 1)
        mode_layout.addWidget(self.random_mode_radio)

        self.manual_mode_radio = QRadioButton("手动选择")
        self.mode_group.addButton(self.manual_mode_radio, 2)
        mode_layout.addWidget(self.manual_mode_radio)

        layout.addWidget(mode_group)

        # 随机选择数量设置
        random_group = QGroupBox("随机选择设置")
        random_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        random_layout = QHBoxLayout(random_group)

        random_layout.addWidget(QLabel("选择数量:"))
        self.random_count_spin = QSpinBox()
        self.random_count_spin.setMinimum(1)
        self.random_count_spin.setMaximum(len(self.video_files))
        self.random_count_spin.setValue(min(10, len(self.video_files)))
        self.random_count_spin.setEnabled(False)
        random_layout.addWidget(self.random_count_spin)

        random_layout.addWidget(QLabel("个视频"))
        random_layout.addStretch()

        layout.addWidget(random_group)

        # 视频文件列表
        list_group = QGroupBox("视频文件列表")
        list_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        list_layout = QVBoxLayout(list_group)

        self.video_list = QListWidget()
        self.video_list.setSelectionMode(QListWidget.MultiSelection)
        self.populate_video_list()
        list_layout.addWidget(self.video_list)

        layout.addWidget(list_group)

        # 按钮
        button_layout = QHBoxLayout()

        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.clicked.connect(self.select_all_videos)
        button_layout.addWidget(self.select_all_btn)

        self.select_none_btn = QPushButton("全不选")
        self.select_none_btn.clicked.connect(self.select_none_videos)
        button_layout.addWidget(self.select_none_btn)

        self.random_select_btn = QPushButton("随机选择")
        self.random_select_btn.clicked.connect(self.random_select_videos)
        self.random_select_btn.setEnabled(False)
        button_layout.addWidget(self.random_select_btn)

        button_layout.addStretch()

        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept_selection)
        self.ok_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #27ae60, stop:1 #2ecc71);
                color: white;
                font-weight: bold;
                padding: 8px 20px;
                border-radius: 6px;
                border: none;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2ecc71, stop:1 #27ae60);
            }
        """)
        button_layout.addWidget(self.ok_btn)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                font-weight: bold;
                padding: 8px 20px;
                border-radius: 6px;
                border: none;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c0392b, stop:1 #e74c3c);
            }
        """)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)

    def setup_connections(self):
        """设置信号连接"""
        self.mode_group.buttonClicked.connect(self.on_mode_changed)

    def on_mode_changed(self, button):
        """选择模式改变时的处理"""
        mode_id = self.mode_group.id(button)

        if mode_id == 0:  # 全部选择
            self.random_count_spin.setEnabled(False)
            self.random_select_btn.setEnabled(False)
            self.video_list.setEnabled(False)
            self.select_all_btn.setEnabled(False)
            self.select_none_btn.setEnabled(False)
            self.select_all_videos()

        elif mode_id == 1:  # 随机选择
            self.random_count_spin.setEnabled(True)
            self.random_select_btn.setEnabled(True)
            self.video_list.setEnabled(False)
            self.select_all_btn.setEnabled(False)
            self.select_none_btn.setEnabled(False)
            self.random_select_videos()

        elif mode_id == 2:  # 手动选择
            self.random_count_spin.setEnabled(False)
            self.random_select_btn.setEnabled(False)
            self.video_list.setEnabled(True)
            self.select_all_btn.setEnabled(True)
            self.select_none_btn.setEnabled(True)
            self.select_none_videos()

    def populate_video_list(self):
        """填充视频文件列表"""
        self.video_list.clear()

        for video_file in self.video_files:
            # 获取文件信息
            file_name = os.path.basename(video_file)
            try:
                file_size = os.path.getsize(video_file)
                size_str = self.format_file_size(file_size)
            except:
                size_str = "未知大小"

            # 创建列表项
            item_text = f"{file_name} ({size_str})"
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, video_file)  # 存储完整路径

            # 设置工具提示
            item.setToolTip(f"完整路径: {video_file}\n文件大小: {size_str}")

            self.video_list.addItem(item)

    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

    def select_all_videos(self):
        """全选视频"""
        for i in range(self.video_list.count()):
            item = self.video_list.item(i)
            item.setSelected(True)

    def select_none_videos(self):
        """全不选视频"""
        for i in range(self.video_list.count()):
            item = self.video_list.item(i)
            item.setSelected(False)

    def random_select_videos(self):
        """随机选择视频"""
        import random

        # 先全不选
        self.select_none_videos()

        # 随机选择指定数量的视频
        count = self.random_count_spin.value()
        total_count = self.video_list.count()

        if count >= total_count:
            # 如果要选择的数量大于等于总数，全选
            self.select_all_videos()
        else:
            # 随机选择
            indices = random.sample(range(total_count), count)
            for index in indices:
                item = self.video_list.item(index)
                item.setSelected(True)

    def accept_selection(self):
        """确认选择"""
        mode_id = self.mode_group.checkedId()

        if mode_id == 0:  # 全部选择
            self.selected_files = self.video_files.copy()
        elif mode_id == 1:  # 随机选择
            # 获取当前选中的项目
            self.selected_files = []
            for i in range(self.video_list.count()):
                item = self.video_list.item(i)
                if item.isSelected():
                    file_path = item.data(Qt.UserRole)
                    self.selected_files.append(file_path)
        elif mode_id == 2:  # 手动选择
            # 获取手动选中的项目
            self.selected_files = []
            for i in range(self.video_list.count()):
                item = self.video_list.item(i)
                if item.isSelected():
                    file_path = item.data(Qt.UserRole)
                    self.selected_files.append(file_path)

        if not self.selected_files:
            QMessageBox.warning(self, "警告", "请至少选择一个视频文件！")
            return

        self.accept()

    def get_selected_files(self):
        """获取选中的文件列表"""
        return self.selected_files


if __name__ == '__main__':
    main()

