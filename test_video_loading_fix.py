#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试视频加载修复
验证safe_load_video方法是否能解决视频加载卡住的问题
"""

import os
import sys
import time
from video_processor import VideoProcessor
from utils import log_info, log_error

def test_safe_video_loading():
    """测试安全视频加载功能"""
    print("=" * 60)
    print("测试安全视频加载功能")
    print("=" * 60)
    
    processor = VideoProcessor()
    
    # 测试视频路径（使用用户提到的路径）
    test_video_path = "D:/25125/Videos/www333.mp4"
    
    print(f"测试视频路径: {test_video_path}")
    
    # 检查文件是否存在
    if not os.path.exists(test_video_path):
        print(f"❌ 测试视频文件不存在: {test_video_path}")
        print("请确保视频文件存在，或修改test_video_path变量")
        return False
    
    # 获取文件信息
    file_size = os.path.getsize(test_video_path)
    print(f"文件大小: {file_size / (1024*1024):.2f} MB")
    
    # 测试安全加载
    print("\n开始测试安全视频加载...")
    start_time = time.time()
    
    try:
        video_clip = processor.safe_load_video(test_video_path, timeout=120)  # 2分钟超时
        
        load_time = time.time() - start_time
        print(f"加载耗时: {load_time:.2f}秒")
        
        if video_clip:
            print(f"✅ 视频加载成功!")
            print(f"   时长: {video_clip.duration:.2f}秒")
            print(f"   分辨率: {video_clip.size}")
            print(f"   帧率: {video_clip.fps}")
            
            # 清理资源
            video_clip.close()
            return True
        else:
            print("❌ 视频加载失败")
            return False
            
    except Exception as e:
        load_time = time.time() - start_time
        print(f"❌ 视频加载异常: {str(e)}")
        print(f"   耗时: {load_time:.2f}秒")
        return False

def test_timeout_mechanism():
    """测试超时机制"""
    print("\n" + "=" * 60)
    print("测试超时机制")
    print("=" * 60)
    
    processor = VideoProcessor()
    
    # 测试不存在的文件
    fake_path = "non_existent_video.mp4"
    print(f"测试不存在的文件: {fake_path}")
    
    start_time = time.time()
    video_clip = processor.safe_load_video(fake_path, timeout=5)
    load_time = time.time() - start_time
    
    if video_clip is None and load_time < 10:
        print(f"✅ 超时机制正常: {load_time:.2f}秒内返回None")
        return True
    else:
        print(f"❌ 超时机制异常: 耗时{load_time:.2f}秒")
        return False

def test_progress_callback_integration():
    """测试进度回调集成"""
    print("\n" + "=" * 60)
    print("测试进度回调集成")
    print("=" * 60)
    
    progress_updates = []
    
    def test_progress_callback(current, total, message):
        progress_updates.append({
            'current': current,
            'total': total,
            'message': message,
            'timestamp': time.time()
        })
        print(f"进度更新: {current}/{total} ({current/total*100:.1f}%) - {message}")
    
    # 模拟replace_and_concatenate_videos调用
    print("模拟视频合成进度回调...")
    
    # 模拟各个阶段的进度更新
    test_progress_callback(5, 100, "初始化视频处理...")
    test_progress_callback(15, 100, "正在加载主视频文件...")
    test_progress_callback(35, 100, "正在加载辅助视频文件...")
    test_progress_callback(40, 100, "开始处理 7 个替换片段...")
    test_progress_callback(70, 100, "开始合成视频...")
    test_progress_callback(80, 100, "正在写入视频文件...")
    test_progress_callback(90, 100, "正在保存最终视频文件...")
    test_progress_callback(100, 100, "视频合成完成！")
    
    print(f"\n✅ 进度回调测试完成，共 {len(progress_updates)} 次更新")
    return len(progress_updates) == 8

def main():
    """主测试函数"""
    print("🔧 视频加载修复测试")
    print("=" * 80)
    
    try:
        test_results = []
        
        # 1. 测试安全视频加载
        loading_result = test_safe_video_loading()
        test_results.append(("安全视频加载", loading_result))
        
        # 2. 测试超时机制
        timeout_result = test_timeout_mechanism()
        test_results.append(("超时机制", timeout_result))
        
        # 3. 测试进度回调集成
        progress_result = test_progress_callback_integration()
        test_results.append(("进度回调集成", progress_result))
        
        # 输出测试结果
        print("\n" + "=" * 80)
        print("🎯 测试结果汇总")
        print("=" * 80)
        
        all_passed = True
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name:20} : {status}")
            if not result:
                all_passed = False
        
        print("\n" + "=" * 80)
        if all_passed:
            print("🎉 所有测试通过！视频加载修复应该能解决卡住问题。")
            print("\n📋 修复内容总结:")
            print("1. ✅ 添加了safe_load_video方法，带超时控制")
            print("2. ✅ 详细的视频加载日志记录")
            print("3. ✅ 文件大小检测和动态超时调整")
            print("4. ✅ 视频有效性验证（时长、帧获取）")
            print("5. ✅ 线程安全的加载机制")
            print("6. ✅ 集成到replace_and_concatenate_videos方法")
            
            print("\n🚀 建议操作:")
            print("1. 重启应用程序以应用修复")
            print("2. 重新尝试相似度策略合成")
            print("3. 观察详细的加载日志")
            print("4. 如果仍有问题，检查视频文件格式")
        else:
            print("❌ 部分测试失败，需要进一步检查")
            
    except Exception as e:
        log_error(f"测试过程中发生错误: {str(e)}")
        print(f"❌ 测试失败: {str(e)}")
        return False
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
