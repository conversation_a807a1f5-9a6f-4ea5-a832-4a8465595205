# 智能视频反推工具配置文件示例
# 
# 这个文件展示了如何通过配置文件设置默认参数
# 复制此文件为 config.ini 并修改参数值

[DEFAULT]
# 场景检测敏感度 (10-100)
scene_threshold = 30

# 相似度匹配精度 (0.50-0.95)
similarity_threshold = 0.80

# 特征提取帧数 (5-50)
feature_frames = 10

# 处理线程数 (1-16)
thread_count = 4

# 是否启用性能优化
enable_optimization = true

# 输出视频质量 (high/standard/fast)
output_quality = standard

[PATHS]
# 默认输入目录
default_input_dir = ./input

# 默认输出目录  
default_output_dir = ./output

# 临时文件目录
temp_dir = ./temp

[ADVANCED]
# 是否启用音频处理
enable_audio_processing = false

# 是否启用智能裁剪
enable_smart_crop = false

# 日志级别 (DEBUG/INFO/WARNING/ERROR)
log_level = INFO
