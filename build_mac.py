#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mac自动化打包脚本
使用PyInstaller将程序打包为Mac应用程序
"""

import os
import sys
import shutil
import subprocess
import platform
from pathlib import Path

# 导入日志系统
try:
    from logger_system import log_info, log_error, log_warning, log_debug
except ImportError:
    # 备用函数
    def log_info(msg): print(msg)
    def log_error(msg): print(f"ERROR: {msg}")
    def log_warning(msg): print(f"WARNING: {msg}")
    def log_debug(msg): print(f"DEBUG: {msg}")

class MacBuildManager:
    """Mac打包管理器"""
    
    def __init__(self):
        self.app_name = "余下视频混剪工具"
        self.app_name_en = "VideoMixingTool"
        self.version = "1.0"
        self.main_script = "enhanced_video_deduplication_gui.py"
        self.build_dir = "build"
        self.dist_dir = "dist"
        self.spec_file = f"{self.app_name_en}.spec"
        self.app_bundle = f"{self.app_name_en}.app"
        
    def check_environment(self):
        """检查Mac打包环境"""
        print(f"🔍 检查Mac打包环境...")
        
        # 检查操作系统
        if platform.system() != "Darwin":
            print(f"❌ 当前系统: {platform.system()}")
            print("此脚本仅适用于macOS系统")
            return False
        print(f"✅ 操作系统: macOS {platform.mac_ver()[0]}")
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version < (3, 8):
            print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
            print("需要Python 3.8或更高版本")
            return False
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # 检查PyInstaller
        try:
            import PyInstaller
            print(f"✅ PyInstaller版本: {PyInstaller.__version__}")
        except ImportError:
            print(f"❌ 未安装PyInstaller")
            print("请运行: pip install pyinstaller")
            return False
        
        # 检查主程序文件
        if not os.path.exists(self.main_script):
            print(f"❌ 未找到主程序文件: {self.main_script}")
            return False
        print(f"✅ 找到主程序文件: {self.main_script}")
        
        # 检查关键依赖
        required_modules = [
            'PyQt5', 'cv2', 'numpy', 'moviepy', 'scenedetect'
        ]
        
        missing_modules = []
        for module in required_modules:
            try:
                __import__(module)
                print(f"✅ {module}")
            except ImportError:
                missing_modules.append(module)
                print(f"❌ {module}")
        
        if missing_modules:
            print(f"缺少依赖模块: {', '.join(missing_modules)}")
            print("请运行: pip install -r requirements.txt")
            return False
        
        # 检查Homebrew和必要工具
        self.check_homebrew_tools()
        
        return True
    
    def check_homebrew_tools(self):
        """检查Homebrew和必要工具"""
        print("🍺 检查Homebrew工具...")
        
        # 检查ffmpeg
        try:
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print("✅ FFmpeg已安装")
            else:
                print("⚠️ FFmpeg可能未正确安装")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("❌ 未找到FFmpeg")
            print("建议安装: brew install ffmpeg")
        
        # 检查ImageMagick
        try:
            result = subprocess.run(['magick', '-version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print("✅ ImageMagick已安装")
            else:
                print("⚠️ ImageMagick可能未正确安装")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("❌ 未找到ImageMagick")
            print("建议安装: brew install imagemagick")
    
    def clean_build(self):
        """清理构建目录"""
        print("🧹 清理构建目录...")
        
        dirs_to_clean = [self.build_dir, self.dist_dir]
        files_to_clean = [self.spec_file]
        
        for dir_path in dirs_to_clean:
            if os.path.exists(dir_path):
                shutil.rmtree(dir_path)
                print(f"✅ 已清理目录: {dir_path}")
        
        for file_path in files_to_clean:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"✅ 已清理文件: {file_path}")
    
    def generate_spec_file(self):
        """生成Mac专用的spec文件"""
        print("📝 生成Mac专用spec文件...")
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 收集数据文件
datas = []

# 添加图标和配置文件
if os.path.exists('img'):
    datas.append(('img', 'img'))
if os.path.exists('config.ini'):
    datas.append(('config.ini', '.'))
if os.path.exists('README.md'):
    datas.append(('README.md', '.'))

# 收集moviepy数据
try:
    moviepy_datas = collect_data_files('moviepy')
    datas.extend(moviepy_datas)
except:
    pass

# 收集scenedetect数据
try:
    scenedetect_datas = collect_data_files('scenedetect')
    datas.extend(scenedetect_datas)
except:
    pass

# 隐藏导入
hiddenimports = [
    'cv2',
    'numpy',
    'moviepy.editor',
    'moviepy.video.io.VideoFileClip',
    'moviepy.audio.io.AudioFileClip',
    'scenedetect',
    'scenedetect.detectors',
    'scenedetect.video_manager',
    'PyQt5.QtNetwork',
    'PyQt5.QtMultimedia',
    'PyQt5.QtMultimediaWidgets',
    'PIL',
    'PIL.Image',
    'imageio',
    'imageio_ffmpeg',
    'tqdm',
    'pandas',
    'sklearn',
    'librosa',
    'soundfile',
    'scipy',
    'numba',
    'psutil',
    'platformdirs',
    'dotenv',
]

# 收集子模块
try:
    moviepy_modules = collect_submodules('moviepy')
    hiddenimports.extend(moviepy_modules)
except:
    pass

try:
    scenedetect_modules = collect_submodules('scenedetect')
    hiddenimports.extend(scenedetect_modules)
except:
    pass

block_cipher = None

a = Analysis(
    ['{self.main_script}'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{self.app_name_en}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='img/logo.ico' if os.path.exists('img/logo.ico') else None,
)

app = BUNDLE(
    exe,
    name='{self.app_bundle}',
    icon='img/logo.ico' if os.path.exists('img/logo.ico') else None,
    bundle_identifier='com.videomixingtool.app',
    version='{self.version}',
    info_plist={{
        'CFBundleName': '{self.app_name}',
        'CFBundleDisplayName': '{self.app_name}',
        'CFBundleVersion': '{self.version}',
        'CFBundleShortVersionString': '{self.version}',
        'NSHighResolutionCapable': True,
        'NSRequiresAquaSystemAppearance': False,
        'LSMinimumSystemVersion': '10.14.0',
        'NSCameraUsageDescription': '此应用需要访问摄像头以进行视频处理',
        'NSMicrophoneUsageDescription': '此应用需要访问麦克风以进行音频处理',
        'NSDocumentsFolderUsageDescription': '此应用需要访问文档文件夹以保存处理结果',
        'NSDesktopFolderUsageDescription': '此应用需要访问桌面文件夹以保存处理结果',
        'NSDownloadsFolderUsageDescription': '此应用需要访问下载文件夹以保存处理结果',
    }},
)
'''
        
        with open(self.spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        print(f"✅ 已生成spec文件: {self.spec_file}")
        return True
    
    def build_app(self):
        """构建Mac应用程序"""
        print("🚀 开始构建Mac应用程序...")
        
        try:
            # 使用spec文件构建
            cmd = ['pyinstaller', '--clean', self.spec_file]
            
            print(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            
            print("✅ 构建成功!")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 构建失败: {e}")
            print(f"错误输出: {e.stderr}")
            return False
    
    def post_build_setup(self):
        """构建后设置"""
        print("🔧 执行构建后设置...")
        
        app_path = os.path.join(self.dist_dir, self.app_bundle)
        
        if not os.path.exists(app_path):
            print(f"❌ 未找到应用程序包: {app_path}")
            return False
        
        # 设置应用程序权限
        try:
            subprocess.run(['chmod', '+x', 
                          os.path.join(app_path, 'Contents', 'MacOS', self.app_name_en)], 
                          check=True)
            print("✅ 已设置执行权限")
        except subprocess.CalledProcessError as e:
            print(f"⚠️ 设置权限失败: {e}")
        
        # 创建DMG文件（可选）
        self.create_dmg()
        
        return True
    
    def create_dmg(self):
        """创建DMG安装包"""
        print("📦 创建DMG安装包...")
        
        try:
            dmg_name = f"{self.app_name_en}_v{self.version}_macOS.dmg"
            dmg_path = os.path.join(self.dist_dir, dmg_name)
            
            # 删除已存在的DMG
            if os.path.exists(dmg_path):
                os.remove(dmg_path)
            
            # 创建DMG
            cmd = [
                'hdiutil', 'create',
                '-volname', self.app_name,
                '-srcfolder', os.path.join(self.dist_dir, self.app_bundle),
                '-ov', '-format', 'UDZO',
                dmg_path
            ]
            
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(f"✅ 已创建DMG: {dmg_name}")
            
        except subprocess.CalledProcessError as e:
            print(f"⚠️ 创建DMG失败: {e}")
        except FileNotFoundError:
            print("⚠️ 未找到hdiutil命令，跳过DMG创建")
    
    def run_build(self, clean=True):
        """执行完整构建流程"""
        print("=" * 50)
        print(f"🎬 {self.app_name} v{self.version} Mac自动打包程序")
        print("=" * 50)
        
        # 检查环境
        if not self.check_environment():
            return False
        
        # 清理构建目录
        if clean:
            self.clean_build()
        
        # 生成spec文件
        if not self.generate_spec_file():
            return False
        
        # 构建应用程序
        if not self.build_app():
            return False
        
        # 构建后设置
        if not self.post_build_setup():
            return False
        
        print("\n" + "=" * 50)
        print("🎉 Mac应用程序打包完成!")
        print("=" * 50)
        print(f"📁 输出目录: {self.dist_dir}/")
        print(f"📱 应用程序: {self.app_bundle}")
        print(f"📦 安装包: {self.app_name_en}_v{self.version}_macOS.dmg")
        print("\n💡 使用建议:")
        print("1. 在Finder中双击.app文件测试应用程序")
        print("2. 测试所有功能是否正常工作")
        print("3. 在不同macOS版本上测试兼容性")
        print("4. 如需分发，考虑进行代码签名")
        
        return True

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Mac自动化打包脚本')
    parser.add_argument('--no-clean', action='store_true', 
                       help='不清理构建目录（快速构建）')
    
    args = parser.parse_args()
    
    builder = MacBuildManager()
    success = builder.run_build(clean=not args.no_clean)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
