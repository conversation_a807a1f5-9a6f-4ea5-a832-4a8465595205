package com.example.activation.mapper;

import com.example.activation.entity.ActivationCode;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 激活码Mapper接口
 */
@Mapper
public interface ActivationCodeMapper {
    
    /**
     * 根据激活码查询
     */
    @Select("SELECT * FROM activation_code WHERE activation_code = #{activationCode}")
    ActivationCode findByActivationCode(@Param("activationCode") String activationCode);
    
    /**
     * 根据机器码查询
     */
    @Select("SELECT * FROM activation_code WHERE machine_code = #{machineCode}")
    List<ActivationCode> findByMachineCode(@Param("machineCode") String machineCode);
    
    /**
     * 根据激活码和机器码查询
     */
    @Select("SELECT * FROM activation_code WHERE activation_code = #{activationCode} AND machine_code = #{machineCode}")
    ActivationCode findByActivationCodeAndMachineCode(@Param("activationCode") String activationCode, 
                                                     @Param("machineCode") String machineCode);
    
    /**
     * 根据ID查询
     */
    @Select("SELECT * FROM activation_code WHERE id = #{id}")
    ActivationCode findById(@Param("id") Long id);
    
    /**
     * 查询所有激活码
     */
    @Select("SELECT * FROM activation_code ORDER BY create_time DESC")
    List<ActivationCode> findAll();
    
    /**
     * 根据状态查询
     */
    @Select("SELECT * FROM activation_code WHERE status = #{status} ORDER BY create_time DESC")
    List<ActivationCode> findByStatus(@Param("status") Integer status);
    
    /**
     * 查询已过期的激活码
     */
    @Select("SELECT * FROM activation_code WHERE expire_time IS NOT NULL AND expire_time < NOW()")
    List<ActivationCode> findExpiredCodes();
    
    /**
     * 插入激活码
     */
    @Insert("INSERT INTO activation_code (activation_code, machine_code, user_id, status, " +
            "create_time, update_time, expire_time, max_activations, current_activations, remark) " +
            "VALUES (#{activationCode}, #{machineCode}, #{userId}, #{status}, " +
            "#{createTime}, #{updateTime}, #{expireTime}, #{maxActivations}, #{currentActivations}, #{remark})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(ActivationCode activationCode);
    
    /**
     * 更新激活码
     */
    @Update("UPDATE activation_code SET " +
            "machine_code = #{machineCode}, " +
            "user_id = #{userId}, " +
            "status = #{status}, " +
            "activation_time = #{activationTime}, " +
            "update_time = #{updateTime}, " +
            "expire_time = #{expireTime}, " +
            "max_activations = #{maxActivations}, " +
            "current_activations = #{currentActivations}, " +
            "remark = #{remark} " +
            "WHERE id = #{id}")
    int update(ActivationCode activationCode);
    
    /**
     * 更新激活状态
     */
    @Update("UPDATE activation_code SET " +
            "status = #{status}, " +
            "activation_time = #{activationTime}, " +
            "update_time = #{updateTime}, " +
            "current_activations = current_activations + 1 " +
            "WHERE id = #{id}")
    int updateActivationStatus(@Param("id") Long id, 
                              @Param("status") Integer status, 
                              @Param("activationTime") LocalDateTime activationTime,
                              @Param("updateTime") LocalDateTime updateTime);
    
    /**
     * 更新机器码
     */
    @Update("UPDATE activation_code SET " +
            "machine_code = #{machineCode}, " +
            "update_time = #{updateTime} " +
            "WHERE id = #{id}")
    int updateMachineCode(@Param("id") Long id, 
                         @Param("machineCode") String machineCode,
                         @Param("updateTime") LocalDateTime updateTime);
    
    /**
     * 禁用激活码
     */
    @Update("UPDATE activation_code SET " +
            "status = 2, " +
            "update_time = #{updateTime} " +
            "WHERE id = #{id}")
    int disableActivationCode(@Param("id") Long id, @Param("updateTime") LocalDateTime updateTime);
    
    /**
     * 启用激活码
     */
    @Update("UPDATE activation_code SET " +
            "status = 0, " +
            "update_time = #{updateTime} " +
            "WHERE id = #{id}")
    int enableActivationCode(@Param("id") Long id, @Param("updateTime") LocalDateTime updateTime);
    
    /**
     * 根据ID删除
     */
    @Delete("DELETE FROM activation_code WHERE id = #{id}")
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据激活码删除
     */
    @Delete("DELETE FROM activation_code WHERE activation_code = #{activationCode}")
    int deleteByActivationCode(@Param("activationCode") String activationCode);
    
    /**
     * 批量删除过期的激活码
     */
    @Delete("DELETE FROM activation_code WHERE expire_time IS NOT NULL AND expire_time < NOW()")
    int deleteExpiredCodes();
    
    /**
     * 统计激活码数量
     */
    @Select("SELECT COUNT(*) FROM activation_code")
    int countAll();
    
    /**
     * 根据状态统计数量
     */
    @Select("SELECT COUNT(*) FROM activation_code WHERE status = #{status}")
    int countByStatus(@Param("status") Integer status);
    
    /**
     * 统计已过期的激活码数量
     */
    @Select("SELECT COUNT(*) FROM activation_code WHERE expire_time IS NOT NULL AND expire_time < NOW()")
    int countExpiredCodes();
    
    /**
     * 检查激活码是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM activation_code WHERE activation_code = #{activationCode}")
    boolean existsByActivationCode(@Param("activationCode") String activationCode);
    
    /**
     * 检查机器码是否已激活
     */
    @Select("SELECT COUNT(*) > 0 FROM activation_code WHERE machine_code = #{machineCode} AND status = 1")
    boolean isActivatedByMachineCode(@Param("machineCode") String machineCode);
    
    /**
     * 分页查询激活码
     */
    @Select("SELECT * FROM activation_code ORDER BY create_time DESC LIMIT #{offset}, #{limit}")
    List<ActivationCode> findWithPagination(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 根据用户ID查询激活码
     */
    @Select("SELECT * FROM activation_code WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<ActivationCode> findByUserId(@Param("userId") String userId);
}
