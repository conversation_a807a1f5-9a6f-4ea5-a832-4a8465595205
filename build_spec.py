#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller打包配置生成器
自动生成适合该项目的PyInstaller配置文件
"""

import os
import sys
import platform

def generate_spec_file():
    """生成PyInstaller spec文件"""
    
    # 基本配置
    app_name = "余下视频混剪工具"
    main_script = "enhanced_video_deduplication_gui.py"
    
    # 检查文件是否存在的函数
    def check_file_exists(file_path):
        if os.path.exists(file_path):
            return file_path
        else:
            print(f"⚠️ 文件不存在，跳过: {file_path}")
            return None

    # 数据文件和资源
    datas = []

    # 基础文件
    basic_files = [
        ('img', 'img'),  # 图标文件夹
        ('config.ini', '.'),  # 配置文件
        ('config_example.ini', '.'),  # 配置示例
        ('余下视频混剪工具v2.0完整说明书.md', '.'),  # 说明书
        ('README.md', '.'),  # 说明文档
    ]

    # 激活验证相关文件（这些是Python模块，不需要作为数据文件包含）
    # PyInstaller会自动处理Python模块的导入

    # 只添加存在的文件
    for src, dst in basic_files:
        if os.path.exists(src):
            datas.append((src, dst))
        else:
            print(f"⚠️ 文件不存在，跳过: {src}")

    print(f"📦 将包含 {len(datas)} 个数据文件")
    
    # 隐藏导入（PyInstaller可能无法自动检测的模块）
    hiddenimports = [
        'cv2',
        'numpy',
        'moviepy.editor',
        'moviepy.video.io.VideoFileClip',
        'moviepy.audio.io.AudioFileClip',
        'scenedetect',
        'scenedetect.detectors',
        'scenedetect.detectors.content_detector',
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'PyQt5.QtNetwork',
        'PIL',
        'PIL.Image',
        'imageio',
        'imageio_ffmpeg',
        'tqdm',
        'pandas',
        'sklearn',
        'matplotlib',
        'seaborn',
        'librosa',
        'scipy',
        'soundfile',
        'numba',
        'psutil',
        'platformdirs',
        'dotenv',
        'pydub',
        'SpeechRecognition',
        'whisper',
        'performance_optimizer',
        'video_processor',
        'multi_rectangle_selector',
        'alternative_subtitle_system',
        'logger_system',
        # 激活验证相关模块
        'activation_dialog',
        'machine_code_generator',
        'api_manager',
        'api_config',
        'api_secrets',
        'requests',
        'requests.adapters',
        'requests.auth',
        'requests.exceptions',
        'urllib3',
        'urllib3.poolmanager',
        'urllib3.util',
        'urllib3.util.retry',
        'json',
        'hashlib',
        'uuid',
        'platform',
        'socket',
        'subprocess',
        'time',
        'datetime',
        'base64',
        'hmac'
    ]
    
    # 排除的模块（减少打包大小）
    excludes = [
        'tkinter',
        'unittest',
        'test',
        'tests',
        'pytest',
        'black',
        'flake8',
        'jupyter',
        'notebook',
        'IPython'
    ]
    
    # 二进制文件（可能需要的DLL等）
    binaries = []
    
    # 根据操作系统添加特定配置
    if platform.system() == "Windows":
        # Windows特定配置
        icon_file = 'img/logo.ico'
        console = False  # 不显示控制台窗口
        
        # 添加可能需要的Windows DLL
        binaries.extend([
            # 如果有特定的DLL需要，在这里添加
        ])
    else:
        # Linux/Mac配置
        icon_file = 'img/logo.png'
        console = False
    
    # 生成spec文件内容
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['{main_script}'],
    pathex=[],
    binaries={binaries},
    datas={datas},
    hiddenimports={hiddenimports},
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes={excludes},
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{app_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console={console},
    disable_windowed_traceback=True,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='{icon_file}',
)
'''
    
    # 写入spec文件
    spec_filename = f"{app_name}.spec"
    with open(spec_filename, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print(f"✅ 已生成PyInstaller配置文件: {spec_filename}")
    return spec_filename

def main():
    """主函数"""
    print("🚀 PyInstaller打包配置生成器")
    print("=" * 50)
    
    # 检查环境
    try:
        import PyInstaller
        print(f"✅ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print(f"❌ 未安装PyInstaller，请先安装:")
        print("pip install pyinstaller")
        return False
    
    # 检查主程序文件
    if not os.path.exists("enhanced_video_deduplication_gui.py"):
        print(f"❌ 未找到主程序文件: enhanced_video_deduplication_gui.py")
        return False
    
    # 检查图标文件
    if not os.path.exists("img/logo.ico"):
        print(f"⚠️ 未找到图标文件: img/logo.ico")
    
    # 生成spec文件
    spec_file = generate_spec_file()
    
    print("\n📋 打包命令:")
    print(f"pyinstaller {spec_file}")
    
    print("\n💡 打包建议:")
    print("1. 确保所有依赖库已正确安装")
    print("2. 在虚拟环境中进行打包以减少文件大小")
    print("3. 打包前测试程序是否正常运行")
    print("4. 打包完成后测试可执行文件")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
