# 智能视频反推工具依赖包列表

# 核心依赖
PyQt5>=5.15.0
opencv-python>=4.5.0
moviepy>=2.0.0
scenedetect>=0.6.0
numpy>=1.20.0

# 图像处理
Pillow>=8.0.0
imageio>=2.9.0
imageio-ffmpeg>=0.4.0

# 数据处理
pandas>=1.3.0
tqdm>=4.60.0

# 系统工具
platformdirs>=2.0.0
python-dotenv>=0.19.0

# 可选依赖（用于高级功能）
scikit-learn>=1.0.0  # 机器学习算法
matplotlib>=3.4.0    # 数据可视化
seaborn>=0.11.0      # 统计图表

# 字幕提取和语音识别
openai-whisper>=20231117  # AI语音识别（推荐）
SpeechRecognition>=3.10.0  # 语音识别库
pyaudio>=0.2.11      # 音频处理（可选，用于麦克风输入）
pydub>=0.25.1        # 音频格式转换

# 音频处理库（用于Whisper音频加载）
librosa>=0.10.0      # 音频分析库（推荐）
scipy>=1.9.0         # 科学计算库（包含音频I/O）
soundfile>=0.12.1    # 音频文件读写库

# 性能加速库
numba>=0.56.0        # JIT编译加速
psutil>=5.8.0        # 系统资源监控

# GPU加速支持（可选）
# opencv-contrib-python-headless>=4.5.0  # 包含CUDA支持的OpenCV（需要CUDA环境）
# cupy-cuda11x>=10.0.0  # GPU数组处理（需要CUDA 11.x）
# tensorrt>=8.0.0      # NVIDIA TensorRT（需要NVIDIA GPU）

# 开发工具（可选）
pytest>=6.0.0       # 单元测试
black>=21.0.0        # 代码格式化
flake8>=3.9.0        # 代码检查

