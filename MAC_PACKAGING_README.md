# Mac自动化打包脚本使用说明

## 📋 概述

本项目提供了完整的Mac自动化打包解决方案，可以将Python应用程序打包为原生的macOS应用程序(.app)和安装包(.dmg)。

## 📁 文件说明

### 核心文件
- `build_mac.py` - Mac自动化打包主程序
- `build_spec_mac.py` - Mac专用spec文件生成器
- `打包程序_mac.sh` - 交互式打包脚本
- `快速打包_mac.sh` - 快速打包脚本

## 🚀 快速开始

### 1. 环境准备

在macOS系统上执行以下步骤：

```bash
# 1. 设置脚本执行权限
chmod +x 打包程序_mac.sh 快速打包_mac.sh

# 2. 安装Homebrew（如果未安装）
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 3. 安装必要工具
brew install ffmpeg imagemagick

# 4. 安装Python依赖
pip3 install -r requirements.txt
pip3 install pyinstaller
```

### 2. 打包方式

#### 方式一：交互式打包（推荐）
```bash
./打包程序_mac.sh
```

选择打包选项：
- `1` - 完整打包（推荐）
- `2` - 快速打包（不清理缓存）
- `3` - 仅生成spec配置文件
- `4` - 查看手动打包命令
- `5` - 安装依赖环境
- `6` - 检查系统环境

#### 方式二：快速打包
```bash
./快速打包_mac.sh
```

#### 方式三：Python脚本打包
```bash
# 完整打包
python3 build_mac.py

# 快速打包（不清理缓存）
python3 build_mac.py --no-clean
```

#### 方式四：手动打包
```bash
# 生成spec文件
python3 build_spec_mac.py

# 使用spec文件打包
pyinstaller VideoMixingTool.spec
```

## 📦 输出文件

打包完成后，在`dist/`目录下会生成：

- `VideoMixingTool.app` - macOS应用程序包
- `VideoMixingTool_v2.0_macOS.dmg` - 安装包（如果成功创建）

## 🔧 高级配置

### 应用程序信息
- **应用名称**: 余下视频混剪工具
- **英文名称**: VideoMixingTool
- **Bundle ID**: com.videomixingtool.app
- **版本**: 2.0
- **最低系统要求**: macOS 10.14 (Mojave)

### 权限配置
应用程序会请求以下权限：
- 摄像头访问（视频处理）
- 麦克风访问（音频处理）
- 文档文件夹访问
- 桌面文件夹访问
- 下载文件夹访问
- 外部存储设备访问

### 支持的文件类型
- **视频**: mp4, avi, mov, mkv, wmv, flv, webm
- **音频**: mp3, wav, aac, m4a, flac

## 🛠️ 故障排除

### 常见问题

#### 1. PyInstaller未安装
```bash
pip3 install pyinstaller
```

#### 2. 缺少依赖模块
```bash
pip3 install -r requirements.txt
```

#### 3. FFmpeg未安装
```bash
brew install ffmpeg
```

#### 4. ImageMagick未安装
```bash
brew install imagemagick
```

#### 5. 权限问题
```bash
chmod +x 打包程序_mac.sh 快速打包_mac.sh
```

#### 6. 应用程序无法打开
检查是否需要代码签名：
```bash
codesign --force --deep --sign "Developer ID Application: Your Name" dist/VideoMixingTool.app
```

### 环境检查
运行环境检查脚本：
```bash
python3 -c "from build_mac import MacBuildManager; MacBuildManager().check_environment()"
```

## 📋 手动打包命令参考

### 基础打包
```bash
pyinstaller --onefile --windowed --icon=img/logo.ico enhanced_video_deduplication_gui.py
```

### 完整打包
```bash
pyinstaller --onefile --windowed --icon=img/logo.ico \
  --add-data "img:img" \
  --add-data "config.ini:." \
  --add-data "README.md:." \
  --hidden-import cv2 \
  --hidden-import numpy \
  --hidden-import moviepy.editor \
  --hidden-import scenedetect \
  --hidden-import PyQt5.QtNetwork \
  --osx-bundle-identifier com.videomixingtool.app \
  enhanced_video_deduplication_gui.py
```

### 创建DMG安装包
```bash
hdiutil create -volname "余下视频混剪工具" \
  -srcfolder dist/VideoMixingTool.app \
  -ov -format UDZO VideoMixingTool.dmg
```

## 🔐 代码签名（可选）

如需分发应用程序，建议进行代码签名：

### 1. 获取开发者证书
从Apple Developer Program获取"Developer ID Application"证书

### 2. 签名应用程序
```bash
codesign --force --deep --sign "Developer ID Application: Your Name" dist/VideoMixingTool.app
```

### 3. 验证签名
```bash
codesign --verify --verbose dist/VideoMixingTool.app
```

### 4. 公证（可选）
```bash
xcrun altool --notarize-app \
  --primary-bundle-id "com.videomixingtool.app" \
  --username "<EMAIL>" \
  --password "@keychain:AC_PASSWORD" \
  --file VideoMixingTool.dmg
```

## 📊 系统要求

### 开发环境
- macOS 10.14 或更高版本
- Python 3.8 或更高版本
- Xcode Command Line Tools
- Homebrew

### 运行环境
- macOS 10.14 或更高版本
- 至少 4GB RAM
- 至少 2GB 可用磁盘空间

## 📞 技术支持

如遇到问题，请检查：
1. 系统环境是否满足要求
2. 所有依赖是否正确安装
3. 脚本是否有执行权限
4. 查看详细错误信息

## 📝 更新日志

### v2.0
- 添加Mac自动化打包支持
- 优化应用程序Bundle配置
- 添加权限请求配置
- 支持DMG安装包创建
- 添加代码签名支持

---

**注意**: 此脚本专为macOS系统设计，在其他系统上可能无法正常工作。
