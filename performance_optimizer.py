import cv2
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
import multiprocessing
import os
import sys

# 导入日志系统
try:
    from logger_system import log_info, log_error, log_warning, log_debug, safe_print
except ImportError:
    # 备用函数
    def log_info(msg): print(msg)
    def log_error(msg): print(f"ERROR: {msg}")
    def log_warning(msg): print(f"WARNING: {msg}")
    def log_debug(msg): print(f"DEBUG: {msg}")
    def safe_print(*args, **kwargs): print(*args, **kwargs)

class PerformanceOptimizer:
    """性能优化模块，提供多线程和并行处理功能"""
    
    def __init__(self, max_workers=None):
        if max_workers is None:
            self.max_workers = min(8, multiprocessing.cpu_count())
        else:
            self.max_workers = max_workers
    
    def extract_features_parallel(self, video_processor, video_path, scenes, feature_frames=10):
        """
        并行提取多个视频段的特征
        :param video_processor: VideoProcessor实例
        :param video_path: 视频文件路径
        :param scenes: 视频段列表
        :param feature_frames: 特征帧数
        :return: 特征列表
        """
        features_list = []

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_scene = {
                executor.submit(
                    video_processor.extract_frame_features,
                    video_path,
                    scene['start_time'],
                    scene['end_time'],
                    feature_frames
                ): i for i, scene in enumerate(scenes)
            }
            
            # 收集结果
            features_list = [None] * len(scenes)
            for future in as_completed(future_to_scene):
                scene_idx = future_to_scene[future]
                try:
                    features = future.result()
                    features_list[scene_idx] = features
                except Exception as exc:
                    log_info(f'Scene {scene_idx} generated an exception: {exc}')
                    features_list[scene_idx] = []
        
        return features_list
    
    def calculate_similarities_parallel(self, video_processor, main_features_list, aux_features_list):
        """
        并行计算相似度矩阵
        :param video_processor: VideoProcessor实例
        :param main_features_list: 主视频特征列表
        :param aux_features_list: 辅助视频特征列表
        :return: 相似度矩阵
        """
        similarity_matrix = np.zeros((len(main_features_list), len(aux_features_list)))
        
        tasks = []
        for i, main_features in enumerate(main_features_list):
            for j, aux_features in enumerate(aux_features_list):
                tasks.append((i, j, main_features, aux_features))
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_task = {
                executor.submit(
                    video_processor.calculate_similarity, 
                    task[2], 
                    task[3]
                ): task for task in tasks
            }
            
            for future in as_completed(future_to_task):
                task = future_to_task[future]
                i, j = task[0], task[1]
                try:
                    similarity = future.result()
                    similarity_matrix[i, j] = similarity
                except Exception as exc:
                    log_info(f'Similarity calculation for ({i}, {j}) generated an exception: {exc}')
                    similarity_matrix[i, j] = 0.0
        
        return similarity_matrix
    
    def find_best_matches(self, similarity_matrix, similarity_threshold=0.8, max_matches=None):
        """
        从相似度矩阵中找到最佳匹配（优化版本）
        :param similarity_matrix: 相似度矩阵
        :param similarity_threshold: 相似度阈值
        :param max_matches: 最大匹配数量，默认为主视频场景数
        :return: 匹配列表
        """
        matches = []
        used_aux_indices = set()
        used_main_indices = set()

        # 如果没有指定最大匹配数，使用主视频场景数作为限制
        if max_matches is None:
            max_matches = similarity_matrix.shape[0]

        # 按相似度从高到低排序
        indices = np.unravel_index(np.argsort(similarity_matrix, axis=None)[::-1], similarity_matrix.shape)

        for main_idx, aux_idx in zip(indices[0], indices[1]):
            similarity = similarity_matrix[main_idx, aux_idx]

            # 确保每个主场景和辅助场景只匹配一次，且不超过最大匹配数
            if (similarity >= similarity_threshold and
                aux_idx not in used_aux_indices and
                main_idx not in used_main_indices and
                len(matches) < max_matches):

                matches.append({
                    'main_scene_index': int(main_idx),
                    'aux_scene_index': int(aux_idx),
                    'similarity': float(similarity)
                })
                used_aux_indices.add(aux_idx)
                used_main_indices.add(main_idx)

        log_info(f"优化匹配完成: 找到 {len(matches)} 个匹配（限制: {max_matches}）")
        return matches

# 优化后的VideoProcessor
class OptimizedVideoProcessor:
    """优化版本的VideoProcessor，集成性能优化功能"""
    
    def __init__(self, enable_gpu=True, num_threads=None):
        try:
            # 确保当前目录在sys.path中，以便可以导入video_processor
            current_dir = os.path.dirname(os.path.abspath(__file__))
            if current_dir not in sys.path:
                sys.path.append(current_dir)

            from video_processor import VideoProcessor
            self.base_processor = VideoProcessor(enable_gpu=enable_gpu, num_threads=num_threads)
            self.optimizer = PerformanceOptimizer(max_workers=num_threads)
        except ImportError as e:
            log_info(f"Error importing VideoProcessor: {e}")
            raise ImportError(f"无法导入VideoProcessor模块，请确保video_processor.py文件存在且可访问: {e}")
        except Exception as e:
            log_info(f"Error initializing OptimizedVideoProcessor: {e}")
            raise Exception(f"初始化OptimizedVideoProcessor失败: {e}")

    def set_output_quality(self, quality):
        """设置输出质量（委托给基础处理器）"""
        if hasattr(self.base_processor, 'set_output_quality'):
            self.base_processor.set_output_quality(quality)

    def find_similar_scenes(self, main_video_scenes, aux_video_scenes, main_video_path, aux_video_path, similarity_threshold=0.8, feature_frames=10):
        """
        查找相似场景（委托给优化版本）
        """
        return self.find_similar_scenes_optimized(
            main_video_scenes, aux_video_scenes, main_video_path, aux_video_path,
            similarity_threshold, feature_frames
        )
    
    def split_video_into_scenes(self, video_path, threshold=30.0):
        """调用基础处理器的场景切割功能"""
        try:
            return self.base_processor.split_video_into_scenes(video_path, threshold)
        except Exception as e:
            log_info(f"Error in split_video_into_scenes: {e}")
            import traceback
            log_info(traceback.format_exc())
            raise
    
    def extract_frame_features(self, video_path, start_time, end_time, num_frames=10):
        """调用基础处理器的特征提取功能"""
        try:
            return self.base_processor.extract_frame_features(video_path, start_time, end_time, num_frames)
        except Exception as e:
            log_info(f"Error in extract_frame_features: {e}")
            import traceback
            log_info(traceback.format_exc())
            raise
    
    def calculate_similarity(self, features1, features2):
        """调用基础处理器的相似度计算功能"""
        try:
            return self.base_processor.calculate_similarity(features1, features2)
        except Exception as e:
            log_info(f"Error in calculate_similarity: {e}")
            import traceback
            log_info(traceback.format_exc())
            raise
    
    def find_similar_scenes_optimized(self, main_video_scenes, aux_video_scenes, main_video_path, aux_video_path, similarity_threshold=0.8, feature_frames=10):
        """
        优化版本的相似视频段查找，使用并行处理和智能限制
        """
        try:
            main_count = len(main_video_scenes)
            aux_count = len(aux_video_scenes)

            log_info(f"优化相似度匹配: 主视频{main_count}场景, 辅助视频{aux_count}场景")

            # 智能采样：如果辅助视频场景过多，进行采样
            if aux_count > main_count * 5:  # 如果辅助视频场景数超过主视频5倍
                max_aux_scenes = main_count * 3  # 最多处理主视频3倍的场景
                log_info(f"辅助视频场景过多，采样处理: {aux_count} -> {max_aux_scenes}")

                # 均匀采样
                step = aux_count / max_aux_scenes
                sampled_indices = [int(i * step) for i in range(max_aux_scenes)]
                sampled_aux_scenes = [aux_video_scenes[i] for i in sampled_indices]
                aux_scene_index_map = {i: sampled_indices[i] for i in range(len(sampled_indices))}
            else:
                sampled_aux_scenes = aux_video_scenes
                aux_scene_index_map = {i: i for i in range(len(aux_video_scenes))}

            log_info("正在并行提取主视频特征...")
            main_features_list = self.optimizer.extract_features_parallel(
                self.base_processor, main_video_path, main_video_scenes, feature_frames
            )

            log_info("正在并行提取辅助视频特征...")
            aux_features_list = self.optimizer.extract_features_parallel(
                self.base_processor, aux_video_path, sampled_aux_scenes, feature_frames
            )

            log_info("正在并行计算相似度矩阵...")
            similarity_matrix = self.optimizer.calculate_similarities_parallel(
                self.base_processor, main_features_list, aux_features_list
            )

            log_info("正在查找最佳匹配...")
            matches = self.optimizer.find_best_matches(
                similarity_matrix, similarity_threshold, max_matches=main_count
            )

            # 如果进行了采样，需要映射回原始索引
            if aux_count > main_count * 5:
                for match in matches:
                    match['aux_scene_index'] = aux_scene_index_map[match['aux_scene_index']]

            return matches
        except Exception as e:
            log_info(f"Error in find_similar_scenes_optimized: {e}")
            import traceback
            log_info(traceback.format_exc())
            raise
            
    def find_uniform_replacement_scenes(self, main_video_scenes, aux_video_scenes, replacement_rate=0.5):
        """
        调用基础处理器的均匀替换功能
        """
        try:
            return self.base_processor.find_uniform_replacement_scenes(
                main_video_scenes, aux_video_scenes, replacement_rate
            )
        except Exception as e:
            log_info(f"Error in find_uniform_replacement_scenes: {e}")
            import traceback
            log_info(traceback.format_exc())
            raise
            
    def find_random_replacement_scenes(self, main_video_scenes, aux_video_scenes, replacement_rate=0.5, seed=None):
        """
        调用基础处理器的随机替换功能
        """
        try:
            return self.base_processor.find_random_replacement_scenes(
                main_video_scenes, aux_video_scenes, replacement_rate, seed
            )
        except Exception as e:
            log_info(f"Error in find_random_replacement_scenes: {e}")
            import traceback
            log_info(traceback.format_exc())
            raise
    
    def replace_and_concatenate_videos(self, main_video_path, aux_video_path, main_video_scenes, aux_video_scenes, similar_matches, output_path, progress_callback=None):
        """调用基础处理器的视频合成功能"""
        try:
            return self.base_processor.replace_and_concatenate_videos(
                main_video_path, aux_video_path, main_video_scenes, aux_video_scenes, similar_matches, output_path, progress_callback
            )
        except Exception as e:
            log_info(f"Error in replace_and_concatenate_videos: {e}")
            import traceback
            log_info(traceback.format_exc())
            raise
            
    def replace_and_concatenate_videos_optimized(self, main_video_path, aux_video_path, main_video_scenes, aux_video_scenes, similar_matches, output_path):
        """
        优化版本的视频替换和合并功能，专为处理大型辅助视频（如2小时视频）设计
        :param main_video_path: 主视频文件路径
        :param aux_video_path: 辅助视频文件路径
        :param main_video_scenes: 主视频的视频段列表
        :param aux_video_scenes: 辅助视频的视频段列表
        :param similar_matches: 相似视频段的匹配列表
        :param output_path: 输出视频文件路径
        """
        try:
            log_info("使用优化版本的视频替换和合并功能...")
            
            # 设置最大内存使用限制
            import gc
            gc.collect()
            
            # 按辅助视频时间排序匹配，减少视频读取次数
            sorted_matches = sorted(similar_matches, key=lambda x: aux_video_scenes[x['aux_scene_index']]['start_time'])
            log_info(f"优化匹配顺序，共 {len(sorted_matches)} 个匹配")
            
            # 使用优化版本的视频替换和合并功能
            self.base_processor.replace_and_concatenate_videos(
                main_video_path, aux_video_path, main_video_scenes, aux_video_scenes, sorted_matches, output_path
            )
            
            return output_path
            
        except Exception as e:
            log_info(f"优化版本的视频替换和合并失败: {e}")
            import traceback
            log_info(traceback.format_exc())
            
            # 尝试使用备用方法
            log_info("尝试使用备用方法处理...")
            
            try:
                # 减少替换数量，只保留相似度最高的部分匹配
                if len(similar_matches) > 10:
                    # 按相似度排序
                    sorted_by_similarity = sorted(similar_matches, key=lambda x: x['similarity'], reverse=True)
                    # 只保留前10个最相似的匹配
                    reduced_matches = sorted_by_similarity[:10]
                    log_info(f"减少匹配数量从 {len(similar_matches)} 到 {len(reduced_matches)}")
                    similar_matches = reduced_matches
                
                # 使用基础处理器的功能，但限制内存使用
                return self.base_processor.replace_and_concatenate_videos(
                    main_video_path, aux_video_path, main_video_scenes, aux_video_scenes, similar_matches, output_path, progress_callback
                )
                
            except Exception as backup_error:
                log_info(f"备用方法也失败: {backup_error}")
                log_info(traceback.format_exc())
                raise Exception(f"视频替换和合并失败: {e}, 备用方法: {backup_error}")
                
    def batch_process_large_video(self, main_video_path, aux_video_path, similarity_threshold=0.8, replacement_rate=0.5, output_path=None):
        """
        批量处理大型视频的完整流程，专为处理大型辅助视频设计
        :param main_video_path: 主视频文件路径
        :param aux_video_path: 辅助视频文件路径
        :param similarity_threshold: 相似度阈值
        :param replacement_rate: 替换率
        :param output_path: 输出视频文件路径，如果为None则自动生成
        :return: 输出视频文件路径
        """
        try:
            import os
            import gc
            
            # 强制垃圾回收
            gc.collect()
            
            # 自动生成输出路径
            if output_path is None:
                main_name = os.path.splitext(os.path.basename(main_video_path))[0]
                aux_name = os.path.splitext(os.path.basename(aux_video_path))[0]
                output_path = f"{main_name}_mixed_with_{aux_name}.mp4"
                
            log_info(f"开始批量处理大型视频...")
            log_info(f"主视频: {main_video_path}")
            log_info(f"辅助视频: {aux_video_path}")
            log_info(f"输出视频: {output_path}")
            
            # 1. 切割主视频
            log_info("切割主视频...")
            main_scenes = self.split_video_into_scenes(main_video_path)
            log_info(f"主视频切割完成，共 {len(main_scenes)} 段")
            
            # 强制垃圾回收
            gc.collect()
            
            # 2. 切割辅助视频
            log_info("切割辅助视频...")
            aux_scenes = self.split_video_into_scenes(aux_video_path)
            log_info(f"辅助视频切割完成，共 {len(aux_scenes)} 段")
            
            # 强制垃圾回收
            gc.collect()
            
            # 3. 查找相似视频段
            log_info("查找相似视频段...")
            similar_matches = self.find_similar_scenes_optimized(
                main_scenes, aux_scenes, main_video_path, aux_video_path, similarity_threshold
            )
            log_info(f"找到 {len(similar_matches)} 对相似视频段")
            
            # 如果找到的相似段太少，尝试使用均匀替换
            if len(similar_matches) < len(main_scenes) * replacement_rate * 0.5:
                log_info(f"相似段数量不足 ({len(similar_matches)}), 尝试使用均匀替换策略")
                uniform_matches = self.find_uniform_replacement_scenes(main_scenes, aux_scenes, replacement_rate)
                
                # 如果均匀替换产生了更多的匹配，使用它
                if len(uniform_matches) > len(similar_matches):
                    log_info(f"使用均匀替换策略，共 {len(uniform_matches)} 个匹配")
                    similar_matches = uniform_matches
            
            # 强制垃圾回收
            gc.collect()
            
            # 4. 替换并合成视频
            log_info("替换并合成视频...")
            self.replace_and_concatenate_videos_optimized(
                main_video_path, aux_video_path, main_scenes, aux_scenes, similar_matches, output_path
            )
            
            # 检查输出文件
            if os.path.exists(output_path) and os.path.getsize(output_path) > 1024:
                log_info(f"视频处理完成，输出文件: {output_path}")
                return output_path
            else:
                raise Exception(f"输出文件无效或不存在: {output_path}")
                
        except Exception as e:
            log_info(f"批量处理大型视频失败: {e}")
            import traceback
            log_info(traceback.format_exc())
            raise
            
    def remove_subtitles_from_scenes(self, video_path, scenes, output_path, subtitle_region=None, detection_sensitivity=0.5, removal_method='inpaint'):
        """
        优化版本的从视频场景中移除字幕功能
        :param video_path: 视频文件路径
        :param scenes: 视频场景列表
        :param output_path: 输出视频路径
        :param subtitle_region: 字幕区域，如果为None则自动检测
        :param detection_sensitivity: 字幕检测敏感度
        :param removal_method: 移除方法
        :return: 处理后的视频路径
        """
        try:
            return self.base_processor.remove_subtitles_from_scenes(
                video_path, scenes, output_path, subtitle_region, detection_sensitivity, removal_method
            )
        except Exception as e:
            log_info(f"Error in remove_subtitles_from_scenes: {e}")
            import traceback
            log_info(traceback.format_exc())
            raise

    def remove_subtitles(self, video_path, output_path, subtitle_region=None, detection_sensitivity=0.5, removal_method='inpaint', use_acceleration=True):
        """
        调用基础处理器的字幕移除功能
        :param video_path: 输入视频路径
        :param output_path: 输出视频路径
        :param subtitle_region: 字幕区域，格式为 (y_start, y_end)
        :param detection_sensitivity: 字幕检测敏感度
        :param removal_method: 移除方法
        :param use_acceleration: 是否使用加速处理
        :return: 处理后的视频路径
        """
        try:
            return self.base_processor.remove_subtitles(
                video_path, output_path, subtitle_region, detection_sensitivity, removal_method, use_acceleration
            )
        except Exception as e:
            log_info(f"Error in remove_subtitles: {e}")
            import traceback
            log_info(traceback.format_exc())
            raise

    def remove_subtitles_multi_regions(self, video_path, output_path, subtitle_regions, removal_method='inpaint'):
        """
        调用基础处理器的多矩形字幕移除功能
        :param video_path: 输入视频路径
        :param output_path: 输出视频路径
        :param subtitle_regions: 多个矩形区域列表 [(x1, y1, x2, y2), ...]
        :param removal_method: 移除方法
        :return: 处理后的视频路径
        """
        try:
            return self.base_processor.remove_subtitles_multi_regions(
                video_path, output_path, subtitle_regions, removal_method
            )
        except Exception as e:
            log_info(f"Error in remove_subtitles_multi_regions: {e}")
            import traceback
            log_info(traceback.format_exc())
            raise

    def _remove_subtitles_from_frame_multi_regions(self, frame, subtitle_regions, removal_method):
        """
        调用基础处理器的多矩形帧处理功能
        :param frame: 输入帧
        :param subtitle_regions: 多个矩形区域列表 [(x1, y1, x2, y2), ...]
        :param removal_method: 移除方法
        :return: 处理后的帧
        """
        try:
            return self.base_processor._remove_subtitles_from_frame_multi_regions(
                frame, subtitle_regions, removal_method
            )
        except Exception as e:
            log_info(f"Error in _remove_subtitles_from_frame_multi_regions: {e}")
            import traceback
            log_info(traceback.format_exc())
            # 如果出错，返回原帧
            return frame

    def set_performance_mode(self, mode='auto'):
        """
        设置性能模式
        :param mode: 性能模式 ('auto', 'gpu', 'cpu', 'single_thread', 'low_memory')
        """
        try:
            return self.base_processor.set_performance_mode(mode)
        except Exception as e:
            log_info(f"Error in set_performance_mode: {e}")
            import traceback
            log_info(traceback.format_exc())
            raise

if __name__ == '__main__':
    # 性能测试
    import time
    
    log_info("性能优化测试...")
    
    # 创建优化版本的处理器
    optimized_processor = OptimizedVideoProcessor()
    
    # 测试优化后的处理流程
    start_time = time.time()
    
    log_info("1. 切割视频...")
    main_scenes = optimized_processor.split_video_into_scenes('test_main_video.mp4')
    aux_scenes = optimized_processor.split_video_into_scenes('test_aux_video.mp4')
    
    log_info("2. 优化版相似度匹配...")
    similar_matches = optimized_processor.find_similar_scenes_optimized(
        main_scenes, aux_scenes, 'test_main_video.mp4', 'test_aux_video.mp4', similarity_threshold=0.5
    )
    
    log_info("3. 合成视频...")
    optimized_processor.replace_and_concatenate_videos(
        'test_main_video.mp4', 'test_aux_video.mp4', main_scenes, aux_scenes, similar_matches, 'optimized_output.mp4'
    )
    
    end_time = time.time()
    log_info(f"优化版本总耗时: {end_time - start_time:.2f} 秒")
    log_info(f"找到 {len(similar_matches)} 对相似视频段")

