<template>
  <div class="machine-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2>设备管理</h2>
        <p class="page-description">查看和管理已激活的设备信息</p>
      </div>
      <div class="header-right">
        <el-button type="info" icon="el-icon-refresh" @click="refreshData">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <el-card>
        <el-form :model="searchForm" inline>
          <el-form-item label="机器码">
            <el-input
              v-model="searchForm.machineCode"
              placeholder="请输入机器码"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="计算机名">
            <el-input
              v-model="searchForm.computerName"
              placeholder="请输入计算机名"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="激活状态">
            <el-select v-model="searchForm.activationStatus" placeholder="请选择状态" clearable style="width: 120px">
              <el-option label="未激活" :value="0" />
              <el-option label="已激活" :value="1" />
              <el-option label="已禁用" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleSearch">
              搜索
            </el-button>
            <el-button icon="el-icon-refresh" @click="resetSearch">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 设备列表 -->
    <div class="table-section">
      <el-card>
        <el-table
          v-loading="tableLoading"
          :data="filteredTableData"
          stripe
          border
          style="width: 100%"
          @sort-change="handleSortChange"
        >
          <el-table-column prop="id" label="ID" width="80" sortable />
          <el-table-column prop="machineCode" label="机器码" width="200" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-tag type="info" size="small">{{ scope.row.machineCode }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="computerName" label="计算机名" width="150" show-overflow-tooltip />
          <el-table-column prop="operatingSystem" label="操作系统" width="150" show-overflow-tooltip />
          <el-table-column prop="processor" label="处理器" show-overflow-tooltip />
          <el-table-column prop="activationStatus" label="激活状态" width="100" align="center">
            <template slot-scope="scope">
              <el-tag
                :type="getStatusTagType(scope.row.activationStatus)"
                size="small"
              >
                {{ getStatusText(scope.row.activationStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="activationCount" label="激活次数" width="100" align="center" />
          <el-table-column prop="firstActivationTime" label="首次激活时间" width="160" sortable>
            <template slot-scope="scope">
              {{ formatDateTime(scope.row.firstActivationTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="lastActivationTime" label="最后激活时间" width="160" sortable>
            <template slot-scope="scope">
              {{ formatDateTime(scope.row.lastActivationTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template slot-scope="scope">
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-view"
                @click="viewDetails(scope.row)"
              >
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          />
        </div>
      </el-card>
    </div>

    <!-- 设备详情对话框 -->
    <MachineDetailDialog
      :visible.sync="detailDialogVisible"
      :machine-info="selectedMachine"
    />
  </div>
</template>

<script>
import activationApi from '@/api/activation'
import MachineDetailDialog from './components/MachineDetailDialog'

export default {
  name: 'MachineManagement',
  components: {
    MachineDetailDialog
  },
  data() {
    return {
      // 表格数据
      tableData: [],
      tableLoading: false,
      
      // 搜索表单
      searchForm: {
        machineCode: '',
        computerName: '',
        activationStatus: null
      },
      
      // 分页
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      
      // 排序
      sortConfig: {
        prop: 'lastActivationTime',
        order: 'descending'
      },
      
      // 对话框状态
      detailDialogVisible: false,
      selectedMachine: null
    }
  },
  computed: {
    // 过滤后的表格数据
    filteredTableData() {
      let data = [...this.tableData]
      
      // 搜索过滤
      if (this.searchForm.machineCode) {
        data = data.filter(item => 
          item.machineCode.toLowerCase().includes(this.searchForm.machineCode.toLowerCase())
        )
      }
      
      if (this.searchForm.computerName) {
        data = data.filter(item => 
          item.computerName && item.computerName.toLowerCase().includes(this.searchForm.computerName.toLowerCase())
        )
      }
      
      if (this.searchForm.activationStatus !== null && this.searchForm.activationStatus !== '') {
        data = data.filter(item => item.activationStatus === this.searchForm.activationStatus)
      }
      
      // 排序
      if (this.sortConfig.prop) {
        data.sort((a, b) => {
          const aVal = a[this.sortConfig.prop]
          const bVal = b[this.sortConfig.prop]
          
          if (this.sortConfig.order === 'ascending') {
            return aVal > bVal ? 1 : -1
          } else {
            return aVal < bVal ? 1 : -1
          }
        })
      }
      
      // 更新总数
      this.pagination.total = data.length
      
      // 分页
      const start = (this.pagination.currentPage - 1) * this.pagination.pageSize
      const end = start + this.pagination.pageSize
      
      return data.slice(start, end)
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    // 加载数据
    async loadData() {
      this.tableLoading = true
      try {
        const response = await activationApi.getAllMachineInfo()
        if (response.success) {
          this.tableData = response.data || []
        } else {
          this.$message.error(response.message || '获取设备列表失败')
        }
      } catch (error) {
        console.error('加载设备列表失败:', error)
        this.$message.error('获取设备列表失败')
      } finally {
        this.tableLoading = false
      }
    },
    
    // 刷新数据
    refreshData() {
      this.loadData()
    },
    
    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1
    },
    
    // 重置搜索
    resetSearch() {
      this.searchForm = {
        machineCode: '',
        computerName: '',
        activationStatus: null
      }
      this.pagination.currentPage = 1
    },
    
    // 排序变化
    handleSortChange({ prop, order }) {
      this.sortConfig = { prop, order }
    },
    
    // 分页大小变化
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.currentPage = 1
    },
    
    // 当前页变化
    handleCurrentChange(val) {
      this.pagination.currentPage = val
    },
    
    // 查看详情
    viewDetails(row) {
      this.selectedMachine = row
      this.detailDialogVisible = true
    },
    
    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        0: 'info',     // 未激活
        1: 'success',  // 已激活
        2: 'danger'    // 已禁用
      }
      return typeMap[status] || 'info'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        0: '未激活',
        1: '已激活',
        2: '已禁用'
      }
      return textMap[status] || '未知'
    },
    
    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      return new Date(dateTime).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.machine-management {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .header-left {
      h2 {
        margin: 0 0 5px 0;
        color: #303133;
        font-size: 24px;
        font-weight: 600;
      }
      
      .page-description {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }
    
    .header-right {
      .el-button {
        margin-left: 10px;
      }
    }
  }
  
  .search-section {
    margin-bottom: 20px;
  }
  
  .table-section {
    .pagination-wrapper {
      margin-top: 20px;
      text-align: right;
    }
  }
}
</style>
