package com.example.activation.controller;

import com.example.activation.entity.ActivationCode;
import com.example.activation.entity.MachineInfo;
import com.example.activation.service.ActivationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 激活控制器
 */
@RestController
@RequestMapping("/api/activation")
@CrossOrigin(origins = "*") // 允许跨域请求
public class ActivationController {
    
    @Autowired
    private ActivationService activationService;
    
    /**
     * 验证激活码
     */
    @PostMapping("/verify")
    public ResponseEntity<Map<String, Object>> verifyActivation(@RequestBody Map<String, Object> request) {
        try {
            String activationCode = (String) request.get("activationCode");
            String machineCode = (String) request.get("machineCode");
            
            // 获取机器详细信息（可选）
            @SuppressWarnings("unchecked")
            Map<String, String> machineDetails = (Map<String, String>) request.get("machineDetails");
            
            Map<String, Object> result = activationService.verifyActivation(activationCode, machineCode, machineDetails);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "服务器内部错误: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }
    
    /**
     * 检查机器是否已激活
     */
    @GetMapping("/check/{machineCode}")
    public ResponseEntity<Map<String, Object>> checkMachineActivation(@PathVariable String machineCode) {
        try {
            boolean isActivated = activationService.isMachineActivated(machineCode);
            MachineInfo machineInfo = activationService.getMachineInfo(machineCode);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("activated", isActivated);
            result.put("machineInfo", machineInfo);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }
    
    /**
     * 生成激活码
     */
    @PostMapping("/generate")
    public ResponseEntity<Map<String, Object>> generateActivationCode(@RequestBody Map<String, Object> request) {
        try {
            String userId = (String) request.get("userId");
            String expireTimeStr = (String) request.get("expireTime");
            Integer maxActivations = (Integer) request.get("maxActivations");
            String remark = (String) request.get("remark");
            
            LocalDateTime expireTime = null;
            if (expireTimeStr != null && !expireTimeStr.isEmpty()) {
                expireTime = LocalDateTime.parse(expireTimeStr);
            }
            
            ActivationCode activationCode = activationService.createActivationCode(userId, expireTime, maxActivations, remark);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "激活码生成成功");
            result.put("activationCode", activationCode);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "生成激活码失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }
    
    /**
     * 获取激活码信息
     */
    @GetMapping("/info/{activationCode}")
    public ResponseEntity<Map<String, Object>> getActivationCodeInfo(@PathVariable String activationCode) {
        try {
            ActivationCode code = activationService.getActivationCode(activationCode);
            
            Map<String, Object> result = new HashMap<>();
            if (code != null) {
                result.put("success", true);
                result.put("activationCode", code);
            } else {
                result.put("success", false);
                result.put("message", "激活码不存在");
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }
    
    /**
     * 禁用激活码
     */
    @PostMapping("/disable/{activationCode}")
    public ResponseEntity<Map<String, Object>> disableActivationCode(@PathVariable String activationCode) {
        try {
            boolean success = activationService.disableActivationCode(activationCode);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "激活码已禁用" : "操作失败");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "禁用失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }
    
    /**
     * 启用激活码
     */
    @PostMapping("/enable/{activationCode}")
    public ResponseEntity<Map<String, Object>> enableActivationCode(@PathVariable String activationCode) {
        try {
            boolean success = activationService.enableActivationCode(activationCode);

            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "激活码已启用" : "操作失败");

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "启用失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    /**
     * 删除激活码
     */
    @DeleteMapping("/delete/{activationCode}")
    public ResponseEntity<Map<String, Object>> deleteActivationCode(@PathVariable String activationCode) {
        try {
            boolean success = activationService.deleteActivationCode(activationCode);

            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "激活码已删除" : "激活码不存在");

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "删除失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    /**
     * 编辑激活码
     */
    @PutMapping("/update/{activationCode}")
    public ResponseEntity<Map<String, Object>> updateActivationCode(
            @PathVariable String activationCode,
            @RequestBody Map<String, Object> request) {
        try {
            System.out.println("收到编辑激活码请求:");
            System.out.println("激活码: " + activationCode);
            System.out.println("请求数据: " + request);
            String userId = (String) request.get("userId");
            String expireTimeStr = (String) request.get("expireTime");
            String remark = (String) request.get("remark");

            // 安全地转换maxActivations
            Integer maxActivations = null;
            Object maxActivationsObj = request.get("maxActivations");
            if (maxActivationsObj != null) {
                if (maxActivationsObj instanceof Integer) {
                    maxActivations = (Integer) maxActivationsObj;
                } else if (maxActivationsObj instanceof Number) {
                    maxActivations = ((Number) maxActivationsObj).intValue();
                } else if (maxActivationsObj instanceof String) {
                    try {
                        maxActivations = Integer.parseInt((String) maxActivationsObj);
                    } catch (NumberFormatException e) {
                        Map<String, Object> errorResult = new HashMap<>();
                        errorResult.put("success", false);
                        errorResult.put("message", "最大激活次数格式错误");
                        return ResponseEntity.badRequest().body(errorResult);
                    }
                }
            }

            // 解析过期时间
            LocalDateTime expireTime = null;
            if (expireTimeStr != null && !expireTimeStr.trim().isEmpty()) {
                try {
                    // 尝试多种时间格式
                    if (expireTimeStr.contains("T")) {
                        // ISO格式：2024-12-31T23:59:59 或 2024-12-31T23:59:59.000Z
                        if (expireTimeStr.endsWith("Z")) {
                            expireTime = LocalDateTime.parse(expireTimeStr.substring(0, expireTimeStr.length() - 1));
                        } else if (expireTimeStr.contains(".")) {
                            // 包含毫秒的格式
                            String cleanTime = expireTimeStr.split("\\.")[0];
                            expireTime = LocalDateTime.parse(cleanTime);
                        } else {
                            expireTime = LocalDateTime.parse(expireTimeStr);
                        }
                    } else {
                        // 简单格式：2024-12-31 23:59:59
                        expireTime = LocalDateTime.parse(expireTimeStr.replace(" ", "T"));
                    }
                } catch (Exception e) {
                    Map<String, Object> errorResult = new HashMap<>();
                    errorResult.put("success", false);
                    errorResult.put("message", "过期时间格式错误，期望格式：yyyy-MM-ddTHH:mm:ss 或 yyyy-MM-dd HH:mm:ss，实际收到：" + expireTimeStr);
                    return ResponseEntity.badRequest().body(errorResult);
                }
            }

            ActivationCode updatedCode = activationService.updateActivationCode(
                activationCode, userId, expireTime, maxActivations, remark);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "激活码已更新");
            result.put("activationCode", updatedCode);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "更新失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }
    
    /**
     * 获取所有激活码
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> getAllActivationCodes() {
        try {
            List<ActivationCode> activationCodes = activationService.getAllActivationCodes();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", activationCodes);
            result.put("total", activationCodes.size());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }
    
    /**
     * 获取所有机器信息
     */
    @GetMapping("/machines")
    public ResponseEntity<Map<String, Object>> getAllMachineInfo() {
        try {
            List<MachineInfo> machineInfos = activationService.getAllMachineInfo();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", machineInfos);
            result.put("total", machineInfos.size());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }
    
    /**
     * 获取激活统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getActivationStatistics() {
        try {
            Map<String, Object> statistics = activationService.getActivationStatistics();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", statistics);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }
    
    /**
     * 清理过期激活码
     */
    @PostMapping("/cleanup")
    public ResponseEntity<Map<String, Object>> cleanupExpiredCodes() {
        try {
            int deletedCount = activationService.cleanExpiredActivationCodes();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "清理完成");
            result.put("deletedCount", deletedCount);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "清理失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "激活服务运行正常");
        result.put("timestamp", LocalDateTime.now());
        return ResponseEntity.ok(result);
    }
}
