"""
机器码生成器
基于用户电脑的唯一硬件标识生成不会随重启变化的唯一机器码
"""

import hashlib
import platform
import subprocess
import uuid
import os
import winreg
from typing import Optional

# 导入日志系统
try:
    from logger_system import log_info, log_error, log_warning, log_debug, safe_print
except ImportError:
    # 备用函数
    def log_info(msg): print(msg)
    def log_error(msg): print(f"ERROR: {msg}")
    def log_warning(msg): print(f"WARNING: {msg}")
    def log_debug(msg): print(f"DEBUG: {msg}")
    def safe_print(*args, **kwargs): print(*args, **kwargs)


class MachineCodeGenerator:
    """机器码生成器类"""
    
    def __init__(self):
        self.system = platform.system()
    
    def get_cpu_id(self) -> str:
        """获取CPU序列号"""
        try:
            if self.system == "Windows":
                # 使用wmic获取CPU序列号
                result = subprocess.run(
                    ['wmic', 'cpu', 'get', 'ProcessorId', '/value'],
                    capture_output=True,
                    text=True,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
                for line in result.stdout.split('\n'):
                    if 'ProcessorId=' in line:
                        return line.split('=')[1].strip()
            return ""
        except Exception:
            return ""
    
    def get_motherboard_serial(self) -> str:
        """获取主板序列号"""
        try:
            if self.system == "Windows":
                result = subprocess.run(
                    ['wmic', 'baseboard', 'get', 'SerialNumber', '/value'],
                    capture_output=True,
                    text=True,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
                for line in result.stdout.split('\n'):
                    if 'SerialNumber=' in line:
                        serial = line.split('=')[1].strip()
                        if serial and serial != "To be filled by O.E.M.":
                            return serial
            return ""
        except Exception:
            return ""
    
    def get_bios_serial(self) -> str:
        """获取BIOS序列号"""
        try:
            if self.system == "Windows":
                result = subprocess.run(
                    ['wmic', 'bios', 'get', 'SerialNumber', '/value'],
                    capture_output=True,
                    text=True,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
                for line in result.stdout.split('\n'):
                    if 'SerialNumber=' in line:
                        serial = line.split('=')[1].strip()
                        if serial and serial != "To be filled by O.E.M.":
                            return serial
            return ""
        except Exception:
            return ""
    
    def get_disk_serial(self) -> str:
        """获取硬盘序列号"""
        try:
            if self.system == "Windows":
                result = subprocess.run(
                    ['wmic', 'diskdrive', 'get', 'SerialNumber', '/value'],
                    capture_output=True,
                    text=True,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
                serials = []
                for line in result.stdout.split('\n'):
                    if 'SerialNumber=' in line:
                        serial = line.split('=')[1].strip()
                        if serial and serial != "To be filled by O.E.M.":
                            serials.append(serial)
                return ''.join(sorted(serials))  # 排序确保一致性
            return ""
        except Exception:
            return ""
    
    def get_mac_address(self) -> str:
        """获取网卡MAC地址"""
        try:
            # 获取第一个有效的MAC地址
            mac = hex(uuid.getnode())[2:].upper()
            return mac
        except Exception:
            return ""
    
    def get_windows_product_id(self) -> str:
        """获取Windows产品ID"""
        try:
            if self.system == "Windows":
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                                  r"SOFTWARE\Microsoft\Windows NT\CurrentVersion") as key:
                    product_id, _ = winreg.QueryValueEx(key, "ProductId")
                    return product_id
            return ""
        except Exception:
            return ""
    
    def get_machine_guid(self) -> str:
        """获取Windows机器GUID"""
        try:
            if self.system == "Windows":
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                                  r"SOFTWARE\Microsoft\Cryptography") as key:
                    machine_guid, _ = winreg.QueryValueEx(key, "MachineGuid")
                    return machine_guid
            return ""
        except Exception:
            return ""
    
    def collect_hardware_info(self) -> dict:
        """收集所有硬件信息"""
        info = {
            'cpu_id': self.get_cpu_id(),
            'motherboard_serial': self.get_motherboard_serial(),
            'bios_serial': self.get_bios_serial(),
            'disk_serial': self.get_disk_serial(),
            'mac_address': self.get_mac_address(),
            'windows_product_id': self.get_windows_product_id(),
            'machine_guid': self.get_machine_guid(),
            'computer_name': platform.node(),
            'system': platform.system(),
            'processor': platform.processor()
        }
        
        # 过滤掉空值
        return {k: v for k, v in info.items() if v}
    
    def generate_machine_code(self) -> str:
        """生成机器码"""
        try:
            # 收集硬件信息
            hardware_info = self.collect_hardware_info()
            
            # 按优先级选择最稳定的标识符
            identifiers = []
            
            # 优先级1: CPU ID (最稳定)
            if hardware_info.get('cpu_id'):
                identifiers.append(f"CPU:{hardware_info['cpu_id']}")
            
            # 优先级2: 机器GUID (Windows特有，很稳定)
            if hardware_info.get('machine_guid'):
                identifiers.append(f"GUID:{hardware_info['machine_guid']}")
            
            # 优先级3: 主板序列号
            if hardware_info.get('motherboard_serial'):
                identifiers.append(f"MB:{hardware_info['motherboard_serial']}")
            
            # 优先级4: BIOS序列号
            if hardware_info.get('bios_serial'):
                identifiers.append(f"BIOS:{hardware_info['bios_serial']}")
            
            # 优先级5: 硬盘序列号
            if hardware_info.get('disk_serial'):
                identifiers.append(f"DISK:{hardware_info['disk_serial']}")
            
            # 优先级6: MAC地址
            if hardware_info.get('mac_address'):
                identifiers.append(f"MAC:{hardware_info['mac_address']}")
            
            # 优先级7: Windows产品ID
            if hardware_info.get('windows_product_id'):
                identifiers.append(f"PID:{hardware_info['windows_product_id']}")
            
            # 如果没有获取到任何硬件信息，使用计算机名作为备选
            if not identifiers:
                identifiers.append(f"NAME:{hardware_info.get('computer_name', 'UNKNOWN')}")
            
            # 组合所有标识符
            combined_info = '|'.join(identifiers)
            
            # 生成SHA256哈希
            machine_code = hashlib.sha256(combined_info.encode('utf-8')).hexdigest().upper()
            
            # 格式化为更易读的格式 (XXXX-XXXX-XXXX-XXXX)
            formatted_code = '-'.join([machine_code[i:i+4] for i in range(0, 16, 4)])
            
            return formatted_code
            
        except Exception as e:
            # 如果所有方法都失败，使用计算机名生成备用机器码
            fallback_info = f"FALLBACK:{platform.node()}:{platform.system()}"
            fallback_code = hashlib.sha256(fallback_info.encode('utf-8')).hexdigest().upper()
            return '-'.join([fallback_code[i:i+4] for i in range(0, 16, 4)])
    
    def get_hardware_summary(self) -> str:
        """获取硬件信息摘要（用于调试）"""
        info = self.collect_hardware_info()
        summary = []
        for key, value in info.items():
            summary.append(f"{key}: {value}")
        return '\n'.join(summary)


def get_machine_code() -> str:
    """获取当前机器的机器码"""
    generator = MachineCodeGenerator()
    return generator.generate_machine_code()


def get_hardware_info() -> str:
    """获取硬件信息摘要"""
    generator = MachineCodeGenerator()
    return generator.get_hardware_summary()


if __name__ == "__main__":
    # 测试代码
    log_info("=== 机器码生成器测试 ===")
    log_info(f"机器码: {get_machine_code()}")
    log_info("\n=== 硬件信息 ===")
    log_info(get_hardware_info())
