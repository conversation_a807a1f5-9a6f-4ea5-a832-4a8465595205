<template>
  <el-dialog
    title="设备详细信息"
    :visible.sync="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
  >
    <div v-if="machineInfo" class="detail-content">
      <!-- 基本信息 -->
      <el-card class="info-card" shadow="never">
        <div slot="header" class="card-header">
          <span>基本信息</span>
          <el-tag
            :type="getStatusTagType(machineInfo.activationStatus)"
            size="small"
          >
            {{ getStatusText(machineInfo.activationStatus) }}
          </el-tag>
        </div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>机器码：</label>
              <div class="value">
                <span class="code-text">{{ machineInfo.machineCode }}</span>
                <el-button
                  type="text"
                  icon="el-icon-copy-document"
                  @click="copyToClipboard(machineInfo.machineCode)"
                >
                  复制
                </el-button>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>计算机名称：</label>
              <div class="value">{{ machineInfo.computerName || '未知' }}</div>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>操作系统：</label>
              <div class="value">{{ machineInfo.operatingSystem || '未知' }}</div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>处理器：</label>
              <div class="value">{{ machineInfo.processor || '未知' }}</div>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>激活次数：</label>
              <div class="value">{{ machineInfo.activationCount || 0 }}</div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>创建时间：</label>
              <div class="value">{{ formatDateTime(machineInfo.createTime) }}</div>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>首次激活时间：</label>
              <div class="value">{{ formatDateTime(machineInfo.firstActivationTime) }}</div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>最后激活时间：</label>
              <div class="value">{{ formatDateTime(machineInfo.lastActivationTime) }}</div>
            </div>
          </el-col>
        </el-row>
        
        <div v-if="machineInfo.remark" class="info-item">
          <label>备注：</label>
          <div class="value">{{ machineInfo.remark }}</div>
        </div>
      </el-card>
      
      <!-- 硬件信息 -->
      <el-card class="info-card" shadow="never">
        <div slot="header" class="card-header">
          <span>硬件信息</span>
        </div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>CPU ID：</label>
              <div class="value">
                <span v-if="machineInfo.cpuId" class="code-text">{{ machineInfo.cpuId }}</span>
                <span v-else class="text-muted">未获取</span>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>主板序列号：</label>
              <div class="value">
                <span v-if="machineInfo.motherboardSerial" class="code-text">{{ machineInfo.motherboardSerial }}</span>
                <span v-else class="text-muted">未获取</span>
              </div>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>BIOS序列号：</label>
              <div class="value">
                <span v-if="machineInfo.biosSerial" class="code-text">{{ machineInfo.biosSerial }}</span>
                <span v-else class="text-muted">未获取</span>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>硬盘序列号：</label>
              <div class="value">
                <span v-if="machineInfo.diskSerial" class="code-text">{{ machineInfo.diskSerial }}</span>
                <span v-else class="text-muted">未获取</span>
              </div>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>MAC地址：</label>
              <div class="value">
                <span v-if="machineInfo.macAddress" class="code-text">{{ machineInfo.macAddress }}</span>
                <span v-else class="text-muted">未获取</span>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>Windows产品ID：</label>
              <div class="value">
                <span v-if="machineInfo.windowsProductId" class="code-text">{{ machineInfo.windowsProductId }}</span>
                <span v-else class="text-muted">未获取</span>
              </div>
            </div>
          </el-col>
        </el-row>
        
        <div class="info-item">
          <label>机器GUID：</label>
          <div class="value">
            <span v-if="machineInfo.machineGuid" class="code-text">{{ machineInfo.machineGuid }}</span>
            <span v-else class="text-muted">未获取</span>
          </div>
        </div>
      </el-card>
      
      <!-- 激活历史 -->
      <el-card class="info-card" shadow="never">
        <div slot="header" class="card-header">
          <span>激活历史</span>
        </div>
        
        <div class="activation-timeline">
          <el-timeline>
            <el-timeline-item
              v-if="machineInfo.firstActivationTime"
              timestamp="首次激活"
              placement="top"
            >
              <el-card>
                <h4>设备首次激活</h4>
                <p>时间：{{ formatDateTime(machineInfo.firstActivationTime) }}</p>
              </el-card>
            </el-timeline-item>
            
            <el-timeline-item
              v-if="machineInfo.lastActivationTime && machineInfo.lastActivationTime !== machineInfo.firstActivationTime"
              timestamp="最近激活"
              placement="top"
            >
              <el-card>
                <h4>最近一次激活</h4>
                <p>时间：{{ formatDateTime(machineInfo.lastActivationTime) }}</p>
              </el-card>
            </el-timeline-item>
            
            <el-timeline-item
              v-if="!machineInfo.firstActivationTime"
              timestamp="未激活"
              placement="top"
              type="warning"
            >
              <el-card>
                <h4>设备尚未激活</h4>
                <p>此设备还没有进行过激活操作</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-card>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'MachineDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    machineInfo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    // 复制到剪贴板
    async copyToClipboard(text) {
      try {
        await navigator.clipboard.writeText(text)
        this.$message.success('已复制到剪贴板')
      } catch (error) {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success('已复制到剪贴板')
      }
    },
    
    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        0: 'info',     // 未激活
        1: 'success',  // 已激活
        2: 'danger'    // 已禁用
      }
      return typeMap[status] || 'info'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        0: '未激活',
        1: '已激活',
        2: '已禁用'
      }
      return textMap[status] || '未知'
    },
    
    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      return new Date(dateTime).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-content {
  .info-card {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
    }
  }
  
  .info-item {
    margin-bottom: 15px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    label {
      display: block;
      font-weight: bold;
      color: #606266;
      margin-bottom: 5px;
      font-size: 14px;
    }
    
    .value {
      color: #303133;
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 10px;
      
      .code-text {
        font-family: 'Courier New', monospace;
        background-color: #F5F7FA;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 13px;
      }
    }
  }
  
  .activation-timeline {
    .el-timeline-item {
      .el-card {
        margin-bottom: 0;
        
        h4 {
          margin: 0 0 10px 0;
          color: #303133;
        }
        
        p {
          margin: 0;
          color: #606266;
          font-size: 14px;
        }
      }
    }
  }
  
  .text-muted {
    color: #C0C4CC;
  }
}
</style>
