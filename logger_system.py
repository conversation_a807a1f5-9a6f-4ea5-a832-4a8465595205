#!/usr/bin/env python3
"""
日志系统模块
用于替换print语句，防止打包后出现黑色控制台窗口
支持文件日志、GUI日志显示和控制台输出
"""

import os
import sys
import logging
import threading
from datetime import datetime
from typing import Optional, Callable, Any
from pathlib import Path
import traceback

class LoggerSystem:
    """统一日志系统"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化日志系统"""
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.gui_callback = None
        self.console_enabled = True
        self.file_enabled = True
        self.log_file_path = None
        
        # 创建日志目录
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        # 设置日志文件路径
        timestamp = datetime.now().strftime("%Y%m%d")
        self.log_file_path = self.log_dir / f"app_{timestamp}.log"
        
        # 配置Python logging
        self._setup_logging()
        
        # 检测是否为打包环境
        self._detect_packaged_environment()
    
    def _setup_logging(self):
        """设置Python logging配置"""
        # 创建logger
        self.logger = logging.getLogger('VideoDeduplicationTool')
        self.logger.setLevel(logging.DEBUG)
        
        # 清除现有handlers
        self.logger.handlers.clear()
        
        # 文件handler
        if self.file_enabled:
            file_handler = logging.FileHandler(
                self.log_file_path, 
                encoding='utf-8',
                mode='a'
            )
            file_handler.setLevel(logging.DEBUG)
            file_formatter = logging.Formatter(
                '%(asctime)s [%(levelname)s] %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)
        
        # 控制台handler（仅在开发环境）
        if self.console_enabled and not self._is_packaged():
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.INFO)
            console_formatter = logging.Formatter('%(message)s')
            console_handler.setFormatter(console_formatter)
            self.logger.addHandler(console_handler)
    
    def _detect_packaged_environment(self):
        """检测是否为打包环境"""
        if self._is_packaged():
            # 在打包环境中禁用控制台输出
            self.console_enabled = False
            # 重定向标准输出和错误输出
            self._redirect_stdout_stderr()
    
    def _is_packaged(self) -> bool:
        """检测是否为PyInstaller打包的环境"""
        return (
            getattr(sys, 'frozen', False) or  # PyInstaller
            hasattr(sys, '_MEIPASS') or      # PyInstaller temp dir
            'python.exe' not in sys.executable.lower()  # 不是标准Python解释器
        )
    
    def _redirect_stdout_stderr(self):
        """重定向标准输出和错误输出到日志文件"""
        try:
            # 创建日志文件的写入器
            log_file = open(self.log_file_path, 'a', encoding='utf-8')
            
            # 重定向stdout和stderr
            sys.stdout = LogFileWrapper(log_file, self, 'INFO')
            sys.stderr = LogFileWrapper(log_file, self, 'ERROR')
            
        except Exception as e:
            # 如果重定向失败，至少记录错误
            self._write_to_file(f"Failed to redirect stdout/stderr: {e}")
    
    def set_gui_callback(self, callback: Callable[[str, str], None]):
        """
        设置GUI回调函数
        
        Args:
            callback: 回调函数，接受(level, message)参数
        """
        self.gui_callback = callback
    
    def _write_to_file(self, message: str, level: str = 'INFO'):
        """直接写入日志文件"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            log_entry = f"{timestamp} [{level}] {message}\n"
            
            with open(self.log_file_path, 'a', encoding='utf-8') as f:
                f.write(log_entry)
        except Exception:
            pass  # 静默处理文件写入错误
    
    def _notify_gui(self, level: str, message: str):
        """通知GUI显示日志"""
        if self.gui_callback:
            try:
                self.gui_callback(level, message)
            except Exception:
                pass  # 静默处理GUI回调错误
    
    def debug(self, message: Any):
        """调试级别日志"""
        msg = str(message)
        self.logger.debug(msg)
        self._notify_gui('DEBUG', msg)
    
    def info(self, message: Any):
        """信息级别日志"""
        msg = str(message)
        self.logger.info(msg)
        self._notify_gui('INFO', msg)
    
    def warning(self, message: Any):
        """警告级别日志"""
        msg = str(message)
        self.logger.warning(msg)
        self._notify_gui('WARNING', msg)
    
    def error(self, message: Any):
        """错误级别日志"""
        msg = str(message)
        self.logger.error(msg)
        self._notify_gui('ERROR', msg)
    
    def critical(self, message: Any):
        """严重错误级别日志"""
        msg = str(message)
        self.logger.critical(msg)
        self._notify_gui('CRITICAL', msg)
    
    def exception(self, message: Any):
        """异常日志（包含堆栈跟踪）"""
        msg = str(message)
        exc_info = traceback.format_exc()
        full_msg = f"{msg}\n{exc_info}"
        self.logger.error(full_msg)
        self._notify_gui('ERROR', full_msg)
    
    def log(self, level: str, message: Any):
        """通用日志方法"""
        level = level.upper()
        if level == 'DEBUG':
            self.debug(message)
        elif level == 'INFO':
            self.info(message)
        elif level == 'WARNING':
            self.warning(message)
        elif level == 'ERROR':
            self.error(message)
        elif level == 'CRITICAL':
            self.critical(message)
        else:
            self.info(message)
    
    def get_log_file_path(self) -> str:
        """获取日志文件路径"""
        return str(self.log_file_path)
    
    def clear_old_logs(self, days: int = 7):
        """清理旧日志文件"""
        try:
            import time
            current_time = time.time()
            cutoff_time = current_time - (days * 24 * 60 * 60)
            
            for log_file in self.log_dir.glob("app_*.log"):
                if log_file.stat().st_mtime < cutoff_time:
                    log_file.unlink()
                    self.info(f"已删除旧日志文件: {log_file.name}")
        except Exception as e:
            self.error(f"清理旧日志文件失败: {e}")


class LogFileWrapper:
    """日志文件包装器，用于重定向stdout/stderr"""
    
    def __init__(self, file_obj, logger_system, level):
        self.file_obj = file_obj
        self.logger_system = logger_system
        self.level = level
        self.buffer = ""
    
    def write(self, text):
        """写入文本"""
        if text and text.strip():
            # 写入文件
            self.file_obj.write(text)
            self.file_obj.flush()
            
            # 记录到日志系统
            self.buffer += text
            if '\n' in self.buffer:
                lines = self.buffer.split('\n')
                for line in lines[:-1]:
                    if line.strip():
                        self.logger_system.log(self.level, line.strip())
                self.buffer = lines[-1]
    
    def flush(self):
        """刷新缓冲区"""
        if self.file_obj:
            self.file_obj.flush()
        if self.buffer.strip():
            self.logger_system.log(self.level, self.buffer.strip())
            self.buffer = ""
    
    def close(self):
        """关闭文件"""
        if self.file_obj:
            self.file_obj.close()


# 创建全局日志实例
logger = LoggerSystem()

# 提供便捷的日志函数
def log_debug(message: Any):
    """调试日志"""
    logger.debug(message)

def log_info(message: Any):
    """信息日志"""
    logger.info(message)

def log_warning(message: Any):
    """警告日志"""
    logger.warning(message)

def log_error(message: Any):
    """错误日志"""
    logger.error(message)

def log_critical(message: Any):
    """严重错误日志"""
    logger.critical(message)

def log_exception(message: Any):
    """异常日志"""
    logger.exception(message)

# 兼容print的函数
def safe_print(*args, **kwargs):
    """安全的print替代函数"""
    try:
        # 将参数转换为字符串
        message = ' '.join(str(arg) for arg in args)
        
        # 检查是否有特殊的print参数
        sep = kwargs.get('sep', ' ')
        end = kwargs.get('end', '\n')
        
        if sep != ' ':
            message = sep.join(str(arg) for arg in args)
        
        # 记录到日志
        logger.info(message)
        
    except Exception:
        # 如果日志记录失败，静默处理
        pass

# 设置GUI回调的便捷函数
def set_gui_log_callback(callback: Callable[[str, str], None]):
    """设置GUI日志回调"""
    logger.set_gui_callback(callback)

# 获取日志文件路径的便捷函数
def get_log_file_path() -> str:
    """获取日志文件路径"""
    return logger.get_log_file_path()
