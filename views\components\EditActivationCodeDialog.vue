<template>
  <el-dialog
    title="编辑激活码"
    :visible.sync="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    @open="loadCodeInfo"
    @close="handleClose"
  >
    <div v-loading="loading">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="激活码">
          <el-input
            v-model="activationCode"
            :disabled="true"
            placeholder="激活码"
          />
          <div class="form-tip">激活码不可修改</div>
        </el-form-item>
        
        <el-form-item label="当前状态">
          <el-tag
            :type="getStatusTagType(codeInfo.status)"
            size="medium"
          >
            {{ getStatusText(codeInfo.status) }}
          </el-tag>
          <div class="form-tip" v-if="codeInfo.status === 1">
            已激活的激活码只能修改备注和过期时间
          </div>
        </el-form-item>
        
        <el-form-item label="用户ID" prop="userId" v-if="codeInfo.status !== 1">
          <el-input
            v-model="form.userId"
            placeholder="请输入用户ID（可选）"
            clearable
          />
          <div class="form-tip">用于标识激活码的归属用户</div>
        </el-form-item>
        
        <el-form-item label="最大激活次数" prop="maxActivations" v-if="codeInfo.status !== 1">
          <el-input-number
            v-model="form.maxActivations"
            :min="1"
            :max="100"
            placeholder="最大激活次数"
            style="width: 100%"
          />
          <div class="form-tip">该激活码最多可以激活的设备数量</div>
        </el-form-item>
        
        <el-form-item label="过期时间" prop="expireTime">
          <el-date-picker
            v-model="form.expireTime"
            type="datetime"
            placeholder="选择过期时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%"
          />
          <div class="form-tip">留空表示永不过期</div>
        </el-form-item>
        
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        保存修改
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import activationApi from '@/api/activation'

export default {
  name: 'EditActivationCodeDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    activationCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      submitting: false,
      codeInfo: {},
      
      form: {
        userId: '',
        maxActivations: 1,
        expireTime: '',
        remark: ''
      },
      
      rules: {
        maxActivations: [
          { type: 'number', min: 1, max: 100, message: '激活次数必须在1-100之间', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    // 加载激活码信息
    async loadCodeInfo() {
      if (!this.activationCode) return
      
      this.loading = true
      try {
        const response = await activationApi.getActivationCodeInfo(this.activationCode)
        if (response.success) {
          this.codeInfo = response.activationCode
          
          // 填充表单数据
          this.form.userId = this.codeInfo.userId || ''
          this.form.maxActivations = this.codeInfo.maxActivations || 1
          this.form.expireTime = this.codeInfo.expireTime || ''
          this.form.remark = this.codeInfo.remark || ''
        } else {
          this.$message.error(response.message || '获取激活码信息失败')
        }
      } catch (error) {
        console.error('加载激活码信息失败:', error)
        this.$message.error('获取激活码信息失败')
      } finally {
        this.loading = false
      }
    },
    
    // 提交表单
    async handleSubmit() {
      try {
        await this.$refs.form.validate()
        
        this.submitting = true
        
        // 准备提交数据 - 先只发送备注，避免复杂的数据类型问题
        const submitData = {
          remark: this.form.remark || null
        }

        // 如果激活码未激活，添加其他字段
        if (this.codeInfo.status !== 1) {
          if (this.form.userId) {
            submitData.userId = this.form.userId
          }
          if (this.form.maxActivations) {
            submitData.maxActivations = parseInt(this.form.maxActivations)
          }
        }

        // 处理过期时间
        if (this.form.expireTime) {
          const formattedTime = this.formatDateTime(this.form.expireTime)
          if (formattedTime) {
            submitData.expireTime = formattedTime
          }
        }

        console.log('提交数据:', submitData)
        console.log('激活码:', this.activationCode)
        console.log('激活码状态:', this.codeInfo.status)

        const response = await activationApi.updateActivationCode(this.activationCode, submitData)
        
        if (response.success) {
          this.$message.success('激活码更新成功')
          this.$emit('success')
          this.dialogVisible = false
        } else {
          this.$message.error(response.message || '更新激活码失败')
        }
      } catch (error) {
        if (error !== false) { // 表单验证失败时不显示错误
          console.error('更新激活码失败:', error)

          // 详细的错误信息
          let errorMessage = '更新激活码失败'
          if (error.response) {
            console.error('错误响应:', error.response)
            if (error.response.data && error.response.data.message) {
              errorMessage = error.response.data.message
            } else {
              errorMessage = `服务器错误 (${error.response.status})`
            }
          } else if (error.message) {
            errorMessage = error.message
          }

          this.$message.error(errorMessage)
        }
      } finally {
        this.submitting = false
      }
    },
    
    // 关闭对话框
    handleClose() {
      this.resetForm()
    },
    
    // 重置表单
    resetForm() {
      this.form = {
        userId: '',
        maxActivations: 1,
        expireTime: '',
        remark: ''
      }
      this.codeInfo = {}
      if (this.$refs.form) {
        this.$refs.form.clearValidate()
      }
    },
    
    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return null

      try {
        let date
        if (typeof dateTime === 'string') {
          date = new Date(dateTime)
        } else {
          date = dateTime
        }

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          console.error('无效的日期:', dateTime)
          return null
        }

        // 返回本地时间格式，不包含时区信息
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        const seconds = String(date.getSeconds()).padStart(2, '0')

        return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`
      } catch (error) {
        console.error('日期格式化失败:', error, dateTime)
        return null
      }
    },
    
    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        0: 'info',     // 未激活
        1: 'success',  // 已激活
        2: 'danger'    // 已禁用
      }
      return statusMap[status] || 'info'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '未激活',
        1: '已激活',
        2: '已禁用'
      }
      return statusMap[status] || '未知'
    }
  }
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
