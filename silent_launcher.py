#!/usr/bin/env python3
"""
静默启动器 - 确保程序启动时不显示控制台窗口
用于打包后的程序启动，完全禁用控制台输出
"""

import sys
import os
import subprocess
from pathlib import Path

def hide_console():
    """隐藏控制台窗口"""
    try:
        import ctypes
        # 获取控制台窗口句柄
        hwnd = ctypes.windll.kernel32.GetConsoleWindow()
        if hwnd != 0:
            # 隐藏控制台窗口
            ctypes.windll.user32.ShowWindow(hwnd, 0)
    except Exception:
        pass

def redirect_output():
    """重定向标准输出和错误输出"""
    try:
        # 重定向到空设备
        if os.name == 'nt':  # Windows
            devnull = open('nul', 'w')
        else:  # Unix/Linux
            devnull = open('/dev/null', 'w')
        
        sys.stdout = devnull
        sys.stderr = devnull
    except Exception:
        pass

def main():
    """主函数"""
    # 隐藏控制台窗口
    hide_console()
    
    # 重定向输出
    redirect_output()
    
    # 导入并启动主程序
    try:
        # 确保日志系统可用
        from logger_system import log_info, log_error
        
        # 启动主程序
        from enhanced_video_deduplication_gui import main as gui_main
        gui_main()
        
    except ImportError:
        # 如果导入失败，尝试直接运行
        try:
            import enhanced_video_deduplication_gui
        except Exception:
            # 最后的备选方案
            sys.exit(1)
    except Exception:
        # 静默处理所有异常
        sys.exit(1)

if __name__ == "__main__":
    main()
