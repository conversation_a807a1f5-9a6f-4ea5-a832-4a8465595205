#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ImageMagick已安装情况诊断和修复脚本
"""

import os
import sys
import subprocess
import glob
from pathlib import Path

def find_imagemagick_installations():
    """查找所有可能的ImageMagick安装"""
    print("🔍 搜索ImageMagick安装...")
    
    installations = []
    
    # 常见安装路径
    search_paths = [
        r"C:\Program Files\ImageMagick*",
        r"C:\Program Files (x86)\ImageMagick*",
        r"C:\ImageMagick*",
        r"C:\tools\ImageMagick*",
        r"D:\Program Files\ImageMagick*",
        r"D:\ImageMagick*"
    ]
    
    for pattern in search_paths:
        matches = glob.glob(pattern)
        for match in matches:
            if os.path.isdir(match):
                # 检查是否包含magick.exe
                magick_exe = os.path.join(match, "magick.exe")
                convert_exe = os.path.join(match, "convert.exe")
                
                if os.path.exists(magick_exe) or os.path.exists(convert_exe):
                    installations.append({
                        'path': match,
                        'magick_exe': magick_exe if os.path.exists(magick_exe) else None,
                        'convert_exe': convert_exe if os.path.exists(convert_exe) else None
                    })
    
    return installations

def test_imagemagick_command():
    """测试ImageMagick命令行"""
    print("\n🧪 测试ImageMagick命令行...")
    
    commands = ['magick', 'convert', 'identify']
    working_commands = []
    
    for cmd in commands:
        try:
            result = subprocess.run([cmd, '-version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                version_line = result.stdout.split('\n')[0]
                print(f"  ✅ {cmd}: {version_line}")
                working_commands.append(cmd)
            else:
                print(f"  ❌ {cmd}: 命令失败")
        except FileNotFoundError:
            print(f"  ❌ {cmd}: 命令未找到")
        except subprocess.TimeoutExpired:
            print(f"  ❌ {cmd}: 命令超时")
        except Exception as e:
            print(f"  ❌ {cmd}: {e}")
    
    return working_commands

def check_path_environment():
    """检查PATH环境变量"""
    print("\n🔍 检查PATH环境变量...")
    
    path_env = os.environ.get('PATH', '')
    path_dirs = path_env.split(';')
    
    imagemagick_in_path = []
    for path_dir in path_dirs:
        if 'imagemagick' in path_dir.lower():
            imagemagick_in_path.append(path_dir)
            print(f"  ✅ 找到ImageMagick路径: {path_dir}")
    
    if not imagemagick_in_path:
        print("  ❌ PATH中未找到ImageMagick路径")
    
    return imagemagick_in_path

def test_moviepy_config():
    """测试MoviePy配置"""
    print("\n🔧 测试MoviePy配置...")
    
    try:
        import moviepy.config as config
        
        # 检查当前配置
        if hasattr(config, 'IMAGEMAGICK_BINARY'):
            current_binary = getattr(config, 'IMAGEMAGICK_BINARY')
            print(f"  当前IMAGEMAGICK_BINARY: {current_binary}")
            
            if current_binary and os.path.exists(current_binary):
                print(f"  ✅ 配置的二进制文件存在")
                return current_binary
            else:
                print(f"  ❌ 配置的二进制文件不存在")
        else:
            print("  ❌ 未设置IMAGEMAGICK_BINARY")
        
        return None
        
    except ImportError:
        print("  ❌ 无法导入moviepy.config")
        return None

def create_moviepy_config(binary_path):
    """创建MoviePy配置文件"""
    print(f"\n⚙️ 创建MoviePy配置: {binary_path}")
    
    try:
        # 方法1: 创建本地配置文件
        config_content = f'''# MoviePy ImageMagick配置
IMAGEMAGICK_BINARY = r"{binary_path}"
'''
        
        with open('moviepy_config.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        print("  ✅ 已创建 moviepy_config.py")
        
        # 方法2: 设置环境变量
        os.environ['IMAGEMAGICK_BINARY'] = binary_path
        print("  ✅ 已设置环境变量 IMAGEMAGICK_BINARY")
        
        # 方法3: 直接设置moviepy配置
        try:
            import moviepy.config as config
            config.IMAGEMAGICK_BINARY = binary_path
            print("  ✅ 已设置 moviepy.config.IMAGEMAGICK_BINARY")
        except Exception as e:
            print(f"  ⚠️ 设置moviepy配置失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 创建配置失败: {e}")
        return False

def test_textclip_with_config(binary_path):
    """使用指定配置测试TextClip"""
    print(f"\n🧪 测试TextClip (使用 {binary_path})...")
    
    try:
        # 临时设置配置
        original_env = os.environ.get('IMAGEMAGICK_BINARY')
        os.environ['IMAGEMAGICK_BINARY'] = binary_path
        
        # 重新导入moviepy以应用新配置
        import importlib
        import moviepy.config
        importlib.reload(moviepy.config)
        moviepy.config.IMAGEMAGICK_BINARY = binary_path
        
        from moviepy.editor import TextClip
        
        # 创建测试TextClip
        txt_clip = TextClip("测试", fontsize=24, color='white', duration=1)
        print("  ✅ TextClip创建成功")
        
        # 清理
        txt_clip.close()
        
        # 恢复环境变量
        if original_env:
            os.environ['IMAGEMAGICK_BINARY'] = original_env
        elif 'IMAGEMAGICK_BINARY' in os.environ:
            del os.environ['IMAGEMAGICK_BINARY']
        
        return True
        
    except Exception as e:
        print(f"  ❌ TextClip测试失败: {e}")
        return False

def fix_imagemagick_config():
    """修复ImageMagick配置"""
    print("\n🔧 开始修复ImageMagick配置...")
    
    # 1. 查找安装
    installations = find_imagemagick_installations()
    
    if not installations:
        print("❌ 未找到ImageMagick安装，请确认已正确安装")
        return False
    
    print(f"\n找到 {len(installations)} 个ImageMagick安装:")
    for i, install in enumerate(installations):
        print(f"  {i+1}. {install['path']}")
        if install['magick_exe']:
            print(f"     magick.exe: ✅")
        if install['convert_exe']:
            print(f"     convert.exe: ✅")
    
    # 2. 选择最佳安装
    best_install = None
    for install in installations:
        if install['magick_exe']:  # 优先选择有magick.exe的
            best_install = install
            break
    
    if not best_install and installations:
        best_install = installations[0]  # 回退到第一个
    
    if not best_install:
        print("❌ 未找到可用的ImageMagick安装")
        return False
    
    print(f"\n选择安装: {best_install['path']}")
    
    # 3. 确定二进制文件路径
    binary_path = None
    if best_install['magick_exe']:
        binary_path = best_install['magick_exe']
    elif best_install['convert_exe']:
        binary_path = best_install['convert_exe']
    
    if not binary_path:
        print("❌ 未找到可执行文件")
        return False
    
    print(f"使用二进制文件: {binary_path}")
    
    # 4. 创建配置
    if not create_moviepy_config(binary_path):
        return False
    
    # 5. 测试配置
    if test_textclip_with_config(binary_path):
        print("\n🎉 ImageMagick配置修复成功！")
        return True
    else:
        print("\n❌ 配置修复失败")
        return False

def main():
    """主诊断和修复流程"""
    print("=" * 60)
    print("🔧 ImageMagick已安装情况诊断和修复")
    print("=" * 60)
    
    # 步骤1: 查找安装
    installations = find_imagemagick_installations()
    if installations:
        print(f"✅ 找到 {len(installations)} 个ImageMagick安装")
    else:
        print("❌ 未找到ImageMagick安装")
        return False
    
    # 步骤2: 测试命令行
    working_commands = test_imagemagick_command()
    
    # 步骤3: 检查PATH
    path_entries = check_path_environment()
    
    # 步骤4: 检查MoviePy配置
    current_config = test_moviepy_config()
    
    # 步骤5: 尝试修复
    print("\n" + "="*40)
    print("🛠️ 开始修复配置")
    print("="*40)
    
    if fix_imagemagick_config():
        print("\n✅ 修复完成！现在可以正常使用智能混剪功能了")
        
        print("\n📋 修复总结:")
        print("  • 找到并配置了ImageMagick路径")
        print("  • 创建了MoviePy配置文件")
        print("  • TextClip功能已验证正常")
        
        return True
    else:
        print("\n❌ 自动修复失败")
        print("\n📋 手动修复建议:")
        if installations:
            best = installations[0]
            binary = best.get('magick_exe') or best.get('convert_exe')
            if binary:
                print(f"1. 在项目根目录创建 moviepy_config.py 文件")
                print(f"2. 文件内容:")
                print(f"   IMAGEMAGICK_BINARY = r\"{binary}\"")
                print(f"3. 重启Python程序")
        
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
