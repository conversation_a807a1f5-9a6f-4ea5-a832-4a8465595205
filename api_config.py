#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API配置管理模块
统一管理API相关的配置信息
"""

import os
from typing import Dict, Any, Optional
from api_secrets import api_secrets


class APIConfig:
    """API配置管理器 - 内置配置，防止客户端访问"""

    def __init__(self):
        """
        初始化API配置管理器
        配置信息直接写在代码中，不使用外部文件
        """
        self.config = self._get_builtin_config()
    
    def _get_builtin_config(self) -> Dict[str, Any]:
        """
        获取内置配置信息
        从加密的密钥管理器获取配置，防止客户端直接访问

        Returns:
            配置字典
        """
        # 从加密的密钥管理器获取配置
        return api_secrets.get_api_config()
    
    def _get_server_url(self, index: int = 0) -> str:
        """
        获取服务器URL（内部方法）

        Args:
            index: 服务器索引

        Returns:
            服务器URL
        """
        servers = api_secrets.get_api_servers()

        if 0 <= index < len(servers):
            return servers[index]
        return servers[0] if servers else "http://localhost:8080"
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键（如 "api.base_url"）
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """
        设置配置值（仅运行时有效，不持久化）

        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        config = self.config

        # 导航到最后一级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]

        # 设置值（仅在内存中，不保存到文件）
        config[keys[-1]] = value
    
    def get_base_url(self) -> str:
        """获取基础URL"""
        return self.get("api.base_url", "http://localhost:8080")
    
    def get_backup_urls(self) -> list:
        """获取备用URL列表"""
        return self.get("api.backup_urls", [])
    
    def get_timeout(self) -> int:
        """获取超时时间"""
        return self.get("api.timeout", 10)
    
    def get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return self.get("headers", {})
    
    def get_endpoint(self, name: str) -> str:
        """
        获取API端点
        
        Args:
            name: 端点名称
            
        Returns:
            端点路径
        """
        return self.get(f"endpoints.{name}", "")
    
    def get_full_endpoint(self, name: str) -> str:
        """
        获取完整的API端点URL
        
        Args:
            name: 端点名称
            
        Returns:
            完整的端点URL
        """
        base_url = self.get_base_url()
        activation_prefix = self.get_endpoint("activation")
        endpoint = self.get_endpoint(name)
        
        if name == "activation":
            return f"{base_url}{activation_prefix}"
        else:
            return f"{base_url}{activation_prefix}{endpoint}"
    
    def is_timestamp_check_enabled(self) -> bool:
        """检查是否启用时间戳验证"""
        return self.get("security.enable_timestamp_check", True)
    
    def get_timestamp_tolerance(self) -> int:
        """获取时间戳容差"""
        return self.get("security.timestamp_tolerance", 300)
    
    def is_offline_mode_enabled(self) -> bool:
        """检查是否启用离线模式"""
        return self.get("offline.enable_offline_mode", True)
    
    def get_grace_period_days(self) -> int:
        """获取离线宽限期天数"""
        return self.get("offline.grace_period_days", 7)
    
    def is_request_logging_enabled(self) -> bool:
        """检查是否启用请求日志"""
        return self.get("logging.enable_request_logging", True)
    
    def is_response_logging_enabled(self) -> bool:
        """检查是否启用响应日志"""
        return self.get("logging.enable_response_logging", True)
    
    def get_retry_count(self) -> int:
        """获取重试次数"""
        return self.get("api.retry_count", 3)
    
    def get_retry_delay(self) -> float:
        """获取重试延迟"""
        return self.get("api.retry_delay", 1.0)
    
    def update_base_url(self, url: str):
        """
        更新基础URL（仅运行时有效）

        Args:
            url: 新的基础URL
        """
        self.set("api.base_url", url)

    def add_backup_url(self, url: str):
        """
        添加备用URL（仅运行时有效）

        Args:
            url: 备用URL
        """
        backup_urls = self.get_backup_urls()
        if url not in backup_urls:
            backup_urls.append(url)
            self.set("api.backup_urls", backup_urls)

    def remove_backup_url(self, url: str):
        """
        移除备用URL（仅运行时有效）

        Args:
            url: 要移除的备用URL
        """
        backup_urls = self.get_backup_urls()
        if url in backup_urls:
            backup_urls.remove(url)
            self.set("api.backup_urls", backup_urls)

    def reset_to_defaults(self):
        """重置为默认配置"""
        self.config = self._get_builtin_config()

    def switch_to_backup_server(self, index: int = 0):
        """
        切换到备用服务器

        Args:
            index: 备用服务器索引
        """
        backup_urls = self.get_backup_urls()
        if 0 <= index < len(backup_urls):
            self.update_base_url(backup_urls[index])
            return True
        return False


# 创建全局配置实例
api_config = APIConfig()


# 便捷函数
def get_api_base_url() -> str:
    """获取API基础URL"""
    return api_config.get_base_url()


def get_api_timeout() -> int:
    """获取API超时时间"""
    return api_config.get_timeout()


def get_api_headers() -> Dict[str, str]:
    """获取API请求头"""
    return api_config.get_headers()


def update_api_base_url(url: str):
    """更新API基础URL"""
    api_config.update_base_url(url)


if __name__ == "__main__":
    # 测试配置管理器
    print("🔍 测试API配置管理器...")
    
    print(f"基础URL: {api_config.get_base_url()}")
    print(f"超时时间: {api_config.get_timeout()}")
    print(f"请求头: {api_config.get_headers()}")
    print(f"验证端点: {api_config.get_full_endpoint('verify')}")
    print(f"激活码列表端点: {api_config.get_full_endpoint('list')}")
    
    print("✅ API配置管理器测试完成")
